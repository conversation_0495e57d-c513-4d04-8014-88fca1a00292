# 全局 amountInputConfig 修复总结

## 问题描述
项目中多个文件使用了 `amountInputConfig` 但没有正确引入 `getNumberInputConfig` 函数，导致运行时错误：
```
Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'min')
```

## 修复方案
统一为所有使用 `amountInputConfig` 的文件添加：
1. 导入 `getNumberInputConfig` 函数
2. 添加 `amountInputConfig` 计算属性
3. 确保在组合式函数中正确导出

## 已修复的文件列表

### 组件文件 (Components)

#### 1. src/components/inventory/SkuEditModal.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 2. src/components/financial/ReceptForm.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`
- ✅ 更新表格内 n-input-number 使用全局配置

#### 3. src/components/financial/ReceivableDialog.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 4. src/components/inventory/StockFormModal.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 5. src/components/financial/PayableDialog.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 6. src/components/inventory/OutboundDialog.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 7. src/components/inventory/StartBillFormModal.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 8. src/components/orders/sections/ProductInfoSection.vue
- ✅ 添加计算属性：`amountInputConfig`（已有导入）

#### 9. src/components/orders/sections/DerivativeIncomeSection.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 10. src/components/orders/sections/VehicleExchangeSection.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 11. src/components/orders/sections/ExclusiveDiscountSection.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 12. src/components/inventory/OutboundBillModal.vue
- ✅ 在解构赋值中添加：`amountInputConfig`

### 组合式函数文件 (Composables)

#### 13. src/components/inventory/OutboundBillModal.js
- ✅ 添加导入：`import { getNumberInputConfig } from '@/config/inputConfig'`
- ✅ 添加计算属性：`amountInputConfig`
- ✅ 在返回对象中导出：`amountInputConfig`

#### 14. src/views/operation/AddSalesPriceLimitRule.js
- ✅ 添加导入：`import { getNumberInputConfig } from '@/config/inputConfig'`
- ✅ 添加计算属性：`amountInputConfig`
- ✅ 在返回对象中导出：`amountInputConfig`

#### 15. src/views/exchange/UsedVehicleInventoryPage.js
- ✅ 添加导入：`import { getNumberInputConfig } from '@/config/inputConfig'`
- ✅ 添加计算属性：`amountInputConfig`
- ✅ 在返回对象中导出：`amountInputConfig`

### 视图文件 (Views)

#### 16. src/views/financial/components/OutboundModal.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

## 全局配置详情

所有金额输入框现在统一使用以下配置：
```javascript
// src/config/inputConfig.js
amount: {
  min: 0,
  max: 1000000,
  precision: 2,
  step: 0.01,
  placeholder: '请输入金额'
}
```

## 使用方式

### 在 Vue 组件中
```javascript
import { computed } from 'vue'
import { getNumberInputConfig } from '@/config/inputConfig'

const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
})
```

### 在组合式函数中
```javascript
import { computed } from 'vue'
import { getNumberInputConfig } from '@/config/inputConfig'

export function useMyComposable() {
  const amountInputConfig = computed(() => {
    return getNumberInputConfig("amount");
  })
  
  return {
    amountInputConfig,
    // ... 其他导出
  }
}
```

### 在模板中使用
```vue
<n-input-number
  :min="amountInputConfig.min"
  :max="amountInputConfig.max"
  :step="amountInputConfig.step"
  :precision="amountInputConfig.precision"
  v-model:value="form.amount"
  button-placement="both"
/>
```

#### 17. src/components/QueryPage.js
- ✅ 添加导入：`import { getNumberInputConfig } from '@/config/inputConfig'`
- ✅ 添加计算属性：`amountInputConfig`
- ✅ 在返回对象中导出：`amountInputConfig`

#### 18. src/components/QueryPage.vue
- ✅ 在解构赋值中添加：`amountInputConfig`

#### 19. src/components/StandardCrud.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 20. src/components/workflows/FormDesigner.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 21. src/views/system/MenusPage.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 22. src/views/inventory/StockPage.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 23. src/views/inventory/BaseDictPage.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

#### 24. src/views/flow/NewCarSalesFlow.vue
- ✅ 添加导入：`import { getNumberInputConfig } from "@/config/inputConfig"`
- ✅ 添加计算属性：`amountInputConfig`

## 高效修复方法总结

通过使用 shell 命令精确定位需要修复的文件，我们大大提高了修复效率：

```bash
# 1. 找到同时使用 NInputNumber 和 amountInputConfig 的文件
find src -name "*.vue" -o -name "*.js" | xargs grep -l "NInputNumber\|n-input-number" | xargs grep -l "amountInputConfig"

# 2. 检查哪些文件缺少配置
for file in $(上述命令结果); do
  echo "=== $file ===";
  grep -n "getNumberInputConfig\|amountInputConfig.*computed" "$file" || echo "❌ 缺少导入或计算属性";
done
```

这种方法比逐个文件搜索效率高得多，能够快速缩小需要修复的文件范围。

## 修复结果
- ✅ 解决了所有 `Cannot read properties of undefined (reading 'min')` 错误
- ✅ 统一了全局金额输入框的最大值、最小值、步长等配置
- ✅ 提高了代码的可维护性和一致性
- ✅ 支持集中管理数字输入框配置
- ✅ 总共修复了 **24 个文件**，包括组件、视图和组合式函数

## 测试建议
1. 测试所有包含金额输入框的页面和组件
2. 验证金额输入框的最大值、最小值限制是否生效
3. 确认步长和精度设置是否正确
4. 检查表格内的可编辑金额输入框是否正常工作
5. 特别测试之前报错的 OutboundBillModal.vue 和 SkuEditModal.vue
