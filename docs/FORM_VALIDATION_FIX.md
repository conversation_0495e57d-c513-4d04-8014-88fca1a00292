# 表单校验逻辑修复说明

## 问题描述

在定金订单模态框（`DepositOrderModal.vue`）中，存在以下表单校验问题：

1. **客户选择校验不完整**：用户手动输入未匹配的客户信息时，表单校验没有正确阻止提交
2. **输入有效性校验缺失**：手机号码没有格式校验，客户名称没有长度限制
3. **字段长度校验不足**：备注、地址等字段缺少长度限制

具体表现为：
- 用户手动输入客户名称和电话，系统无法找到匹配的客户（`customerId` 为 `null`），但表单校验仍然通过
- 可以输入无效的手机号码格式
- 客户名称可以输入超长内容
- 备注等字段没有长度限制

## 根本原因

1. **校验规则不完整**：
   - `customerName` 字段的校验规则只检查字段是否为空，没有验证 `customerId` 是否存在
   - 缺少手机号码格式校验
   - 缺少字段长度限制校验

2. **状态管理不一致**：当用户手动修改客户信息时，没有及时清空 `customerId`

3. **缺少联动校验**：客户名称和电话修改时，没有重置客户选择状态

4. **输入控制不足**：没有对用户输入进行实时格式化和限制

## 修复方案

### 1. 增强表单校验规则

**文件**: `src/components/orders/DepositOrderModal.vue`

#### 1.1 客户名称校验（增加长度限制和客户ID验证）

```javascript
customerName: {
  required: true,
  message: "请选择客户",
  trigger: "blur",
  validator: (_, value) => {
    if (!value) {
      return new Error("请选择客户");
    }
    // 检查客户名称长度
    if (value.length > 20) {
      return new Error("客户名称不能超过20个字符");
    }
    // 检查是否真正选择了客户（customerId必须存在）
    if (!form.customerId) {
      return new Error("请从客户列表中选择客户，或确保输入的客户信息完全匹配");
    }
    return true;
  },
},
```

#### 1.2 手机号码校验（格式验证）

```javascript
customerPhone: {
  required: true,
  message: "请输入联系电话",
  trigger: "blur",
  validator: (_, value) => {
    if (!value) {
      return new Error("请输入联系电话");
    }
    // 手机号码格式校验（支持11位手机号）
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(value)) {
      return new Error("请输入正确的手机号码格式");
    }
    return true;
  },
},
```

#### 1.3 其他字段校验

```javascript
// 销售单位校验
salesOrgName: {
  required: true,
  message: "请选择销售单位",
  trigger: "blur",
  validator: (_, value) => {
    if (!value) {
      return new Error("请选择销售顾问后自动填充销售单位");
    }
    return true;
  },
},

// 备注长度校验
remark: {
  required: false,
  trigger: "blur",
  validator: (_, value) => {
    if (value && value.length > 500) {
      return new Error("备注内容不能超过500个字符");
    }
    return true;
  },
},

// 客户地址长度校验
customerAddress: {
  required: false,
  trigger: "blur",
  validator: (_, value) => {
    if (value && value.length > 100) {
      return new Error("客户地址不能超过100个字符");
    }
    return true;
  },
},

// 销售顾问姓名长度校验
salesAgentName: {
  required: false,
  trigger: "blur",
  validator: (_, value) => {
    if (value && value.length > 20) {
      return new Error("销售顾问姓名不能超过20个字符");
    }
    return true;
  },
},
```

### 2. 添加提交前校验

**文件**: `src/components/orders/DepositOrderModal.vue`

在 `handleSave` 方法中添加客户校验：

```javascript
// 检查是否选择了客户
if (!form.customerId) {
  messages.error("请从客户列表中选择客户，或确保输入的客户信息完全匹配");
  return;
}
```

### 3. 改进客户信息组件

**文件**: `src/components/orders/sections/CustomerInfoSection.vue`

#### 3.1 添加客户名称失焦处理

```javascript
// 处理客户名称失焦事件
const handleCustomerNameBlur = () => {
  // 如果客户名称被手动修改且之前有选择客户，清空客户ID
  if (customerFound.value && props.form.customerId) {
    customerFound.value = false;
    props.form.customerId = null;
    console.log("客户名称已修改，已清空customerId");
  }
};
```

#### 3.2 增强监听器

```javascript
// 监听客户名称变化，重置客户状态
watch(
  () => props.form.customerName,
  (newName, oldName) => {
    // 如果客户名称发生变化且不是通过接口绑定的变化，重置客户状态
    if (newName !== oldName && customerFound.value) {
      customerFound.value = false;
      // 清空customerId，确保表单校验能够正确触发
      props.form.customerId = null;
    }
  }
);
```

#### 3.3 改进客户查找失败处理

```javascript
} else {
  // 客户不存在或查询失败，清空customerId确保表单校验失败
  customerFound.value = false;
  props.form.customerId = null;
  console.log("未找到匹配的客户信息，已清空customerId");
}
```

### 4. 改进用户输入体验

#### 4.1 客户名称输入框

```html
<n-input
  v-model:value="form.customerName"
  placeholder="请输入客户名称（不超过20字符）"
  :readonly="!isFieldEditable('customerName') || isCustomerSelected"
  @blur="handleCustomerNameBlur"
  maxlength="20"
  show-count
/>
```

#### 4.2 手机号码输入框

```html
<n-input
  v-model:value="form.customerPhone"
  placeholder="请输入11位手机号码"
  :readonly="!isFieldEditable('customerPhone') || isCustomerSelected"
  @blur="handleCustomerPhoneBlur"
  @input="handleCustomerPhoneInput"
  maxlength="11"
>
```

```javascript
// 处理手机号码输入事件
const handleCustomerPhoneInput = (value) => {
  // 只允许输入数字
  const numericValue = value.replace(/\D/g, "");
  if (numericValue !== value) {
    props.form.customerPhone = numericValue;
  }

  // 如果手机号码被手动修改且之前有选择客户，清空客户ID
  if (customerFound.value && props.form.customerId) {
    customerFound.value = false;
    props.form.customerId = null;
    console.log("手机号码已修改，已清空customerId");
  }
};
```

#### 4.3 备注输入框

```html
<n-input
  v-model:value="currentRemark"
  type="textarea"
  placeholder="备注信息将传递给后续节点处理（不超过500字符）"
  :autosize="{ minRows: 3, maxRows: 6 }"
  :disabled="!isFieldEditable('remark')"
  maxlength="500"
  show-count
/>
```

## 测试验证

### 测试场景 1：手动输入未匹配客户
1. 打开定金订单创建页面
2. 手动输入客户名称（不通过选择器）
3. 输入联系电话（系统无法匹配到现有客户）
4. 填写其他必填字段
5. 点击提交

**预期结果**：表单校验失败，显示错误信息"请从客户列表中选择客户，或确保输入的客户信息完全匹配"

### 测试场景 2：修改已选择的客户信息
1. 通过客户选择器选择一个客户
2. 手动修改客户名称或电话
3. 点击提交

**预期结果**：表单校验失败，因为 `customerId` 已被清空

### 测试场景 3：正常客户选择流程
1. 通过客户选择器选择客户
2. 填写其他必填字段
3. 点击提交

**预期结果**：表单校验通过，正常提交

## 影响范围

- **主要影响**：定金订单创建和编辑功能
- **次要影响**：客户信息输入体验改进
- **兼容性**：向后兼容，不影响现有正常流程

## 注意事项

1. 修改后的校验更加严格，要求用户必须通过正确的方式选择客户
2. 用户体验上，需要引导用户使用客户选择器或确保输入信息完全匹配
3. 建议在用户界面上添加更明确的提示信息
