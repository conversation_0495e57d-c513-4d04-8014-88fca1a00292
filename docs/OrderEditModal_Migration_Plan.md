# OrderEditModal 到 ConfigurableOrderForm 迁移计划

## 概述

本文档制定了从 OrderEditModal 到 ConfigurableOrderForm 的迁移策略，目标是统一订单表单组件，消除代码重复，提高可维护性。

## 问题分析

### 当前存在的问题
1. **逻辑重复**：OrderEditModal 和 ConfigurableOrderForm 功能重复
2. **备注处理不一致**：ConfigurableOrderForm 缺少备注合并逻辑（已修复）
3. **维护成本高**：需要同时维护两套相似的代码
4. **功能不统一**：两个组件的功能特性可能不同步

### 已完成的修复
- ✅ 修复了 ConfigurableOrderForm 的备注处理问题
- ✅ 添加了 remarkSectionRef 引用
- ✅ 确保备注合并逻辑正常工作

## 迁移策略

### 阶段1：准备阶段（当前阶段）
**目标**：确保 ConfigurableOrderForm 功能完整性

**任务**：
- [x] 修复备注处理问题
- [x] 创建测试页面验证功能
- [x] 分析现有使用场景
- [ ] 创建 OrderEditModal 到 ConfigurableOrderForm 的配置映射

### 阶段2：兼容性阶段
**目标**：创建兼容层，确保平滑过渡

**任务**：
- [ ] 创建 OrderEditModal 的配置工厂函数
- [ ] 修改现有使用场景，使用配置化的 ConfigurableOrderForm
- [ ] 保留 OrderEditModal 作为向后兼容

### 阶段3：迁移阶段
**目标**：逐步替换所有使用场景

**任务**：
- [ ] 迁移 OrdersPage.vue
- [ ] 迁移 StockOutboundBillPage.vue
- [ ] 更新相关文档和示例

### 阶段4：清理阶段
**目标**：移除冗余代码

**任务**：
- [ ] 删除 OrderEditModal.vue
- [ ] 删除 useOrderForm.js（如果不再使用）
- [ ] 更新导入语句
- [ ] 清理相关文档

## 具体使用场景迁移

### 1. OrdersPage.vue
**当前使用**：
```vue
<order-edit-modal
  ref="orderEditModalRef"
  v-model:visible="dialogVisible"
  :is-edit="isEdit"
  :title="dialogTitle"
  :vehicle-category-options="vehicleCategoryOptions"
  :order-status-options="orderStatusOptions"
  @save="handleSaveSuccess"
  @cancel="dialogVisible = false"
/>
```

**迁移后**：
```vue
<configurable-order-form
  ref="orderFormRef"
  v-model:visible="dialogVisible"
  :config="orderFormConfig"
  :title="dialogTitle"
  :initial-data="initialData"
  @save="handleSaveSuccess"
  @cancel="dialogVisible = false"
/>
```

### 2. StockOutboundBillPage.vue
类似的迁移模式

## 配置映射

### OrderEditModal Props → ConfigurableOrderForm Config

| OrderEditModal Props | ConfigurableOrderForm Config |
|----------------------|------------------------------|
| `isEdit: true` | `config.mode: 'edit'` |
| `isEdit: false` | `config.mode: 'create'` |
| `vehicleCategoryOptions` | 通过配置控制相关字段 |
| `orderStatusOptions` | 通过配置控制相关字段 |

## 风险评估

### 低风险
- ConfigurableOrderForm 使用相同的 useOrderEditModal 逻辑
- 备注处理问题已修复
- 现有的 section 组件无需修改

### 中等风险
- 需要确保所有 props 和事件正确映射
- 需要测试所有业务场景

### 高风险
- 如果发现 ConfigurableOrderForm 缺少某些功能特性

## 测试计划

### 功能测试
- [ ] 新增订单功能
- [ ] 编辑订单功能
- [ ] 备注添加和历史显示
- [ ] 所有 section 的显示和编辑
- [ ] 表单验证
- [ ] 数据保存

### 回归测试
- [ ] OrdersPage 的所有功能
- [ ] StockOutboundBillPage 的所有功能
- [ ] 相关的业务流程

## 时间计划

### 第1周
- 完成配置映射和兼容层开发
- 创建迁移工具函数

### 第2周
- 迁移 OrdersPage.vue
- 进行功能测试

### 第3周
- 迁移 StockOutboundBillPage.vue
- 进行回归测试

### 第4周
- 清理冗余代码
- 更新文档

## 回滚计划

如果迁移过程中发现重大问题：
1. 立即停止迁移
2. 恢复到 OrderEditModal
3. 分析问题原因
4. 修复 ConfigurableOrderForm 后重新开始

## 成功标准

- [ ] 所有现有功能正常工作
- [ ] 备注功能正确处理历史记录
- [ ] 代码量减少（消除重复）
- [ ] 维护性提高（统一组件）
- [ ] 性能无明显下降
