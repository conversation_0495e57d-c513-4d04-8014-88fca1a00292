# 字典抽象架构设计文档

## 概述

基于响应式方案，我们设计了一套完整的字典抽象架构，提供统一、类型安全、高性能的字典数据管理解决方案。

## 架构组成

### 1. 核心层 (Core Layer)

#### 字典常量定义 (`src/constants/dictConstants.js`)
- **DICT_CODES**: 所有字典编码的常量定义
- **DICT_GROUP_CONFIGS**: 字典组配置信息
- **DICT_DISPLAY_TYPES**: 显示类型常量
- **DICT_COLORS**: 颜色常量
- **COMMON_DICT_CONFIGS**: 常用配置预设

```javascript
import { DICT_CODES } from '@/constants/dictConstants'

// 使用字典编码常量
const brandCode = DICT_CODES.VEHICLE_BRAND
```

#### 字典配置中心 (`src/config/dictConfig.js`)
- **PAGE_DICT_CONFIGS**: 页面字典配置映射
- **DICT_VALIDATION_CONFIGS**: 字典验证规则
- **DICT_LINKAGE_CONFIGS**: 字典联动配置
- **DICT_CACHE_STRATEGIES**: 缓存策略配置

```javascript
import { getPageDictConfig } from '@/config/dictConfig'

// 获取页面字典配置
const pageConfig = getPageDictConfig('ORDER_EDIT')
```

### 2. 组合式函数层 (Composables Layer)

#### 高级字典组合式函数 (`src/composables/useAdvancedDict.js`)

**useAdvancedDictOptions** - 高级字典选项管理
```javascript
import { useAdvancedDictOptions } from '@/composables/useAdvancedDict'

const { options, loading, refresh } = useAdvancedDictOptions(
  DICT_CODES.VEHICLE_BRAND,
  {
    includeAll: true,
    filter: (options, userBrands) => filterByUserPermission(options, userBrands),
    dependencies: [userBrands]
  }
)
```

**useCascadingDict** - 级联字典管理
```javascript
const {
  parentOptions,
  childOptions,
  selectedParentValue,
  setParentValue
} = useCascadingDict(
  DICT_CODES.PROVINCE_CITY,
  DICT_CODES.CITY_DISTRICT
)
```

**useMultipleDict** - 多字典管理
```javascript
const multiDictData = useMultipleDict([
  DICT_CODES.VEHICLE_BRAND,
  DICT_CODES.ORDER_STATUS,
  DICT_CODES.CUSTOMER_TYPE
])
```

**useDictSearch** - 字典搜索
```javascript
const { searchResults, search, clearSearch } = useDictSearch(
  DICT_CODES.VEHICLE_BRAND,
  { searchFields: ['label', 'value', 'remark'] }
)
```

### 3. 组件层 (Components Layer)

#### 通用字典组件

**DictSelector** - 字典选择器
```vue
<dict-selector
  v-model="selectedValue"
  :dict-code="DICT_CODES.VEHICLE_BRAND"
  preset="BRAND_SELECTOR"
  @change="handleChange"
/>
```

**CascadingDictSelector** - 级联字典选择器
```vue
<cascading-dict-selector
  v-model:parent-model-value="province"
  v-model:child-model-value="city"
  :parent-dict-code="DICT_CODES.PROVINCE_CITY"
  :child-dict-code="DICT_CODES.CITY_DISTRICT"
/>
```

**DictTag** - 字典标签显示
```vue
<dict-tag
  :dict-code="DICT_CODES.ORDER_STATUS"
  :value="orderStatus"
  show-icon
  round
/>
```

### 4. 管理层 (Management Layer)

#### 字典管理器 (`src/utils/dictManager.js`)
- 统一的字典数据管理
- 缓存策略实现
- 预加载机制
- 状态监控

```javascript
import { dictManager } from '@/utils/dictManager'

// 初始化字典管理器
await dictManager.initialize({
  preloadHighFrequency: true
})

// 获取字典状态
const status = dictManager.getAllDictStatus()
```

## 使用指南

### 1. 基础使用

**在Vue组件中使用字典选择器**:
```vue
<template>
  <dict-selector
    v-model="selectedBrand"
    :dict-code="DICT_CODES.VEHICLE_BRAND"
    :include-all="false"
    @change="handleBrandChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { DICT_CODES } from '@/constants/dictConstants'
import DictSelector from '@/components/common/DictSelector.vue'

const selectedBrand = ref(null)

const handleBrandChange = (value, item) => {
  console.log('选择的品牌:', value, item)
}
</script>
```

### 2. 高级使用

**使用页面配置**:
```javascript
import { getPageDictConfig } from '@/config/dictConfig'
import { useAdvancedDictOptions } from '@/composables/useAdvancedDict'

// 获取页面配置
const pageConfig = getPageDictConfig('ORDER_EDIT')

// 使用配置创建字典选项
const { options: loanChannelOptions } = useAdvancedDictOptions(
  DICT_CODES.LOAN_CHANNEL,
  pageConfig.loanChannel?.config || {}
)
```

**自定义过滤器**:
```javascript
const { options: brandOptions } = useAdvancedDictOptions(
  DICT_CODES.VEHICLE_BRAND,
  {
    filter: (options, userBrands) => {
      // 根据用户权限过滤品牌
      return options.filter(option => 
        option.value === null || userBrands.includes(option.value)
      )
    },
    dependencies: [userBrands]
  }
)
```

### 3. 级联字典使用

```vue
<template>
  <cascading-dict-selector
    v-model:parent-model-value="selectedProvince"
    v-model:child-model-value="selectedCity"
    :parent-dict-code="DICT_CODES.PROVINCE_CITY"
    :child-dict-code="DICT_CODES.CITY_DISTRICT"
    @change="handleLocationChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { DICT_CODES } from '@/constants/dictConstants'

const selectedProvince = ref(null)
const selectedCity = ref(null)

const handleLocationChange = ({ parent, child }) => {
  console.log('地理位置变化:', { province: parent, city: child })
}
</script>
```

## 最佳实践

### 1. 字典编码管理
- 使用 `DICT_CODES` 常量，避免硬编码字典编码
- 新增字典时先在常量文件中定义

### 2. 配置管理
- 使用页面配置统一管理字典使用方式
- 利用预设配置减少重复代码

### 3. 性能优化
- 高频字典设置预加载
- 合理设置缓存策略
- 使用懒加载减少初始加载时间

### 4. 类型安全
- 使用TypeScript类型定义
- 利用字典验证器检查配置

### 5. 错误处理
- 提供默认值和回退机制
- 使用字典管理器监控状态

## 迁移指南

### 从旧架构迁移到新架构

**旧代码**:
```javascript
import { dictOptions } from '@/mock/dictData'

const loanChannelOptions = computed(() => {
  return dictOptions['loan_channel'].map(item => ({
    label: item.optionLabel,
    value: item.optionValue
  }))
})
```

**新代码**:
```javascript
import { useAdvancedDictOptions } from '@/composables/useAdvancedDict'
import { DICT_CODES } from '@/constants/dictConstants'

const { options: loanChannelOptions } = useAdvancedDictOptions(
  DICT_CODES.LOAN_CHANNEL,
  { includeAll: false }
)
```

## 开发工具

### 1. 字典管理器控制台
```javascript
// 在浏览器控制台中使用
window.dictManager.getAllDictStatus()  // 查看所有字典状态
window.dictManager.refreshDict('vehicle_brand')  // 刷新指定字典
```

### 2. 字典测试工具
```javascript
// 运行字典测试
window.dictTestHelper.runAllDictTests()
```

### 3. 示例页面
访问 `/examples/dict` 查看完整的使用示例。

## 总结

新的字典抽象架构提供了：

1. **统一性** - 所有字典使用统一的接口和规范
2. **类型安全** - 完整的TypeScript类型定义
3. **高性能** - 智能缓存和预加载机制
4. **可扩展** - 灵活的配置和插件机制
5. **易维护** - 清晰的架构分层和文档

通过这套架构，开发者可以更高效、更安全地使用字典数据，同时系统的可维护性和扩展性也得到了显著提升。
