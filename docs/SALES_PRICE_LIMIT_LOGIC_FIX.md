# 销售限价逻辑修复说明

## 问题描述

在销售限价检查逻辑中发现了一个重要问题：系统错误地将**成交价**(`dealAmount`)与销售限价进行对比，而正确的逻辑应该是将**车辆售价**(`salesAmount`)与销售限价进行对比。

### 业务逻辑说明

- **车辆售价** (`salesAmount`)：车辆的原始销售价格，不包含优惠
- **成交价** (`dealAmount`)：扣除各种优惠后的最终成交价格
- **销售限价**：车辆售价的最低限制，用于防止恶意低价销售

**正确的检查逻辑**：车辆售价不得低于销售限价，而不是成交价不得低于销售限价。

## 修改内容

### 1. 订单编辑模态框 (`src/components/orders/OrderEditModal.js`)

**修改位置**：第737-744行

**修改前**：
```javascript
// 销售限价检查
if (currentPriceLimit.value && form.dealAmount) {
  const dealAmountInCents = Math.round(form.dealAmount * 100) // 将成交价从元转换为分
  if (dealAmountInCents < currentPriceLimit.value) {
    const priceLimitInYuan = (currentPriceLimit.value / 100).toFixed(2) // 将限价从分转换为元
    return messages.error(`车辆成交价不得低于销售限价${priceLimitInYuan}元`)
  }
}
```

**修改后**：
```javascript
// 销售限价检查
if (currentPriceLimit.value && form.salesAmount) {
  const salesAmountInCents = Math.round(form.salesAmount * 100) // 将车辆售价从元转换为分
  if (salesAmountInCents < currentPriceLimit.value) {
    const priceLimitInYuan = (currentPriceLimit.value / 100).toFixed(2) // 将限价从分转换为元
    return messages.error(`车辆售价不得低于销售限价${priceLimitInYuan}元`)
  }
}
```

### 2. 出库单模态框 (`src/components/inventory/OutboundBillModal.js`)

#### 2.1 修复 validateSalePrice 函数

**修改位置**：第432-446行

**修改前**：
```javascript
// 验证车辆售价是否低于销售限价
const validateSalePrice = () => {
  const salePrice = form.value.salesAmount || 0
  const limitPrice = form.value.dealAmount || 0  // ❌ 错误：使用成交价作为限价

  if (limitPrice > 0 && salePrice < limitPrice) {
    salePriceLimitError.value = `车辆售价不能低于限价${limitPrice}元`
    return false
  } else {
    salePriceLimitError.value = ''
    return true
  }
}
```

**修改后**：
```javascript
// 验证车辆售价是否低于销售限价
const validateSalePrice = () => {
  // 使用VIN销售限价进行验证
  if (currentVinPriceLimit.value && form.value.salesAmount) {
    const salesAmountInCents = Math.round(form.value.salesAmount * 100) // 将车辆售价从元转换为分
    if (salesAmountInCents < currentVinPriceLimit.value) {
      const priceLimitInYuan = (currentVinPriceLimit.value / 100).toFixed(2) // 将限价从分转换为元
      salePriceLimitError.value = `车辆售价不能低于销售限价${priceLimitInYuan}元`
      return false
    }
  }
  
  salePriceLimitError.value = ''
  return true
}
```

#### 2.2 移除重复的销售限价检查

**修改位置**：第453-467行

**修改前**：
```javascript
// 验证车辆售价是否低于销售限价
if (!validateSalePrice()) {
  messages.error('车辆售价验证失败，请检查售价设置')
  return
}

// VIN销售限价检查（重复检查）
if (currentVinPriceLimit.value && form.value.dealAmount) {
  const dealAmountInCents = Math.round(form.value.dealAmount * 100)
  if (dealAmountInCents < currentVinPriceLimit.value) {
    const priceLimitInYuan = (currentVinPriceLimit.value / 100).toFixed(2)
    messages.error(`成交价不得低于销售限价${priceLimitInYuan}元`)
    return
  }
}
```

**修改后**：
```javascript
// 验证车辆售价是否低于销售限价
if (!validateSalePrice()) {
  messages.error('车辆售价验证失败，请检查售价设置')
  return
}
```

## 修改影响

### 正面影响

1. **逻辑正确性**：销售限价检查现在使用正确的字段（车辆售价）进行验证
2. **业务合规性**：符合销售限价的业务定义和用途
3. **代码简化**：移除了重复的检查逻辑
4. **错误信息准确**：错误提示信息更加准确和清晰

### 测试验证

通过测试验证了以下场景：

1. **车辆售价高于限价**：✅ 正常通过检查
2. **车辆售价等于限价**：✅ 正常通过检查  
3. **车辆售价低于限价**：❌ 正确报错阻止提交
4. **边界情况处理**：✅ 各种边界情况处理正确

### 示例场景

**场景**：车辆售价15万元，现金优惠1万元，成交价14万元，销售限价14.5万元

- **修改前**：检查成交价14万 < 限价14.5万 → ❌ 错误报错
- **修改后**：检查车辆售价15万 > 限价14.5万 → ✅ 正确通过

## 兼容性说明

- ✅ 不影响现有的数据结构
- ✅ 不影响其他业务逻辑
- ✅ 向后兼容，不会破坏现有功能
- ✅ 只修正了检查逻辑，不改变业务流程

## 相关文件

1. `src/components/orders/OrderEditModal.js` - 订单编辑销售限价检查
2. `src/components/inventory/OutboundBillModal.js` - 出库单销售限价检查
3. `src/api/salesPriceLimitRule.js` - 销售限价规则API（无修改）

## 总结

此次修复解决了销售限价检查逻辑中的关键错误，确保系统按照正确的业务规则进行价格验证。修改后的逻辑更加符合业务需求，提高了系统的准确性和可靠性。
