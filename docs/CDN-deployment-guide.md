# CDN部署指南 - 解决SPA路由404问题

## 问题描述

当Vue SPA应用部署在CDN上时，使用History模式的路由会导致直接访问或刷新页面时出现404错误，因为CDN会尝试查找对应的静态文件，但实际上这些路径并不存在。

## 解决方案

### 方案一：使用Hash模式（已实施，推荐）

**配置说明：**
- 开发环境：使用History模式（URL美观，便于开发调试）
- 生产环境：自动切换为Hash模式（避免CDN 404问题）

**URL格式变化：**
- 开发环境：`http://localhost:5173/app/index/1`
- 生产环境：`https://your-domain.com/#/app/index/1`

**优点：**
- ✅ 无需服务器配置
- ✅ 完全避免404问题
- ✅ 开发环境保持美观URL
- ✅ 适合管理系统（SEO要求不高）

**缺点：**
- ❌ 生产环境URL包含#号
- ❌ SEO不友好（但管理系统通常不需要SEO）

### 方案二：配置CDN重定向规则

如果必须使用History模式，需要配置CDN重定向规则：

**腾讯云COS：**
```json
{
  "Condition": {
    "HttpErrorCodeReturnedEquals": "404"
  },
  "Redirect": {
    "ReplaceKeyWith": "index.html"
  }
}
```

**阿里云OSS：**
```xml
<RoutingRule>
  <Condition>
    <HttpErrorCodeReturnedEquals>404</HttpErrorCodeReturnedEquals>
  </Condition>
  <Redirect>
    <ReplaceKeyWith>index.html</ReplaceKeyWith>
  </Redirect>
</RoutingRule>
```

## 测试验证

访问 `/test/router` 页面可以测试当前路由模式和跳转功能。

## 部署步骤

1. **构建生产版本：**
   ```bash
   npm run build
   ```

2. **上传到CDN：**
   - 将 `dist` 目录下的所有文件上传到CDN
   - 确保 `index.html` 在根目录

3. **验证部署：**
   - 访问首页确认正常加载
   - 直接访问 `/#/app/index/1` 确认路由正常
   - 刷新页面确认不会出现404

## 注意事项

1. **缓存策略：**
   - `index.html` 不要设置长期缓存
   - 静态资源（JS/CSS）可以设置长期缓存

2. **环境变量：**
   - 确保 `VITE_CDN_BASE_URL` 正确配置
   - 生产环境会自动使用Hash模式

3. **兼容性：**
   - Hash模式兼容所有浏览器
   - 无需担心服务器配置问题

## 回滚方案

如果需要回滚到纯History模式：

```javascript
// src/router/index.js
const router = createRouter({
  history: createWebHistory(), // 强制使用History模式
  routes: staticRoutes
})
```

但需要同时配置CDN重定向规则。
