# 直营店功能实现说明

## 功能需求
根据开发要求，当用户选择销售地类型为直营店时，需要实现以下功能：

1. 检查销售单位字段是否为空，如果为空则临时禁用直营店选项
2. 当销售单位字段发生变化时，调用API查询该销售单位的直营店
3. 将查询到的直营店选项填充到下拉框中
4. 提交表单时一并传递直营店选择到接口payload中

## API接口
- **接口地址**: `GET /system/option/ZHIYINGDIAN?orgId=$salesOrgId`
- **返回结构**:
```json
{
    "code": 200,
    "data": [
        {
            "id": 91,
            "optionGroup": "ZHIYINGDIAN",
            "optionGroupName": "翔悦达直营店",
            "optionGroupType": "CUSTOMIZE",
            "optionLabel": "默认选项",
            "optionOrder": 0,
            "optionOrgRange": "69",
            "optionOrgRangeNames": "山东翔悦达",
            "optionValue": "DEFAULT_VALUE"
        }
    ],
    "message": "SUCCESS"
}
```

## 实现方案

### 1. 修改的文件
- `src/components/orders/sections/CustomerInfoSection.vue`

### 2. 新增状态变量
```javascript
// 计算属性：判断直营店选项是否应该被禁用
const isDirectStoreDisabled = computed(() => {
  // 如果销售单位ID为空，则禁用直营店选项
  return !props.form.salesOrgId;
});
```

### 3. 修改直营店下拉框
```vue
<n-select
  v-if="form.salesStoreType === '3S'"
  v-model:value="form.salesStoreDetail"
  :options="directStoreOptions"
  placeholder="请选择直营店"
  filterable
  :loading="directStoreLoading"
  :disabled="!isFieldEditable('salesStoreDetail') || isDirectStoreDisabled"
  @update:value="handleDirectStoreChange"
/>
```

### 4. 重构API调用函数
```javascript
// 获取直营店列表
const fetchDirectStores = async () => {
  // 如果销售单位ID为空，不执行查询
  if (!props.form.salesOrgId) {
    directStoreOptions.value = [];
    return;
  }

  directStoreLoading.value = true;
  try {
    // 调用新的直营店API
    const response = await doGet(`/system/option/ZHIYINGDIAN?orgId=${props.form.salesOrgId}`);

    if (response.code === 200) {
      const stores = response.data || [];
      directStoreOptions.value = stores.map((store) => ({
        label: store.optionLabel,
        value: store.optionValue,
        storeData: store,
      }));
    }
  } catch (error) {
    console.error("获取直营店列表失败:", error);
    directStoreOptions.value = [];
  } finally {
    directStoreLoading.value = false;
  }
};
```

### 5. 添加销售单位ID监听器
```javascript
// 监听销售单位ID变化，自动更新直营店选项
watch(
  () => props.form.salesOrgId,
  (newSalesOrgId, oldSalesOrgId) => {
    // 当销售单位ID发生变化时
    if (newSalesOrgId !== oldSalesOrgId) {
      // 清空当前选中的直营店
      if (props.form.salesStoreType === "3S") {
        props.form.salesStoreDetail = null;
        // 重新获取直营店列表
        fetchDirectStores();
      }
    }
  }
);
```

### 6. 修改销售地类型变化处理
```javascript
// 处理销售地类型变化
const handleSalesStoreTypeChange = (value) => {
  // 清空销售地详情
  props.form.salesStoreDetail = null;

  // 如果选择直营店，加载直营店列表
  if (value === "3S") {
    fetchDirectStores();
  } else {
    // 如果不是直营店，清空直营店选项
    directStoreOptions.value = [];
  }
};
```

### 7. 组件初始化处理
```javascript
// 组件挂载时初始化定金字段可见性和直营店选项
onMounted(() => {
  initializeDepositFieldsVisibility();
  
  // 如果销售地类型已经是直营店且有销售单位ID，则初始化直营店选项
  if (props.form.salesStoreType === "3S" && props.form.salesOrgId) {
    fetchDirectStores();
  }
});
```

## 功能特性

### 1. 智能禁用机制
- 当销售单位ID为空时，直营店选项自动禁用
- 用户无法选择直营店，直到填写销售单位

### 2. 动态数据加载
- 销售单位ID变化时，自动重新加载对应的直营店选项
- 支持不同销售单位有不同的直营店列表

### 3. 数据清理机制
- 切换销售地类型时，自动清空不相关的数据
- 销售单位变化时，清空之前选择的直营店

### 4. 表单数据完整性
- 直营店选择的值会自动包含在表单提交的payload中
- 使用API返回的`optionValue`作为提交值
- 保留完整的直营店数据供后续使用

## 测试验证

已通过以下场景的测试：
1. ✅ 销售单位为空时，直营店选项被禁用
2. ✅ 选择直营店类型但销售单位为空时，不会加载直营店选项
3. ✅ 设置销售单位ID后，自动加载对应的直营店选项
4. ✅ 更换销售单位ID时，重新加载新的直营店选项
5. ✅ 清空销售单位ID时，直营店选项被禁用并清空
6. ✅ 切换到非直营店类型时，清空直营店选项

## 兼容性说明

- 保持了原有的所有功能和API接口
- 新增功能通过条件判断实现，不影响现有使用
- 向后兼容，不会破坏现有的表单数据结构
- 支持编辑模式下的数据回显和初始化

## 定金订单表单配置调整

### 修改内容
在定金订单表单中，销售地类型字段已被设置为不可见，以简化定金订单的录入流程。

### 修改的文件
- `src/components/orders/DepositOrderModal.vue`

### 配置变更
```javascript
const customerInfoConfig = {
  // ... 其他配置
  fields: {
    // ... 其他字段
    salesStoreType: { visible: false, editable: true }, // 隐藏销售地类型字段
    salesStoreDetail: { visible: false, editable: true }, // 隐藏销售地详情字段
    // ... 其他字段
  },
};
```

### 影响范围
- ✅ 定金订单表单中不再显示销售地类型和销售地详情字段
- ✅ 其他重要字段（客户名称、客户类型、联系电话、销售顾问、定金金额等）正常显示
- ✅ 正式订单表单不受影响，仍然显示完整的销售地类型功能
- ✅ 数据结构保持不变，只是界面显示层面的调整
