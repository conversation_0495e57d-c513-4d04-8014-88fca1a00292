# 优雅重构对比：金额字段转换

## 问题描述
原代码中存在大量重复的金额字段转换逻辑，每个字段都需要单独写一遍转换代码，导致代码冗余且难以维护。

## 重构前的写法 ❌

### orderFormFactory.js 中的重复代码
```javascript
// 处理车辆价格字段（从分转换为元）
if (data.sbAmount !== undefined) {
  formData.sbAmount = parseFloat((data.sbAmount / 100).toFixed(2))
}
if (data.salesAmount !== undefined) {
  formData.salesAmount = parseFloat((data.salesAmount / 100).toFixed(2))
}
if (data.invoiceAmount !== undefined) {
  formData.invoicePrice = parseFloat((data.invoiceAmount / 100).toFixed(2))
}
if (data.depositAmount !== undefined) {
  formData.depositAmount = parseFloat((data.depositAmount / 100).toFixed(2))
}
if (data.discountAmount !== undefined) {
  formData.discountAmount = parseFloat((data.discountAmount / 100).toFixed(2))
}
// ... 更多重复代码
```

### orderFormUtils.js 中的重复代码
```javascript
// 金额字段从元转换为分
const amountFields = ['sbAmount', 'salesAmount', 'depositAmount', ...]
amountFields.forEach(field => {
  if (orderPayload[field] !== undefined && orderPayload[field] !== null) {
    orderPayload[field] = Math.round(orderPayload[field] * 100)
  }
})

// 处理二手车相关金额字段
if (form.hasUsedVehicle === 'YES' && form.usedVehicleId) {
  const usedVehicleAmountFields = ['usedVehicleAmount', 'usedVehicleDiscountAmount']
  usedVehicleAmountFields.forEach(field => {
    if (orderPayload[field] !== undefined && orderPayload[field] !== null) {
      orderPayload[field] = Math.round(orderPayload[field] * 100)
    }
  })
}
// ... 更多重复的forEach循环
```

## 重构后的优雅写法 ✅

### 1. 复用 money.js 中的工具函数
```javascript
import { convertCentsToYuan, convertYuanToCents } from './money'
```

### 2. 提取通用转换函数
```javascript
/**
 * 批量转换金额字段从分到元
 * @param {Object} data - 源数据
 * @param {Object} formData - 目标表单数据
 * @param {Array} fieldMappings - 字段映射配置数组
 */
const convertAmountFields = (data, formData, fieldMappings) => {
  fieldMappings.forEach(({ source, target }) => {
    if (data[source] !== undefined) {
      formData[target || source] = convertCentsToYuan(data[source])
    }
  })
}

/**
 * 批量转换金额字段从元到分
 * @param {Object} data - 源数据
 * @param {Array} fields - 需要转换的字段数组
 */
const convertAmountFieldsToCents = (data, fields) => {
  fields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      data[field] = convertYuanToCents(data[field])
    }
  })
}
```

### 3. 配置驱动的字段转换
```javascript
// 定义金额字段映射配置
const basicAmountFields = [
  { source: 'sbAmount', target: 'sbAmount' },
  { source: 'salesAmount', target: 'salesAmount' },
  { source: 'invoiceAmount', target: 'invoicePrice' }, // 支持字段名映射
  { source: 'depositAmount', target: 'depositAmount' },
  { source: 'discountAmount', target: 'discountAmount' }
]

// 批量转换基础金额字段
convertAmountFields(data, formData, basicAmountFields)
```

### 4. 分组处理不同类型的金额字段
```javascript
// 定义专项优惠和成交相关金额字段
const advancedAmountFields = [
  { source: 'exclusiveDiscountAmount', target: 'exclusiveDiscountAmount' },
  { source: 'dealAmount', target: 'dealAmount' },
  { source: 'grossProfitAmount', target: 'grossProfitAmount' }
]

// 批量转换高级金额字段
convertAmountFields(data, formData, advancedAmountFields)
```

### 5. 条件性字段的优雅处理
```javascript
// 处理条件性金额字段转换
if (form.hasUsedVehicle === 'YES' && form.usedVehicleId) {
  const usedVehicleAmountFields = ['usedVehicleAmount', 'usedVehicleDiscountAmount']
  convertAmountFieldsToCents(orderPayload, usedVehicleAmountFields)
}

if (orderPayload.paymentMethod === 'LOAN') {
  const loanAmountFields = [
    'loanAmount', 'loanInitialAmount', 'loanRebateAmount', 'loanFee',
    'loanRebateReceivableAmount', 'loanRebatePayableAmount'
  ]
  convertAmountFieldsToCents(orderPayload, loanAmountFields)
}
```

## 重构效果对比

### 代码行数减少
- **重构前**: 每个字段需要 3-4 行代码
- **重构后**: 多个字段只需要 1 行配置 + 1 行调用

### 可维护性提升
- **重构前**: 新增字段需要复制粘贴转换代码
- **重构后**: 新增字段只需要添加到配置数组中

### 一致性保证
- **重构前**: 每个地方的转换逻辑可能不一致
- **重构后**: 统一使用 money.js 中的标准转换函数

### 可读性增强
- **重构前**: 大量重复代码影响阅读
- **重构后**: 配置清晰，逻辑简洁

## 进一步优化建议

### 1. 使用常量定义字段配置
```javascript
// constants/orderFields.js
export const ORDER_AMOUNT_FIELD_MAPPINGS = {
  BASIC: [
    { source: 'sbAmount', target: 'sbAmount' },
    { source: 'salesAmount', target: 'salesAmount' },
    // ...
  ],
  ADVANCED: [
    { source: 'dealAmount', target: 'dealAmount' },
    // ...
  ],
  LOAN: ['loanAmount', 'loanInitialAmount', 'loanFee'],
  DERIVATIVE: ['notaryFee', 'carefreeIncome', 'vpsIncome']
}
```

### 2. 使用装饰器模式
```javascript
@withAmountConversion(['sbAmount', 'salesAmount'])
function processOrderData(data) {
  // 自动处理金额转换
  return data
}
```

### 3. 使用 Proxy 实现响应式转换
```javascript
const orderProxy = createAmountProxy(orderData, ['sbAmount', 'salesAmount'])
// 读取时自动转换为元，写入时自动转换为分
```

## 总结

通过这次重构，我们实现了：
1. **复用现有工具函数**：避免重复造轮子
2. **配置驱动**：通过配置数组管理字段转换
3. **函数抽象**：提取通用转换逻辑
4. **分组处理**：按业务逻辑分组处理不同类型字段
5. **代码简化**：大幅减少重复代码

这种优雅的写法不仅提高了代码质量，还为后续维护和扩展奠定了良好基础。
