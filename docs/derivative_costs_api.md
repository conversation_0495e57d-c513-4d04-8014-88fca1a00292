# 衍生费用管理API接口文档

## 数据库表结构

### 表名：derivative_costs

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | int(11) UNSIGNED | 主键ID | 自增 |
| order_sn | varchar(64) | 销售订单号 | 唯一索引 |
| vin | varchar(17) | 车辆VIN码 | 可为空 |
| confirmed | tinyint(1) | 确认状态 | 0-未确认，1-已确认 |
| editor_org_id | int(11) UNSIGNED | 经办机构ID | 可为空 |
| editor_org_name | varchar(200) | 经办机构名称 | 可为空 |
| editor_id | bigint(20) | 经办人ID | 可为空 |
| editor_name | varchar(100) | 经办人姓名 | 可为空 |
| notary_fee_income | bigint(20) | 公证费收入（分） | 默认0 |
| notary_fee_cost | bigint(20) | 公证费成本（分） | 默认0 |
| carefree_income | bigint(20) | 畅行无忧收入（分） | 默认0 |
| carefree_cost | bigint(20) | 畅行无忧成本（分） | 默认0 |
| extended_warranty_income | bigint(20) | 延保收入（分） | 默认0 |
| extended_warranty_cost | bigint(20) | 延保成本（分） | 默认0 |
| vps_income | bigint(20) | VPS收入（分） | 默认0 |
| vps_cost | bigint(20) | VPS成本（分） | 默认0 |
| pre_interest_income | bigint(20) | 预收利息收入（分） | 默认0 |
| pre_interest_cost | bigint(20) | 预收利息成本（分） | 默认0 |
| license_plate_fee_income | bigint(20) | 牌照费收入（分） | 默认0 |
| license_plate_fee_cost | bigint(20) | 牌照费成本（分） | 默认0 |
| temp_plate_fee_income | bigint(20) | 临牌费收入（分） | 默认0 |
| temp_plate_fee_cost | bigint(20) | 临牌费成本（分） | 默认0 |
| delivery_equipment_income | bigint(20) | 交车装备收入（分） | 默认0 |
| delivery_equipment_cost | bigint(20) | 交车装备成本（分） | 默认0 |
| other_income | bigint(20) | 其他收入（分） | 默认0 |
| other_cost | bigint(20) | 其他成本（分） | 默认0 |
| create_time | timestamp | 创建时间 | 自动设置 |
| update_time | timestamp | 更新时间 | 自动更新 |
| creator_id | bigint(20) | 创建人ID | 必填 |

## API接口

### 1. 获取衍生费用列表

**接口地址：** `GET /derivative/costs`

**请求参数：**
```json
{
  "pageNum": 1,              // 页码
  "pageSize": 20,            // 每页条数
  "keywords": "ORD2024",     // 关键词搜索（订单号/VIN）
  "bizOrgIds": "1,2,3",      // 销售单位ID（逗号分隔）
  "confirmed": true          // 确认状态筛选（true/false/null）
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "orderSn": "ORD202412010001",
        "vin": "LSGBF53E8KH123456",
        "confirmed": false,
        "editorOrgId": 1,
        "editorOrgName": "北京销售中心",
        "editorId": 1001,
        "editorName": "张三",
        "notaryFeeIncome": 50000,
        "notaryFeeCost": 45000,
        "carefreeIncome": 120000,
        "carefreeCost": 110000,
        "extendedWarrantyIncome": 300000,
        "extendedWarrantyCost": 280000,
        "vpsIncome": 80000,
        "vpsCost": 75000,
        "preInterestIncome": 150000,
        "preInterestCost": 140000,
        "licensePlateFeeIncome": 100000,
        "licensePlateFeeCost": 95000,
        "tempPlateFeeIncome": 5000,
        "tempPlateFeeCost": 4500,
        "deliveryEquipmentIncome": 200000,
        "deliveryEquipmentCost": 180000,
        "otherIncome": 50000,
        "otherCost": 45000,
        "createTime": "2024-12-01T10:30:00",
        "updateTime": "2024-12-01T10:30:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  }
}
```

### 2. 更新衍生费用记录

**接口地址：** `PUT /derivative/costs/{id}`

**请求参数：**
```json
{
  "notaryFeeCost": 45000,        // 公证费成本（分）
  "carefreeCost": 110000,        // 畅行无忧成本（分）
  "extendedWarrantyCost": 280000, // 延保成本（分）
  "vpsCost": 75000,              // VPS成本（分）
  "preInterestCost": 140000,     // 预收利息成本（分）
  "licensePlateFeeCost": 95000,  // 牌照费成本（分）
  "tempPlateFeeCost": 4500,      // 临牌费成本（分）
  "deliveryEquipmentCost": 180000, // 交车装备成本（分）
  "otherCost": 45000             // 其他成本（分）
}
```

### 3. 更新确认状态

**接口地址：** `PUT /derivative/costs/{id}/confirm-status`

**请求参数：**
```json
{
  "confirmed": true          // 确认状态（true/false）
}
```

## 重要说明

1. **金额单位**：所有金额字段都以**分**为单位存储和传输
2. **字段命名**：数据库使用下划线命名，API返回时转换为驼峰命名
3. **确认状态**：使用布尔值，true表示已确认，false表示未确认
4. **经办信息**：使用editor_开头的字段名，与订单表保持一致
5. **索引优化**：在order_sn、vin、confirmed等字段上建立索引以提高查询性能
