# QueryPage 组件路由测试指南

## 🎯 测试路由列表

我已经为您添加了完整的测试路由，可以通过以下URL访问各个测试页面：

### 主要测试页面

| 路由路径 | 页面名称 | 功能说明 |
|---------|----------|----------|
| `/test` | 测试导航页面 | 所有测试页面的导航入口 |
| `/test/query-page` | QueryPage功能测试 | QueryPage组件功能介绍和导航 |
| `/examples/enhanced-query-page` | 增强功能演示 | QueryPage新功能完整演示 |
| `/test/biz-org-selector` | 机构选择器测试 | 业务机构选择器组件测试 |

### 其他相关页面

| 路由路径 | 页面名称 | 功能说明 |
|---------|----------|----------|
| `/app` | 应用首页 | 主应用页面 |
| `/inventory/deposit-order` | 定金订单管理 | 业务页面示例 |
| `/examples/receivable-selector` | 应收选择器示例 | 应收账款选择器演示 |

## 🚀 快速开始测试

### 1. 访问测试导航页面
```
http://localhost:3000/test
```
这是测试的主入口，包含所有测试页面的导航链接和功能说明。

### 2. 直接访问QueryPage演示
```
http://localhost:3000/examples/enhanced-query-page
```
这是QueryPage增强功能的完整演示页面，展示所有新功能。

### 3. 访问功能测试页面
```
http://localhost:3000/test/query-page
```
这是QueryPage功能的介绍和测试导航页面。

## 🎨 演示页面功能

### EnhancedQueryPageExample 功能演示

该页面展示了QueryPage组件的所有新功能：

#### 1. 冻结列功能
- **VIN列**：第一个冻结列，使用等宽字体
- **车型列**：第二个冻结列
- **其他列**：支持横向滚动
- **操作列**：固定在右侧

#### 2. 新增字段类型
- **数字字段**：库存数量，千分位格式化
- **货币字段**：价格，带¥符号，绿色加粗显示
- **字典字段**：状态和品牌，渲染为彩色tag
- **多值字段**：权限，多个tag并排显示

#### 3. 筛选功能
- **数字筛选**：使用数字输入框
- **货币筛选**：支持小数位数
- **字典筛选**：下拉选择器，支持多选
- **自定义筛选**：品牌选择器插槽

#### 4. 表格功能
- **内部滚动**：表格内部垂直滚动
- **冻结表头**：滚动时表头保持可见
- **响应式设计**：适配不同屏幕尺寸
- **操作按钮**：查看、编辑、删除图标

## 🔧 测试数据说明

### 字典数据
项目使用模拟字典数据，包含以下字典：

- **vehicle_status**：车辆状态（在库、预售、已售、维修中）
- **vehicle_brand**：车辆品牌（奥迪、奔驰、宝马、大众、丰田）
- **user_permissions**：用户权限（读取、写入、删除、管理）

### API模拟
- 所有API调用都使用模拟数据
- 支持分页、筛选、排序等功能
- 模拟网络延迟（500-800ms）

## 🎯 测试要点

### 1. 字段类型测试
- [ ] 数字字段显示千分位格式
- [ ] 货币字段显示货币符号和绿色字体
- [ ] 字典字段渲染为彩色tag
- [ ] 多值字段显示多个tag

### 2. 冻结列测试
- [ ] VIN和车型列固定在左侧
- [ ] 其他列可以横向滚动
- [ ] 操作列固定在右侧
- [ ] 滚动时冻结列保持可见

### 3. 筛选功能测试
- [ ] 数字筛选使用数字输入框
- [ ] 字典筛选使用下拉选择器
- [ ] 自定义筛选组件正常工作
- [ ] 筛选条件展开/收起功能

### 4. 表格功能测试
- [ ] 表格内部垂直滚动
- [ ] 冻结表头功能
- [ ] 分页功能正常
- [ ] 选择功能正常

### 5. 响应式测试
- [ ] 大屏幕（>1080px）：筛选条件3列布局
- [ ] 小屏幕（<1080px）：筛选条件单列布局
- [ ] 表格在不同屏幕尺寸下正常显示

## 🐛 常见问题排查

### 1. 页面无法访问
- 检查路由配置是否正确
- 确认组件文件路径是否存在
- 查看浏览器控制台错误信息

### 2. 字典数据不显示
- 检查字典API是否正常返回数据
- 确认dictKey配置是否正确
- 查看网络请求是否成功

### 3. 冻结列不生效
- 确认字段配置中frozen属性设置
- 检查表格scrollX配置
- 验证表格容器宽度设置

### 4. 样式显示异常
- 检查SCSS文件是否正确引入
- 确认CSS变量是否定义
- 验证NaiveUI组件版本兼容性

## 📱 移动端测试

虽然主要针对桌面端设计，但也支持移动端访问：

- 使用浏览器开发者工具切换到移动端视图
- 测试触摸滚动功能
- 验证响应式布局效果

## 🔍 调试技巧

### 1. 使用浏览器开发者工具
- F12打开开发者工具
- 查看Console面板的日志信息
- 使用Network面板监控API请求

### 2. Vue DevTools
- 安装Vue DevTools浏览器扩展
- 查看组件状态和props
- 监控响应式数据变化

### 3. 性能分析
- 使用Performance面板分析页面性能
- 查看组件渲染时间
- 监控内存使用情况

## 📝 测试反馈

如果在测试过程中发现问题，请记录以下信息：

1. **问题描述**：具体的问题现象
2. **复现步骤**：如何重现该问题
3. **浏览器信息**：浏览器类型和版本
4. **屏幕尺寸**：测试时的屏幕分辨率
5. **控制台错误**：相关的错误信息

这样可以帮助快速定位和解决问题。
