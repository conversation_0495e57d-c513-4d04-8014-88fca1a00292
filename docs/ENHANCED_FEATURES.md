# QueryPage 组件增强功能说明

## 🚀 新增功能概览

基于您的需求，我们为QueryPage组件新增了以下高级功能：

1. **扩展字段类型支持**：数字、货币、字典类型
2. **字典字段增强**：支持dictKey配置和n-tag渲染
3. **冻结列功能**：支持横向滚动+冻结首列效果
4. **多值字段支持**：支持逗号分隔的多值显示

## 📊 新增字段类型详解

### 1. 数字字段 (number)

```javascript
// 基础数字字段
FieldTypes.number('stock', '库存数量', {
  filter: true,
  decimals: 0, // 小数位数
  width: 100
})

// 配置示例
{
  name: 'quantity',
  label: '数量',
  type: 'number',
  filter: true,
  table: true,
  decimals: 2, // 保留2位小数
  width: 120
}
```

**特性：**
- 筛选时使用数字输入框 (`n-input-number`)
- 表格显示时使用等宽字体
- 支持小数位数配置
- 自动格式化千分位显示

### 2. 货币字段 (currency)

```javascript
// 基础货币字段
FieldTypes.currency('price', '价格', {
  currency: '¥', // 货币符号
  decimals: 2,   // 小数位数
  filter: true
})

// 配置示例
{
  name: 'amount',
  label: '金额',
  type: 'currency',
  currency: '$',  // 支持不同货币符号
  decimals: 2,
  filter: true,
  table: true,
  width: 140,
  align: 'right'
}
```

**特性：**
- 筛选时使用数字输入框
- 表格显示时带货币符号和千分位格式
- 支持自定义货币符号 (¥, $, €, 等)
- 金额字段使用绿色加粗显示

### 3. 字典字段 (dict)

```javascript
// 使用dictKey从API加载
FieldTypes.dict('status', '状态', 'vehicle_status', {
  renderAsTag: true,  // 渲染为tag
  multiple: false     // 是否多选
})

// 使用自定义options
{
  name: 'category',
  label: '分类',
  type: 'dict',
  options: [
    { label: '分类A', value: 'A', type: 'success' },
    { label: '分类B', value: 'B', type: 'warning' }
  ],
  renderAsTag: true,
  filter: true,
  table: true
}
```

**特性：**
- 支持 `dictKey` 从API动态加载字典数据
- 支持自定义 `options` 静态配置
- 表格中可渲染为 `n-tag` 形式
- 支持多值字段（逗号分隔）
- 支持tag颜色和类型配置

## 🔒 冻结列功能

### 基础用法

```javascript
// 方式1：使用frozen属性
{
  name: 'vin',
  label: 'VIN',
  type: 'string',
  frozen: true, // 冻结此列
  width: 200,
  table: true
}

// 方式2：使用FieldTypes.frozen
FieldTypes.frozen('code', '编码', 'string', {
  width: 150,
  sortable: true
})

// 方式3：VIN字段默认冻结
FieldTypes.vin() // 自动设置frozen: true
```

### 冻结列特性

- **自动左侧固定**：冻结列自动固定在表格左侧
- **选择列固定**：选择列（checkbox）自动固定在最左侧
- **操作列固定**：操作列自动固定在表格右侧
- **横向滚动**：非冻结列支持横向滚动
- **视觉分隔**：冻结列与滚动列有视觉分隔效果

### 冻结列顺序

1. 选择列（如果启用）- 最左侧
2. 冻结列（按配置顺序）- 左侧固定
3. 普通列（可滚动）- 中间区域
4. 操作列（如果启用）- 最右侧

## 🏷️ 字典数据管理

### API集成

```javascript
// 字典API调用
import { getDictOptions } from '@/api/dict'

// 字段配置
{
  name: 'status',
  type: 'dict',
  dictKey: 'vehicle_status', // 字典key
  renderAsTag: true
}
```

### 字典数据格式

```javascript
// API返回格式
{
  code: 200,
  data: [
    {
      optionLabel: '在库',
      optionValue: '1',
      color: '#18a058',    // 可选：tag颜色
      type: 'success'      // 可选：tag类型
    },
    {
      optionLabel: '已售',
      optionValue: '2',
      color: '#d03050',
      type: 'error'
    }
  ]
}
```

### 多值字段支持

```javascript
// 数据格式
{
  permissions: 'read,write,delete', // 逗号分隔
  // 或
  permissions: ['read', 'write', 'delete'] // 数组格式
}

// 字段配置
{
  name: 'permissions',
  type: 'dict',
  dictKey: 'user_permissions',
  renderAsTag: true,
  multiple: true, // 支持多值
  width: 200
}
```

## 🎨 UI渲染效果

### 数字字段渲染
- 使用等宽字体 (Monaco, Courier New)
- 千分位格式化显示
- 右对齐显示

### 货币字段渲染
- 带货币符号前缀
- 绿色加粗字体
- 千分位格式化
- 右对齐显示

### 字典字段渲染

#### 单值渲染
```html
<!-- renderAsTag: false -->
<span>在库</span>

<!-- renderAsTag: true -->
<n-tag type="success">在库</n-tag>
```

#### 多值渲染
```html
<!-- 多个tag并排显示 -->
<div style="display: flex; gap: 4px;">
  <n-tag type="info" size="small">读取</n-tag>
  <n-tag type="warning" size="small">写入</n-tag>
  <n-tag type="error" size="small">删除</n-tag>
</div>
```

## 📝 完整配置示例

```javascript
import { createQueryPageConfig, FieldTypes } from '@/utils/queryPageConfig'

const config = createQueryPageConfig({
  pageConfig: {
    scrollX: 1400, // 支持横向滚动
    selection: true
  },
  fields: [
    // 冻结列
    FieldTypes.vin(), // 自动冻结
    FieldTypes.frozen('code', '编码', 'string', { width: 120 }),
    
    // 数字字段
    FieldTypes.number('stock', '库存', { decimals: 0 }),
    
    // 货币字段
    FieldTypes.currency('price', '价格', { 
      currency: '¥', 
      decimals: 2 
    }),
    
    // 字典字段
    FieldTypes.dict('status', '状态', 'vehicle_status', {
      renderAsTag: true
    }),
    
    // 多值字典字段
    FieldTypes.dict('permissions', '权限', 'user_permissions', {
      renderAsTag: true,
      multiple: true,
      filter: false
    }),
    
    // 操作列（自动右侧固定）
    FieldTypes.actions()
  ]
})
```

## 🔧 技术实现细节

### 字典数据缓存
- 组件内部维护字典数据缓存
- 避免重复API调用
- 支持多个字段共享同一字典

### 格式化函数
```javascript
// 数字格式化
formatNumber(123456.789, 2) // "123,456.79"

// 货币格式化
formatCurrency(123456.789, '¥', 2) // "¥123,456.79"
```

### 冻结列实现
- 使用NaiveUI的 `fixed` 属性
- 自动计算列的固定位置
- 支持左右两侧固定

## 🎯 使用建议

### 1. 字段类型选择
- **数字数据**：使用 `number` 类型
- **金额数据**：使用 `currency` 类型
- **状态/分类**：使用 `dict` 类型
- **重要标识**：使用冻结列

### 2. 冻结列使用
- 重要标识字段（如ID、VIN）设为冻结列
- 冻结列数量建议不超过3个
- 冻结列总宽度建议不超过表格宽度的40%

### 3. 字典字段优化
- 优先使用 `dictKey` 从API加载
- 状态类字段建议渲染为tag
- 多值字段合理控制显示宽度

### 4. 性能优化
- 字典数据会自动缓存，避免重复加载
- 大数据量时合理设置表格高度
- 冻结列过多会影响滚动性能

## 📈 兼容性说明

- **完全向后兼容**：现有配置无需修改
- **渐进式升级**：可逐步应用新功能
- **零破坏性变更**：不影响现有页面功能

这些增强功能让QueryPage组件能够处理更复杂的业务场景，同时保持了良好的用户体验和开发体验。
