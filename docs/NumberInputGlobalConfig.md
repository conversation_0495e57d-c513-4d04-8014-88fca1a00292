# 数字输入框全局配置使用指南

## 概述

为了统一管理全系统的数字输入框配置，我们创建了一套全局配置系统，可以为所有 `n-input-number` 组件设置统一的最小值、最大值和其他属性。

## 配置文件

### 1. 基础配置 (`src/config/inputConfig.js`)

```javascript
// 数字输入框默认配置
export const numberInputConfig = {
  min: 0,           // 最小值：0
  max: 1000000,     // 最大值：100万
  precision: 2,     // 默认保留2位小数
  step: 1,
  
  // 金额相关配置
  amount: {
    min: 0,
    max: 1000000,
    precision: 2,
    step: 0.01,
    placeholder: '请输入金额'
  },
  
  // 数量相关配置
  quantity: {
    min: 0,
    max: 999999,
    precision: 0,
    step: 1,
    placeholder: '请输入数量'
  }
}
```

### 2. 全局配置插件 (`src/plugins/naiveUIGlobalConfig.js`)

提供了便捷的配置方法和组合式函数。

## 使用方法

### 方法1：在组件中直接使用配置

```vue
<template>
  <n-input-number           :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
    v-model:value="form.amount"
    v-bind="amountInputConfig"
    @update:value="handleAmountChange"
  />
</template>

<script setup>
import { computed } from 'vue'
import { getNumberInputConfig } from '@/config/inputConfig'

// 获取金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig('amount', {
    // 可以覆盖默认配置
    disabled: false,
    readonly: false
  })
})
</script>
```

### 方法2：使用配置函数

```vue
<template>
  <n-input-number           :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
    v-model:value="form.price"
    :min="priceConfig.min"
    :max="priceConfig.max"
    :precision="priceConfig.precision"
    :step="priceConfig.step"
    :placeholder="priceConfig.placeholder"
    button-placement="both"
  />
</template>

<script setup>
import { createPriceInputProps } from '@/plugins/naiveUIGlobalConfig'

// 创建价格输入框配置
const priceConfig = createPriceInputProps({
  disabled: false  // 自定义属性
})
</script>
```

### 方法3：使用全局组件包装器

```vue
<template>
  <GlobalNumberInput
    v-model="form.depositAmount"
    type="amount"
    :disabled="isDisabled"
  />
</template>

<script setup>
import GlobalNumberInput from '@/components/common/GlobalNumberInput.vue'
</script>
```

## 配置类型

### 可用的配置类型

- `default`: 默认配置 (0 - 1,000,000)
- `amount`: 金额配置 (0 - 1,000,000, 精度2位)
- `quantity`: 数量配置 (0 - 999,999, 精度0位)
- `percentage`: 百分比配置 (0 - 100, 精度2位)
- `price`: 价格配置 (0 - 1,000,000, 精度2位)

### 默认值说明

- **最小值**: 0
- **最大值**: 1,000,000 (100万)
- **精度**: 根据类型而定
- **步长**: 根据类型而定
- **按钮位置**: both (两侧都有)

## 在现有组件中应用

### 示例：修改定金订单中的金额输入

```vue
<!-- 修改前 -->
<n-input-number           :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
  v-model:value="form.depositAmount"
  placeholder="请输入已付定金"
  :precision="2"
  
  button-placement="both"
/>

<!-- 修改后 -->
<n-input-number           :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
  v-model:value="form.depositAmount"
  :placeholder="amountInputConfig.placeholder"
  :precision="amountInputConfig.precision"
  :min="amountInputConfig.min"
  :max="amountInputConfig.max"
  :step="amountInputConfig.step"
  button-placement="both"
/>
```

## 全局设置 (可选)

如果要在整个应用中设置默认值，可以在 `main.js` 中添加：

```javascript
import { installNaiveUIGlobalConfig } from '@/plugins/naiveUIGlobalConfig'

const app = createApp(App)
installNaiveUIGlobalConfig(app)
```

## 自定义配置

如果需要为特定场景自定义配置，可以：

```javascript
// 创建自定义配置
const customConfig = getNumberInputConfig('amount', {
  max: 500000,  // 自定义最大值为50万
  min: 1000     // 自定义最小值为1000
})
```

## 注意事项

1. 配置的优先级：组件属性 > 自定义配置 > 全局配置
2. 建议在新组件中统一使用配置系统
3. 现有组件可以逐步迁移到新的配置系统
4. 特殊需求的组件可以覆盖全局配置
