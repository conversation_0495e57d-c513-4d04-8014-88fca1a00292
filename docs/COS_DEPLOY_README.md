# COS自动部署配置指南

## 概述

本项目已配置自动部署到腾讯云COS（对象存储），支持CDN加速访问。

## 快速开始

如果您已经有COS配置，可以直接：

1. 复制 `.env.example` 为 `.env.production` 并填入配置
2. 运行 `npm run deploy` 即可完成构建和部署

当前配置的存储桶访问地址：https://qj-prod-1357102482.cos.ap-beijing.myqcloud.com/

## 配置步骤

### 1. 环境变量配置

复制 `.env.example` 文件为 `.env.production`：

```bash
cp .env.example .env.production
```

编辑 `.env.production` 文件，填入您的COS配置：

```env
# 腾讯云COS配置
VITE_COS_SECRET_ID=your_secret_id_here
VITE_COS_SECRET_KEY=your_secret_key_here
VITE_COS_BUCKET=your_bucket_name
VITE_COS_REGION=ap-beijing

# CDN域名配置（可选）
# 如果您的存储桶配置了CDN，请将下面的域名替换为您的实际CDN域名
VITE_CDN_BASE_URL=https://your-cdn-domain.com/
```

### 2. COS存储桶配置

确保您的COS存储桶已正确配置：

1. **静态网站托管**：启用静态网站托管功能
2. **跨域配置**：如需要，配置CORS规则
3. **CDN加速**：可选配置CDN域名

### 3. 部署命令

#### 方式一：使用npm脚本

```bash
# 构建并部署到COS
npm run deploy

# 仅构建
npm run build:prod

# 仅上传（需要先构建）
npm run upload
```

#### 方式二：使用shell脚本

```bash
# 执行完整的COS部署流程
./bin/deploy_cos.sh
```

## 文件结构

```
├── bin/
│   ├── upload_to_cos.js    # COS上传脚本
│   └── deploy_cos.sh       # COS部署脚本
├── .env.production         # 生产环境配置
├── .env.example           # 配置模板
└── vite.config.js         # Vite配置（支持CDN）
```

## 功能特性

### 上传脚本特性

- ✅ 自动递归上传整个dist目录
- ✅ 智能MIME类型识别
- ✅ 缓存策略优化（静态资源缓存1年，HTML/JS/CSS不缓存）
- ✅ 并发上传提升速度
- ✅ 详细的上传进度显示
- ✅ 错误处理和重试机制

### 构建优化

- ✅ 代码分包（vendor、ui、charts）
- ✅ 资源文件分类存储
- ✅ 文件名hash化防缓存
- ✅ 生产环境CDN域名配置

## 故障排除

### 常见问题

1. **上传失败**
   - 检查COS配置是否正确
   - 确认存储桶权限设置
   - 验证网络连接

2. **CDN访问异常**
   - 确认CDN域名配置正确
   - 检查CDN缓存刷新
   - 验证CNAME解析

3. **构建失败**
   - 检查Node.js版本兼容性
   - 清理node_modules重新安装
   - 查看具体错误信息

### 调试模式

如需调试上传过程，可以修改 `bin/upload_to_cos.js` 中的日志级别。

## 安全注意事项

- ⚠️ 不要将 `.env.production` 文件提交到版本控制
- ⚠️ 定期轮换COS访问密钥
- ⚠️ 使用最小权限原则配置COS策略

## 性能优化建议

1. **CDN配置**：启用CDN加速访问
2. **压缩优化**：启用Gzip/Brotli压缩
3. **缓存策略**：合理设置缓存时间
4. **图片优化**：使用WebP格式图片

## 监控和维护

建议定期检查：
- COS存储用量
- CDN流量消耗
- 访问日志分析
- 错误率监控
