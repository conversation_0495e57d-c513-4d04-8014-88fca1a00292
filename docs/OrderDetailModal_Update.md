# OrderDetailModal.vue 更新说明

## 概述
根据完整的订单字段和数据库表结构，对 `OrderDetailModal.vue` 进行了全面的更新，增加了更多的订单详情字段显示，优化了样式和布局。

## 主要更新内容

### 1. 基本信息区域
- 增加了订单类型字段
- 增加了交付日期字段
- 增加了创建人和最后编辑人字段
- 调整了行间距为更紧凑的12px

### 2. 客户信息区域
- 增加了客户类型字段（个人/机构）
- 增加了客户识别码字段（身份证号/统一社会信用代码）
- 增加了成交状态字段（已成交/潜在客户）
- 增加了客户归属机构字段
- 增加了客户备注字段

### 3. 车辆信息区域
- 重新组织了车辆信息字段
- 增加了车型名称、车型代码、配置代码、颜色代码
- 增加了物料代码(SKU)字段
- 移除了不存在的字段（年款、排量、发动机类型等）

### 4. 金额信息区域
- 增加了启票价格字段
- 增加了优惠转车款状态标签
- 增加了专享优惠类型、金额、转车款状态和备注
- 优化了毛利润和毛利率的显示（正负值不同颜色）

### 5. 定金信息区域（新增）
- 定金类型（线上/线下）
- 定金金额
- 定金转车款状态

### 6. 贷款信息区域
- 增加了贷款期限字段
- 增加了分期服务费字段
- 增加了应收机构分期返利字段
- 增加了应付客户分期返利字段
- 增加了返利转车款状态标签

### 7. 二手车置换信息区域
- 增加了转车款金额字段
- 增加了应收厂家补贴字段
- 增加了应付客户补贴字段
- 增加了补贴转车款状态标签

### 8. 其他衍生收入区域（新增）
- 公证费、畅行无忧收入、延保收入
- VPS收入、前置利息
- 挂牌费、临牌费、外卖装具
- 其他收入
- 仅在 `hasDerivativeIncome === 'YES'` 时显示

### 9. 其他信息区域
- 增加了订单备注字段
- 优化了时间字段的格式化显示

## 样式优化

### 1. 字段值突出显示
- 字段值使用更粗的字重（font-weight: 600）
- 调整字体大小为14px
- 减小行高为28px，使布局更紧凑

### 2. 行间距优化
- 统一调整所有网格的y-gap为12px
- 使整体布局更紧凑，信息密度更高

### 3. 状态标签
- 为布尔值字段添加了绿色/灰色的状态标签
- 提高了视觉识别度

## 新增辅助函数

### 1. getOrderTypeText()
- 订单类型文本转换（正常订单/定金订单）

### 2. getCustomerTypeText()
- 客户类型文本转换（个人客户/机构客户）

### 3. getExclusiveDiscountTypeText()
- 专享优惠类型文本转换

### 4. getDepositTypeText()
- 定金类型文本转换（线上定金/线下定金）

### 5. 更新getOrderStatusText()
- 更新订单状态映射，匹配数据库枚举值

## 数据库查询SQL

### 1. 完整查询（order_detail_query.sql）
- 包含所有关联表的完整信息
- 适用于复杂的数据分析和报表

### 2. API查询（order_detail_api_query.sql）
- 简化版查询，专门为前端API设计
- 包含OrderDetailModal.vue所需的所有字段
- 优化了查询性能

## 字段映射说明

### 数据库字段 -> 前端显示字段
- `order_date` -> 订单日期
- `delivery_date` -> 交付日期
- `customer_id_code` -> 客户识别码
- `deal_status` -> 成交状态
- `sb_amount` -> 启票价格
- `discount_deductible` -> 优惠转车款
- `exclusive_discount_*` -> 专享优惠相关
- `deposit_*` -> 定金相关
- `loan_*` -> 贷款相关
- `used_vehicle_*` -> 二手车置换相关
- `*_income` -> 各类衍生收入

## 注意事项

1. **金额字段处理**：所有金额字段在数据库中以分为单位存储，前端需要除以100转换为元

2. **时间字段格式化**：使用formatDate函数统一处理时间字段的显示格式

3. **条件显示**：某些区域（如贷款信息、二手车置换、衍生收入）根据相关字段值条件显示

4. **状态标签**：布尔值字段使用n-tag组件显示，提高视觉效果

5. **响应式布局**：保持4列网格布局，在小屏幕上自动适应

## 后续扩展建议

1. 可以考虑添加赠品明细的展示（如果hasGiftItems为YES）
2. 可以添加订单状态变更历史的展示
3. 可以添加相关收付款记录的展示
4. 可以考虑添加打印功能
5. 可以添加订单编辑的快捷入口
