# 衍生费用字段映射对照表

## 数据库字段 ↔ Java实体类 ↔ 前端字段

### 基础信息字段

| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| id | id | id | 主键ID |
| order_sn | orderSn | orderSn | 销售订单号 |
| vin | vin | vin | 车辆VIN码 |
| confirmed | confirmed | confirmed | 确认状态 |
| editor_org_id | editorOrgId | editorOrgId | 经办机构ID |
| editor_org_name | editorOrgName | editorOrgName | 经办机构名称 |
| editor_id | editorId | editorId | 经办人ID |
| editor_name | editorName | editorName | 经办人姓名 |

### 衍生费用字段

#### 公证费
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| notary_fee_income | notaryFeeIncome | notaryFeeIncome | 公证费收入（分） |
| notary_fee_cost | notaryFeeCost | notaryFeeCost | 公证费成本（分） |

#### 畅行无忧
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| carefree_income | carefreeIncome | carefreeIncome | 畅行无忧收入（分） |
| carefree_cost | carefreeCost | carefreeCost | 畅行无忧成本（分） |

#### 延保收入
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| extended_warranty_income | extendedWarrantyIncome | extendedWarrantyIncome | 延保收入（分） |
| extended_warranty_cost | extendedWarrantyCost | extendedWarrantyCost | 延保成本（分） |

#### VPS收入
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| vps_income | vpsIncome | vpsIncome | VPS收入（分） |
| vps_cost | vpsCost | vpsCost | VPS成本（分） |

#### 预收利息
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| pre_interest_income | preInterestIncome | preInterestIncome | 预收利息收入（分） |
| pre_interest_cost | preInterestCost | preInterestCost | 预收利息成本（分） |

#### 牌照费
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| license_plate_fee_income | licensePlateFeeIncome | licensePlateFeeIncome | 牌照费收入（分） |
| license_plate_fee_cost | licensePlateFeeCost | licensePlateFeeCost | 牌照费成本（分） |

#### 临牌费
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| temp_plate_fee_income | tempPlateFeeIncome | tempPlateFeeIncome | 临牌费收入（分） |
| temp_plate_fee_cost | tempPlateFeeCost | tempPlateFeeCost | 临牌费成本（分） |

#### 交车装备
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| delivery_equipment_income | deliveryEquipmentIncome | deliveryEquipmentIncome | 交车装备收入（分） |
| delivery_equipment_cost | deliveryEquipmentCost | deliveryEquipmentCost | 交车装备成本（分） |

#### 其他收入
| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| other_income | otherIncome | otherIncome | 其他收入（分） |
| other_cost | otherCost | otherCost | 其他成本（分） |

### 系统字段

| 数据库字段 | Java实体类字段 | 前端字段 | 说明 |
|-----------|---------------|---------|------|
| create_time | createTime | createTime | 创建时间 |
| update_time | updateTime | updateTime | 更新时间 |

## 主要变更说明

1. **成本字段命名规范化**：所有成本字段统一使用 `_cost` 后缀
   - `notary_fee` → `notary_fee_cost`
   - `license_plate_fee` → `license_plate_fee_cost`
   - `temp_plate_fee` → `temp_plate_fee_cost`
   - `delivery_equipment` → `delivery_equipment_cost`

2. **预收利息字段完整化**：
   - 收入字段：`pre_interest` → `pre_interest_income`
   - 成本字段：`pre_interest_cost`（新增）

3. **字段命名一致性**：
   - 数据库使用下划线命名（snake_case）
   - Java实体类使用驼峰命名（camelCase）
   - 前端也使用驼峰命名（camelCase）

4. **经办信息字段**：
   - 使用 `editor_` 前缀，与订单表保持一致
   - 包含ID和名称两个字段，便于显示和关联

## 前端代码更新要点

1. **字段映射更新**：所有成本字段的引用都已更新为新的字段名
2. **保存数据时**：API请求参数使用正确的字段名
3. **显示数据时**：表格渲染使用正确的字段名
4. **编辑功能**：内联编辑保存时使用正确的字段名
