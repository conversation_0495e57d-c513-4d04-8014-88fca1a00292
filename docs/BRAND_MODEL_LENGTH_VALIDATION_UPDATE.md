# 置换品牌和置换车型长度限制更新

## 更新概述

本次更新为置换车品牌和置换车型字段添加了20个字符的长度限制，确保数据输入的规范性和数据库存储的一致性。

## 更新的文件

### 1. `src/utils/orderFormRules.js`
- **新增字段**: `usedVehicleBrand` 和 `usedVehicleModel` 的校验规则
- **影响范围**: 订单表单中的二手车置换信息校验

### 2. `src/views/exchange/UsedVehicleInventoryPage.js`
- **更新字段**: `brand` 和 `series` 的校验规则
- **影响范围**: 二手车库存管理页面的表单校验

### 3. `src/components/orders/sections/VehicleExchangeSection.vue`
- **更新内容**: 为置换车品牌和置换车型输入框添加 `maxlength` 和 `show-count` 属性
- **影响范围**: 订单表单的用户界面

### 4. `src/views/exchange/UsedVehicleInventoryPage.vue`
- **更新内容**: 为车辆品牌和车型系列输入框添加 `maxlength` 和 `show-count` 属性
- **影响范围**: 二手车库存页面的用户界面

## 校验规则详情

### 置换车品牌（usedVehicleBrand）

#### 基本要求
- **字段类型**: 选填字段
- **最大长度**: 20个字符
- **校验条件**: 仅在选择"有二手车置换"时进行校验

#### 校验逻辑
1. 如果未选择二手车置换，跳过校验
2. 如果字段为空或仅包含空白字符，通过校验（选填）
3. 如果字符长度超过20，返回错误

#### 错误提示信息
- 长度超限: "置换车品牌不能超过20个字符"

### 置换车型（usedVehicleModel）

#### 基本要求
- **字段类型**: 选填字段
- **最大长度**: 20个字符
- **校验条件**: 仅在选择"有二手车置换"时进行校验

#### 校验逻辑
1. 如果未选择二手车置换，跳过校验
2. 如果字段为空或仅包含空白字符，通过校验（选填）
3. 如果字符长度超过20，返回错误

#### 错误提示信息
- 长度超限: "置换车型不能超过20个字符"

### 车辆品牌（brand - 库存页面）

#### 基本要求
- **字段类型**: 必填字段
- **最大长度**: 20个字符

#### 校验逻辑
1. 如果字段为空，返回错误
2. 如果字符长度超过20，返回错误

#### 错误提示信息
- 必填校验: "请输入车辆品牌"
- 长度超限: "车辆品牌不能超过20个字符"

### 车型系列（series - 库存页面）

#### 基本要求
- **字段类型**: 必填字段
- **最大长度**: 20个字符

#### 校验逻辑
1. 如果字段为空，返回错误
2. 如果字符长度超过20，返回错误

#### 错误提示信息
- 必填校验: "请输入车型系列"
- 长度超限: "车型系列不能超过20个字符"

## 用户界面增强

### 输入框属性
所有相关输入框都添加了以下属性：
- `maxlength="20"`: 限制用户最多输入20个字符
- `show-count`: 显示字符计数器，让用户实时了解输入长度

### 用户体验改进
1. **实时反馈**: 用户可以看到当前输入的字符数量
2. **输入限制**: 防止用户输入超过限制的字符
3. **清晰提示**: 当超过长度限制时，显示明确的错误信息

## 校验触发时机

### 订单表单
- `blur`: 失去焦点时校验
- `change`: 值改变时校验

### 库存页面
- `blur`: 失去焦点时校验
- `input`: 输入时校验

## 技术实现

### 校验函数结构
```javascript
validator: (_, value) => {
  // 1. 检查是否需要校验（置换状态）
  // 2. 检查是否为空（必填/选填逻辑）
  // 3. 检查长度限制
  // 4. 返回校验结果
}
```

### 前端输入限制
```vue
<n-input
  v-model:value="form.fieldName"
  maxlength="20"
  show-count
  placeholder="请输入..."
/>
```

## 兼容性说明

- 与现有表单验证框架完全兼容
- 不影响现有数据的显示和编辑
- 仅对新输入的数据进行长度限制
- 支持中文、英文、数字和特殊字符

## 测试场景

建议测试以下场景：
1. 输入正常长度的品牌名称（如：奔驰、宝马）
2. 输入正常长度的车型名称（如：C200L、X5）
3. 输入空字符串（置换字段应通过，库存字段应失败）
4. 输入超过20个字符的长字符串
5. 在非置换状态下输入任意值（应跳过校验）
6. 测试字符计数器显示是否正确
7. 测试输入框的长度限制是否生效

## 数据库影响

此更新不会影响数据库结构，仅在应用层面添加校验规则。建议确认数据库字段长度是否足够存储20个字符的数据。
