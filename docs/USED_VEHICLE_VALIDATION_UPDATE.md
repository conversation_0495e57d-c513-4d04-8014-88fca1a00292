# 二手车表单校验规则更新

## 更新概述

本次更新为二手车相关表单添加了准确的车牌号和车架号（VIN）校验规则，确保数据输入的准确性和规范性。

## 更新的文件

### 1. `src/utils/orderFormRules.js`
- **更新内容**: 为二手车车牌号和VIN添加了详细的格式校验
- **影响范围**: 订单表单中的二手车信息校验

### 2. `src/views/exchange/UsedVehicleInventoryPage.js`
- **更新内容**: 为二手车库存页面的车牌号和VIN添加了详细的格式校验
- **影响范围**: 二手车库存管理页面的表单校验

## 校验规则详情

### 车牌号校验规则

#### 基本要求
- **长度**: 必须是7位或8位字符
- **格式**: 支持普通车牌（7位）和新能源车牌（8位）

#### 具体格式
- **普通车牌（7位）**: 省份简称(1位) + 地区代码(1位) + 5位数字或字母
  - 示例: `京A12345`、`沪B23456`
- **新能源车牌（8位）**: 省份简称(1位) + 地区代码(1位) + 6位数字或字母
  - 示例: `京AD12345`、`粤AF23456`

#### 字符限制
- 省份简称: 支持所有中国省份、直辖市、自治区简称
- 地区代码: A-Z（大写字母）
- 后续字符: A-H, J-N, P-Z, 0-9（不包含I、O、Q）

#### 错误提示信息
- 长度错误: "车牌号必须是7位或8位字符"
- 7位格式错误: "请输入正确的7位车牌号格式（如：京A12345）"
- 8位格式错误: "请输入正确的8位新能源车牌号格式（如：京AD12345）"

### 车架号（VIN）校验规则

#### 基本要求
- **长度**: 必须是17位字符
- **开头**: 必须以字母开头
- **字符组成**: 字母和数字组合

#### 具体格式
- **第1位**: 必须是字母（A-H, J-N, P-Z，不包含I、O、Q）
- **第2-17位**: 字母或数字（A-H, J-N, P-Z, 0-9，不包含I、O、Q）

#### 字符限制
- 不包含字母: I、O、Q（避免与数字1、0混淆）
- 大小写不敏感（自动转换为大写）

#### 错误提示信息
- 长度错误: "车架号VIN必须是17位字符"
- 格式错误: "车架号VIN必须以字母开头，由17位字母数字组成（不包含I、O、Q）"

## 校验逻辑

### 动态校验条件
- **订单表单**: 只有当选择了"有二手车置换"时才进行校验
- **库存页面**: 始终进行校验（必填字段）

### 校验触发时机
- `blur`: 失去焦点时校验
- `change`: 值改变时校验
- `input`: 输入时校验（仅库存页面）

## 技术实现

### 正则表达式
```javascript
// 7位车牌号
const plateRegex7 = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{5}$/

// 8位新能源车牌号
const plateRegex8 = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{6}$/

// VIN码
const vinRegex = /^[A-HJ-NPR-Z][A-HJ-NPR-Z0-9]{16}$/i
```

### 校验函数结构
```javascript
validator: (_, value) => {
  // 1. 检查是否为空
  // 2. 检查长度
  // 3. 检查格式
  // 4. 返回校验结果
}
```

## 兼容性说明

- 支持所有中国大陆省份、直辖市、自治区的车牌号
- 支持新能源车牌和普通车牌
- 遵循国际VIN码标准
- 与现有表单验证框架完全兼容

## 测试建议

建议测试以下场景：
1. 正确格式的7位车牌号
2. 正确格式的8位新能源车牌号
3. 错误长度的车牌号
4. 包含非法字符的车牌号
5. 正确格式的17位VIN码
6. 错误长度的VIN码
7. 数字开头的VIN码
8. 包含I、O、Q的VIN码
