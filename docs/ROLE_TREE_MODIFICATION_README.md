# 机构树选中逻辑修改说明

## 修改内容

已成功修改了角色编辑弹窗中的机构树选中逻辑，现在会将**选中的节点**和**半选中的节点**都传递给接口，同时通过叶子节点交集的方式解决编辑时显示问题。

## 核心思路

通过缓存所有叶子节点ID，在编辑角色时将后端返回的权限数据与叶子节点取交集，只传递叶子节点权限给树组件渲染，从根本上解决显示问题。

## 主要修改

### 1. 在 `RolesPage.js` 中新增叶子节点缓存
- 添加 `leafMenuIds` 响应式数据用于缓存所有叶子节点ID
- 新增 `getLeafMenuIds()` 函数遍历菜单树获取叶子节点
- 在 `fetchMenuTree()` 中调用缓存叶子节点

### 2. 修改编辑角色逻辑 `handleEdit`
- 获取角色的原始权限数据
- 计算与叶子节点的交集：`originalMenus.filter(menuId => leafMenuIds.value.has(menuId))`
- 只传递叶子节点权限给树组件渲染

### 3. 保持 `handleMenuCheck` 函数的简洁性
- 直接使用用户选择的节点，保持操作的自然性
- 用户可以自由选择和取消选择节点，不会被强制选中

### 4. 保持 `getCompletePermissionData` 函数
- 专门用于获取完整的权限数据（选中+半选中）
- 使用 `menuTreeRef.value.getCheckedData()` 获取完全选中的节点
- 使用 `menuTreeRef.value.getIndeterminateData()` 获取半选中的节点
- 将两种状态的节点ID合并并去重

### 5. 保持 `handleSave` 函数的数据隔离
- 在提交前调用 `getCompletePermissionData()` 获取完整权限数据
- 创建新的提交数据对象，不修改原始的 `formData.menus`
- 通过 `emit('save', submitData)` 将完整权限数据传递给父组件

### 6. 传递叶子节点缓存给子组件
- 在 `RoleEditModal.vue` 中添加 `leafMenuIds` prop
- 为后续扩展功能预留接口

## 数据流程

1. **页面初始化** → 获取菜单树数据并缓存所有叶子节点ID
2. **编辑角色** → 后端返回完整权限数据（包含选中+半选中的节点）
3. **权限数据过滤** → 与叶子节点取交集，只保留叶子节点权限
4. **渲染树状态** → 根据叶子节点权限正确渲染树的选中状态
5. **用户操作树节点** → `handleMenuCheck` 被触发 → 直接更新用户选择到 `formData.menus`
6. **用户继续操作** → 可以自由选择/取消选择节点，不会被强制选中
7. **点击保存** → `handleSave` 被触发
8. **获取完整数据** → 调用 `getCompletePermissionData()` 获取选中+半选中的节点
9. **创建提交数据** → 创建新的数据对象，不修改原始 `formData`
10. **传递给父组件** → 通过 `emit('save', submitData)` 传递完整权限数据
11. **父组件处理** → 接收完整权限数据并提交给API

## 控制台日志

修改后会在控制台输出详细的调试信息：
- **叶子节点缓存**：显示缓存的所有叶子节点ID
- **编辑角色权限处理**：显示原始权限数据、叶子节点、交集结果
- **获取完整权限数据**：显示选中节点、半选中节点和最终合并结果
- **最终提交数据**：显示提交给API的完整权限数据

## 测试方法

### 新增角色测试
1. 打开角色管理页面，点击"新增角色"
2. 在权限树中选择部分子节点（观察父节点变为半选中状态）
3. 点击保存，查看控制台输出，确认选中和半选中的节点都被包含
4. 验证后端接收到的数据包含完整的权限信息

### 编辑角色测试（重点）
1. 新增一个角色并保存（选择一些权限）
2. 编辑刚才创建的角色
3. 确认权限树正确显示之前选择的权限状态（不会显示多余的选中项）
4. 修改一些权限选择（添加或删除）
5. 保存后再次编辑，确认显示最新的权限状态
6. 查看控制台的权限优化过程日志

### 级联选择测试
1. 选择一个父节点的所有子节点
2. 观察父节点自动变为选中状态
3. 保存后编辑，确认显示为选中父节点而不是所有子节点

## 方案优势

1. **简洁高效**：通过叶子节点交集的方式，从根本上解决显示问题，避免复杂的树结构分析
2. **性能优良**：叶子节点缓存只需计算一次，后续编辑操作只需简单的数组过滤
3. **逻辑清晰**：数据流程简单明了，易于理解和维护
4. **扩展性强**：叶子节点缓存可用于其他功能扩展

## 兼容性

- 添加了错误处理，如果 `getCheckedData()` 或 `getIndeterminateData()` 方法不可用，会回退到原来的逻辑
- 保持了与现有API接口的兼容性
- 不影响其他功能的正常使用

## 文件修改

- `src/views/system/RolesPage.js` - 主要修改文件，添加叶子节点缓存和编辑逻辑
- `src/views/system/RolesPage.vue` - 传递叶子节点缓存给子组件
- `src/views/system/components/RoleEditModal.vue` - 添加leafMenuIds prop
- `src/views/system/components/RoleEditModal.js` - 保持权限获取逻辑
