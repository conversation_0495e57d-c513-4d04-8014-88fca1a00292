OK，我已经初始化了sd-lal-vue3项目，我们来以一个资深web前端架构师的视角设计这个vue3项目。
我认为我们应该：
1、UI规范：
1.1 使用NaiveUI组件库，我们使用 xicons 作为图标库;
1.2 全局中文字体必须使用https://static.zeoseven.com/cn/288/main/fe574996dbfee5d1e43c5927d23cfe4a.woff2，
1.3 全局数字和英文字母使用：https://static.zeoseven.com/en/ce/main.woff2

2、组件规范：页面按所在业务模块放在src/views/目录下，例如系统用户页面应该是src/views/system/UsersPage.vue；需要抽象复用的组件则按照功能命名，放置在src/components目录下，例如src/components/DataTable.vue；

3、请求规范：封装一个统一的基本网络请求库，例如requests.js，用于跟服务端API进行交互：它应该包括请求和响应拦截器；所有请求都必须带有Authorization请求头用于鉴权；它支持标准RESTFul风格的API；

以上基础规范，如果你有更好的建议可以直接应用到代码中。

开发环境中阶段我们将所有/open-api/前缀的请求转发到localhost:9999端口

我们来初始化这个系统的路由体系：
1、默认路由：/ 转发到MainPage.vue
2、静态路由：/login 转发到system/LoginPage.vue
3、动态路由：MainPage在渲染之前应该调用服务器端接口获取用户的菜单内容；GET /open-api/system/menus，结果中返回了树形菜单结构；