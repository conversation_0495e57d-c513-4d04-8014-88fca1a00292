# 车辆置换转车款金额验证功能

## 功能概述

在销售订单提交时，系统会自动检查车辆置换中的转车款金额是否合理，确保所有转车款选项勾选后的总金额不超过车辆成交价。

## 业务场景

**示例场景**：客户置换了一台50万元的二手车，购买了一台20万元的新车
- ✅ **正确**：转车款金额设置为15万元（不超过新车价格20万，也不超过置换金额50万）
- ❌ **错误1**：转车款金额设置为25万元（超过新车价格20万）
- ❌ **错误2**：如果置换金额只有10万元，转车款设置为15万元（超过置换金额10万）

## 验证规则

### 1. 基本规则
- 所有转车款选项勾选后的**总金额不能超过车辆售价**
- 单独的**置换转车款金额不能超过车辆售价**
- **置换转车款金额不能超过置换金额**（新增限制）

### 2. 转车款项目包括
系统会检查以下所有勾选了"转车款"选项的项目：

1. **已付定金**（勾选转车款时）
2. **现金优惠**（勾选转车款时）
3. **应付-客户-分期返利**（勾选转车款时）
4. **应付-客户-置换补贴**（勾选转车款时）
5. **置换转车款**（直接金额）
6. **应付-客户-专项优惠**（勾选转车款时）

### 3. 验证时机
- **实时验证**：在车辆置换区域输入转车款金额时显示提示
- **提交验证**：在订单保存时进行最终验证，不通过则阻止保存

## 用户界面

### 实时提示
在"转车款金额"输入框下方会显示：
- ❌ **错误提示**：当金额超限时显示红色错误信息
- ⚠️ **建议提示**：当金额接近上限时显示黄色建议信息

### 错误消息示例

**超过车辆售价时：**
```
转车款总金额(21.00万元)不能超过车辆售价(20.00万元)
```

**超过置换金额时：**
```
置换转车款金额(12.00万元)不能超过置换金额(10.00万元)
```

## 技术实现

### 核心文件
- `src/utils/vehicleExchangeValidation.js` - 验证逻辑工具
- `src/components/orders/OrderEditModal.js` - 订单保存验证
- `src/components/orders/sections/VehicleExchangeSection.vue` - 实时验证提示

### 主要函数
```javascript
// 验证转车款金额
validateVehicleExchangeDeductible(form)

// 格式化错误消息
formatValidationErrorMessage(validationResult)

// 获取建议的转车款金额
getSuggestedDeductibleAmount(form)
```

## 测试用例

### 正常情况
```javascript
{
  salesAmount: 200000,        // 车辆售价20万
  usedVehicleAmount: 500000,  // 置换金额50万
  usedVehicleDeductibleAmount: 150000, // 转车款15万
  // 其他转车款选项为0或false
}
// 结果：✅ 验证通过
```

### 异常情况
```javascript
{
  salesAmount: 200000,        // 车辆售价20万
  usedVehicleAmount: 500000,  // 置换金额50万
  usedVehicleDeductibleAmount: 250000, // 转车款25万（超限）
  // 其他转车款选项为0或false
}
// 结果：❌ 验证失败，显示错误信息
```

### 复杂情况（多个转车款选项）
```javascript
{
  salesAmount: 200000,        // 车辆售价20万
  usedVehicleDeductibleAmount: 100000, // 置换转车款10万
  depositAmount: 50000,       // 定金5万
  depositDeductible: true,    // 定金转车款
  discountAmount: 30000,      // 现金优惠3万
  discountDeductible: true,   // 现金优惠转车款
  loanRebatePayableAmount: 20000, // 分期返利2万
  loanRebatePayableDeductible: true, // 分期返利转车款
  usedVehicleDiscountPayableAmount: 10000, // 置换补贴1万
  usedVehicleDiscountPayableDeductible: true, // 置换补贴转车款
}
// 转车款总计：10+5+3+2+1 = 21万 > 20万车辆售价
// 结果：❌ 验证失败，显示详细明细
```

## 业务逻辑说明

### 置换差价处理
- **置换金额**：客户二手车的评估价值
- **转车款金额**：用于抵扣新车车款的部分
- **置换差价**：置换金额 - 转车款金额（以现金支付给客户）

### 示例计算
- 客户置换车辆：50万元
- 购买新车：20万元
- 转车款：15万元
- 置换差价：50万 - 15万 = 35万元（现金支付给客户）
- 客户实际支付：20万 - 15万 = 5万元

## 注意事项

1. **金额单位**：系统内部以"元"为单位进行计算
2. **精度处理**：金额计算保留2位小数
3. **边界情况**：转车款金额等于车辆售价时允许通过
4. **用户体验**：提供清晰的错误提示和建议信息
5. **数据一致性**：确保前端验证与后端验证逻辑一致

## 扩展性

该验证框架支持：
- 添加新的转车款项目类型
- 自定义验证规则
- 多语言错误消息
- 不同业务场景的验证策略
