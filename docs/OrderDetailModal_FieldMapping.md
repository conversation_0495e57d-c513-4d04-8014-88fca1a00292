# OrderDetailModal.vue 字段映射对照表

## 概述
根据您重写的SQL查询，OrderDetailModal.vue组件已经更新为使用数据库原始字段名，不再使用对象映射。以下是所有字段的映射对照表。

## 字段映射对照

### 基本信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| orderSn | order_sn | 订单编号 |
| orderType | order_type | 订单类型 |
| orderDate | order_date | 订单日期 |
| orderStatus | order_status | 订单状态 |
| paymentMethod | payment_method | 支付方式 |
| deliveryDate | delivery_date | 交付日期 |
| creatorName | creator_name | 创建人 |
| editorName | editor_name | 最后编辑人 |

### 客户信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| customerName | customer_name | 客户名称 |
| customerType | customer_type | 客户类型 |
| dealStatus | deal_status | 成交状态 |
| customerIdCode | customer_id_code | 客户识别码 |
| mobile | mobile | 联系电话（无变化） |
| address | address | 客户地址（无变化） |
| customerRemark | customer_remark | 客户备注 |
| ownerOrgName | owner_org_name | 客户归属机构 |

### 销售信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| salesOrgName | sales_org_name | 销售单位 |
| salesAgentName | sales_agent_name | 销售顾问 |
| salesLeaderName | sales_leader_name | 销售主管 |
| deliveryOrgName | delivery_org_name | 交付单位 |

### 车辆信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| brand | brand | 品牌（无变化） |
| series | series | 车系（无变化） |
| modelName | model_name | 车型名称 |
| configName | config_name | 配置名称 |
| modelCode | model_code | 车型代码 |
| configCode | config_code | 配置代码 |
| colorCode | color_code | 颜色代码 |
| skuId | sku_id | 物料代码(SKU) |

### 金额信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| sbAmount | sb_amount | 启票价格 |
| dealAmount | deal_amount | 成交价格 |
| dealAmountCn | deal_amount_cn | 成交价格(大写) |
| discountAmount | discount_amount | 优惠金额 |
| discountDeductible | discount_deductible | 优惠转车款 |
| exclusiveDiscountType | exclusive_discount_type | 专享优惠类型 |
| exclusiveDiscountAmount | exclusive_discount_amount | 专享优惠金额 |
| exclusiveDiscountPayableDeductible | exclusive_discount_deductible | 专享优惠转车款 |
| exclusiveDiscountRemark | exclusive_discount_remark | 专享优惠备注 |

### 定金信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| depositType | deposit_type | 定金类型 |
| depositAmount | deposit_amount | 定金金额 |
| depositDeductible | deposit_deductible | 定金转车款 |

### 贷款信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| loanChannel | loan_channel | 贷款渠道 |
| loanAmount | loan_amount | 贷款金额 |
| loanInitialAmount | loan_initial_amount | 首付金额 |
| loanInitialRatio | loan_initial_ratio | 首付比例 |
| loanMonths | loan_months | 贷款期限 |
| loanFee | loan_fee | 分期服务费 |
| loanRebateReceivableAmount | loan_rebate_receivable_amount | 应收机构分期返利 |
| loanRebatePayableAmount | loan_rebate_payable_amount | 应付客户分期返利 |
| loanRebatePayableDeductible | loan_rebate_deductible | 返利转车款 |

### 二手车置换信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| usedVehicleId | used_vehicle_id | 二手车车牌号 |
| usedVehicleVin | used_vehicle_vin | 二手车VIN |
| usedVehicleBrand | used_vehicle_brand | 二手车品牌 |
| usedVehicleModel | used_vehicle_model | 二手车车型 |
| usedVehicleColor | used_vehicle_color | 二手车颜色 |
| usedVehicleAmount | used_vehicle_amount | 置换金额 |
| usedVehicleDiscountPayableDeductibleAmount | used_vehicle_deductible_amount | 转车款金额 |
| usedVehicleDiscountReceivableAmount | used_vehicle_discount_receivable_amount | 应收厂家补贴 |
| usedVehicleDiscountPayableAmount | used_vehicle_discount_payable_amount | 应付客户补贴 |
| usedVehicleDiscountPayableDeductible | used_vehicle_discount_deductible | 补贴转车款 |

### 其他衍生收入信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| hasDerivativeIncome | has_derivative_income | 是否有衍生收入 |
| notaryFee | notary_fee | 公证费 |
| carefreeIncome | carefree_income | 畅行无忧收入 |
| extendedWarrantyIncome | extended_warranty_income | 延保收入 |
| vpsIncome | vps_income | VPS收入 |
| preInterest | pre_interest | 前置利息 |
| licensePlateFee | license_plate_fee | 挂牌费 |
| tempPlateFee | temp_plate_fee | 临牌费 |
| deliveryEquipment | delivery_equipment | 外卖装具 |
| otherIncome | other_income | 其他收入 |

### 其他信息
| 原字段名 | 新字段名 | 说明 |
|---------|---------|------|
| createTime | create_time | 创建时间 |
| updateTime | update_time | 更新时间 |
| remark | remark | 订单备注（无变化） |

## 注意事项

### 1. 移除的字段
以下字段在您的SQL查询中没有包含，已从组件中移除或注释：
- `salesAmount` (销售价格) - 在SQL中被移除
- `salesCostAmount` (销售费用) - 在SQL中被移除
- `grossProfitAmount` (毛利润) - 在SQL中被移除
- `grossProfitRate` (毛利率) - 在SQL中被移除

如果需要这些字段，请在SQL查询中添加相应的计算字段。

### 2. 条件显示逻辑
以下模板条件也已相应更新：
- `v-if="orderDetail.paymentMethod === 'LOAN'"` → `v-if="orderDetail.payment_method === 'LOAN'"`
- `v-if="orderDetail.usedVehicleId"` → `v-if="orderDetail.used_vehicle_id"`
- `v-if="orderDetail.hasDerivativeIncome === 'YES'"` → `v-if="orderDetail.has_derivative_income === 'YES'"`

### 3. 数据类型保持不变
- 金额字段仍然以分为单位，使用`formatMoney()`函数转换显示
- 时间字段使用`formatDate()`函数格式化显示
- 布尔值字段使用n-tag组件显示状态

### 4. 辅助函数无需修改
所有的辅助函数（如`getOrderTypeText`、`getCustomerTypeText`等）保持不变，只是传入的参数字段名发生了变化。

## 测试建议

1. **字段显示测试**：确保所有字段都能正确显示
2. **条件显示测试**：测试贷款信息、二手车置换、衍生收入等条件显示区域
3. **金额格式测试**：确保金额字段正确从分转换为元并显示
4. **时间格式测试**：确保时间字段正确格式化显示
5. **状态标签测试**：确保布尔值字段的状态标签正确显示

## 后续工作

如果需要显示销售费用、毛利润、毛利率等字段，请在SQL查询中添加相应的计算字段：

```sql
-- 可以在SQL中添加这些计算字段
vo.sales_cost_amount,
(vo.deal_amount - vo.sb_amount - vo.sales_cost_amount) as gross_profit_amount,
CASE 
  WHEN vo.sb_amount > 0 THEN 
    ROUND(((vo.deal_amount - vo.sb_amount - vo.sales_cost_amount) / vo.sb_amount) * 100, 2)
  ELSE 0 
END as gross_profit_rate
```
