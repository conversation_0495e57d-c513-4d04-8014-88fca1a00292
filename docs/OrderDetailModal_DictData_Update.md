# OrderDetailModal.vue 字典数据集成更新

## 概述
根据您的建议，已将 OrderDetailModal.vue 组件中的文本转换函数更新为使用 `@/mock/dictData.js` 中的字典数据，提高了数据的一致性和可维护性。

## 更新内容

### 1. 导入字典数据
```javascript
import { dictOptions } from "@/mock/dictData";
```

### 2. 更新的文本转换函数

#### 2.1 订单状态相关
```javascript
// 获取订单状态文本
const getOrderStatusText = (status) => {
  if (!status) return "未知";
  
  const orderStatusOptions = dictOptions["order_status"] || [];
  const option = orderStatusOptions.find(
    (item) => item.optionValue === status
  );
  return option ? option.optionLabel : status;
};

// 获取订单状态类型（用于标签颜色）
const getOrderStatusType = (status) => {
  if (!status) return "default";
  
  const orderStatusOptions = dictOptions["order_status"] || [];
  const option = orderStatusOptions.find(
    (item) => item.optionValue === status
  );
  return option ? option.type : "default";
};
```

#### 2.2 客户类型相关
```javascript
// 获取客户类型文本
const getCustomerTypeText = (type) => {
  if (!type) return "个人客户";
  
  const customerTypeOptions = dictOptions['customer_type'] || [];
  const option = customerTypeOptions.find(item => item.optionValue === type);
  return option ? option.optionLabel : type;
};
```

#### 2.3 成交状态相关
```javascript
// 获取成交状态文本
const getDealStatusText = (status) => {
  if (!status) return "未成交";
  
  const dealStatusOptions = dictOptions['deal_status'] || [];
  const option = dealStatusOptions.find(item => item.optionValue === status);
  return option ? option.optionLabel : status;
};

// 获取成交状态类型（用于标签颜色）
const getDealStatusType = (status) => {
  if (!status) return "warning";
  
  const dealStatusOptions = dictOptions['deal_status'] || [];
  const option = dealStatusOptions.find(item => item.optionValue === status);
  return option ? option.type : "warning";
};
```

#### 2.4 专享优惠类型相关
```javascript
// 获取专享优惠类型文本
const getExclusiveDiscountTypeText = (type) => {
  if (!type) return "无";
  
  const exclusiveDiscountOptions = dictOptions['exclusive_discount_type'] || [];
  const option = exclusiveDiscountOptions.find(
    (item) => item.optionValue === type
  );
  return option ? option.optionLabel : type;
};
```

### 3. 模板更新

#### 3.1 成交状态显示优化
**更新前：**
```vue
<n-tag
  :type="orderDetail.deal_status === 'CUSTOMER' ? 'success' : 'warning'"
  size="small"
>
  {{ orderDetail.deal_status === "CUSTOMER" ? "已成交" : "潜在客户" }}
</n-tag>
```

**更新后：**
```vue
<n-tag
  :type="getDealStatusType(orderDetail.deal_status)"
  size="small"
>
  {{ getDealStatusText(orderDetail.deal_status) }}
</n-tag>
```

## 字典数据映射

### 订单状态 (order_status)
| 值 | 标签 | 类型 | 颜色 |
|---|------|------|------|
| pending | 待处理 | warning | #f0a020 |
| confirmed | 已确认 | info | #2080f0 |
| delivered | 已交付 | success | #18a058 |
| canceled | 已取消 | error | #d03050 |
| archived | 已归档 | default | #909399 |

### 客户类型 (customer_type)
| 值 | 标签 | 类型 | 颜色 |
|---|------|------|------|
| individual | 个人客户 | info | #2080f0 |
| institutional | 法人客户 | success | #18a058 |

### 成交状态 (deal_status)
| 值 | 标签 | 类型 | 颜色 |
|---|------|------|------|
| LEADS | 未成交 | warning | #f0a020 |
| CUSTOMER | 已成交 | success | #18a058 |

### 专享优惠类型 (exclusive_discount_type)
根据字典数据中的配置动态获取标签文本。

## 优势

### 1. 数据一致性
- 所有状态文本和颜色都来自统一的字典数据源
- 避免了硬编码的文本和颜色值
- 确保整个应用中状态显示的一致性

### 2. 可维护性
- 修改状态文本或颜色只需在字典数据中修改
- 新增状态类型时自动支持
- 减少了代码重复

### 3. 扩展性
- 支持动态添加新的状态类型
- 字典数据可以来自后端API
- 支持国际化扩展

### 4. 类型安全
- 使用字典数据中的 `type` 字段确保标签颜色正确
- 提供默认值处理异常情况

## 注意事项

### 1. 字典数据依赖
- 确保 `@/mock/dictData.js` 文件存在且格式正确
- 字典数据结构应包含 `optionValue`、`optionLabel`、`type` 字段

### 2. 默认值处理
- 所有函数都提供了默认值处理
- 当字典数据不存在或找不到匹配项时，返回合理的默认值

### 3. 性能考虑
- 字典数据查找使用 `find()` 方法，对于大量数据可考虑优化
- 可以考虑将字典数据转换为 Map 结构提高查找效率

## 后续建议

### 1. 其他字典数据集成
可以考虑将以下函数也更新为使用字典数据：
- `getOrderTypeText()` - 如果有订单类型字典
- `getDepositTypeText()` - 如果有定金类型字典

### 2. 缓存优化
对于频繁使用的字典数据，可以考虑添加缓存机制：
```javascript
const statusCache = new Map();
const getOrderStatusText = (status) => {
  if (statusCache.has(status)) {
    return statusCache.get(status);
  }
  // ... 查找逻辑
  statusCache.set(status, result);
  return result;
};
```

### 3. 类型定义
可以考虑添加 TypeScript 类型定义，提高代码的类型安全性。
