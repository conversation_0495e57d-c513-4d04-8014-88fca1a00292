# 字典API添加listAll=true参数修改说明

## 问题背景

在订单列表中，销售地类型(`sales_store_type`)和收款状态(`recept_status`)字段显示异常，原因是：
- 这两个字段是系统字典项(`dict_type = 'SYSTEM'`)
- 后端字典接口默认只返回客户字典项，没有包含系统字典项
- 前端无法获取到完整的字典数据，导致显示为"未知"

## 解决方案

在所有获取字典项的API调用中添加 `listAll=true` 参数，确保返回所有类型的字典项（包括系统字典和客户字典）。

## 修改的文件和位置

### 1. src/mock/dictData.js

**修改位置**: 第373行
```javascript
// 修改前
const response = await doGet(`/system/options/${dictCode}`)

// 修改后  
const response = await doGet(`/system/options/${dictCode}?listAll=true`)
```

**说明**: 这是字典数据加载的核心方法，在系统启动时预加载所有字典项时会调用。

### 2. src/api/dict.js

**修改位置**: 第67行
```javascript
// 修改前
return doGet(`/system/options/${optionGroup}`)

// 修改后
return doGet(`/system/options/${optionGroup}?listAll=true`)
```

**说明**: 这是字典API的封装方法，当其他模块直接调用字典API时会使用。

## 调用链路分析

### 主要预加载路径
```
MainPage.vue 
  └── dictManagementUtils.initialize()
      └── dictManager.initialize() (from src/mock/dictData.js)
          └── dictLoader.loadAllDictionaries()
              └── dictLoader.loadDictOptions(dictCode) ✅ 已修改
```

### 按需加载路径
```
组件中调用字典工具函数
  └── getDictOptionsAsync() 或 useDictOptions()
      └── dictManager.getDictOptions()
          └── dictLoader.loadDictOptions(dictCode) ✅ 已修改
```

### 直接API调用路径
```
组件直接调用API
  └── getDictOptions(optionGroup) (from src/api/dict.js) ✅ 已修改
```

## 未修改的API调用

以下API调用**不需要**添加 `listAll=true` 参数：

1. **获取字典列表**: `/system/options` - 用于获取字典组列表，不是获取具体字典项
2. **字典管理相关**: 创建、更新、删除字典的API调用

## 验证方法

1. **开发环境调试**: 
   - 打开浏览器控制台
   - 查看字典加载的调试日志
   - 确认API调用包含 `listAll=true` 参数

2. **订单列表检查**:
   - 进入订单列表页面
   - 检查销售地类型和收款状态字段是否正确显示
   - 应该显示具体的标签而不是"未知"

3. **网络请求检查**:
   - 打开浏览器开发者工具的Network标签
   - 查看 `/system/options/*` 请求
   - 确认请求URL包含 `?listAll=true` 参数

## 预期效果

修改完成后：
- 销售地类型应显示：单店、直营店、二网
- 收款状态应显示：未收款、已收款
- 所有系统字典项都能正确加载和显示

## 注意事项

1. 这个修改只影响前端的API调用，需要后端同步支持 `listAll` 参数
2. 如果后端还未实现该参数，需要先修改后端接口
3. 修改后建议清除浏览器缓存，确保使用最新的字典数据
