# 部署使用说明

## 🚀 一键部署

您的项目现在已经配置好了自动部署到腾讯云COS！

### 使用方法

```bash
# 方式1：使用npm脚本（推荐）
npm run deploy

# 方式2：使用shell脚本
./bin/deploy_cos.sh

# 方式3：分步执行
npm run build:prod  # 构建
npm run upload      # 上传
```

### 当前配置

- **存储桶**: qj-prod-1357102482
- **区域**: ap-beijing  
- **访问地址**: https://qj-prod-1357102482.cos.ap-beijing.myqcloud.com/

### 如需修改配置

编辑 `.env.production` 文件：

```env
# 修改为您的COS配置
VITE_COS_SECRET_ID=your_secret_id
VITE_COS_SECRET_KEY=your_secret_key
VITE_COS_BUCKET=your_bucket_name
VITE_COS_REGION=your_region

# 如果有CDN域名，填入这里
VITE_CDN_BASE_URL=https://your-cdn-domain.com/
```

### 部署流程

1. **清理** - 删除旧的dist目录
2. **构建** - 执行 `vite build --mode production`
3. **上传** - 将所有文件上传到COS
4. **完成** - 显示访问地址

### 注意事项

- 首次使用需要确保COS配置正确
- 上传过程中请保持网络连接稳定
- 大文件上传可能需要较长时间
- 建议配置CDN加速访问

### 故障排除

如果遇到问题，请检查：
1. COS配置是否正确
2. 网络连接是否正常
3. 存储桶权限是否足够
4. 查看详细的错误信息

更多详细配置请参考 `COS_DEPLOY_README.md`
