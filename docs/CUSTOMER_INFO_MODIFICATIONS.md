# 客户信息组件修改说明

## 修改概述
根据开发要求，对 `src/components/orders/sections/CustomerInfoSection.vue` 组件进行了布局和交互样式的修改。

## 主要修改内容

### 1. 布局调整
- **第一行（4列）**：
  - 客户类型
  - 客户名称  
  - 联系电话
  - **新增：客户编码**（只读，默认显示"系统自动生成"，匹配到客户时显示customerId）

- **第二行（4列）**：
  - 销售顾问
  - 销售单位
  - **移动：销售地类型**（从第一行移到第二行第三列）
  - **新增：销售地详情**（第四列动态填充）

- **第三行（2列）**：
  - **移动：定金类型**（从第二行移到第三行第一列）
  - **移动：已付定金**（从第二行移到第三行第二列）

### 2. 新增功能

#### 客户编码字段
- 字段名：`customerCode`
- 默认显示："系统自动生成"
- 当匹配到客户时，自动显示客户ID
- 只读状态

#### 销售地详情动态字段
- 字段名：`salesStoreDetail`
- 根据销售地类型动态显示不同控件：
  - **单店（4S）**：留空（占位div）
  - **直营店（3S）**：显示可筛选的直营店下拉框
  - **二网（2S）**：显示单行输入框作为二网备注

### 3. 新增方法和逻辑

#### 销售地类型处理
- `handleSalesStoreTypeChange(value)`: 处理销售地类型变化
- `getSalesStoreDetailLabel()`: 获取销售地详情标签
- `fetchDirectStores()`: 获取直营店列表
- `handleDirectStoreChange(value)`: 处理直营店选择

#### 客户编码处理
- 监听 `customerId` 变化，自动更新客户编码显示

### 4. 新增状态变量
- `directStoreOptions`: 直营店选项列表
- `directStoreLoading`: 直营店加载状态

### 5. API集成
- 引入 `getBizOrgList` API 用于获取直营店列表
- 通过 `orgType: '3S'` 参数筛选直营店类型的机构

## 字段配置
组件支持通过 `config.fields` 配置各字段的可见性和可编辑性：

```javascript
fields: {
  customerCode: { visible: true, editable: false },
  salesStoreDetail: { visible: true, editable: true },
  // ... 其他字段
}
```

## 兼容性
- 保持了原有的所有功能和API接口
- 新增字段通过可见性配置控制，不影响现有使用
- 向后兼容，不会破坏现有的表单数据结构

## 技术实现
- 使用 Vue 3 Composition API
- 集成 Naive UI 组件库
- 响应式数据绑定
- 动态组件渲染
- API异步调用处理
