<template>
  <n-layout position="absolute" has-sider>
    <n-layout-sider
      :collapsed="isCollapsed"
      :collapsed-width="64"
      :width="240"
      show-trigger
      @collapse="isCollapsed = true"
      @expand="isCollapsed = false"
      bordered
    >
      <n-menu
        v-if="menuOptions.length > 0"
        :collapsed="isCollapsed"
        :options="menuOptions"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :render-label="renderMenuLabel"
        :render-icon="renderMenuIcon"
        @update:value="handleMenuSelect"
      />
      <div v-else class="no-menu-tip">
        {{ isCollapsed ? '' : '暂无子菜单' }}
      </div>
    </n-layout-sider>
    <n-layout-content class="main-content">
      <slot></slot>
    </n-layout-content>
  </n-layout>
</template>

<script setup>
import { ref, onMounted, computed, h } from 'vue'
import { useMainStore } from '@/stores/mainStore'
import { useRouter, useRoute } from 'vue-router'
import { NLayout, NLayoutSider, NLayoutContent, NMenu, NIcon } from 'naive-ui'
import { ChevronForward } from '@vicons/ionicons5'

const props = defineProps({
  // 当前子系统的路径前缀，例如 '/tasks'
  systemPath: {
    type: String,
    required: true
  }
})

const router = useRouter()
const route = useRoute()
const mainStore = useMainStore()
const isCollapsed = ref(false)
const currentSystemMenu = ref(null)

// 处理菜单选择
const handleMenuSelect = (key) => {
  console.log('选中菜单项:', key)
  router.push(key)
}

// 自定义渲染菜单标签
const renderMenuLabel = (option) => {
  return h('div', { class: 'menu-label' }, [
    h('span', option.label),
    option.children && h(NIcon, { class: 'expand-icon' }, {
      default: () => h(ChevronForward)
    })
  ])
}

// 自定义渲染菜单图标
const renderMenuIcon = (option) => {
  if (option.icon) {
    return h(NIcon, null, { default: () => h(option.icon) })
  }
  return null
}

// 转换菜单数据格式
const menuOptions = computed(() => {
  if (!currentSystemMenu.value?.children) return []
  
  return currentSystemMenu.value.children.map(item => ({
    label: item.label,
    key: item.key,
    icon: typeof item.icon === 'function' ? item.icon : null,
    children: item.children?.map(child => ({
      label: child.label,
      key: child.key,
      icon: typeof child.icon === 'function' ? child.icon : null
    }))
  }))
})

onMounted(async () => {
  // 获取所有菜单
  if (!mainStore.menusLoaded) {
    await mainStore.fetchMenus()
  }
  
  const menus = mainStore.getMenus
  
  // 查找当前子系统的菜单项
  const findCurrentSystemMenu = (menuList) => {
    for (const menu of menuList) {
      // 检查菜单路径是否匹配当前子系统路径
      if (menu.key === props.systemPath) {
        return menu
      }
      
      // 递归检查子菜单
      if (menu.children) {
        const found = findCurrentSystemMenu(menu.children)
        if (found) return found
      }
    }
    return null
  }

  currentSystemMenu.value = findCurrentSystemMenu(menus)
  
  if (currentSystemMenu.value) {
    console.log('当前子系统菜单:', currentSystemMenu.value)
    if (currentSystemMenu.value.children) {
      console.log('子菜单列表:', currentSystemMenu.value.children)
    } else {
      console.log('该子系统没有子菜单')
    }
  } else {
    console.log('未找到当前子系统对应的菜单项')
  }
})
</script>

<style scoped>
.main-content {
  padding: 20px;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.no-menu-tip {
  padding: 12px;
  color: #999;
  text-align: center;
}

.menu-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.expand-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #999;
}
</style>
