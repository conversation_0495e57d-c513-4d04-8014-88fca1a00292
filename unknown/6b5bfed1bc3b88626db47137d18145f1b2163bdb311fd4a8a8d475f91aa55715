-- 业务组织架构管理系统数据库设计
-- 创建时间: 2024-01-15
-- 说明: 包含业务组织架构、成员管理、字典数据等核心表结构

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 业务组织架构主表
-- ----------------------------
DROP TABLE IF EXISTS `biz_org`;
CREATE TABLE `biz_org` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_name` varchar(200) NOT NULL COMMENT '机构名称',
  `province` varchar(50) DEFAULT NULL COMMENT '省/直辖市代码，关联字典表',
  `city` varchar(50) DEFAULT NULL COMMENT '市/区代码，关联字典表',
  `address` varchar(500) DEFAULT NULL COMMENT '详细地址',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `sales_brands` varchar(100) COMMENT '主营品牌，逗号分隔的字典值',
  `biz_permissions` varchar(100) COMMENT '业务权限，逗号分隔的字典值(can_stock_in,can_sell,can_stock_out,can_settle)',
  `org_type` varchar(50) DEFAULT NULL COMMENT '机构类型，字典值(group,single_store,secondary_network)',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active:启用,inactive:停用)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务组织架构表';

-- ----------------------------
-- 2. 业务组织成员表
-- ----------------------------
DROP TABLE IF EXISTS `biz_org_member`;
CREATE TABLE `biz_org_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_id` bigint(20) NOT NULL COMMENT '机构ID，关联biz_organizations.id',
  `agent_id` varchar(50) NOT NULL COMMENT '员工ID/工号',
  `agent_name` varchar(100) NOT NULL COMMENT '员工姓名',
  `business_role` varchar(50) DEFAULT NULL COMMENT '业务角色，字典值(sales_manager,warehouse_keeper,finance_specialist等)',
  `data_range` text DEFAULT NULL COMMENT '数据查询范围，逗号分割的biz_org_id字符串',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active:启用,inactive:停用)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_agent` (`org_id`, `agent_id`),
  KEY `idx_agent_id` (`agent_id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务组织成员表';
