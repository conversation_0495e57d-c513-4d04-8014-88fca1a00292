<template>
  <n-modal
    :show="props.visible"
    @update:show="(val) => emit('update:visible', val)"
    :title="'客户详情'"
    preset="card"
    :style="{ width: '800px', maxWidth: '90%' }"
    :mask-closable="false"
    :auto-focus="false"
    class="customer-detail-modal"
    :class="{ 'maximized': isMaximized }"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleMaximize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>
    <n-spin :show="loading">
      <div v-if="customerData" class="customer-detail-content">
        <n-descriptions bordered :column="2" label-placement="left">
          <n-descriptions-item label="客户ID">
            {{ customerData.id }}
            <n-button quaternary circle size="small" @click="copyToClipboard(customerData.id)">
              <template #icon>
                <n-icon color="#18a058"><CopyOutline /></n-icon>
              </template>
            </n-button>
          </n-descriptions-item>
          <n-descriptions-item label="客户名称">{{ customerData.customerName }}</n-descriptions-item>
          <n-descriptions-item label="客户类型">
            <n-tag :type="customerData.customerType === 'individual' ? 'info' : 'success'" size="small">
              {{ customerData.customerType === 'individual' ? '个人客户' : '法人客户' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="成交状态">
            <n-tag :type="customerData.dealStatus === 'LEADS' ? 'warning' : 'success'" size="small">
              {{ customerData.dealStatus === 'LEADS' ? '未成交' : '已成交' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item :label="customerData.customerType === 'individual' ? '身份证号码' : '统一社会信用代码'">
            {{ customerData.customerIdCode }}
            <n-button quaternary circle size="small" @click="copyToClipboard(customerData.customerIdCode)">
              <template #icon>
                <n-icon color="#18a058"><CopyOutline /></n-icon>
              </template>
            </n-button>
          </n-descriptions-item>
          <n-descriptions-item label="手机号码">
            {{ customerData.mobile }}
            <n-button quaternary circle size="small" @click="copyToClipboard(customerData.mobile)">
              <template #icon>
                <n-icon color="#18a058"><CopyOutline /></n-icon>
              </template>
            </n-button>
          </n-descriptions-item>
          <n-descriptions-item label="所属单位">{{ customerData.ownerOrgName }}</n-descriptions-item>
          <n-descriptions-item label="销售顾问">{{ customerData.ownerSellerName }}</n-descriptions-item>
          <n-descriptions-item label="创建时间">{{ customerData.createTime }}</n-descriptions-item>
          <n-descriptions-item label="更新时间">{{ customerData.updateTime }}</n-descriptions-item>
          <n-descriptions-item label="客户地址" :span="2">{{ customerData.address || '无' }}</n-descriptions-item>
          <n-descriptions-item label="备注" :span="2">{{ customerData.remark || '无' }}</n-descriptions-item>
        </n-descriptions>
      </div>
      <div v-else class="empty-data">
        <n-empty description="暂无客户数据" />
      </div>
    </n-spin>
    <template #footer>
      <n-space justify="end">
        <n-button @click="emit('update:visible', false)">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { NModal, NSpin, NDescriptions, NDescriptionsItem, NButton, NSpace, NEmpty, NIcon, NTag } from 'naive-ui'
import { ExpandOutline, ContractOutline, CopyOutline } from '@vicons/ionicons5'
import customerApi from '@/api/customer'
import messages from '@/utils/messages'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [Number, String],
    default: null
  }
})

// 定义组件事件
const emit = defineEmits(['update:visible'])

// 组件状态
const loading = ref(false)
const customerData = ref(null)
const isMaximized = ref(true)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.id) {
    fetchCustomerDetail()
  }
})

// 监听id属性变化
watch(() => props.id, (newVal) => {
  if (props.visible && newVal) {
    fetchCustomerDetail()
  }
})

// 获取客户详情
const fetchCustomerDetail = async () => {
  if (!props.id) return

  loading.value = true
  try {
    // 调用API获取客户详情
    const response = await customerApi.getCustomerDetail(props.id)

    if (response.code === 200) {
      customerData.value = response.data
    } else {
      messages.error(response.message || '获取客户详情失败')
    }
  } catch (error) {
    console.error('获取客户详情失败:', error)
    messages.error('获取客户详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 切换最大化/最小化
const toggleMaximize = () => {
  isMaximized.value = !isMaximized.value
}

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    messages.success('复制成功')
  }).catch(() => {
    messages.error('复制失败')
  })
}
</script>

<style scoped>
.customer-detail-modal {
  transition: all 0.3s;
}

.customer-detail-modal.maximized {
  width: 90vw !important;
  height: 80vh !important;
}

.customer-detail-content {
  padding: 16px 0;
}

.empty-data {
  padding: 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.n-descriptions-table-header) {
  background-color: #f5f7fa;
}
</style>
