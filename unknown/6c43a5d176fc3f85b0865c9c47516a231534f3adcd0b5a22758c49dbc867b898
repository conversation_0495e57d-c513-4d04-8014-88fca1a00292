import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 使用环境变量来决定是否启用代理
  const enableProxy = mode !== 'production';

  // 生产环境使用CDN域名作为base
  const base = mode === 'production' && env.VITE_CDN_BASE_URL
    ? env.VITE_CDN_BASE_URL
    : '/';

  return {
    base,
    plugins: [vue()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    server: {
      proxy: enableProxy ? {
        '/open-api': {
          target: 'http://localhost:9999',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/open-api/, '')
        }
      } : {}
    },
    build: {
      // 生产环境构建配置
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false,
      minify: 'terser',
      rollupOptions: {
        output: {
          // 分包策略
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            ui: ['naive-ui'],
            charts: ['echarts', 'echarts-gl']
          },
          // 文件命名
          chunkFileNames: `assets/js/[name]-[hash]-${Date.now()}.js`,
          entryFileNames: `assets/js/[name]-[hash]-${Date.now()}.js`,
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              return `assets/media/[name]-[hash].${ext}`;
            }
            if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
              return `assets/images/[name]-[hash].${ext}`;
            }
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              return `assets/fonts/[name]-[hash].${ext}`;
            }
            return `assets/[ext]/[name]-[hash].${ext}`;
          }
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 使用新的 Sass JavaScript API
          implementation: 'sass',
          sassOptions: {
            outputStyle: 'expanded'
          }
        }
      }
    }
  }
})
