<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重定向中...</title>
    <script>
        // 获取当前URL
        const currentPath = window.location.pathname;
        // 移除.html后缀（如果有）
        const cleanPath = currentPath.replace(/\.html$/, '');
        // 重定向到应用根目录
        window.location.href = '/' + (cleanPath === '/error' ? '' : cleanPath.substring(1));
    </script>
</head>
<body>
    <div style="text-align: center; padding: 20px;">
        <h2>页面加载中...</h2>
        <p>如果页面没有自动跳转，请<a href="/">点击这里</a>返回首页。</p>
    </div>
</body>
</html>