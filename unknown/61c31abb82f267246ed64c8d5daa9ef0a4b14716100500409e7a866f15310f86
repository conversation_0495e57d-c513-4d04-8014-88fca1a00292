<template>
    <div class="department-selector" :class="{ 'is-disabled': disabled, 'single-mode': mode === 'single' }"
        :style="{ width }" @click="handleClick" ref="containerRef">
        <div class="selected-departments">
            <template v-if="hasSelectedDepartments">
                <div class="selected-tags" :class="{ 'single-mode': mode === 'single' }">
                    <template v-if="mode === 'single' || selectedDepartments.length <= 5">
                        <n-tag v-for="dept in selectedDepartments" :key="dept.id" size="medium" round type="success"
                            class="dept-tag" :closable="true" @close.stop="removeDepartment(dept)">
                            <template #avatar>
                                <n-icon>
                                    <Building />
                                </n-icon>
                            </template>
                            {{ dept.name }}
                        </n-tag>
                    </template>
                    <template v-else>
                        <n-tag v-for="dept in selectedDepartments.slice(0, 5)" :key="dept.id" size="medium" round
                            type="success" class="dept-tag" :closable="true" @close.stop="removeDepartment(dept)">
                            <template #avatar>
                                <n-icon>
                                    <Building />
                                </n-icon>
                            </template>
                            {{ dept.name }}
                        </n-tag>
                        <n-tag size="medium" round type="info" class="dept-tag more-tag" closable
                            @close.stop="removeDepartment('all')">
                            等{{ selectedDepartments.length }}个部门
                        </n-tag>
                    </template>
                </div>
            </template>
            <template v-else>
                <n-icon size="24">
                    <Building />
                </n-icon>
                <span class="selector-text">{{ label }}</span>
            </template>
        </div>

        <template v-if="!props.forceMobile">
            <n-modal v-model:show="visible" :style="isMobile ? mobileModalStyle : pcModalStyle" :mask-closable="false"
                preset="card" :class="{ 'mobile-modal': isMobile }" @close="hidePanel">
                <template #header>
                    <div class="panel-header">
                        <template v-if="isMobile">
                            <n-button quaternary circle @click="hidePanel">
                                <template #icon>
                                    <n-icon>
                                        <ArrowBack />
                                    </n-icon>
                                </template>
                            </n-button>
                        </template>
                        <div class="panel-title">{{ label }}</div>
                        <template v-if="isMobile">
                            <n-button type="primary" size="small" :disabled="!tempSelectedDepartments.length"
                                @click="confirmSelection">
                                确定({{ tempSelectedDepartments.length }})
                            </n-button>
                        </template>
                    </div>
                </template>

                <div class="modal-content" :class="{ 'mobile-content': isMobile }">
                    <div class="selected-department-container">
                        <template v-if="tempSelectedDepartments.length > 0">
                            <div class="selected-departments-list">
                                <template v-if="tempSelectedDepartments.length > 5">
                                    <n-tag v-for="dept in tempSelectedDepartments.slice(0, 5)" :key="dept.key"
                                        size="medium" round closable class="dept-tag"
                                        @close="clearTempSelection(dept.key)">
                                        <template #avatar>
                                            <n-icon>
                                                <Building />
                                            </n-icon>
                                        </template>
                                        {{ dept.label }}
                                    </n-tag>
                                    <n-tag size="medium" round type="info" class="dept-tag more-tag" closable
                                        @close="removeDepartment('all')">
                                        等{{ tempSelectedDepartments.length }}个部门
                                    </n-tag>
                                </template>
                                <template v-else>
                                    <n-tag v-for="dept in tempSelectedDepartments" :key="dept.key" size="medium" round
                                        closable class="dept-tag" @close="clearTempSelection(dept.key)">
                                        <template #avatar>
                                            <n-icon>
                                                <Building />
                                            </n-icon>
                                        </template>
                                        {{ dept.label }}
                                    </n-tag>
                                </template>
                            </div>
                        </template>
                        <div v-else class="no-selection">未选择部门</div>
                    </div>

                    <n-input v-model:value="searchText" placeholder="搜索部门" clearable>
                        <template #prefix>
                            <n-icon>
                                <Search />
                            </n-icon>
                        </template>
                    </n-input>

                    <div class="panel-content">
                        <div v-if="!isMobile" class="org-tree">
                            <n-tree block-line :data="filteredTreeData" :expanded-keys="expandedKeys"
                                @update:expanded-keys="handleExpand"
                                :checked-keys="mode === 'multiple' ? checkedKeys : undefined"
                                @update:checked-keys="handleCheckedKeysUpdate"
                                :selected-keys="mode === 'single' ? selectedKeys : undefined"
                                @update:selected-keys="handleSelectedKeysUpdate" :loading="loading"
                                :render-switcher-icon="renderSwitcherIcon" :checkable="mode === 'multiple'" selectable
                                key-field="key" label-field="label" children-field="children"
                                cascade :check-on-click="mode === 'multiple'" />
                        </div>

                        <div v-else class="mobile-dept-list">
                            <div class="breadcrumb" v-if="currentPath.length > 0">
                                <n-space align="center">
                                    <n-button quaternary circle size="small" @click="goBack">
                                        <template #icon>
                                            <n-icon>
                                                <ArrowBack />
                                            </n-icon>
                                        </template>
                                    </n-button>
                                    <n-breadcrumb>
                                        <n-breadcrumb-item v-for="(item, index) in currentPath" :key="item.key"
                                            @click="goToLevel(index)">
                                            {{ item.label }}
                                        </n-breadcrumb-item>
                                    </n-breadcrumb>
                                </n-space>
                            </div>

                            <div class="dept-list">
                                <div v-for="dept in currentLevelDepts" :key="dept.key" class="dept-item"
                                    @click="handleDeptClick(dept)">
                                    <div class="dept-info">
                                        <n-space align="center">
                                            <template v-if="mode === 'multiple'">
                                                <n-checkbox :checked="checkedKeys.includes(dept.key)"
                                                    @update:checked="(checked) => handleDeptCheck(dept, checked)"
                                                    @click.stop />
                                            </template>
                                            <template v-else>
                                                <n-radio :checked="selectedKeys.includes(dept.key)"
                                                    @update:checked="(checked) => handleDeptSelect(dept, checked)"
                                                    @click.stop />
                                            </template>
                                            <span class="dept-name">{{ dept.label }}</span>
                                        </n-space>
                                    </div>
                                    <n-icon v-if="dept.children?.length" class="dept-arrow">
                                        <ChevronForward />
                                    </n-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <template #footer>
                    <n-space v-if="!isMobile" justify="end">
                        <n-button @click="hidePanel">取消</n-button>
                        <n-button type="primary" :disabled="!tempSelectedDepartments.length" @click="confirmSelection">
                            确定
                        </n-button>
                    </n-space>
                </template>
            </n-modal>
        </template>

        <template v-else>
            <div v-show="visible" class="mobile-preview-panel">
                <div class="mobile-preview-header">
                    <n-button quaternary circle @click="hidePanel">
                        <template #icon>
                            <n-icon>
                                <ArrowBack />
                            </n-icon>
                        </template>
                    </n-button>
                    <div class="panel-title">{{ label }}</div>
                    <n-button type="primary" size="small" :disabled="!tempSelectedDepartments.length"
                        @click="confirmSelection">
                        确定({{ tempSelectedDepartments.length }})
                    </n-button>
                </div>

                <div class="mobile-preview-content">
                    <div class="selected-department-container">
                        <template v-if="tempSelectedDepartments.length > 0">
                            <div class="selected-departments-list">
                                <template v-if="tempSelectedDepartments.length > 5">
                                    <n-tag v-for="dept in tempSelectedDepartments.slice(0, 5)" :key="dept.key"
                                        size="medium" round closable class="dept-tag"
                                        @close="clearTempSelection(dept.key)">
                                        <template #avatar>
                                            <n-icon>
                                                <Building />
                                            </n-icon>
                                        </template>
                                        {{ dept.label }}
                                    </n-tag>
                                    <n-tag size="medium" round type="info" class="dept-tag more-tag" closable
                                        @close="removeDepartment('all')">
                                        等{{ tempSelectedDepartments.length }}个部门
                                    </n-tag>
                                </template>
                                <template v-else>
                                    <n-tag v-for="dept in tempSelectedDepartments" :key="dept.key" size="medium" round
                                        closable class="dept-tag" @close="clearTempSelection(dept.key)">
                                        <template #avatar>
                                            <n-icon>
                                                <Building />
                                            </n-icon>
                                        </template>
                                        {{ dept.label }}
                                    </n-tag>
                                </template>
                            </div>
                        </template>
                        <div v-else class="no-selection">未选择部门</div>
                    </div>

                    <n-input v-model:value="searchText" placeholder="搜索部门" clearable>
                        <template #prefix>
                            <n-icon>
                                <Search />
                            </n-icon>
                        </template>
                    </n-input>

                    <div class="mobile-dept-list">
                        <div class="breadcrumb" v-if="currentPath.length > 0">
                            <n-space align="center">
                                <n-button quaternary circle size="small" @click="goBack">
                                    <template #icon>
                                        <n-icon>
                                            <ArrowBack />
                                        </n-icon>
                                    </template>
                                </n-button>
                                <n-breadcrumb>
                                    <n-breadcrumb-item v-for="(item, index) in currentPath" :key="item.key"
                                        @click="goToLevel(index)">
                                        {{ item.label }}
                                    </n-breadcrumb-item>
                                </n-breadcrumb>
                            </n-space>
                        </div>

                        <div class="dept-list">
                            <div v-for="dept in currentLevelDepts" :key="dept.key" class="dept-item"
                                @click="handleDeptClick(dept)">
                                <div class="dept-info">
                                    <n-space align="center">
                                        <template v-if="mode === 'multiple'">
                                            <n-checkbox :checked="checkedKeys.includes(dept.key)"
                                                @update:checked="(checked) => handleDeptCheck(dept, checked)"
                                                @click.stop />
                                        </template>
                                        <template v-else>
                                            <n-radio :checked="selectedKeys.includes(dept.key)"
                                                @update:checked="(checked) => handleDeptSelect(dept, checked)"
                                                @click.stop />
                                        </template>
                                        <span class="dept-name">{{ dept.label }}</span>
                                    </n-space>
                                </div>
                                <n-icon v-if="dept.children?.length" class="dept-arrow">
                                    <ChevronForward />
                                </n-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup>
import { ref, computed, h, watch } from 'vue'
import { NInput, NButton, NSpace, NTree, NIcon, NTag } from 'naive-ui'
import { Search, Building } from '@vicons/tabler'
import { MinusSquareOutlined, PlusSquareOutlined } from '@vicons/antd'
import { getDepartments } from '@/api/users'
import { ArrowBack, ChevronForward } from '@vicons/ionicons5'

// Props 定义
const props = defineProps({
    mode: {
        type: String,
        default: 'single',
        validator: (value) => ['single', 'multiple'].includes(value)
    },
    disabled: {
        type: Boolean,
        default: false
    },
    modelValue: {
        type: [Object, Array],
        default: null
    },
    label: {
        type: String,
        default: '选择部门'
    },
    width: {
        type: String,
        default: '100%'
    },
    forceMobile: {
        type: Boolean,
        default: false
    },
    renderMode: {
        type: String,
        default: 'auto',  // auto: 自动检测, desktop: PC模式, mobile: 移动端模式
        validator: (value) => ['auto', 'desktop', 'mobile'].includes(value)
    }
})

const emit = defineEmits(['update:modelValue'])

// 组件状态
const visible = ref(false)
const searchText = ref('')
const expandedKeys = ref([])
const checkedKeys = ref([])
const selectedKeys = ref([])
const loading = ref(false)
const treeData = ref([])
const tempSelectedDepartments = ref([])

// 添加移动端层级导航相关的状态
const currentPath = ref([])
const currentLevelDepts = ref([])

// 计算属性
const selectedDepartments = computed(() => {
    if (props.mode === 'single') {
        return props.modelValue ? [props.modelValue] : []
    }
    return Array.isArray(props.modelValue) ? props.modelValue : []
})

const hasSelectedDepartments = computed(() => selectedDepartments.value.length > 0)

// 添加搜索相关的计算属性
const filteredTreeData = computed(() => {
    if (!searchText.value) {
        return treeData.value
    }

    const searchLower = searchText.value.toLowerCase()

    // 递归搜索函数
    const filterNode = (nodes) => {
        if (!nodes) return []

        return nodes.reduce((filtered, node) => {
            // 创建节点的副本，避免修改原始数据
            const newNode = { ...node }

            // 检查当前节点是否匹配
            const isMatch = node.label.toLowerCase().includes(searchLower)

            // 递归处理子节点
            if (node.children) {
                const filteredChildren = filterNode(node.children)
                if (filteredChildren.length > 0) {
                    newNode.children = filteredChildren
                    filtered.push(newNode)
                    return filtered
                }
            }

            // 如果当前节点匹配，则保留该节点
            if (isMatch) {
                // 如果有子节点，保留完整的子节点结构
                if (node.children) {
                    newNode.children = node.children
                }
                filtered.push(newNode)
            }

            return filtered
        }, [])
    }

    const filtered = filterNode(treeData.value)

    // 搜索时自动展开所有节点
    if (searchText.value) {
        // 收集所有节点的 key
        const collectKeys = (nodes) => {
            let keys = []
            nodes.forEach(node => {
                keys.push(node.key)
                if (node.children) {
                    keys = keys.concat(collectKeys(node.children))
                }
            })
            return keys
        }
        // 设置展开的键值
        expandedKeys.value = collectKeys(filtered)
    }

    return filtered
})

// 方法
const buildTreeData = (departments) => {
    const options = []
    const map = {}

    departments.forEach(dept => {
        map[dept.id] = {
            key: dept.id.toString(),
            label: dept.name,
            children: [],
            isLeaf: true
        }
    })

    departments.forEach(dept => {
        if (dept.parentId === 0 || !dept.parentId) {
            options.push(map[dept.id])
        } else {
            const parent = map[dept.parentId]
            if (parent) {
                parent.children.push(map[dept.id])
                parent.isLeaf = false
            }
        }
    })

    return options
}

const loadDepartments = async () => {
    try {
        loading.value = true
        const response = await getDepartments()
        if (response.code === 200) {
            treeData.value = buildTreeData(response.data)
            // 初始化移动端列表
            if (isMobile.value) {
                currentLevelDepts.value = treeData.value
                currentPath.value = []
            }
            // PC端展开第一层
            else if (treeData.value.length > 0) {
                expandedKeys.value = [treeData.value[0].key]
            }
        }
    } catch (error) {
        console.error('Failed to load departments:', error)
    } finally {
        loading.value = false
    }
}

const renderSwitcherIcon = ({ expanded, isLeaf }) => {
    if (isLeaf) return null
    return expanded ? h(MinusSquareOutlined) : h(PlusSquareOutlined)
}

const handleCheckedKeysUpdate = (keys) => {
    if (props.mode !== 'multiple') return

    // 更新选中的键值
    checkedKeys.value = keys

    // 更新临时选择的部门列表
    const selectedDepts = keys.map(key => {
        const dept = findDepartmentInTree(key, treeData.value)
        return dept ? {
            key: dept.key,
            label: dept.label
        } : null
    }).filter(Boolean)

    tempSelectedDepartments.value = selectedDepts
}

// 递归获取所有子节点的key
const getAllChildrenKeys = (node) => {
    if (!node || !node.children || node.children.length === 0) {
        return []
    }

    let keys = []
    for (const child of node.children) {
        keys.push(child.key)
        keys = keys.concat(getAllChildrenKeys(child))
    }
    return keys
}

const handleSelectedKeysUpdate = (keys) => {
    if (props.mode !== 'single') return
    selectedKeys.value = keys
    if (keys.length > 0) {
        const dept = findDepartmentInTree(keys[0], treeData.value)
        tempSelectedDepartments.value = dept ? [{
            key: dept.key,
            label: dept.label
        }] : []
    } else {
        tempSelectedDepartments.value = []
    }
}

const findDepartmentInTree = (key, nodes) => {
    for (const node of nodes) {
        if (node.key === key) return node
        if (node.children) {
            const found = findDepartmentInTree(key, node.children)
            if (found) return found
        }
    }
    return null
}

const confirmSelection = () => {
    const selected = tempSelectedDepartments.value.map(dept => ({
        id: dept.key,
        name: dept.label
    }))

    if (props.mode === 'single') {
        emit('update:modelValue', selected[0] || null)
    } else {
        emit('update:modelValue', selected)
    }

    visible.value = false
    searchText.value = ''
}

const handleExpand = (keys) => {
    expandedKeys.value = keys
}

const handleClick = () => {
    if (!props.disabled) {
        visible.value = true
        initializeSelection()
        if (!treeData.value.length) {
            loadDepartments()
        }
    }
}

const initializeSelection = () => {
    const currentSelection = selectedDepartments.value
    tempSelectedDepartments.value = currentSelection.map(dept => ({
        key: dept.id?.toString(),
        label: dept.name
    }))

    const keys = tempSelectedDepartments.value.map(dept => dept.key)
    if (props.mode === 'multiple') {
        checkedKeys.value = keys
    } else {
        selectedKeys.value = keys
    }
}

const hidePanel = () => {
    visible.value = false
    searchText.value = ''
}

const clearTempSelection = (deptKey) => {
    tempSelectedDepartments.value = tempSelectedDepartments.value.filter(d => d.key !== deptKey)

    if (props.mode === 'multiple') {
        checkedKeys.value = tempSelectedDepartments.value.map(d => d.key)
    } else {
        selectedKeys.value = tempSelectedDepartments.value.map(d => d.key)
    }
}

const removeDepartment = (dept) => {
    if (dept === 'all') {
        emit('update:modelValue', props.mode === 'single' ? null : [])
    } else {
        if (props.mode === 'single') {
            emit('update:modelValue', null)
        } else {
            const newSelection = selectedDepartments.value.filter(d => d.id !== dept.id)
            emit('update:modelValue', newSelection)
        }
    }
}

// 修改 searchText 的监听
watch(searchText, (newValue) => {
    if (!newValue) {
        // 清空搜索时，只保留第一层展开
        if (treeData.value.length > 0) {
            expandedKeys.value = [treeData.value[0].key]
        } else {
            expandedKeys.value = []
        }
    }
})

// 添加移动端检测
const isMobile = computed(() => {
    if (props.renderMode !== 'auto') {
        return props.renderMode === 'mobile'
    }
    return window.innerWidth <= 768
})

// 修改 PC 和移动端的样式配置
const pcModalStyle = {
    width: '60vw',
    maxWidth: '800px'
}

const mobileModalStyle = {
    width: '100%',
    height: '100%',
    margin: '0',
    padding: '0',
    borderRadius: '0'
}

// 处理移动端部门点击
const handleDeptClick = (dept) => {
    if (dept.children?.length) {
        // 有子部门，进入下一级
        currentPath.value.push(dept)
        currentLevelDepts.value = dept.children
    } else {
        // 没有子部门，选中当前部门
        if (props.mode === 'single') {
            handleDeptSelect(dept, !selectedKeys.value.includes(dept.key))
        } else {
            handleDeptCheck(dept, !checkedKeys.value.includes(dept.key))
        }
    }
}

// 返回上一级
const goBack = () => {
    currentPath.value.pop()
    if (currentPath.value.length === 0) {
        currentLevelDepts.value = filteredTreeData.value
    } else {
        const lastDept = currentPath.value[currentPath.value.length - 1]
        currentLevelDepts.value = lastDept.children || []
    }
}

// 跳转到指定层级
const goToLevel = (index) => {
    currentPath.value = currentPath.value.slice(0, index + 1)
    const targetDept = currentPath.value[currentPath.value.length - 1]
    currentLevelDepts.value = targetDept.children || []
}

// 添加处理部门选择的方法
const handleDeptCheck = (dept, checked) => {
    if (checked) {
        // 添加到选中列表
        if (!checkedKeys.value.includes(dept.key)) {
            // 选中当前部门
            checkedKeys.value.push(dept.key)
            tempSelectedDepartments.value.push({
                key: dept.key,
                label: dept.label
            })

            // 级联选择：选中所有子部门
            if (dept.children && dept.children.length > 0) {
                const childrenKeys = getAllChildrenKeys(dept)

                // 添加所有未选中的子部门
                for (const childKey of childrenKeys) {
                    if (!checkedKeys.value.includes(childKey)) {
                        checkedKeys.value.push(childKey)

                        // 添加到临时选择列表
                        const childDept = findDepartmentInTree(childKey, treeData.value)
                        if (childDept) {
                            tempSelectedDepartments.value.push({
                                key: childDept.key,
                                label: childDept.label
                            })
                        }
                    }
                }
            }
        }
    } else {
        // 从选中列表移除当前部门
        checkedKeys.value = checkedKeys.value.filter(key => key !== dept.key)
        tempSelectedDepartments.value = tempSelectedDepartments.value.filter(d => d.key !== dept.key)

        // 级联取消选择：移除所有子部门
        if (dept.children && dept.children.length > 0) {
            const childrenKeys = getAllChildrenKeys(dept)

            // 移除所有子部门
            checkedKeys.value = checkedKeys.value.filter(key => !childrenKeys.includes(key))
            tempSelectedDepartments.value = tempSelectedDepartments.value.filter(d => !childrenKeys.includes(d.key))
        }
    }
}

const handleDeptSelect = (dept, checked) => {
    if (checked) {
        // 单选模式：替换当前选择
        selectedKeys.value = [dept.key]
        tempSelectedDepartments.value = [{
            key: dept.key,
            label: dept.label
        }]

        // 单选模式可以直接确认选择
        if (!props.multiple) {
            confirmSelection()
        }
    } else {
        // 取消选择
        selectedKeys.value = []
        tempSelectedDepartments.value = []
    }
}
</script>

<style scoped>
.department-selector {
    min-width: 350px;
    max-width: 100%;
    position: relative;
}

.selected-departments {
    border: 2px dashed var(--primary-color);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(var(--primary-color-rgb), 0.02);
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.department-selector.single-mode .selected-departments {
    min-height: 20px;
    padding: 8px 12px;
}

.selected-departments:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.selected-departments.is-disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: #f5f5f5;
    border-color: #ddd;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    padding: 4px;
    min-height: 32px;
    align-items: center;
}

.selected-tags.single-mode {
    min-height: unset;
    padding: 0;
}

.dept-tag {
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.dept-tag:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.modal-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 60vh;
    max-height: 600px;
}

.panel-content {
    flex: 1;
    min-height: 0;
    display: flex;
    gap: 16px;
}

.org-tree {
    width: 100%;
    overflow: auto;
    padding-right: 16px;
}

.selected-department-container {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    min-height: 48px;
}

.selected-departments-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.more-tag {
    background-color: #f5f5f5 !important;
    border: 1px solid #e8e8e8;
    color: #666;
}

/* 其他样式保持不变... */

/* 添加搜索相关样式 */
.n-input {
    margin-bottom: 16px;
}

.org-tree {
    position: relative;
}

/* 当没有搜索结果时的样式 */
.org-tree:empty::after {
    content: '没有找到匹配的部门';
    display: block;
    text-align: center;
    color: #999;
    padding: 20px 0;
}

/* 移除高亮相关样式 */
:deep(.n-tree-node-content) {
    transition: background-color 0.3s;
}

:deep(.n-tree-node-content:hover) {
    background-color: var(--n-node-color-hover);
}

/* 修改移动端模态框样式 */
.mobile-modal {
    animation: slideInRight 0.3s ease-out;
}

.mobile-modal :deep(.n-card) {
    height: 100%;
    border-radius: 0;
}

.mobile-modal :deep(.n-card-header) {
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
}

.mobile-modal :deep(.n-card-footer) {
    display: none;
}

/* 移除之前的固定定位样式 */
:deep(.mobile-modal) {
    position: absolute !important;
    inset: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    border-radius: 0 !important;
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 12px;
}

.panel-title {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
}

.mobile-content {
    height: calc(100vh - 56px);
    /* 减去头部高度 */
    padding: 0;
}

.mobile-content .selected-department-container {
    padding: 12px 16px;
    background-color: #f5f5f5;
}

.mobile-content .n-input {
    margin: 12px 16px;
}

.mobile-content .org-tree {
    padding: 0 16px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
    .selected-department-container {
        background-color: #f5f5f5;
    }

    .modal-content {
        padding: 0;
    }

    .org-tree {
        padding: 0 16px 16px;
    }

    :deep(.n-card-header) {
        padding: 8px 16px;
    }

    :deep(.n-card-content) {
        padding: 0;
    }
}

/* PC 端特定样式 */
@media screen and (min-width: 769px) {
    .modal-content {
        padding: 16px;
    }
}

/* 移动端层级列表样式 */
.mobile-dept-list {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.breadcrumb {
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
}

.dept-list {
    flex: 1;
    overflow-y: auto;
}

.dept-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fff;
    cursor: pointer;
}

.dept-item:active {
    background-color: #f9f9f9;
}

.dept-info {
    flex: 1;
    margin-right: 16px;
}

.dept-name {
    font-size: 15px;
}

.dept-arrow {
    color: #999;
    font-size: 20px;
}

:deep(.n-breadcrumb-item) {
    cursor: pointer;
}

:deep(.n-checkbox) {
    margin-right: 8px;
}

:deep(.n-radio) {
    margin-right: 8px;
}

/* 添加移动端预览特定样式 */
:deep(.mobile-modal) {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    border-radius: 0 !important;
}

/* 添加预览模式下的移动端样式 */
.preview-mobile-modal {
    position: absolute !important;
    top: -10px !important;
    left: -10px !important;
    right: -10px !important;
    bottom: -10px !important;
    width: calc(100% + 20px) !important;
    height: calc(100% + 20px) !important;
    margin: 0 !important;
    border-radius: 30px !important;
    transform: none !important;
}

.preview-mobile-modal :deep(.n-modal-mask) {
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
}

.preview-mobile-modal :deep(.n-modal) {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
}

.preview-mobile-modal :deep(.n-card) {
    height: 100% !important;
    max-height: 100% !important;
    border-radius: 30px !important;
}

.preview-mobile-modal :deep(.n-card-content) {
    max-height: calc(100% - 108px) !important;
    overflow: auto;
}

/* 修改移动端模式下的样式配置 */
.mobile-modal :deep(.n-modal) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
}

.mobile-modal :deep(.n-card) {
    height: 100%;
    border-radius: 0;
}

/* 移动端预览面板样式 */
.mobile-preview-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    z-index: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.mobile-preview-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
    background: #fff;
    flex-shrink: 0;
    /* 防止头部被压缩 */
}

.mobile-preview-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    height: calc(100% - 56px);
    /* 减去头部高度 */
}

/* 调整内容区域的布局 */
.selected-department-container {
    flex-shrink: 0;
    /* 防止已选区域被压缩 */
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #eee;
}

.n-input {
    flex-shrink: 0;
    /* 防止搜索框被压缩 */
    margin: 12px 16px;
}

.mobile-dept-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
}

.breadcrumb {
    flex-shrink: 0;
    /* 防止面包屑被压缩 */
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
}

.dept-list {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 16px;
    /* 添加底部内边距 */
}

/* 调整部门列表项样式 */
.dept-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fff;
    cursor: pointer;
}

/* 确保在预览模式下部门选择器容器是相对定位的 */
.preview-mobile-modal .department-selector {
    position: relative;
    height: 100%;
}

/* 移除之前的模态框相关样式 */
.preview-mobile-modal,
.preview-mobile-modal :deep(.n-modal),
.preview-mobile-modal :deep(.n-card) {
    position: static !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
}
</style>