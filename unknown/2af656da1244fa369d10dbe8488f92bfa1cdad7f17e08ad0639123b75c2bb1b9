#!/bin/bash

# COS部署脚本
set -e  # 遇到错误立即退出

echo "🚀 开始部署到COS..."
echo ""

# 检查环境变量文件
if [ ! -f ".env.production" ]; then
    echo "❌ .env.production 文件不存在，请先创建配置文件"
    exit 1
fi

# 检查必要的依赖
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

# 清理旧的构建文件
if [ -d "dist" ]; then
    echo "🧹 清理旧的构建文件..."
    rm -rf dist
fi

# 构建项目
echo "📦 构建生产版本..."
# npm run build:prod
yarn build:prod

# 检查构建是否成功
if [ ! -d "dist" ]; then
    echo "❌ 构建失败，dist目录不存在"
    exit 1
fi

echo "✅ 构建完成"
echo ""

# 上传到COS
echo "☁️ 上传到COS..."
npm run upload

echo ""
echo "🎉 部署完成！"
echo ""

# 读取配置并显示访问地址
if [ -f ".env.production" ]; then
    BUCKET=$(grep "VITE_COS_BUCKET" .env.production | cut -d '=' -f2)
    REGION=$(grep "VITE_COS_REGION" .env.production | cut -d '=' -f2)
    CDN_URL=$(grep "VITE_CDN_BASE_URL" .env.production | cut -d '=' -f2)

    if [ ! -z "$BUCKET" ] && [ ! -z "$REGION" ]; then
        echo "🔗 COS直接访问: https://$BUCKET.cos.$REGION.myqcloud.com/"
    fi

    if [ ! -z "$CDN_URL" ] && [ "$CDN_URL" != "https://cos-cdn-v1.qj-robots.com/" ]; then
        echo "🚀 CDN访问地址: $CDN_URL"
    fi
fi