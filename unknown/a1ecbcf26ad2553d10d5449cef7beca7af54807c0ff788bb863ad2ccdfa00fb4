import axios from 'axios'
import message from '@/utils/messages'
import { createDiscreteApi } from 'naive-ui'
import eventBus from '@/utils/eventBus'

const { loadingBar } = createDiscreteApi(['loadingBar'])

// 请求防抖动管理器
class RequestDeduplicator {
	constructor() {
		this.pendingRequests = new Map() // 存储正在进行的请求
		this.requestCache = new Map()    // 存储请求结果缓存
		this.cacheTimeout = 100          // 缓存超时时间（毫秒）
	}

	/**
	 * 生成请求的唯一标识
	 * @param {Object} config - axios请求配置
	 * @returns {string} 请求的哈希值
	 */
	generateRequestKey(config) {
		const { method, url, params, data } = config
		const keyObject = {
			method: method?.toLowerCase(),
			url,
			params: params || {},
			data: data || {}
		}

		// 简单的哈希函数，将对象转换为字符串
		const keyString = JSON.stringify(keyObject, Object.keys(keyObject).sort())

		// 生成简单的哈希值
		let hash = 0
		for (let i = 0; i < keyString.length; i++) {
			const char = keyString.charCodeAt(i)
			hash = ((hash << 5) - hash) + char
			hash = hash & hash // 转换为32位整数
		}

		return `req_${Math.abs(hash)}`
	}

	/**
	 * 检查是否为可防抖的请求（主要针对GET请求）
	 * @param {Object} config - axios请求配置
	 * @returns {boolean}
	 */
	shouldDeduplicate(config) {
		// 只对GET请求进行防抖处理
		return config.method?.toLowerCase() === 'get'
	}

	/**
	 * 处理请求防抖
	 * @param {Object} config - axios请求配置
	 * @returns {Promise|Object} 返回Promise或修改后的config
	 */
	async handleRequest(config) {
		if (!this.shouldDeduplicate(config)) {
			return config
		}

		const requestKey = this.generateRequestKey(config)

		// 检查是否有相同的请求正在进行
		if (this.pendingRequests.has(requestKey)) {
			console.log(`[RequestDeduplicator] 发现重复请求，复用结果: ${config.url}`)
			// 返回正在进行的请求的Promise
			return this.pendingRequests.get(requestKey)
		}

		// 检查缓存中是否有最近的结果
		const cachedResult = this.requestCache.get(requestKey)
		if (cachedResult && Date.now() - cachedResult.timestamp < this.cacheTimeout) {
			console.log(`[RequestDeduplicator] 使用缓存结果: ${config.url}`)
			return Promise.resolve(cachedResult.data)
		}

		return config
	}

	/**
	 * 记录正在进行的请求
	 * @param {string} requestKey - 请求标识
	 * @param {Promise} requestPromise - 请求Promise
	 */
	recordPendingRequest(requestKey, requestPromise) {
		this.pendingRequests.set(requestKey, requestPromise)

		// 请求完成后清理
		requestPromise
			.finally(() => {
				this.pendingRequests.delete(requestKey)
			})
	}

	/**
	 * 缓存请求结果
	 * @param {string} requestKey - 请求标识
	 * @param {any} data - 响应数据
	 */
	cacheResult(requestKey, data) {
		this.requestCache.set(requestKey, {
			data,
			timestamp: Date.now()
		})

		// 设置缓存清理定时器
		setTimeout(() => {
			this.requestCache.delete(requestKey)
		}, this.cacheTimeout * 2) // 缓存时间的2倍后清理
	}

	/**
	 * 清理所有缓存
	 */
	clearCache() {
		this.pendingRequests.clear()
		this.requestCache.clear()
	}
}

// 创建全局请求防抖管理器实例
const requestDeduplicator = new RequestDeduplicator()

const instance = axios.create({
	baseURL: import.meta.env.VITE_API_BASE_URL,
	timeout: 10000,
})

// 请求拦截器
instance.interceptors.request.use(
	(config) => {
		// 正常的请求配置处理
		loadingBar.start() // 开始加载进度条
		const token = localStorage.getItem('access_token')
		if (token) {
			config.headers['Authorization'] = `${token}`
		}

		// 为可防抖的请求生成key
		if (requestDeduplicator.shouldDeduplicate(config)) {
			const requestKey = requestDeduplicator.generateRequestKey(config)
			config._requestKey = requestKey // 将key附加到config上，供响应拦截器使用
		}

		return config
	},
	(error) => {
		loadingBar.error() // 加载出错
		message.error('请求配置错误')
		return Promise.reject(error)
	}
)

// 响应拦截器
instance.interceptors.response.use(
	(response) => {
		loadingBar.finish() // 完成加载进度条

		if (response.data.code !== 200) {
			if (response.data.code === 401) {
				localStorage.removeItem('access_token')
				eventBus.emit('auth:logout')
			}
			message.error(response.data.message || '请求失败')
			return Promise.reject(new Error(response.data.message || '请求失败'))
		}

		return response.data
	},
	(error) => {
		loadingBar.error() // 加载出错
		if (axios.isCancel(error)) {
			message.info('请求已取消')
		} else if (error.response) {
			switch (error.response.status) {
				case 400:
					message.error(error.response.data.message || '输入有误，请检查输入信息')
					break
				case 401:
					message.error(error.response.data.message || '授权已过期，请重新登录')
					localStorage.removeItem('access_token')
					eventBus.emit('auth:logout') // 触发登出事件
					break
				case 403:
					message.error('拒绝访问')
					break
				case 404:
					message.error('请求的资源不存在')
					break
				case 500:
					message.error('服务器内部错误')
					break
				default:
					message.error(`连接错误 ${error.response.status}`)
			}
		} else if (error.request) {
			message.error('网络错误，请检查您的网络连接')
		} else {
			message.error('发生未知错误，请稍后重试')
		}
		return Promise.reject(error)
	}
)

// 带防抖功能的GET请求
export const doGet = async (url, params) => {
	const config = {
		method: 'get',
		url,
		params: params || {}
	}

	// 检查是否需要防抖
	if (requestDeduplicator.shouldDeduplicate(config)) {
		const requestKey = requestDeduplicator.generateRequestKey(config)

		// 检查是否有相同的请求正在进行
		if (requestDeduplicator.pendingRequests.has(requestKey)) {
			console.log(`[RequestDeduplicator] 发现重复请求，复用结果: ${url}`)
			return requestDeduplicator.pendingRequests.get(requestKey)
		}

		// 检查缓存中是否有最近的结果
		const cachedResult = requestDeduplicator.requestCache.get(requestKey)
		if (cachedResult && Date.now() - cachedResult.timestamp < requestDeduplicator.cacheTimeout) {
			console.log(`[RequestDeduplicator] 使用缓存结果: ${url}`)
			return Promise.resolve(cachedResult.data)
		}

		// 创建新请求并记录
		const requestPromise = instance.get(url, { params })
		requestDeduplicator.recordPendingRequest(requestKey, requestPromise)

		// 缓存结果
		requestPromise.then(data => {
			requestDeduplicator.cacheResult(requestKey, data)
		}).catch(() => {
			// 请求失败时不缓存
		})

		return requestPromise
	}

	// 非防抖请求直接执行
	return instance.get(url, { params })
}

export const doPost = (url, data) => instance.post(url, data)
export const doPut = (url, data) => instance.put(url, data)
export const doDelete = (url) => instance.delete(url)

// 导出防抖管理器，供调试使用
export const debugDeduplicator = {
	getStats: () => ({
		pendingCount: requestDeduplicator.pendingRequests.size,
		cacheCount: requestDeduplicator.requestCache.size,
		pendingRequests: Array.from(requestDeduplicator.pendingRequests.keys()),
		cachedRequests: Array.from(requestDeduplicator.requestCache.keys())
	}),
	clearCache: () => requestDeduplicator.clearCache(),
	setDebugMode: (enabled) => {
		requestDeduplicator.debugMode = enabled
	}
}

export default instance
