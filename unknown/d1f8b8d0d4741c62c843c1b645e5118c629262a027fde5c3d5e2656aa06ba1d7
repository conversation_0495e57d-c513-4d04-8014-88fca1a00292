<template>
  <div>
    <!-- 显示完整的 URL -->
    <p>完整的 URL: {{ fullUrl }}</p>
    <!-- 显示 URL 参数 -->
    <p>URL 参数:</p>
    <ul>
      <li v-for="(value, key) in queryParams" :key="key">
        {{ key }}: {{ value }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fullUrl: '',
      queryParams: {}
    };
  },
  created() {
    this.fullUrl = window.location.href; // 获取完整的 URL
    this.queryParams = this.$route.query; // 获取 URL 参数
  }
}
</script>
