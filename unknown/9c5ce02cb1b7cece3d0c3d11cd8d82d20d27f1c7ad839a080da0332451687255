<template>
  <div class="biz-org-page">
    <!-- 筛选条件区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <!-- 默认显示的前3组筛选条件 -->
        <div class="filter-row">
          <div class="filter-label">省/直辖市</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.province"
              @update:value="handleProvinceChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in provinceOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <!-- 市/区选项只在选择了省/直辖市之后才显示 -->
        <transition name="city-filter" mode="out-in">
          <div
            class="filter-row city-filter"
            v-if="filterForm.province"
            key="city-filter"
          >
            <div class="filter-label">市/区</div>
            <div class="filter-options">
              <n-radio-group
                v-model:value="filterForm.city"
                @update:value="handleSearch"
                class="custom-radio-group"
              >
                <n-radio-button
                  v-for="option in filteredCityOptions"
                  :key="option.value"
                  :value="option.value"
                  class="custom-radio-button"
                >
                  {{ option.label }}
                </n-radio-button>
              </n-radio-group>
            </div>
          </div>
        </transition>

        <div class="filter-row">
          <div class="filter-label">主营品牌</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.mainBrand"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in mainBrandOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <!-- 可展开的筛选条件 -->
        <n-collapse-transition :show="isFilterExpanded">
          <div class="expandable-filters">
            <div class="filter-row">
              <div class="filter-label">业务权限</div>
              <div class="filter-options">
                <n-radio-group
                  v-model:value="filterForm.businessPermission"
                  @update:value="handleSearch"
                  class="custom-radio-group"
                >
                  <n-radio-button
                    v-for="option in businessPermissionOptions"
                    :key="option.value"
                    :value="option.value"
                    class="custom-radio-button"
                  >
                    {{ option.label }}
                  </n-radio-button>
                </n-radio-group>
              </div>
            </div>

            <div class="filter-row">
              <div class="filter-label">机构类型</div>
              <div class="filter-options">
                <n-radio-group
                  v-model:value="filterForm.orgType"
                  @update:value="handleSearch"
                  class="custom-radio-group"
                >
                  <n-radio-button
                    v-for="option in orgTypeOptions"
                    :key="option.value"
                    :value="option.value"
                    class="custom-radio-button"
                  >
                    {{ option.label }}
                  </n-radio-button>
                </n-radio-group>
              </div>
            </div>
          </div>
        </n-collapse-transition>
      </div>

      <!-- 展开/收起按钮 -->
      <div class="filter-toggle">
        <n-button
          text
          type="primary"
          @click="toggleFilterExpansion"
          class="toggle-button"
          size="small"
        >
          <template #icon>
            <n-icon>
              <component
                :is="
                  isFilterExpanded
                    ? ChevronUpOutlineIcon
                    : ChevronDownOutlineIcon
                "
              />
            </n-icon>
          </template>
          {{ isFilterExpanded ? "收起筛选" : "展开更多" }}
        </n-button>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><component :is="RefreshOutlineIcon" /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><component :is="AddOutlineIcon" /></n-icon>
          </template>
          新增机构
        </n-button>

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="输入机构代码或名称，按回车键搜索"
          style="width: 300px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><component :is="SearchOutlineIcon" /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格容器 -->
    <div class="table-container">
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="filteredData"
        :loading="loading"
        :pagination="false"
        :row-key="(row) => row.id"
        :scroll-x="1600"
        class="data-table"
      />

      <!-- 分页工具栏 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          :show-size-picker="pagination.showSizePicker"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
          size="medium"
        />
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <biz-org-edit-modal
      ref="bizOrgEditModalRef"
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :title="dialogTitle"
      :province-options="provinceOptions.filter((item) => item.value !== null)"
      :city-options="cityOptions"
      :main-brand-options="
        mainBrandOptions.filter((item) => item.value !== null)
      "
      :business-permission-options="
        businessPermissionOptions.filter((item) => item.value !== null)
      "
      :org-type-options="orgTypeOptions.filter((item) => item.value !== null)"
      @save="handleSaveSuccess"
      @cancel="dialogVisible = false"
    />

    <!-- 成员管理弹窗 -->
    <biz-org-members-modal ref="membersModalRef" />
  </div>
</template>

<script setup>
import { useBizOrgPage } from "./BizOrgPage.js";

// 使用组合式函数获取所有状态和方法
/* eslint-enable @typescript-eslint/no-unused-vars */

const {
  // 组件引用
  tableRef,
  bizOrgEditModalRef,
  membersModalRef,

  // 图标组件
  SearchOutlineIcon,
  RefreshOutlineIcon,
  AddOutlineIcon,
  CreateOutlineIcon,
  ChevronUpOutlineIcon,
  ChevronDownOutlineIcon,
  PeopleOutlineIcon,

  // 状态变量
  loading,
  dialogVisible,
  dialogTitle,
  isEdit,
  isFilterExpanded,

  // 数据
  provinceOptions,
  cityOptions,
  mainBrandOptions,
  businessPermissionOptions,
  orgTypeOptions,
  filteredCityOptions,
  filterForm,
  bizOrgData,
  pagination,
  filteredData,
  columns,

  // 工具函数
  getDictLabel,

  // 方法
  loadDictOptions,
  refreshData,
  handleSearch,
  handleProvinceChange,
  showAddDialog,
  handleEdit,
  handleEmployeeManagement,
  handleSaveSuccess,
  handlePageChange,
  handlePageSizeChange,
  toggleFilterExpansion,

  // 组件
  BizOrgEditModal,
  BizOrgMembersModal,
} = useBizOrgPage();
</script>

<style scoped lang="scss">
@use "./BizOrgPage.scss";
</style>