<template>
    <div>
      <div id="map"></div>
    </div>
  </template>
  <script>
  import { Scene, PointLayer } from "@antv/l7";
  import { GaodeMap } from "@antv/l7-maps";
  export default {
    data() {
      return {};
    },
    mounted() {
      const scene = new Scene({
        id: "map",
        map: new GaodeMap({
          pitch: 0,
          style: "dark",
          center: [121.435159, 31.256971],
          zoom: 14.89,
          minZoom: 10,
        }),
      });
    },
  };
  </script>
  <style>
  ::-webkit-scrollbar {
    display: none;
  }
  
  html,
  body {
    overflow: hidden;
    margin: 0;
  }
  
  #map {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
  }
  </style>