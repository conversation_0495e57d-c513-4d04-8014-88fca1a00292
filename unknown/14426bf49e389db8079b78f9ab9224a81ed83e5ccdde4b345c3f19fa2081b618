<template>
  <div class="customer-selector-example">
    <n-card title="客户选择器示例">
      <n-space vertical>
        <n-button type="primary" @click="showCustomerSelector">
          选择客户
        </n-button>

        <n-divider />

        <div v-if="selectedCustomer" class="selected-customer-info">
          <h3>已选择的客户信息</h3>
          <n-descriptions bordered :column="2" label-placement="left">
            <n-descriptions-item label="客户ID">{{ selectedCustomer.id }}</n-descriptions-item>
            <n-descriptions-item label="客户名称">{{ selectedCustomer.customerName }}</n-descriptions-item>
            <n-descriptions-item label="客户类型">
              {{ selectedCustomer.customerType === 'individual' ? '个人客户' : '法人客户' }}
            </n-descriptions-item>
            <n-descriptions-item label="手机号码">{{ selectedCustomer.mobile }}</n-descriptions-item>
            <n-descriptions-item label="所属单位">{{ selectedCustomer.ownerOrgName }}</n-descriptions-item>
            <n-descriptions-item label="销售顾问">{{ selectedCustomer.ownerSellerName }}</n-descriptions-item>
          </n-descriptions>
        </div>
        <div v-else class="no-customer-selected">
          <n-empty description="尚未选择客户" />
        </div>
      </n-space>
    </n-card>

    <!-- 客户选择器组件 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      :initial-customer="selectedCustomer"
      @select="handleCustomerSelected"
      @cancel="handleCustomerSelectCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NCard, NButton, NSpace, NDivider, NDescriptions, NDescriptionsItem, NEmpty } from 'naive-ui'
import CustomerSelector from '@/components/customer/CustomerSelector.vue'
import messages from '@/utils/messages'

// 组件状态
const customerSelectorVisible = ref(false)
const selectedCustomer = ref(null)

// 显示客户选择器
const showCustomerSelector = () => {
  customerSelectorVisible.value = true
}

// 处理客户选择
const handleCustomerSelected = (customer) => {
  selectedCustomer.value = customer
  messages.success(`已选择客户: ${customer.customerName}`)
}

// 处理取消选择
const handleCustomerSelectCancel = () => {
  messages.info('已取消选择客户')
}
</script>

<style scoped>
.customer-selector-example {
  padding: 20px;
}

.selected-customer-info {
  margin-top: 16px;
}

.selected-customer-info h3 {
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.no-customer-selected {
  margin-top: 16px;
  padding: 24px;
  background-color: #f9f9f9;
  border-radius: 4px;
}
</style>
