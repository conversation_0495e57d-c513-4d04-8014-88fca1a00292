const fs = require('fs');
const path = require('path');
const COS = require('cos-nodejs-sdk-v5');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.production') });

// 从环境变量读取配置
const config = {
  SecretId: process.env.VITE_COS_SECRET_ID,
  SecretKey: process.env.VITE_COS_SECRET_KEY,
  Bucket: process.env.VITE_COS_BUCKET,
  Region: process.env.VITE_COS_REGION
};

const cos = new COS({
  SecretId: config.SecretId,
  SecretKey: config.SecretKey
});

// 检查存储桶权限和文件
async function checkCOSAccess() {
  console.log('🔍 检查COS访问权限和文件状态...');
  console.log(`🪣 存储桶: ${config.Bucket}`);
  console.log(`🌍 区域: ${config.Region}`);
  console.log('');

  try {
    // 检查存储桶是否存在
    console.log('1. 检查存储桶访问权限...');
    const headResult = await new Promise((resolve, reject) => {
      cos.headBucket({
        Bucket: config.Bucket,
        Region: config.Region
      }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    console.log('✅ 存储桶访问正常');

    // 检查index.html文件
    console.log('2. 检查index.html文件...');
    const indexResult = await new Promise((resolve, reject) => {
      cos.headObject({
        Bucket: config.Bucket,
        Region: config.Region,
        Key: 'index.html'
      }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    console.log('✅ index.html文件存在');
    console.log(`   文件大小: ${indexResult.headers['content-length']} bytes`);
    console.log(`   最后修改: ${indexResult.headers['last-modified']}`);

    // 检查存储桶ACL
    console.log('3. 检查存储桶ACL权限...');
    const aclResult = await new Promise((resolve, reject) => {
      cos.getBucketAcl({
        Bucket: config.Bucket,
        Region: config.Region
      }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    console.log('✅ ACL权限信息:');
    console.log(`   所有者: ${aclResult.Owner.DisplayName}`);
    console.log(`   权限: ${JSON.stringify(aclResult.AccessControlList, null, 2)}`);

    // 生成访问URL
    console.log('4. 生成访问URL...');
    const cosUrl = `https://${config.Bucket}.cos.${config.Region}.myqcloud.com/index.html`;
    const cdnUrl = `${process.env.VITE_CDN_BASE_URL}index.html`;
    
    console.log('');
    console.log('🔗 访问地址:');
    console.log(`   COS直接访问: ${cosUrl}`);
    console.log(`   CDN访问: ${cdnUrl}`);

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    
    if (error.statusCode === 403) {
      console.log('');
      console.log('💡 403错误解决方案:');
      console.log('1. 确保存储桶设置为"公有读私有写"');
      console.log('2. 配置存储桶策略允许匿名访问');
      console.log('3. 启用静态网站托管');
      console.log('4. 检查CDN配置');
    }
  }
}

// 执行检查
checkCOSAccess().catch(error => {
  console.error('❌ 程序执行失败:', error.message);
  process.exit(1);
});
