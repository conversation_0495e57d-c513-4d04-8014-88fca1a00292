<template>
  <div class="kpi-cards" :class="{ 'slide-in': show }">
    <div class="kpi-card" v-for="(item, index) in kpiData" :key="index">
      <div class="kpi-title">{{ item.title }}</div>
      <div class="kpi-value">{{ item.value }}</div>
      <div class="kpi-unit">{{ item.unit }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const show = ref(false);

// 模拟数据，实际项目中应该通过API获取
const kpiData = ref([
  {
    title: '总销量',
    value: '1,234,567',
    unit: '辆'
  },
  {
    title: '销售额',
    value: '1,901,234',
    unit: '万元'
  },
  {
    title: '库存周转率',
    value: '23.45',
    unit: '%'
  }
]);

onMounted(() => {
  setTimeout(() => {
    show.value = true;
  }, 300);
});
</script>

<style scoped>
.kpi-cards {
  position: absolute;
  top: 90px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  padding: 20px;
  z-index: 999;
}

.kpi-card {
  background: rgba(25, 112, 195, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(25, 112, 195, 0.3);
  border-radius: 12px;
  padding: 20px;
  width: 180px;
  color: #fff;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.kpi-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.9);
}

.kpi-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  background: linear-gradient(135deg, #29F1FA, #1970c3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.kpi-unit {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}
</style>