<template>
  <n-modal
    :show="visible"
    @update:show="handleVisibleChange"
    preset="card"
    title="车型详情"
    :style="isMaximized ? { width: '90%', height: '90%' } : { width: '600px' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div class="detail-content">
      <n-spin :show="loading">
        <n-descriptions
          v-if="detailData"
          bordered
          size="medium"
          :column="2"
          label-placement="left"
        >
          <n-descriptions-item label="SKU_ID">
            {{ detailData.skuId }}
          </n-descriptions-item>
          <n-descriptions-item label="品牌">
            {{ detailData.brand }}
          </n-descriptions-item>
          <n-descriptions-item label="车系">
            {{ detailData.series }}
          </n-descriptions-item>
          <n-descriptions-item label="车型代码">
            {{ detailData.modelCode }}
          </n-descriptions-item>
          <n-descriptions-item label="车型名称">
            {{ detailData.modelName }}
          </n-descriptions-item>
          <n-descriptions-item label="配置代码">
            {{ detailData.configCode }}
          </n-descriptions-item>
          <n-descriptions-item label="配置名称">
            {{ detailData.configName }}
          </n-descriptions-item>
          <n-descriptions-item label="启票价格(元)">
            {{ detailData.sbPrice }}
          </n-descriptions-item>
          <n-descriptions-item label="颜色代码">
            {{ detailData.colorCode }}
          </n-descriptions-item>
          <n-descriptions-item v-if="detailData.paymentRemark" label="付款备注" :span="2">
            {{ detailData.paymentRemark }}
          </n-descriptions-item>
        </n-descriptions>
        <div v-else-if="!loading" class="empty-data">
          <n-empty description="暂无数据" />
        </div>
      </n-spin>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, watch, defineComponent } from 'vue'
import { NModal, NButton, NIcon, NSpin, NDescriptions, NDescriptionsItem, NEmpty, NSpace } from 'naive-ui'
import { ContractOutline, ExpandOutline } from '@vicons/ionicons5'
import skuApi from '@/api/sku'
import messages from '@/utils/messages'

// 显式声明组件，消除IDE警告
defineComponent({
  components: {
    NModal, NButton, NIcon, NSpin, NDescriptions, NDescriptionsItem, NEmpty, NSpace,
    ContractOutline, ExpandOutline
  }
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible'])

// 状态变量
const loading = ref(false)
const detailData = ref(null)
const isMaximized = ref(true)

// 切换弹窗大小
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 处理弹窗显示状态变化
const handleVisibleChange = (value) => {
  emit('update:visible', value)
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 获取详情数据
const fetchDetail = async (id) => {
  if (!id) {
    detailData.value = null
    return
  }

  loading.value = true
  try {
    const response = await skuApi.getSkuDetail(id)
    if (response.code === 200) {
      detailData.value = response.data
    } else {
      messages.error(response.message || '获取详情失败')
      detailData.value = null
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    messages.error('获取详情失败，请稍后重试')
    detailData.value = null
  } finally {
    loading.value = false
  }
}

// 监听ID变化，获取详情
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      fetchDetail(newId)
    } else {
      detailData.value = null
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.id) {
      fetchDetail(props.id)
    }
  }
)
</script>

<style scoped>
.detail-content {
  min-height: 300px;
  padding: 8px 0;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

:deep(.n-descriptions-table-header) {
  background-color: #f9f9f9;
}

:deep(.n-descriptions-table-header th) {
  font-weight: 500;
}
</style>
