<template>
  <div class="chart-3d" ref="chartRef"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const chartRef = ref(null);
let chart = null;

// 定义3D柱状图的自定义图形
const CubeLeft = echarts.graphic.extendShape({
  shape: { x: 0, y: 0 },
  buildPath: function(ctx, shape) {
    const xAxisPoint = shape.xAxisPoint;
    const c0 = [shape.x, shape.y];
    const c1 = [shape.x - 13, shape.y - 13];
    const c2 = [xAxisPoint[0] - 13, xAxisPoint[1] - 13];
    const c3 = [xAxisPoint[0], xAxisPoint[1]];
    ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath();
  }
});

const CubeRight = echarts.graphic.extendShape({
  shape: { x: 0, y: 0 },
  buildPath: function(ctx, shape) {
    const xAxisPoint = shape.xAxisPoint;
    const c0 = [shape.x, shape.y];
    const c1 = [xAxisPoint[0], xAxisPoint[1]];
    const c2 = [xAxisPoint[0] + 18, xAxisPoint[1] - 9];
    const c3 = [shape.x + 18, shape.y - 9];
    ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath();
  }
});

const CubeTop = echarts.graphic.extendShape({
  shape: { x: 0, y: 0 },
  buildPath: function(ctx, shape) {
    const c0 = [shape.x, shape.y];
    const c1 = [shape.x + 18, shape.y - 9];
    const c2 = [shape.x + 5, shape.y - 22];
    const c3 = [shape.x - 13, shape.y - 13];
    ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath();
  }
});

// 注册自定义图形
echarts.graphic.registerShape('CubeLeft', CubeLeft);
echarts.graphic.registerShape('CubeRight', CubeRight);
echarts.graphic.registerShape('CubeTop', CubeTop);

// 图表数据
const VALUE = [210.9, 260.8, 204.2, 504.9, 740.5, 600.3, 119, 210.9, 260.8, 204.2, 504.9, 740.5, 600.3];

// 图表配置项
const option = {
  backgroundColor: 'rgba(17, 42, 62, 1)',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function(params) {
      const item = params[1];
      return item.name + '\n' + item.value;
    }
  },
  grid: {
    left: 20,
    right: 20,
    bottom: 20,
    top: 60,
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['2021/9', '2021/10', '2021/11', '2021/12', '2022/1', '2022/2', '2022/3', '2022/4', '2022/5', '2022/6', '2022/7', '2022/8', '2022/9'],
    axisLine: {
      show: false,
      lineStyle: {
        color: 'white'
      }
    },
    axisTick: {
      show: false,
      length: 9,
      alignWithLabel: true,
      lineStyle: {
        color: '#7DFFFD'
      }
    },
    axisLabel: {
      show: true,
      fontSize: 12,
      rotate: 45
    }
  },
  yAxis: [{
    min: 0,
    max: 1200,
    interval: 200,
    type: 'value',
    name: '%',
    axisLine: {
      show: false,
      lineStyle: {
        color: 'white'
      }
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255,255,255,0.1)'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true,
      fontSize: 12
    },
    boundaryGap: ['20%', '20%']
  }, {
    min: 0,
    max: 1200,
    interval: 200,
    name: '亿元',
    type: 'value',
    axisLine: {
      show: false,
      lineStyle: {
        color: 'white'
      }
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: 'rgba(255,255,255,0.1)'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true,
      fontSize: 12
    },
    boundaryGap: ['20%', '20%']
  }],
  series: [{
    type: 'custom',
    renderItem: (params, api) => {
      const location = api.coord([api.value(0), api.value(1)]);
      let color = api.value(1) > 10000 ? 'red' : new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        offset: 0,
        color: '#5cc4eb'
      }, {
        offset: 0.8,
        color: '#21658c'
      }]);

      return {
        type: 'group',
        children: [{
          type: 'CubeLeft',
          shape: {
            api: api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: color
          }
        }, {
          type: 'CubeRight',
          shape: {
            api: api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#048fd4'
            }, {
              offset: 0.8,
              color: '#195684'
            }])
          }
        }, {
          type: 'CubeTop',
          shape: {
            api: api,
            xValue: api.value(0),
            yValue: api.value(1),
            x: location[0],
            y: location[1],
            xAxisPoint: api.coord([api.value(0), 0])
          },
          style: {
            fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#65c7ec'
            }, {
              offset: 1,
              color: '#65c7ec'
            }])
          }
        }]
      };
    },
    data: VALUE
  }, {
    type: 'line',
    smooth: true,
    itemStyle: {
      normal: {
        color: '#FFCC64'
      }
    },
    data: VALUE
  }]
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    chart.setOption(option);

    window.addEventListener('resize', () => {
      chart && chart.resize();
    });
  }
};

// 组件卸载时清理图表实例
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
});

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.chart-3d {
  width: 33.33%;
  height: 300px;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 999;
}
</style>