# QueryPage 通用查询页面组件

## 概述

QueryPage 是一个高度可配置的通用查询页面组件，提供了标准的筛选、表格、分页功能，支持自定义字段类型和组件。

## 特性

- 🔍 **可配置筛选区域**：支持多种字段类型，可展开/收起
- 🛠️ **灵活工具栏**：支持自定义按钮和操作
- 📊 **高性能表格**：内部滚动、冻结表头、响应式布局
- 🔌 **插槽支持**：支持自定义筛选组件和弹窗
- 📱 **响应式设计**：适配不同屏幕尺寸
- 🎨 **统一样式**：遵循项目设计规范

## 基本用法

```vue
<template>
  <query-page
    :config="pageConfig"
    :fields="fieldConfig"
    :api-service="apiService"
    @add="handleAdd"
    @custom-action="handleCustomAction"
  >
    <!-- 自定义筛选组件 -->
    <template #filter-orgSelector="{ field, value, updateValue }">
      <biz-org-selector
        :value="value"
        @select="updateValue"
      />
    </template>
    
    <!-- 自定义弹窗 -->
    <template #modals="{ refresh }">
      <my-form-modal
        v-model:visible="formVisible"
        @success="refresh"
      />
    </template>
  </query-page>
</template>

<script setup>
import QueryPage from '@/components/QueryPage.vue'
import myApi from '@/api/myApi'

const pageConfig = {
  // 基础配置
  rowKey: 'id',
  scrollX: 1200,
  
  // 搜索配置
  search: true,
  searchPlaceholder: '请输入关键词',
  searchWidth: '300px',
  
  // 操作配置
  selection: true,
  actions: {
    add: true,
    addText: '新增记录'
  },
  
  // 自定义按钮
  buttons: [
    {
      key: 'export',
      text: '导出',
      type: 'info',
      icon: 'DownloadOutline'
    },
    {
      key: 'batchDelete',
      text: '批量删除',
      type: 'error',
      requireSelection: true
    }
  ]
}

const fieldConfig = [
  {
    name: 'name',
    label: '名称',
    type: 'string',
    filter: true,
    table: true,
    width: 150
  },
  {
    name: 'status',
    label: '状态',
    type: 'dict',
    filter: true,
    table: true,
    options: [
      { label: '全部', value: null },
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    name: 'dateRange',
    label: '日期范围',
    type: 'date',
    filter: true,
    table: false
  },
  {
    name: 'orgSelector',
    label: '机构选择',
    type: 'custom',
    filter: true,
    table: false
  }
]

const apiService = myApi
</script>
```

## 配置说明

### pageConfig 页面配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| rowKey | String | 'id' | 表格行的唯一标识字段 |
| scrollX | Number | 1200 | 表格横向滚动宽度 |
| search | Boolean | true | 是否显示搜索框 |
| searchPlaceholder | String | '请输入关键词' | 搜索框占位符 |
| searchWidth | String | '300px' | 搜索框宽度 |
| selection | Boolean | true | 是否显示选择列 |
| actions | Object | - | 操作配置 |
| buttons | Array | [] | 自定义按钮配置 |

### fieldConfig 字段配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | String | - | 字段名称（必填） |
| label | String | - | 字段显示标签 |
| type | String | 'string' | 字段类型 |
| filter | Boolean | false | 是否在筛选区域显示 |
| table | Boolean | true | 是否在表格中显示 |
| width | Number | - | 表格列宽度 |
| fixed | String | - | 表格列固定位置 ('left'/'right') |
| align | String | 'center' | 表格列对齐方式 |
| sortable | Boolean | false | 是否可排序 |
| options | Array | [] | 字典选项（type为dict时） |
| defaultValue | Any | null | 筛选字段默认值 |
| span | Number | 8 | 筛选字段栅格占位 |
| render | Function | - | 自定义表格列渲染函数 |

### 支持的字段类型

- **string**: 字符串输入框
- **number**: 数字输入框
- **dict**: 字典选择器
- **radio**: 单选按钮组
- **date**: 日期范围选择器
- **datetime**: 日期时间范围选择器
- **custom**: 自定义组件（需要提供插槽）

## API 服务要求

API 服务对象需要提供以下方法之一：

```javascript
// 方式1：通用方法
apiService.getList(params)

// 方式2：实体特定方法
apiService.get{Entity}List(params)
```

返回数据格式：
```javascript
{
  code: 200,
  data: {
    list: [], // 数据列表
    total: 0, // 总记录数
    pageNum: 1, // 当前页码
    pages: 1 // 总页数
  },
  message: 'SUCCESS'
}
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| add | - | 点击新增按钮 |
| edit | row | 点击编辑按钮 |
| delete | row | 点击删除按钮 |
| view | row | 点击查看按钮 |
| custom-action | button, selectedRows | 自定义按钮点击 |
| selection-change | selectedRows | 选择变化 |
| data-loaded | data | 数据加载完成 |

## 插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| filter-{fieldName} | field, value, updateValue | 自定义筛选组件 |
| toolbar-left | selectedRows, refresh | 左侧工具栏扩展 |
| toolbar-right | selectedRows, refresh | 右侧工具栏扩展 |
| modals | refresh | 弹窗区域 |

## 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| refreshData | - | Promise | 刷新数据 |
| getSelectedRows | - | Array | 获取选中行 |
| getFilterForm | - | Object | 获取筛选表单数据 |

## 样式定制

组件使用 SCSS 编写样式，支持 CSS 变量定制：

```scss
:root {
  --primary-color: #18a058; // 主色调
  --error-color: #d03050;   // 错误色
}
```

## 最佳实践

1. **字段配置复用**：将常用字段配置提取为常量
2. **API 统一**：确保 API 返回格式一致
3. **插槽使用**：合理使用插槽扩展功能
4. **响应式设计**：考虑不同屏幕尺寸的显示效果
5. **性能优化**：大数据量时考虑虚拟滚动
