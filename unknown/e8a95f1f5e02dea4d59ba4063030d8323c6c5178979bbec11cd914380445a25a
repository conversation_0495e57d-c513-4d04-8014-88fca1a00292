import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

// 模拟数据
const mockBizOrgData = [
  {
    id: 1,
    orgName: '长安汽车济南总店',
    province: 'shandong',
    city: 'jinan',
    address: '山东省济南市历下区经十路123号',
    contactPerson: '张经理',
    contactPhone: '13800138001',
    salesBrands: '引力,启源,凯程',
    bizPermissions: 'can_stock_in,can_sell,can_stock_out,can_settle',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2020-01-15 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    orgName: '深蓝德州分店',
    province: 'shandong',
    city: 'dezhou',
    address: '山东省德州市德城区东风中路456号',
    contactPerson: '李经理',
    contactPhone: '13800138002',
    salesBrands: '深蓝',
    bizPermissions: 'can_sell,can_settle',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2021-03-20 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 3,
    orgName: '长安集团河北总部',
    province: 'hebei',
    city: 'shijiazhuang',
    address: '河北省石家庄市长安区中山路789号',
    contactPerson: '王总',
    contactPhone: '13800138003',
    salesBrands: '引力',
    bizPermissions: 'can_stock_in,can_sell,can_stock_out,can_settle',
    orgType: 'group',
    status: 'active',
    createdTime: '2019-06-10 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 4,
    orgName: '阿维塔郑州二网',
    province: 'henan',
    city: 'zhengzhou',
    address: '河南省郑州市金水区花园路321号',
    contactPerson: '赵经理',
    contactPhone: '13800138004',
    salesBrands: '阿维塔',
    bizPermissions: 'can_sell',
    orgType: 'secondary_network',
    status: 'active',
    createdTime: '2022-08-15 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 5,
    orgName: '凯程重庆渝中店',
    province: 'chongqing',
    city: 'yuzhong',
    address: '重庆市渝中区解放碑步行街100号',
    contactPerson: '陈经理',
    contactPhone: '13800138005',
    salesBrands: '凯程',
    bizPermissions: 'can_stock_in,can_sell,can_stock_out',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2021-11-30 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 6,
    orgName: '启源淄博专营店',
    province: 'shandong',
    city: 'zibo',
    address: '山东省淄博市张店区中心路888号',
    contactPerson: '刘经理',
    contactPhone: '13800138006',
    salesBrands: '启源',
    bizPermissions: 'can_sell,can_settle',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2022-05-15 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 7,
    orgName: '深蓝临沂分店',
    province: 'shandong',
    city: 'linyi',
    address: '山东省临沂市兰山区沂蒙路999号',
    contactPerson: '张总',
    contactPhone: '13800138007',
    salesBrands: '深蓝',
    bizPermissions: 'can_stock_in,can_sell,can_stock_out,can_settle',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2023-01-10 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 8,
    orgName: '引力沧州二网',
    province: 'hebei',
    city: 'cangzhou',
    address: '河北省沧州市运河区解放西路666号',
    contactPerson: '马经理',
    contactPhone: '13800138008',
    salesBrands: '引力',
    bizPermissions: 'can_sell',
    orgType: 'secondary_network',
    status: 'active',
    createdTime: '2022-09-20 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 9,
    orgName: '阿维塔洛阳专营店',
    province: 'henan',
    city: 'luoyang',
    address: '河南省洛阳市西工区中州中路777号',
    contactPerson: '周经理',
    contactPhone: '13800138009',
    salesBrands: '阿维塔',
    bizPermissions: 'can_stock_in,can_sell,can_settle',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2023-03-25 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 10,
    orgName: '凯程聊城分店',
    province: 'shandong',
    city: 'liaocheng',
    address: '山东省聊城市东昌府区东昌西路555号',
    contactPerson: '孙经理',
    contactPhone: '13800138010',
    salesBrands: '凯程',
    bizPermissions: 'can_sell,can_stock_out',
    orgType: 'single_store',
    status: 'active',
    createdTime: '2023-06-12 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  }
]

// 判断是否为开发环境
const isDev = false

/**
 * 获取业务组织架构列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回组织架构列表的Promise
 */
export function getBizOrgList(params = {}) {
  if (isDev) {
    // 开发环境使用模拟数据
    let filteredData = [...mockBizOrgData]

    // 应用筛选条件
    if (params.province) {
      filteredData = filteredData.filter(item => item.province === params.province)
    }

    if (params.city) {
      filteredData = filteredData.filter(item => item.city === params.city)
    }

    if (params.mainBrand) {
      filteredData = filteredData.filter(item =>
        item.salesBrands && item.salesBrands.includes(params.mainBrand)
      )
    }

    if (params.businessPermission) {
      filteredData = filteredData.filter(item =>
        item.bizPermissions && item.bizPermissions.includes(params.businessPermission)
      )
    }

    if (params.orgType) {
      filteredData = filteredData.filter(item => item.orgType === params.orgType)
    }

    if (params.keywords) {
      const keyword = params.keywords.toLowerCase()
      filteredData = filteredData.filter(item =>
        item.orgName.toLowerCase().includes(keyword) ||
        item.contactPerson.toLowerCase().includes(keyword) ||
        item.address.toLowerCase().includes(keyword)
      )
    }

    // 分页处理
    const page = params.page || 1
    const size = params.size || 10
    const start = (page - 1) * size
    const end = start + size
    const list = filteredData.slice(start, end)

    return Promise.resolve({
      code: 200,
      data: {
        list,
        total: filteredData.length,
        pages: Math.ceil(filteredData.length / size),
        page,
        size
      },
      message: 'success'
    })
  }

  return doGet('/biz/org', params)
}

/**
 * 获取业务组织架构详情
 * @param {Number} id - 组织架构ID
 * @returns {Promise} 返回组织架构详情的Promise
 */
export function getBizOrgDetail(id) {
  if (isDev) {
    const org = mockBizOrgData.find(item => item.id === id)
    return Promise.resolve({
      code: 200,
      data: org || null,
      message: org ? 'success' : 'not found'
    })
  }

  return doGet(`/biz/org/${id}`)
}

/**
 * 创建业务组织架构
 * @param {Object} orgData - 组织架构数据
 * @returns {Promise} 返回创建结果的Promise
 */
export function createBizOrg(orgData) {
  if (isDev) {
    const newOrg = {
      ...orgData,
      id: Math.max(...mockBizOrgData.map(item => item.id)) + 1,
      createdTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      updatedTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }

    mockBizOrgData.push(newOrg)

    return Promise.resolve({
      code: 200,
      data: newOrg,
      message: 'success'
    })
  }

  return doPost('/biz/org', orgData)
}

/**
 * 更新业务组织架构
 * @param {Object} orgData - 组织架构数据
 * @returns {Promise} 返回更新结果的Promise
 */
export function updateBizOrg(orgData) {
  if (isDev) {
    const index = mockBizOrgData.findIndex(item => item.id === orgData.id)
    if (index === -1) {
      return Promise.reject({
        code: 404,
        message: '组织架构不存在'
      })
    }

    mockBizOrgData[index] = {
      ...mockBizOrgData[index],
      ...orgData,
      updatedTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }

    return Promise.resolve({
      code: 200,
      data: mockBizOrgData[index],
      message: 'success'
    })
  }

  return doPut(`/biz/org/${orgData.id}`, orgData)
}

/**
 * 删除业务组织架构
 * @param {Number} id - 组织架构ID
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteBizOrg(id) {
  if (isDev) {
    const index = mockBizOrgData.findIndex(item => item.id === id)
    if (index === -1) {
      return Promise.reject({
        code: 404,
        message: '组织架构不存在'
      })
    }

    mockBizOrgData.splice(index, 1)

    return Promise.resolve({
      code: 200,
      message: 'success'
    })
  }

  return doDelete(`/biz/org/${id}`)
}

export default {
  getBizOrgList,
  getBizOrgDetail,
  createBizOrg,
  updateBizOrg,
  deleteBizOrg
}
