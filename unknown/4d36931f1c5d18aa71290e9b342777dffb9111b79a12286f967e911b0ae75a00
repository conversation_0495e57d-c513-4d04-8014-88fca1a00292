<template>
  <div>
    <component :is="currentComponent" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, shallowRef, markRaw } from 'vue'
import { useRoute } from 'vue-router'
import { addDynamicRoutes } from '@/router'
import { findMenuByPath, loadDynamicRoutes } from '@/router/menuUtils'
import NotFoundPage from '@/views/system/NotFoundPage.vue'
import Loading from '@/components/Loading.vue'

const route = useRoute()
const currentComponent = shallowRef(markRaw(Loading))
const isLoading = ref(true)

// 当前路径
const currentPath = computed(() => {
  return route.path
})

// 加载组件
const loadComponent = async () => {
  try {
    isLoading.value = true

    // 从localStorage获取菜单数据
    const menusData = localStorage.getItem('menus')
    if (!menusData) {
      console.warn('未找到菜单数据，尝试重新获取')
      // 尝试从服务器获取菜单数据
      const success = await loadDynamicRoutes()
      if (!success) {
        console.error('无法获取菜单数据')
        currentComponent.value = markRaw(NotFoundPage)
        return
      }
    } else {
      // 确保动态路由已添加
      try {
        const parsedMenus = JSON.parse(menusData)
        addDynamicRoutes(parsedMenus)
      } catch (error) {
        console.error('解析存储的菜单数据失败:', error)
      }
    }

    const menus = JSON.parse(localStorage.getItem('menus'))

    // 查找匹配的菜单项
    const matchedMenu = findMenuByPath(menus, currentPath.value)

    if (matchedMenu && matchedMenu.viewPath) {
      console.log(`找到匹配的菜单项: ${matchedMenu.menuLabel}, 视图路径: ${matchedMenu.viewPath}`)

      // 尝试加载组件
      try {
        // 使用import.meta.glob预加载所有可能的视图组件
        // 同时使用多种路径格式来确保能找到组件
        const viewModules = {
          ...import.meta.glob('@/views/**/*.vue'),
          ...import.meta.glob('/src/views/**/*.vue'),
          ...import.meta.glob('../views/**/*.vue')
        }

        // 构建可能的组件路径
        const possiblePaths = [
          `@/views/${matchedMenu.viewPath}.vue`,
          `/src/views/${matchedMenu.viewPath}.vue`,
          `../views/${matchedMenu.viewPath}.vue`
        ];

        // 检查组件是否存在于预加载的映射中
        const componentPath = possiblePaths.find(path => viewModules[path]);

        if (componentPath) {
          const module = await viewModules[componentPath]()
          currentComponent.value = markRaw(module.default)
          console.log(`成功加载组件: ${componentPath}`)
        } else {
          // 如果在预加载映射中找不到，尝试直接动态导入
          try {
            console.log(`尝试直接动态导入: ${matchedMenu.viewPath}`)
            const module = await import(`@/views/${matchedMenu.viewPath}.vue`)
            currentComponent.value = markRaw(module.default)
          } catch (importError) {
            console.error(`加载组件 ${matchedMenu.viewPath} 失败:`, importError)
            currentComponent.value = markRaw(NotFoundPage)
          }
        }
      } catch (error) {
        console.error(`加载组件 ${matchedMenu.viewPath} 失败:`, error)
        currentComponent.value = markRaw(NotFoundPage)
      }
    } else {
      console.warn(`未找到匹配路径 ${currentPath.value} 的菜单项`)
      currentComponent.value = markRaw(NotFoundPage)
    }
  } catch (error) {
    console.error('动态路由解析错误:', error)
    currentComponent.value = markRaw(NotFoundPage)
  } finally {
    isLoading.value = false
  }
}

// 监听路由变化
watch(currentPath, () => {
  loadComponent()
})

// 组件挂载时加载
onMounted(() => {
  loadComponent()
})
</script>
