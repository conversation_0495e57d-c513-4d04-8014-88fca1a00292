import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 收款单管理 API 服务 - RESTful风格
 */
export const receptApi = {
  /**
   * 获取收款单列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.receptNo] - 收款单号
   * @param {String} [params.receptUnit] - 收款单位
   * @param {String} [params.receptAccount] - 收款账户
   * @param {String} [params.operator] - 经办人
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回收款单列表的 Promise
   */
  getReceptList(params) {
    return doGet('/financial/recept/bill', params)
  },

  /**
   * 获取收款单详情
   * @param {Number} id - 收款单ID
   * @returns {Promise} 返回收款单详情的 Promise
   */
  getReceptDetail(id) {
    return doGet(`/financial/recept/bill/${id}`)
  },

  /**
   * 新增收款单
   * @param {Object} data - 收款单数据
   * @param {String} [data.receptUnit] - 收款单位
   * @param {String} [data.receptAccount] - 收款账户
   * @param {Number} [data.receptAmount] - 收款金额（分）
   * @returns {Promise} 返回新增结果的 Promise
   */
  createRecept(data) {
    return doPost('/financial/recept/bill', data)
  },

  /**
   * 更新收款单
   * @param {Object} data - 收款单数据
   * @param {Number} data.id - 收款单ID
   * @param {String} [data.receptUnit] - 收款单位
   * @param {String} [data.receptAccount] - 收款账户
   * @param {Number} [data.receptAmount] - 收款金额（分）
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateRecept(data) {
    return doPut(`/financial/recept/bill`, data)
  },

  /**
   * 删除收款单
   * @param {Number} id - 收款单ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteRecept(id) {
    return doDelete(`/financial/recept/bill/${id}`)
  },

  /**
   * 批量删除收款单
   * @param {Array} ids - 收款单ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteRecepts(ids) {
    return doPost('/financial/recept/bill/batch', { ids })
  },

  /**
   * 导出收款单数据
   * @param {Object} params - 查询参数，同getReceptList
   * @returns {Promise} 返回导出结果的 Promise
   */
  exportRecepts(params) {
    return doGet('/financial/recept/bill/export', params, { responseType: 'blob' })
  },

  /**
   * 导入收款单数据
   * @param {FormData} formData - 包含文件的表单数据
   * @returns {Promise} 返回导入结果的 Promise
   */
  importRecepts(formData) {
    return doPost('/financial/recept/bill/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default receptApi
