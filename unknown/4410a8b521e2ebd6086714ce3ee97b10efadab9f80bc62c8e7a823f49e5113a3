/* NaiveUI 样式覆盖 */
@use '@/styles/variables.scss' as *;

/* 覆盖n-layout-scroll-container的样式 */
.n-layout-scroll-container {
  overflow-y: hidden !important;
}

/* 分割线样式覆盖 - 使用更高优先级选择器 */
.order-form .section-container .section-divider.n-divider,
.order-form .section-divider.n-divider,
.section-container .section-divider.n-divider,
.section-divider.n-divider {
  height: 2px !important;
  background-image: linear-gradient(to right, $primary-color 0%, rgba($primary-color, 0.1) 100%) !important;
  border: none !important;
  opacity: 1 !important;
  margin-top: 4px !important;
  margin-bottom: 16px !important;
}

/* 表单标签样式 */
.n-form-item-label {
  font-size: $font-size-base !important;
  font-weight: $font-weight-medium !important;
  color: $text-color-primary !important;
}

/* 确保标题与表单之间的间距合适 */
.section-title+.section-divider {
  margin-top: 4px !important;
}

/* 确保分割线下方与表单之间的间距合适 */
.section-divider+.n-grid,
.section-divider+.option-row {
  margin-top: 12px !important;
}