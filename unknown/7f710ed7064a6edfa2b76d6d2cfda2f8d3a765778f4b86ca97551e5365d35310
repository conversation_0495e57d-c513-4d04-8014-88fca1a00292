/* 最大化弹窗样式 */
:deep(.n-modal) {
  margin: 0 !important;
  border-radius: 0 !important;
}

:deep(.n-card) {
  height: 100vh !important;
  border-radius: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

:deep(.n-card__content) {
  flex: 1 !important;
  overflow: hidden !important;
  padding: 16px !important;
}

.members-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.toolbar {
  flex-shrink: 0;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

:deep(.toolbar .member-selector .selected-members) {
  min-height: 20px !important;
  height: 20px !important;
  padding: 4px 8px !important;
  font-size: 14px !important;
}

:deep(.toolbar .member-selector .selected-members .selector-text) {
  font-size: 14px !important;
}

:deep(.toolbar .member-selector .selected-members .n-icon) {
  font-size: 14px !important;
}

:deep(.toolbar .member-selector .selected-tags) {
  min-height: 24px !important;
  padding: 0 !important;
}

:deep(.toolbar .member-selector .member-tag) {
  font-size: 12px !important;
  height: 24px !important;
}

:deep(.toolbar .member-selector .n-button) {
  height: 32px;
  font-size: 14px;
}

/* 搜索框样式优化 */
:deep(.toolbar .n-input) {
  height: 32px;
}

:deep(.toolbar .n-input .n-input__input-el) {
  font-size: 14px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  min-height: 0;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fff;
}

/* 表格内部滚动条样式 */
:deep(.data-table .n-data-table-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.data-table .n-data-table-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.data-table .n-data-table-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.data-table .n-data-table-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
