import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 应收账款 API 服务
 */
export const accountsReceivableApi = {
  /**
   * 获取应收账款列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.orderNo] - 订单号
   * @param {String} [params.customerName] - 客户名称
   * @param {Number} [params.salesOrgId] - 销售单位ID
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回应收账款列表的 Promise
   */
  getReceivableList(params) {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: 'success',
          data: {
            total: mockData.length,
            list: mockData.slice((params.page - 1) * params.size, params.page * params.size)
          }
        });
      }, 300);
    });
    // 实际API调用
    // return doGet('/financial/receivable', params)
  },

  /**
   * 获取应收账款详情
   * @param {Number} id - 应收账款ID
   * @returns {Promise} 返回应收账款详情的 Promise
   */
  getReceivableDetail(id) {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const detail = mockData.find(item => item.id === id);
        resolve({
          code: 200,
          message: 'success',
          data: detail
        });
      }, 300);
    });
    // 实际API调用
    // return doGet(`/financial/receivable/${id}`)
  },

  /**
   * 获取应收账款明细
   * @param {Number} orderId - 订单ID
   * @returns {Promise} 返回应收账款明细的 Promise
   */
  getReceivableItems(orderId) {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const items = mockItems.filter(item => item.orderId === orderId);
        resolve({
          code: 200,
          message: 'success',
          data: items
        });
      }, 300);
    });
    // 实际API调用
    // return doGet(`/financial/receivable/items/${orderId}`)
  }
}

// 模拟数据 - 应收账款订单
const mockData = [
  {
    id: 1,
    orderNo: 'SO20230001',
    orderDate: '2023-01-15',
    customerName: '张三',
    customerPhone: '13800138001',
    salesOrgName: '北京销售中心',
    totalAmount: 350000,
    paidAmount: 150000,
    unpaidAmount: 200000,
    status: 'PARTIAL_PAID', // 部分支付
    paymentDueDate: '2023-02-15',
    overdueDays: 0,
    remark: '首付款已收'
  },
  {
    id: 2,
    orderNo: 'SO20230002',
    orderDate: '2023-01-20',
    customerName: '李四',
    customerPhone: '13900139001',
    salesOrgName: '上海销售中心',
    totalAmount: 420000,
    paidAmount: 0,
    unpaidAmount: 420000,
    status: 'UNPAID', // 未支付
    paymentDueDate: '2023-02-20',
    overdueDays: 0,
    remark: '客户申请延期付款'
  },
  {
    id: 3,
    orderNo: 'SO20230003',
    orderDate: '2023-01-25',
    customerName: '王五',
    customerPhone: '13700137001',
    salesOrgName: '广州销售中心',
    totalAmount: 380000,
    paidAmount: 380000,
    unpaidAmount: 0,
    status: 'PAID', // 已支付
    paymentDueDate: '2023-02-25',
    overdueDays: 0,
    remark: '全款已收'
  },
  {
    id: 4,
    orderNo: 'SO20230004',
    orderDate: '2023-01-30',
    customerName: '赵六',
    customerPhone: '13600136001',
    salesOrgName: '深圳销售中心',
    totalAmount: 520000,
    paidAmount: 200000,
    unpaidAmount: 320000,
    status: 'PARTIAL_PAID', // 部分支付
    paymentDueDate: '2023-02-28',
    overdueDays: 0,
    remark: '首付款已收，剩余款项分期付款'
  },
  {
    id: 5,
    orderNo: 'SO20230005',
    orderDate: '2023-02-05',
    customerName: '钱七',
    customerPhone: '13500135001',
    salesOrgName: '成都销售中心',
    totalAmount: 450000,
    paidAmount: 0,
    unpaidAmount: 450000,
    status: 'UNPAID', // 未支付
    paymentDueDate: '2023-03-05',
    overdueDays: 0,
    remark: '客户申请贷款中'
  }
];

// 模拟数据 - 应收账款明细
const mockItems = [
  {
    id: 101,
    orderId: 1,
    itemType: 'DEPOSIT', // 订金
    amount: 50000,
    dueDate: '2023-01-20',
    paidDate: '2023-01-18',
    paidAmount: 50000,
    status: 'PAID',
    remark: '订金已收'
  },
  {
    id: 102,
    orderId: 1,
    itemType: 'DOWN_PAYMENT', // 首付款
    amount: 100000,
    dueDate: '2023-01-30',
    paidDate: '2023-01-28',
    paidAmount: 100000,
    status: 'PAID',
    remark: '首付款已收'
  },
  {
    id: 103,
    orderId: 1,
    itemType: 'LOAN_PAYMENT', // 贷款
    amount: 200000,
    dueDate: '2023-02-15',
    paidDate: null,
    paidAmount: 0,
    status: 'UNPAID',
    remark: '银行贷款审批中'
  },
  {
    id: 201,
    orderId: 2,
    itemType: 'FULL_PAYMENT', // 全款
    amount: 420000,
    dueDate: '2023-02-20',
    paidDate: null,
    paidAmount: 0,
    status: 'UNPAID',
    remark: '客户申请延期付款'
  },
  {
    id: 301,
    orderId: 3,
    itemType: 'FULL_PAYMENT', // 全款
    amount: 380000,
    dueDate: '2023-02-25',
    paidDate: '2023-01-25',
    paidAmount: 380000,
    status: 'PAID',
    remark: '全款已收'
  },
  {
    id: 401,
    orderId: 4,
    itemType: 'DOWN_PAYMENT', // 首付款
    amount: 200000,
    dueDate: '2023-02-05',
    paidDate: '2023-02-01',
    paidAmount: 200000,
    status: 'PAID',
    remark: '首付款已收'
  },
  {
    id: 402,
    orderId: 4,
    itemType: 'LOAN_PAYMENT', // 贷款
    amount: 320000,
    dueDate: '2023-02-28',
    paidDate: null,
    paidAmount: 0,
    status: 'UNPAID',
    remark: '银行贷款审批中'
  },
  {
    id: 501,
    orderId: 5,
    itemType: 'FULL_PAYMENT', // 全款
    amount: 450000,
    dueDate: '2023-03-05',
    paidDate: null,
    paidAmount: 0,
    status: 'UNPAID',
    remark: '客户申请贷款中'
  }
];

export default accountsReceivableApi;
