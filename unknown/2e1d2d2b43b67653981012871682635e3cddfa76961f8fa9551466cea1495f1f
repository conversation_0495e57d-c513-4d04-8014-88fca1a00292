<template>
  <div class="preview-wrapper">
    <div class="preview-mask" @click="$emit('close')" />

    <div class="form-preview">
      <div class="preview-header">
        <div class="preview-tabs">
          <div v-for="tab in tabs" :key="tab.value" :class="['preview-tab', { active: currentDevice === tab.value }]"
            @click="currentDevice = tab.value">
            <n-icon size="20">
              <component :is="tab.icon" />
            </n-icon>
          </div>
        </div>
        <n-button quaternary circle @click="$emit('close')">
          <template #icon>
            <n-icon>
              <Close />
            </n-icon>
          </template>
        </n-button>
      </div>

      <div class="preview-content" :class="currentDevice">
        <div class="preview-container">
          <div class="mobile-container" :class="{ 'is-mobile': currentDevice === 'mobile' }">
            <n-form ref="formRef" :model="formData">
              <n-grid :cols="currentDevice === 'mobile' ? 1 : 24" :x-gap="12" :y-gap="12">
                <template v-for="field in formFields" :key="field.id">
                  <n-grid-item v-if="field.type === 'divider'" :span="24">
                    <n-divider />
                  </n-grid-item>

                  <n-grid-item v-else :span="getFieldSpan(field.props.width)">
                    <n-form-item :label="field.label" :required="field.props.required">
                      <template #label>
                        {{ field.label }}
                        <span v-if="field.description" class="field-description">
                          {{ field.description }}
                        </span>
                      </template>

                      <template v-if="field.type === 'departmentSelect' || field.type === 'departmentMultiSelect'">
                        <div class="mobile-field-wrapper">
                          <department-selector
                            v-model="formData[field.id]"
                            :mode="field.type === 'departmentSelect' ? 'single' : 'multiple'"
                            :label="field.label || field.props.placeholder || '请选择部门'"
                            :disabled="field.props.permission === 'visible'"
                            :width="currentDevice === 'mobile' ? '100%' : field.props.width"
                            :render-mode="currentDevice"
                          />
                        </div>
                      </template>

                      <template v-else>
                        <component 
                          :is="getComponentByType(field.type)" 
                          v-model="formData[field.id]"
                          v-bind="getFieldProps(field)"
                        >
                          <template #default>
                            <!-- 单选按钮组 -->
                            <template v-if="field.type === 'radio'">
                              <n-radio-group v-model:value="formData[field.id]" :layout="field.props.layout">
                                <n-radio-button v-for="option in field.props.options" :key="option.value"
                                  :value="option.value">
                                  {{ option.label }}
                                </n-radio-button>
                              </n-radio-group>
                            </template>

                            <!-- 复选框组 -->
                            <template v-else-if="field.type === 'checkbox'">
                              <n-checkbox-group v-model:value="formData[field.id]" :layout="field.props.layout">
                                <n-checkbox v-for="option in field.props.options" :key="option.value"
                                  :value="option.value" :label="option.label" />
                              </n-checkbox-group>
                            </template>

                            <!-- 其他组件类型 -->
                            <component v-else :is="getComponentByType(field.type)" v-model:value="formData[field.id]" v-bind="getFieldProps(field)">
                              <!-- 保留现有的 slot 内容 -->
                              <template v-if="field.type === 'number'" #prefix>
                                {{ getNumberPrefix(field.props.format) }}
                              </template>
                              <template v-if="field.type === 'number'" #suffix>
                                {{ getNumberSuffix(field.props.format) }}
                              </template>
                            </component>
                          </template>
                        </component>
                      </template>
                    </n-form-item>
                  </n-grid-item>
                </template>
              </n-grid>
            </n-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import { DeviceDesktop } from '@vicons/tabler'
import {
  NForm,
  NFormItem,
  NIcon,
  NButton,
  NInput,
  NInputNumber,
  NDatePicker,
  NRadioGroup,
  NCheckboxGroup,
  NSelect,
  NGrid,
  NGridItem,
  NDivider
} from 'naive-ui'
import { PhonePortraitOutline as Phone, Close } from '@vicons/ionicons5'
import MemberSelector from '@/components/users/MemberSelector.vue'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'

const props = defineProps({
  formFields: {
    type: Array,
    required: true
  }
})

defineEmits(['close'])

const currentDevice = ref('desktop')
const formData = ref({})

const tabs = [
  { label: 'PC端', value: 'desktop', icon: DeviceDesktop },
  { label: '移动端', value: 'mobile', icon: Phone }
]

const getComponentByType = (type) => {
  const componentMap = {
    memberSelectorSingle: MemberSelector,
    memberSelectorMultiple: MemberSelector,
    input: NInput,
    textarea: NInput,
    number: NInputNumber,
    datetime: NDatePicker,
    radio: NRadioGroup,
    checkbox: NCheckboxGroup,
    select: NSelect,
    multiSelect: NSelect,
    departmentSelect: DepartmentSelector,
    departmentMultiSelect: DepartmentSelector,
    // ... 其他组件映射
  }
  return componentMap[type] || 'div'
}

const getFieldProps = (field) => {
  const baseProps = { ...field.props }

  // 根据字段类型处理特定属性
  switch (field.type) {
    case 'input':
    case 'textarea':
      return {
        ...baseProps,
        type: field.type === 'textarea' ? 'textarea' : 'text',
        maxlength: baseProps.maxLength,
        placeholder: baseProps.placeholder,
      }

    case 'number':
      const numberProps = {
        buttonPlacement: 'both',
        placeholder: baseProps.placeholder,
        showButton: true,
        min: baseProps.min ?? -Infinity,
        max: baseProps.max ?? Infinity,
        step: baseProps.step || 1,
        precision: baseProps.precision || 0,
      }

      // 添加千分位分隔符
      if (baseProps.thousandSeparator) {
        numberProps.thousandsSeparator = ','
      }

      // 处理格式化需求
      if (baseProps.format === 'currency') {
        numberProps.formatter = (value) => `¥${value}`
        numberProps.parser = (value) => value.replace(/[^-\d.]/g, '')
      } else if (baseProps.format === 'percent') {
        numberProps.formatter = (value) => `${value}%`
        numberProps.parser = (value) => value.replace(/[^-\d.]/g, '')
      }

      // 设置默认值
      if (baseProps.defaultValue != null && formData.value[field.id] === null) {
        formData.value[field.id] = Number(baseProps.defaultValue)
      }

      return numberProps

    case 'datetime':
      return {
        ...baseProps,
        type: baseProps.type || 'date',
        clearable: true,
        placeholder: baseProps.placeholder,
        format: baseProps.type === 'datetime' ? 'yyyy-MM-dd HH:mm'
          : baseProps.type === 'daterange' ? 'yyyy-MM-dd'
            : baseProps.type === 'datetimerange' ? 'yyyy-MM-dd HH:mm'
              : 'yyyy-MM-dd'
      }

    case 'radio':
    case 'checkbox':
      return {
        ...baseProps,
        options: baseProps.options,
        layout: baseProps.layout,
      }

    case 'select':
    case 'multiSelect':
      return {
        ...baseProps,
        options: baseProps.options,
        multiple: field.type === 'multiSelect',
        placeholder: baseProps.placeholder,
      }

    case 'memberSelectorSingle':
    case 'memberSelectorMultiple':
      return {
        mode: field.type === 'memberSelectorSingle' ? 'single' : 'multiple',
        width: currentDevice === 'mobile' ? '100%' : field.props.width,
        label: field.label || field.props.placeholder || '请选择成员',
        disabled: field.props.permission === 'visible',
        ...field.props
      }

    default:
      return baseProps
  }
}

const initFormData = () => {
  console.log('Initializing form data with fields:', props.formFields)
  const data = {}
  props.formFields.forEach(field => {
    switch (field.type) {
      case 'memberSelectorSingle':
        data[field.id] = field.props.defaultValue ? {
          id: field.props.defaultValue.id,
          name: field.props.defaultValue.name
        } : null
        break
      case 'memberSelectorMultiple':
        data[field.id] = field.props.defaultValue ? 
          field.props.defaultValue.map(item => ({
            id: item.id,
            name: item.name
          })) : []
        break
      case 'departmentSelect':
        data[field.id] = null  // 单选模式初始值为 null
        break
      case 'departmentMultiSelect':
        data[field.id] = []    // 多选模式初始值为空数组
        break
      case 'checkbox':
        data[field.id] = []
        break
      case 'radio':
        data[field.id] = field.props.options?.[0]?.value || null
        break
      case 'number':
        data[field.id] = field.props.defaultValue != null ? 
          Number(field.props.defaultValue) : null
        break
      default:
        data[field.id] = null  // 改为 null 而不是空字符串
    }
  })
  formData.value = data
  console.log('Initialized formData:', data)
}

watch(() => props.formFields, initFormData, { 
  immediate: true,
  deep: true 
})

// 添加阻止 ESC 事件处理
const handleKeyUp = (event) => {
  if (event.key === 'Escape') {
    event.preventDefault()
    event.stopPropagation()
  }
}

// 添加和移除事件监听器
onMounted(() => {
  window.addEventListener('keyup', handleKeyUp, true)
})

onBeforeUnmount(() => {
  window.removeEventListener('keyup', handleKeyUp, true)
})

// 添加宽度转换方法
const getFieldSpan = (width) => {
  const widthMap = {
    '25%': 6,     // 1/4
    '33.33%': 8,  // 1/3
    '50%': 12,    // 1/2
    '66.67%': 16, // 2/3
    '75%': 18,    // 3/4
    '100%': 24    // 整行
  }
  return widthMap[width] || 24 // 默认整行
}

// 添加获取前缀的方法
const getNumberPrefix = (format) => {
  switch (format) {
    case 'currency':
      return '¥'
    default:
      return ''
  }
}

// 添加获取后缀的方法
const getNumberSuffix = (format) => {
  switch (format) {
    case 'percent':
      return '%'
    default:
      return ''
  }
}

// 添加 watch 来监控 formData 的变化
watch(formData, (newValue) => {
  console.log('FormPreview formData changed:', newValue)
}, { deep: true })
</script>

<style scoped>
.preview-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  animation: fadeIn 0.3s ease-out;
}

.form-preview {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 90vh;
  background: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1001;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.preview-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.preview-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.preview-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s;
}

.preview-tab.active {
  background: #f0f0f0;
  color: var(--primary-color);
}

.preview-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.preview-container {
  margin: 0 auto;
  transition: all 0.3s;
}

.preview-content.desktop .preview-container {
  width: 40%;
  margin: 0 auto;
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.preview-content.mobile .preview-container {
  width: 375px;
  height: 667px;
  margin: 0 auto;
  background: #fff;
  border: 10px solid #333;
  border-radius: 40px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.preview-content.mobile :deep(.n-form-item) {
  margin-bottom: 12px;
}

.preview-content.mobile :deep(.n-form-item-label) {
  font-size: 14px;
  height: 32px;
  line-height: 32px;
}

.preview-content.mobile :deep(.department-selector) {
  min-width: unset !important;
  width: 100% !important;
}

/* 确保移动端视图下所有字段都是整行显示 */
.preview-content.mobile :deep(.n-grid-item) {
  width: 100% !important;
  max-width: 100% !important;
  flex: 0 0 100% !important;
}

.preview-header .n-button {
  position: absolute;
  right: 16px;
  top: 16px;
}

.field-description {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
  font-weight: normal;
}

:deep(.n-form-item) {
  margin-bottom: 0;
  /* 移除底部边距，因为使用了栅格的间距 */
}

:deep(.n-grid) {
  width: 100%;
}

/* 添加数字输入框样式 */
:deep(.n-input-number) {
  width: 100%;
}

/* 添加数值居中显示的样式 */
:deep(.n-input-number .n-input__input-el) {
  text-align: center;
}

.mobile-container {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.mobile-container.is-mobile {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.preview-content.mobile .preview-container {
  width: 375px;
  height: 667px;
  margin: 0 auto;
  background: #fff;
  border: 10px solid #333;
  border-radius: 40px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* 修改移动端字段包装器样式 */
.mobile-field-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 修改部门选择器在移动预览模式下的样式 */
.preview-content.mobile :deep(.mobile-preview-panel) {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: #fff;
  z-index: 100;
  border-radius: 30px;
  overflow: hidden;
}

/* 确保部门选择器的内容区域可以滚动 */
.preview-content.mobile :deep(.mobile-preview-content) {
  height: calc(100% - 56px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 确保表单容器也继承高度 */
.preview-content.mobile .mobile-container.is-mobile :deep(.n-form) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 确保网格容器也继承高度 */
.preview-content.mobile .mobile-container.is-mobile :deep(.n-grid) {
  flex: 1;
  height: 100%;
  overflow: auto;
}

/* 移除可能影响点击事件的样式 */
.preview-content.mobile :deep(.n-form-item) {
  pointer-events: auto;
}

.preview-content.mobile :deep(.department-selector) {
  pointer-events: auto;
}
</style>