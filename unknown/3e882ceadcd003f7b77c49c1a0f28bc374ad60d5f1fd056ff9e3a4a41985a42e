/**
 * 全局 SCSS 变量
 */

// 主题色
$primary-color: #18a058;
$primary-color-hover: #36ad6a;
$primary-color-pressed: #0c7a43;
$primary-color-light: rgba(24, 160, 88, 0.1);

// 信息色
$info-color: #2080f0;
$info-color-hover: #4098fc;
$info-color-pressed: #1060c9;
$info-color-light: rgba(32, 128, 240, 0.1);

// 成功色
$success-color: #18a058;
$success-color-hover: #36ad6a;
$success-color-pressed: #0c7a43;
$success-color-light: rgba(24, 160, 88, 0.1);

// 警告色
$warning-color: #f0a020;
$warning-color-hover: #fcb040;
$warning-color-pressed: #c97c10;
$warning-color-light: rgba(240, 160, 32, 0.1);

// 错误色
$error-color: #d03050;
$error-color-hover: #de576d;
$error-color-pressed: #ab1f3f;
$error-color-light: rgba(208, 48, 80, 0.1);

// 文本色
$text-color-primary: #333333;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景色
$background-color-base: #f5f7fa;
$background-color-light: #f9f9f9;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体粗细
$font-weight-bold: 600;
$font-weight-medium: 500;
$font-weight-regular: 400;

// 边距
$spacing-extra-large: 32px;
$spacing-large: 24px;
$spacing-medium: 16px;
$spacing-base: 12px;
$spacing-small: 8px;
$spacing-extra-small: 4px;

// 圆角
$border-radius-large: 8px;
$border-radius-medium: 6px;
$border-radius-base: 4px;
$border-radius-small: 2px;

// 阴影
$box-shadow-base: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-light: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
$box-shadow-dark: 0 4px 16px 0 rgba(0, 0, 0, 0.2);

// 过渡
$transition-duration: 0.3s;
$transition-timing-function: ease-in-out;
