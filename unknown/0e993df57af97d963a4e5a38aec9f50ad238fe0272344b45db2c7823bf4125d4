/**
 * 组件加载测试工具
 * 用于测试动态组件加载是否成功
 */

/**
 * 测试单个组件加载
 * @param {string} viewPath - 组件路径，例如 'system/UsersPage'
 * @returns {Promise<object>} 测试结果
 */
export const testComponentLoad = async (viewPath) => {
  try {
    // 规范化路径
    const normalizedPath = viewPath.startsWith('/') ? viewPath.substring(1) : viewPath
    
    // 尝试动态导入组件
    const component = await import(`@/views/${normalizedPath}.vue`)
    
    if (component) {
      return {
        viewPath,
        success: true,
        message: `组件 ${viewPath} 加载成功`
      }
    } else {
      return {
        viewPath,
        success: false,
        message: `组件 ${viewPath} 加载失败: 未找到组件`
      }
    }
  } catch (error) {
    console.error(`测试组件 ${viewPath} 加载失败:`, error)
    return {
      viewPath,
      success: false,
      message: `组件 ${viewPath} 加载失败: ${error.message}`
    }
  }
}

/**
 * 测试多个组件加载
 * @param {Array<string>} viewPaths - 组件路径数组
 * @returns {Promise<Array<object>>} 测试结果数组
 */
export const testMultipleComponentsLoad = async (viewPaths) => {
  const results = []
  
  for (const viewPath of viewPaths) {
    const result = await testComponentLoad(viewPath)
    results.push(result)
  }
  
  return results
}
