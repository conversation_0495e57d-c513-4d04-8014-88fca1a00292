/* 区域市场份额样式优化 */
.market-share-container {
    height: 3.54rem;
    overflow: hidden;
    padding: 0.1rem 0.15rem;
}

.market-share-list {
    display: flex;
    flex-direction: column;
    gap: 0.12rem;
}

.market-share-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(25, 186, 139, 0.17);
    border-radius: 3px;
    padding: 0.08rem 0.15rem;
    position: relative;
    transition: all 0.3s ease;
}

.market-share-item:hover {
    background: rgba(25, 186, 139, 0.1);
    transform: translateX(5px);
}

.market-share-rank {
    width: 0.3rem;
    height: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(25, 186, 139, 0.2);
    border-radius: 50%;
    color: #fff;
    font-weight: bold;
    margin-right: 0.15rem;
    font-size: 0.16rem;
}

.market-share-brand {
    flex: 1;
    color: #fff;
    font-size: 0.18rem;
    font-weight: 500;
}

.market-share-volume {
    color: #49bcf7;
    font-size: 0.16rem;
    margin-right: 0.15rem;
}

.market-share-percentage {
    width: 0.8rem;
    text-align: right;
    color: #ffeb7b;
    font-size: 0.18rem;
    font-weight: bold;
}

/* 进度条样式 */
.market-share-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(to right, rgba(25, 186, 139, 0.7), rgba(73, 188, 247, 0.7));
    border-radius: 0 3px 3px 0;
    transition: width 1s ease;
}

/* 标题样式优化 */
.market-share-title {
    font-size: 0.24rem;
    color: #fff;
    text-align: center;
    line-height: 0.6rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 20;
    margin-bottom: 0.1rem;
}

.market-share-title:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -1px;
    width: 30%;
    height: 2px;
    transform: translateX(-50%);
    background: linear-gradient(to right, rgba(25, 186, 139, 0), rgba(25, 186, 139, 1), rgba(25, 186, 139, 0));
}