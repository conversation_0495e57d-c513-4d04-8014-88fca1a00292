/**
 * 日期工具函数
 */

/**
 * 格式化日期
 * @param {string|number|Date} date - 要格式化的日期
 * @param {string} format - 格式化模式，如 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '';
  
  let dateObj;
  
  // 转换输入为Date对象
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else if (typeof date === 'string') {
    // 尝试解析日期字符串
    dateObj = new Date(date);
  } else {
    return '';
  }
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  // 获取日期各部分
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  
  // 根据格式替换
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 获取日期快捷选项
 * 用于日期选择器的快捷选项
 */
export const dateShortcuts = {
  今日: () => {
    const end = new Date()
    const start = new Date(end.getFullYear(), end.getMonth(), end.getDate())
    return [start, end]
  },
  昨日: () => {
    const end = new Date()
    end.setTime(end.getTime() - 3600 * 1000 * 24)
    const start = new Date(end.getFullYear(), end.getMonth(), end.getDate())
    return [start, end]
  },
  最近7天: () => {
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    return [start, end]
  },
  最近30天: () => {
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
    return [start, end]
  }
}

export default {
  formatDate,
  dateShortcuts
}
