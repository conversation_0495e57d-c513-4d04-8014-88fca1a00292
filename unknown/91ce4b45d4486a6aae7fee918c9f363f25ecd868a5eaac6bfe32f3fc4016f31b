/**
 * 提示词定位指令
 * 用于将提示词定位在最右侧的选项按钮右侧10像素处
 */

// 定位提示词的函数
function positionTip(el) {
  // 获取父元素中的radio-group元素
  const radioGroup = el.parentElement.querySelector('.n-radio-group');
  if (!radioGroup) return;

  // 获取radio-group中的最后一个radio-button元素
  const lastRadioButton = radioGroup.querySelector('.n-radio-button:last-child');
  if (!lastRadioButton) return;

  // 计算最右侧按钮的右边界位置
  const radioGroupRect = radioGroup.getBoundingClientRect();
  const lastRadioButtonRect = lastRadioButton.getBoundingClientRect();

  // 计算提示词的左侧位置（最右侧按钮的右边界 + 10px）
  const tipLeft = lastRadioButtonRect.right - radioGroupRect.left + 10;

  // 设置提示词的左侧位置
  el.style.left = `${tipLeft}px`;
  el.style.marginLeft = '0';
}

export default {
  // 在元素挂载时定位提示词
  mounted(el, binding) {
    // 延迟执行，确保DOM已经渲染完成
    setTimeout(() => {
      positionTip(el);
    }, 0);
  },

  // 在元素更新时重新定位提示词
  updated(el, binding) {
    // 延迟执行，确保DOM已经更新完成
    setTimeout(() => {
      positionTip(el);
    }, 0);
  }
};
