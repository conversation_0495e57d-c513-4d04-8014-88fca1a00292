/**
 * 字段名统一重构测试
 * 验证前后端字段名一致性
 */

import { createDefaultOrderForm } from './src/utils/orderFormFactory.js'
import { convertFormToApiData } from './src/utils/orderFormUtils.js'

// 测试字段名统一性
function testFieldMapping() {
  console.log('=== 字段名统一重构测试 ===')
  
  // 创建默认表单数据
  const form = createDefaultOrderForm()
  
  // 设置测试数据
  form.sbAmount = 100000 // 启票价格 10万元
  form.salesAmount = 135000 // 销售价格 13.5万元
  form.dealAmount = 130000 // 成交总价 13万元
  form.dealAmountCn = '拾叁万元整' // 成交总价大写
  form.grossProfitAmount = 30000 // 毛利润 3万元
  form.deliveryDate = Date.now() // 交付日期
  form.deliveryOrgId = 1 // 交付单位ID
  form.remark = '测试备注' // 备注
  
  console.log('1. 表单数据（统一后的字段名）:')
  console.log({
    sbAmount: form.sbAmount,
    salesAmount: form.salesAmount,
    dealAmount: form.dealAmount,
    dealAmountCn: form.dealAmountCn,
    grossProfitAmount: form.grossProfitAmount,
    deliveryDate: form.deliveryDate,
    deliveryOrgId: form.deliveryOrgId,
    remark: form.remark
  })
  
  // 转换为API数据
  const apiData = convertFormToApiData(form)
  
  console.log('2. API数据（应该保持相同的字段名，只转换金额单位）:')
  console.log({
    sbAmount: apiData.sbAmount, // 应该是 10000000 分
    salesAmount: apiData.salesAmount, // 应该是 13500000 分
    dealAmount: apiData.dealAmount, // 应该是 13000000 分
    dealAmountCn: apiData.dealAmountCn, // 应该保持不变
    grossProfitAmount: apiData.grossProfitAmount, // 应该是 3000000 分
    deliveryDate: apiData.deliveryDate, // 应该保持不变
    deliveryOrgId: apiData.deliveryOrgId, // 应该保持不变
    remark: apiData.remark // 应该保持不变
  })
  
  // 验证字段名一致性
  const fieldMappingTests = [
    { field: 'sbAmount', formValue: form.sbAmount, apiValue: apiData.sbAmount / 100 },
    { field: 'salesAmount', formValue: form.salesAmount, apiValue: apiData.salesAmount / 100 },
    { field: 'dealAmount', formValue: form.dealAmount, apiValue: apiData.dealAmount / 100 },
    { field: 'dealAmountCn', formValue: form.dealAmountCn, apiValue: apiData.dealAmountCn },
    { field: 'grossProfitAmount', formValue: form.grossProfitAmount, apiValue: apiData.grossProfitAmount / 100 },
    { field: 'deliveryDate', formValue: form.deliveryDate, apiValue: apiData.deliveryDate },
    { field: 'deliveryOrgId', formValue: form.deliveryOrgId, apiValue: apiData.deliveryOrgId },
    { field: 'remark', formValue: form.remark, apiValue: apiData.remark }
  ]
  
  console.log('3. 字段名一致性验证:')
  let allTestsPassed = true
  
  fieldMappingTests.forEach(test => {
    const passed = test.formValue === test.apiValue
    console.log(`${test.field}: ${passed ? '✅' : '❌'} (表单: ${test.formValue}, API: ${test.apiValue})`)
    if (!passed) allTestsPassed = false
  })
  
  console.log(`\n测试结果: ${allTestsPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`)
  console.log('=========================')
  
  return allTestsPassed
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testFieldMapping }
} else {
  testFieldMapping()
}
