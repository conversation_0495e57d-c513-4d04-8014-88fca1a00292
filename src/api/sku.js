import { doGet, doPost, doPut, doDelete } from '@/utils/requests'
import messages from '@/utils/messages'

/**
 * 车辆SKU API 服务
 */
export const skuApi = {
  /**
   * 获取车辆SKU列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.brands] - 多个品牌，逗号分隔（如：深蓝,启源,阿维塔）
   * @param {String} [params.series] - 车系
   * @param {String} [params.modelCode] - 车型代码
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回车辆SKU列表的 Promise
   */
  getSkuList(params) {
    // 直接使用page和size参数，无需转换
    // 后端API已经支持page和size作为分页参数
    return doGet('/vehicle/sku', params)
  },

  /**
   * 获取车辆SKU详情
   * @param {Number} id - SKU记录ID
   * @returns {Promise} 返回车辆SKU详情的 Promise
   */
  getSkuDetail(id) {
    return doGet(`/vehicle/sku/${id}`)
  },

  /**
   * 新增车辆SKU
   * @param {Object} data - SKU数据
   * @param {String} data.brand - 车辆品牌
   * @param {String} data.series - 车系
   * @param {String} data.modelCode - 车型代码
   * @param {String} data.modelName - 车型名称
   * @param {String} data.configCode - 配置代码
   * @param {String} data.configName - 配置名称
   * @param {Number} data.sbPrice - 启票价格（单位：分）
   * @param {String} data.colorCode - 颜色代码
   * @param {String} data.skuId - SKU编码
   * @param {String} [data.paymentRemark] - 备注
   * @returns {Promise} 返回新增结果的 Promise
   */
  addSku(data) {
    return doPost('/vehicle/sku', data)
  },

  /**
   * 更新车辆SKU
   * @param {Object} data - SKU数据
   * @param {Number} data.id - SKU记录ID
   * @param {String} [data.brand] - 车辆品牌
   * @param {String} [data.series] - 车系
   * @param {String} [data.modelCode] - 车型代码
   * @param {String} [data.modelName] - 车型名称
   * @param {String} [data.configCode] - 配置代码
   * @param {String} [data.configName] - 配置名称
   * @param {Number} [data.sbPrice] - 启票价格（单位：分）
   * @param {String} [data.colorCode] - 颜色代码
   * @param {String} [data.skuId] - SKU编码
   * @param {String} [data.paymentRemark] - 备注
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateSku(data) {
    const { id, ...updateData } = data
    return doPut(`/vehicle/sku/${id}`, updateData)
  },

  /**
   * 删除车辆SKU
   * @param {Number} id - SKU记录ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteSku(id) {
    return doDelete(`/vehicle/sku/${id}`)
  },

  /**
   * 批量删除车辆SKU
   * @param {Array} ids - SKU记录ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteSku(ids) {
    return doPost('/vehicle/sku/batch', { ids })
  },

  /**
   * 导入车辆SKU
   * @param {String} fileKey - 文件路径
   * @returns {Promise} 返回导入结果的 Promise
   */
  importSku(fileKey) {
    return doPost('/vehicle/sku/import', { fileKey })
  },

  /**
   * 获取SKU选项数据
   * @param {String} type - 选项类型 (series, configName)
   * @param {Object} params - 查询参数
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.series] - 车系
   * @param {String} [params.configName] - 配置名称
   * @returns {Promise} 返回SKU选项数据的 Promise
   */
  getSkuOptions(type, params = {}) {
    return doGet(`/vehicle/sku/options/${type}`, params)
  }
}

export default skuApi
