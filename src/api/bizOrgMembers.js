import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

// 模拟成员数据
const mockMembersData = [
  {
    id: 1,
    orgId: 1,
    agentId: 'EMP001',
    agentName: '张三',
    position: '销售经理',
    businessRole: 'sales_manager',
    dataPermissions: 'store_level',
    dataRange: '1,2',
    dataRangeNames: '长安汽车济南总店, 深蓝德州分店',
    status: 'active',
    createdTime: '2020-03-15 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    orgId: 1,
    agentId: 'EMP002',
    agentName: '李四',
    position: '仓库管理员',
    businessRole: 'warehouse_keeper',
    dataPermissions: 'employee_level',
    dataRange: '1',
    dataRangeNames: '长安汽车济南总店',
    status: 'active',
    createdTime: '2020-06-20 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 3,
    orgId: 1,
    agentId: 'EMP003',
    agentName: '王五',
    position: '财务专员',
    businessRole: 'finance_specialist',
    dataPermissions: 'region_level',
    dataRange: '1,2,3',
    dataRangeNames: '长安汽车济南总店, 深蓝德州分店, 长安集团河北总部',
    status: 'active',
    createdTime: '2021-01-10 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 4,
    orgId: 2,
    agentId: 'EMP004',
    agentName: '赵六',
    position: '店长',
    businessRole: 'store_manager',
    dataPermissions: 'brand_level',
    dataRange: '2',
    dataRangeNames: '深蓝德州分店',
    status: 'active',
    createdTime: '2021-03-20 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 5,
    orgId: 2,
    agentId: 'EMP005',
    agentName: '孙七',
    position: '销售顾问',
    businessRole: 'sales_consultant',
    dataPermissions: 'employee_level',
    dataRange: null,
    dataRangeNames: null,
    status: 'active',
    createdTime: '2021-08-15 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  },
  {
    id: 6,
    orgId: 3,
    agentId: 'EMP006',
    agentName: '周八',
    position: '区域经理',
    businessRole: 'regional_manager',
    dataPermissions: 'group_level',
    dataRange: '1,2,3,4',
    dataRangeNames: '长安汽车济南总店, 深蓝德州分店, 长安集团河北总部, 阿维塔郑州二网',
    status: 'active',
    createdTime: '2019-06-10 10:00:00',
    updatedTime: '2024-01-15 10:00:00'
  }
]

// 判断是否为开发环境
const isDev = false

/**
 * 获取机构成员列表
 * @param {Number} orgId - 机构ID
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回成员列表的Promise
 */
export function getBizOrgMembersList(orgId, params = {}) {
  if (isDev) {
    // 开发环境使用模拟数据
    let filteredData = mockMembersData.filter(item => item.orgId === orgId)

    // 应用搜索条件
    if (params.keywords) {
      const keyword = params.keywords.toLowerCase()
      filteredData = filteredData.filter(item =>
        item.agentName.toLowerCase().includes(keyword) ||
        item.agentId.toLowerCase().includes(keyword)
      )
    }

    // 返回数据直接在data中，不再包装在data.list中
    return Promise.resolve({
      code: 200,
      data: filteredData,
      message: 'SUCCESS'
    })
  }

  params.orgId = orgId
  return doGet(`/biz/org/member`, params)
}

/**
 * 获取成员详情
 * @param {Number} id - 成员ID
 * @returns {Promise} 返回成员详情的Promise
 */
export function getBizOrgMemberDetail(id) {
  if (isDev) {
    const member = mockMembersData.find(item => item.id === id)
    return Promise.resolve({
      code: 200,
      data: member || null,
      message: member ? 'success' : 'not found'
    })
  }

  return doGet(`/biz/org/member/${id}`)
}

/**
 * 创建成员
 * @param {Object} memberData - 成员数据
 * @returns {Promise} 返回创建结果的Promise
 */
export function createBizOrgMember(memberData) {
  if (isDev) {
    const newMember = {
      ...memberData,
      id: Math.max(...mockMembersData.map(item => item.id)) + 1,
      createdTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      updatedTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }

    mockMembersData.push(newMember)

    return Promise.resolve({
      code: 200,
      data: newMember,
      message: 'success'
    })
  }

  return doPost('/biz/org/member', memberData)
}

/**
 * 批量创建成员
 * @param {Array} membersData - 成员数据数组，直接传入对象数组
 * @returns {Promise} 返回批量创建结果的Promise
 */
export function createBizOrgMembersBatch(membersData) {
  if (isDev) {
    let maxId = Math.max(...mockMembersData.map(item => item.id))

    for (const memberData of membersData) {
      const newMember = {
        ...memberData,
        id: ++maxId,
        createdTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        updatedTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
      }

      mockMembersData.push(newMember)
    }

    // 模拟真实接口返回格式：data 为 null
    return Promise.resolve({
      code: 200,
      data: null,
      message: 'SUCCESS'
    })
  }

  return doPost('/biz/org/member/batch', membersData)
}

/**
 * 更新成员
 * @param {Object} memberData - 成员数据
 * @returns {Promise} 返回更新结果的Promise
 */
export function updateBizOrgMember(memberData) {
  if (isDev) {
    const index = mockMembersData.findIndex(item => item.id === memberData.id)
    if (index === -1) {
      return Promise.reject({
        code: 404,
        message: '成员不存在'
      })
    }

    mockMembersData[index] = {
      ...mockMembersData[index],
      ...memberData,
      updatedTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }

    return Promise.resolve({
      code: 200,
      data: mockMembersData[index],
      message: 'success'
    })
  }

  return doPut(`/biz/org/member/${memberData.id}`, memberData)
}

/**
 * 批量更新成员
 * @param {Array} membersData - 成员数据数组，直接传入对象数组
 * @returns {Promise} 返回批量更新结果的Promise
 */
export function updateBizOrgMembersBatch(membersData) {
  if (isDev) {
    for (const memberData of membersData) {
      const index = mockMembersData.findIndex(item => item.id === memberData.id)
      if (index !== -1) {
        mockMembersData[index] = {
          ...mockMembersData[index],
          ...memberData,
          updatedTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
        }
      }
    }

    // 模拟真实接口返回格式：data 为 null
    return Promise.resolve({
      code: 200,
      data: null,
      message: 'SUCCESS'
    })
  }

  return doPut('/biz/org/member/batch', membersData)
}

/**
 * 删除成员
 * @param {Number} id - 成员ID
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteBizOrgMember(id) {
  if (isDev) {
    const index = mockMembersData.findIndex(item => item.id === id)
    if (index === -1) {
      return Promise.reject({
        code: 404,
        message: '成员不存在'
      })
    }

    mockMembersData.splice(index, 1)

    return Promise.resolve({
      code: 200,
      message: 'success'
    })
  }

  return doDelete(`/biz/org/member/${id}`)
}

/**
 * 批量删除成员
 * @param {String} ids - 逗号分割的成员ID字符串，如 "1,2,3"
 * @returns {Promise} 返回批量删除结果的Promise
 */
export function deleteBizOrgMembersBatch(ids) {
  if (isDev) {
    const idArray = ids.split(',').map(id => parseInt(id.trim()))
    let deletedCount = 0

    // 从后往前删除，避免索引变化问题
    for (let i = mockMembersData.length - 1; i >= 0; i--) {
      if (idArray.includes(mockMembersData[i].id)) {
        mockMembersData.splice(i, 1)
        deletedCount++
      }
    }

    return Promise.resolve({
      code: 200,
      data: null,
      message: 'SUCCESS'
    })
  }

  return doDelete(`/biz/org/member/batch?ids=${ids}`)
}

export default {
  getBizOrgMembersList,
  getBizOrgMemberDetail,
  createBizOrgMember,
  createBizOrgMembersBatch,
  updateBizOrgMember,
  updateBizOrgMembersBatch,
  deleteBizOrgMember,
  deleteBizOrgMembersBatch
}
