import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 车险管理 API 服务 - RESTful风格
 */
export const vehicleInsuranceApi = {
  /**
   * 获取车险列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.orderSn] - 订单号
   * @param {String} [params.customerName] - 客户名称
   * @param {String} [params.customerPhone] - 客户电话
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.series] - 车型系列
   * @param {String} [params.rebateStatus] - 返利状态
   * @param {String} [params.insuranceOrgIds] - 保险机构ID，多个用逗号分隔
   * @param {Number} [params.bizOrgId] - 经办机构ID
   * @param {Number} [params.bizAgentId] - 经办员工ID
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回车险列表的 Promise
   */
  getInsuranceList(params) {
    return doGet('/vehicle/insurance', params)
  },

  /**
   * 获取车险详情
   * @param {Number} id - 车险记录ID
   * @returns {Promise} 返回车险详情的 Promise
   */
  getInsuranceDetail(id) {
    return doGet(`/vehicle/insurance/${id}`)
  },

  /**
   * 新增车险记录
   * @param {Object} data - 车险数据
   * @param {Number} data.vehicleOrderId - 新车订单ID
   * @param {Number} data.ciFee - 交强险保费(分)
   * @param {Number} data.ciRebate - 交强险返利(分)
   * @param {Number} data.biFee - 商业险保费(分)
   * @param {Number} data.biRebate - 商业险返利(分)
   * @param {Number} data.oiFee - 非车险保费(分)
   * @param {Number} data.oiRebate - 非车险返利(分)
   * @param {String} data.rebateStatus - 返利状态
   * @param {Number} data.bizOrgId - 经办机构ID
   * @param {Number} data.bizAgentId - 经办员工ID
   * @returns {Promise} 返回新增结果的 Promise
   */
  createInsurance(data) {
    return doPost('/vehicle/insurance', data)
  },

  /**
   * 更新车险记录
   * @param {Number} id - 车险记录ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateInsurance(id, data) {
    return doPut(`/vehicle/insurance/${id}`, data)
  },

  /**
   * 删除车险记录
   * @param {Number} id - 车险记录ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteInsurance(id) {
    return doDelete(`/vehicle/insurance/${id}`)
  },

  /**
   * 批量删除车险记录
   * @param {Array} ids - 车险记录ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteInsurance(ids) {
    return doPost('/vehicle/insurance/batch-delete', { ids })
  },

  /**
   * 更新返利状态
   * @param {Number} id - 车险记录ID
   * @param {String} status - 返利状态
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateRebateStatus(id, status) {
    return doPut(`/vehicle/insurance/${id}/rebate-status`, { rebateStatus: status })
  },

  /**
   * 批量更新返利状态
   * @param {Array} ids - 车险记录ID数组
   * @param {String} status - 返利状态
   * @returns {Promise} 返回批量更新结果的 Promise
   */
  batchUpdateRebateStatus(ids, status) {
    return doPut(`/vehicle/insurance/rebate-status?ids=${ids.join(',')}`, { rebateStatus: status })
  }
}

export default vehicleInsuranceApi
