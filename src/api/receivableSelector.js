import arbApi from '@/api/arb'

/**
 * 应收账款选择器 API 服务
 */
export const receivableSelectorApi = {
  /**
   * 获取应收账款列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回应收账款列表的 Promise
   */
  async getReceivableList(params) {
    try {
      console.log('应收账款选择器 - 请求参数:', params)

      // 转换参数名称，从pageNum/pageSize转为page/size
      const apiParams = {
        ...params,
        page: params.pageNum,
        size: params.pageSize
      }

      // 删除多余的参数
      delete apiParams.pageNum
      delete apiParams.pageSize

      // 调用arbApi获取数据
      const response = await arbApi.getArbList(apiParams)
      console.log('应收账款选择器 - API返回数据:', response)

      // 返回原始响应
      return response
    } catch (error) {
      console.error('获取应收账款列表失败:', error)
      return {
        code: 500,
        message: error.message || '获取应收账款列表失败',
        data: {
          list: [],
          total: 0,
          pages: 0
        }
      }
    }
  },

  /**
   * 获取应收账款详情
   * @param {Number} id - 应收账款ID
   * @returns {Promise} 返回应收账款详情的 Promise
   */
  async getReceivableDetail(id) {
    try {
      // 调用arbApi获取详情
      const response = await arbApi.getArbDetail(id)
      console.log('应收账款选择器 - 获取详情返回数据:', response)

      return response
    } catch (error) {
      console.error('获取应收账款详情失败:', error)
      return {
        code: 500,
        message: error.message || '获取应收账款详情失败',
        data: null
      }
    }
  }
}

export default receivableSelectorApi
