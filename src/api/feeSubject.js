import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

// 判断是否为开发环境
const isDev = false // 使用真实API数据

// 模拟费用科目数据
const mockFeeSubjects = [
  {
    id: 1,
    subjectName: '车款',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: false,
    defaultAmount: 0,
    createdTime: '2025-05-09 10:32:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:49:07'
  },
  {
    id: 2,
    subjectName: '定金',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: false,
    defaultAmount: 500000,
    createdTime: '2025-05-09 10:33:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:50:07'
  },
  {
    id: 3,
    subjectName: '保险费',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: true,
    defaultAmount: 300000,
    createdTime: '2025-05-09 10:34:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:51:07'
  },
  {
    id: 4,
    subjectName: '上牌费',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: true,
    defaultAmount: 150000,
    createdTime: '2025-05-09 10:35:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:52:07'
  },
  {
    id: 5,
    subjectName: '服务费',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: false,
    defaultAmount: 200000,
    createdTime: '2025-05-09 10:36:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:53:07'
  },
  {
    id: 6,
    subjectName: '装潢费',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: true,
    defaultAmount: 800000,
    createdTime: '2025-05-09 10:37:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:54:07'
  },
  {
    id: 7,
    subjectName: '员工薪资',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 600000,
    createdTime: '2025-05-09 10:32:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:49:07'
  },
  {
    id: 8,
    subjectName: '运费',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: true,
    payable: true,
    defaultAmount: 100000,
    createdTime: '2025-05-09 10:38:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 10:55:07'
  },
  {
    id: 9,
    subjectName: '维修费',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: false,
    payable: true,
    defaultAmount: 250000,
    createdTime: '2025-05-09 11:00:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:15:07'
  },
  {
    id: 10,
    subjectName: '检测费',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: true,
    defaultAmount: 50000,
    createdTime: '2025-05-09 11:05:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:20:07'
  },
  {
    id: 11,
    subjectName: '过户费',
    subjectCategory: 'DEAL_FEE',
    subjectCategoryName: '销售关联费用',
    receivable: true,
    payable: true,
    defaultAmount: 120000,
    createdTime: '2025-05-09 11:10:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:25:07'
  },
  {
    id: 12,
    subjectName: '办公费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 0,
    createdTime: '2025-05-09 11:15:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:30:07'
  },
  {
    id: 13,
    subjectName: '租金',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 1000000,
    createdTime: '2025-05-09 11:20:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:35:07'
  },
  {
    id: 14,
    subjectName: '水电费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 80000,
    createdTime: '2025-05-09 11:25:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:40:07'
  },
  {
    id: 15,
    subjectName: '通讯费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 30000,
    createdTime: '2025-05-09 11:30:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:45:07'
  },
  {
    id: 16,
    subjectName: '广告费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 500000,
    createdTime: '2025-05-09 11:35:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:50:07'
  },
  {
    id: 17,
    subjectName: '差旅费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 0,
    createdTime: '2025-05-09 11:40:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 11:55:07'
  },
  {
    id: 18,
    subjectName: '招待费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 0,
    createdTime: '2025-05-09 11:45:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:00:07'
  },
  {
    id: 19,
    subjectName: '培训费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 200000,
    createdTime: '2025-05-09 11:50:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:05:07'
  },
  {
    id: 20,
    subjectName: '咨询费',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: true,
    payable: true,
    defaultAmount: 150000,
    createdTime: '2025-05-09 11:55:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:10:07'
  },
  {
    id: 21,
    subjectName: '手续费',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: true,
    payable: true,
    defaultAmount: 50000,
    createdTime: '2025-05-09 12:00:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:15:07'
  },
  {
    id: 22,
    subjectName: '违约金',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: true,
    payable: false,
    defaultAmount: 0,
    createdTime: '2025-05-09 12:05:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:20:07'
  },
  {
    id: 23,
    subjectName: '延期费',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: true,
    payable: false,
    defaultAmount: 100000,
    createdTime: '2025-05-09 12:10:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:25:07'
  },
  {
    id: 24,
    subjectName: '税费',
    subjectCategory: 'OPENING_FEE',
    subjectCategoryName: '企业运营',
    receivable: false,
    payable: true,
    defaultAmount: 0,
    createdTime: '2025-05-09 12:15:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:30:07'
  },
  {
    id: 25,
    subjectName: '退款',
    subjectCategory: 'OTHER_FEE',
    subjectCategoryName: '其他费用',
    receivable: false,
    payable: true,
    defaultAmount: 0,
    createdTime: '2025-05-09 12:20:12',
    creatorId: 0,
    editorId: 0,
    editorName: 'system',
    updateTime: '2025-05-09 12:35:07'
  }
]

/**
 * 费用科目 API 服务 - RESTful风格
 */
export const feeSubjectApi = {
  /**
   * 获取费用科目列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.keywords] - 关键词搜索
   * @param {Boolean} [params.receivable] - 是否可收款
   * @param {Boolean} [params.payable] - 是否可付款
   * @returns {Promise} 返回费用科目列表的 Promise
   */
  getFeeSubjectList(params) {
    if (isDev) {
      // 开发环境使用模拟数据
      let filteredData = [...mockFeeSubjects]

      // 按可收款过滤
      if (params.receivable !== undefined) {
        filteredData = filteredData.filter(item => item.receivable === params.receivable)
      }

      // 按可付款过滤
      if (params.payable !== undefined) {
        filteredData = filteredData.filter(item => item.payable === params.payable)
      }

      // 按关键词搜索
      if (params.keywords) {
        const keyword = params.keywords.toLowerCase()
        filteredData = filteredData.filter(item =>
          item.subjectName.toLowerCase().includes(keyword) ||
          (item.remark && item.remark.toLowerCase().includes(keyword))
        )
      }

      // 分页处理
      const page = params.page || 1
      const size = params.size || 20
      const start = (page - 1) * size
      const end = start + size
      const paginatedData = filteredData.slice(start, end)

      return Promise.resolve({
        code: 200,
        message: 'success',
        data: {
          endRow: Math.min(start + paginatedData.length, filteredData.length),
          firstPage: 1,
          hasNextPage: page * size < filteredData.length,
          hasPreviousPage: page > 1,
          isFirstPage: page === 1,
          isLastPage: page * size >= filteredData.length,
          lastPage: Math.ceil(filteredData.length / size),
          list: paginatedData,
          total: filteredData.length
        }
      })
    }
    return doGet('/financial/fee/subject/setting', params)
  },

  /**
   * 获取费用科目详情
   * @param {Number} id - 费用科目ID
   * @returns {Promise} 返回费用科目详情的 Promise
   */
  getFeeSubjectDetail(id) {
    return doGet(`/financial/fee/subject/setting/${id}`)
  },

  /**
   * 新增费用科目
   * @param {Object} data - 费用科目数据
   * @param {String} data.subjectName - 科目名称
   * @param {String} data.subjectCategory - 科目类别
   * @param {Boolean} data.receivable - 是否可收款
   * @param {Boolean} data.payable - 是否可付款
   * @param {Boolean} data.usable - 科目状态（true启用，false停用）
   * @param {Number} data.defaultAmount - 默认金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回新增结果的 Promise
   */
  createFeeSubject(data) {
    return doPost('/financial/fee/subject/setting', data)
  },

  /**
   * 更新费用科目
   * @param {Object} data - 费用科目数据
   * @param {Number} data.id - 费用科目ID
   * @param {String} [data.subjectName] - 科目名称
   * @param {String} [data.subjectCategory] - 科目类别
   * @param {Boolean} [data.receivable] - 是否可收款
   * @param {Boolean} [data.payable] - 是否可付款
   * @param {Boolean} [data.usable] - 科目状态（true启用，false停用）
   * @param {Number} [data.defaultAmount] - 默认金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateFeeSubject(data) {
    return doPut('/financial/fee/subject/setting', data)
  },

  /**
   * 删除费用科目
   * @param {Number} id - 费用科目ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteFeeSubject(id) {
    return doDelete(`/financial/fee/subject/setting/${id}`)
  },

  /**
   * 获取科目类别选项
   * @returns {Array} 返回科目类别选项数组
   */
  getSubjectCategoryOptions() {
    return [
      { label: '系统内置', value: 'SYSTEM_FEE' },
      { label: '企业运营', value: 'OPENING_FEE' },
      { label: '销售关联费用', value: 'DEAL_FEE' },
      { label: '其他费用', value: 'OTHER_FEE' }
    ]
  }
}

export default feeSubjectApi
