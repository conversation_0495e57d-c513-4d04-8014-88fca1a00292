import { doGet } from '@/utils/requests'

/**
 * 菜单 API 服务
 */
export const menuApi = {
  /**
   * 获取菜单数据
   * @returns {Promise} 返回菜单数据的 Promise
   */
  getMenus() {
    return doGet('/system/menu')
  },

  /**
   * 获取菜单详情
   * @param {Number} id - 菜单 ID
   * @returns {Promise} 返回菜单详情的 Promise
   */
  getMenuDetail(id) {
    return doGet(`/system/menu/${id}`)
  }
}

/**
 * 构建菜单树
 * @param {Array} menuList - 扁平的菜单列表
 * @returns {Array} 树形结构的菜单
 */
export function buildMenuTree(menuList) {
  if (!menuList || !Array.isArray(menuList)) {
    console.error('无效的菜单数据:', menuList)
    return []
  }

  const menuMap = new Map()
  const rootMenus = []

  // 第一遍遍历，创建所有菜单项的映射
  menuList.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] })
  })

  // 第二遍遍历，构建树结构
  menuList.forEach(menu => {
    const menuItem = menuMap.get(menu.id)

    if (menu.parentId && menu.parentId !== 1) {
      // 如果有父菜单且不是顶级菜单
      const parentMenu = menuMap.get(menu.parentId)
      if (parentMenu) {
        parentMenu.children.push(menuItem)
      } else {
        // 如果找不到父菜单，则添加到根菜单
        rootMenus.push(menuItem)
      }
    } else {
      // 顶级菜单直接添加到根菜单
      rootMenus.push(menuItem)
    }
  })

  return rootMenus
}

/**
 * 获取顶级菜单（parentId 为 1 的菜单）
 * @param {Array} menuList - 扁平的菜单列表
 * @returns {Array} 顶级菜单列表
 */
export function getTopLevelMenus(menuList) {
  if (!menuList || !Array.isArray(menuList)) {
    return []
  }

  return menuList.filter(menu => menu.parentId === 1)
}

export default menuApi
