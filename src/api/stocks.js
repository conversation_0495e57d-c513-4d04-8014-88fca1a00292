import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 车辆库存 API 服务
 */
export const stocksApi = {
  /**
   * 获取车辆库存列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.vin] - 车辆VIN
   * @param {Number} [params.ownerOrgId] - 所属单位ID
   * @param {Number} [params.stockOrgId] - 仓储单位ID
   * @param {String} [params.stockStatus] - 库存状态
   * @param {String} [params.trial] - 试驾状态过滤 (none: 非试驾车, trialing: 试驾中)
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回车辆库存列表的 Promise
   */
  getStocksList(params) {
    // 生产环境使用真实API
    return doGet('/vehicle/stock', params)
  },

  /**
   * 获取车辆库存详情
   * @param {Number} id - 库存记录ID
   * @returns {Promise} 返回车辆库存详情的 Promise
   */
  getStockDetail(id) {
    return doGet(`/vehicle/stock/${id}`)
  },

  /**
   * 获取车辆库存详情(通过VIN)
   * @param {String} vin - 车辆VIN
   * @returns {Promise} 返回车辆库存详情的 Promise
   */
  getStockByVin(vin) {
    return doGet(`/vehicle/stock/vin/${vin}`)
  },

  /**
   * 新增车辆库存
   * @param {Object} data - 库存数据
   * @param {String} data.skuId - 车型SKU ID
   * @param {String} data.vin - 车辆VIN
   * @param {Number} data.stockAmount - 库存成本(分)
   * @param {Number} data.stockInDate - 入库日期(时间戳)
   * @param {String} data.stockStatus - 库存状态
   * @param {String} data.stockType - 库存类型 (CASH/none)
   * @param {String} data.trialStatus - 试驾状态 (trialing/none)
   * @param {Number} [data.trialingBeginDate] - 试驾开始日期(时间戳)
   * @returns {Promise} 返回新增结果的 Promise
   */
  addStock(data) {
    return doPost('/vehicle/stock', data)
  },

  /**
   * 更新车辆库存
   * @param {Object} data - 库存数据
   * @param {Number} data.id - 库存记录ID
   * @param {Number} data.ownerOrgId - 所属单位ID
   * @param {String} data.ownerOrgName - 所属单位名称
   * @param {Number} data.stockOrgId - 仓储单位ID
   * @param {String} data.stockOrgName - 仓储单位名称
   * @param {Number} data.sbOrgId - 销售单位ID
   * @param {String} data.sbOrgName - 销售单位名称
   * @param {Number} data.stockAmount - 库存成本(元)
   * @param {String} data.vin - 车辆VIN
   * @param {String} data.stockStatus - 库存状态
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateStock(data) {
    const { id, ...updateData } = data
    return doPut(`/vehicle/stock/${id}`, updateData)
  },

  /**
   * 删除车辆库存
   * @param {Number} id - 库存记录ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteStock(id) {
    return doDelete(`/vehicle/stock/${id}`)
  },

  /**
   * 批量删除车辆库存
   * @param {Array} ids - 库存记录ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteStocks(ids) {
    return doPost('/vehicle/stock/batch', { ids })
  },

  /**
   * 更新车辆库存状态
   * @param {Number} id - 库存记录ID
   * @param {String} status - 新状态
   * @returns {Promise} 返回状态更新结果的 Promise
   */
  updateStockStatus(id, status) {
    return doPut(`/vehicle/stock/${id}/status`, { status })
  },

  /**
   * 批量更新车辆库存状态
   * @param {Array} ids - 库存记录ID数组
   * @param {String} status - 新状态
   * @returns {Promise} 返回批量状态更新结果的 Promise
   */
  batchUpdateStockStatus(ids, status) {
    return doPut(`/vehicle/stock/status/batch?ids=${ids.join(',')}&status=${status}`)
  },

  /**
   * 导入车辆库存
   * @param {String} fileKey - 文件路径
   * @returns {Promise} 返回导入结果的 Promise
   */
  importStocks(fileKey) {
    return doPost('/vehicle/stock/import', { fileKey })
  },

  /**
   * 库存调拨
   * @param {Object} data - 请求体数据
   * @param {Array} data.ids - 库存ID数组
   * @param {Number} data.targetOrgId - 目标机构ID
   * @param {String} data.transferType - 调拨类型 (INTERNAL/EXTERNAL)
   * @param {String} data.transferReason - 调拨原因
   * @param {String} [data.remark] - 备注
   * @param {Number} [data.receivableRatio] - 应收比例(外部调拨时必填)
   * @returns {Promise} 返回调拨结果的 Promise
   */
  transferStock(data) {
    return doPut('/vehicle/stock/transfer/outbound', data)
  },

  /**
   * 确认调拨接收
   * @param {Number} stockId - 库存记录ID
   * @returns {Promise} 返回确认结果的 Promise
   */
  confirmTransfer(stockId) {
    return doPut(`/vehicle/stock/${stockId}/transfer/confirm`)
  },

  /**
   * 拒绝调拨接收
   * @param {Number} stockId - 库存记录ID
   * @param {String} rejectReason - 拒绝原因
   * @returns {Promise} 返回拒绝结果的 Promise
   */
  rejectTransfer(stockId, rejectReason) {
    return doPut(`/vehicle/stock/${stockId}/transfer/reject`, { rejectReason })
  }
}

export default stocksApi
