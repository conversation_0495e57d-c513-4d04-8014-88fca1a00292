import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 衍生费用管理 API 服务 - RESTful风格
 */
export const derivativeCostsApi = {
  /**
   * 获取衍生费用列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.orderSn] - 订单号
   * @param {String} [params.customerName] - 客户名称
   * @param {String} [params.customerPhone] - 客户电话
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.series] - 车型系列
   * @param {String} [params.rebateStatus] - 返利状态
   * @param {Number} [params.bizOrgId] - 经办机构ID
   * @param {Number} [params.bizAgentId] - 经办员工ID
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回衍生费用列表的 Promise
   */
  getDerivativeCostsList(params) {
    return doGet('/derivative/costs', params)
  },

  /**
   * 获取衍生费用详情
   * @param {Number} id - 衍生费用记录ID
   * @returns {Promise} 返回衍生费用详情的 Promise
   */
  getDerivativeCostsDetail(id) {
    return doGet(`/derivative/costs/${id}`)
  },

  /**
   * 新增衍生费用记录
   * @param {Object} data - 衍生费用数据
   * @param {Number} data.vehicleOrderId - 新车订单ID
   * @param {Number} data.notaryFee - 公证费(分)
   * @param {Number} data.carefreeIncome - 畅行无忧收入(分)
   * @param {Number} data.extendedWarrantyIncome - 延保收入(分)
   * @param {Number} data.vpsIncome - VPS收入(分)
   * @param {Number} data.preInterest - 预收利息(分)
   * @param {Number} data.licensePlateFee - 牌照费(分)
   * @param {Number} data.tempPlateFee - 临牌费(分)
   * @param {Number} data.deliveryEquipment - 交车装备(分)
   * @param {Number} data.otherIncome - 其他收入(分)
   * @param {String} data.rebateStatus - 返利状态
   * @param {Number} data.bizOrgId - 经办机构ID
   * @param {Number} data.bizAgentId - 经办员工ID
   * @returns {Promise} 返回新增结果的 Promise
   */
  createDerivativeCosts(data) {
    return doPost('/derivative/costs', data)
  },

  /**
   * 更新衍生费用记录
   * @param {Number} id - 衍生费用记录ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateDerivativeCosts(id, data) {
    return doPut(`/derivative/costs/${id}`, data)
  },

  /**
   * 删除衍生费用记录
   * @param {Number} id - 衍生费用记录ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteDerivativeCosts(id) {
    return doDelete(`/derivative/costs/${id}`)
  },

  /**
   * 批量删除衍生费用记录
   * @param {Array} ids - 衍生费用记录ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteDerivativeCosts(ids) {
    return doPost('/derivative/costs/batch-delete', { ids })
  },

  /**
   * 更新返利状态
   * @param {Number} id - 衍生费用记录ID
   * @param {String} status - 返利状态
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateRebateStatus(id, status) {
    return doPut(`/derivative/costs/${id}/rebate-status`, { rebateStatus: status })
  },

  /**
   * 更新确认状态
   * @param {Number} id - 衍生费用记录ID
   * @param {Boolean} confirmed - 确认状态
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateConfirmStatus(id, confirmed) {
    return doPut(`/derivative/costs/${id}/confirm-status`, { confirmed })
  }
}

export default derivativeCostsApi
