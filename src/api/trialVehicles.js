import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

// 模拟数据
const mockData = [
  {
    id: 1,
    vin: 'LSGKF53A8KA123456',
    brand: '深蓝',
    series: 'SL03',
    configName: '515KM 后驱版',
    color: '星河蓝',
    status: 'AVAILABLE', // 可用
    orgId: 1,
    orgName: '北京销售中心',
    purchaseDate: '2024-01-15',
    purchasePrice: 18980000, // 189800元，以分为单位
    mileage: 1250, // 公里数
    lastMaintenanceDate: '2024-11-01',
    nextMaintenanceDate: '2024-12-01',
    insuranceExpireDate: '2025-01-15',
    createTime: '2024-01-15T10:30:00',
    updateTime: '2024-11-20T14:20:00'
  },
  {
    id: 2,
    vin: 'LSGKF53A8KA234567',
    brand: '深蓝',
    series: 'S7',
    configName: '520KM 四驱版',
    color: '珠光白',
    status: 'IN_USE', // 使用中
    orgId: 2,
    orgName: '上海销售中心',
    purchaseDate: '2024-02-20',
    purchasePrice: 21980000, // 219800元
    mileage: 2100,
    lastMaintenanceDate: '2024-10-15',
    nextMaintenanceDate: '2024-11-15',
    insuranceExpireDate: '2025-02-20',
    createTime: '2024-02-20T09:15:00',
    updateTime: '2024-11-18T16:45:00'
  },
  {
    id: 3,
    vin: 'LSGKF53A8KA345678',
    brand: '长安启源',
    series: 'A07',
    configName: '515KM 智享版',
    color: '曜石黑',
    status: 'MAINTENANCE', // 维护中
    orgId: 1,
    orgName: '北京销售中心',
    purchaseDate: '2024-03-10',
    purchasePrice: 16980000, // 169800元
    mileage: 3200,
    lastMaintenanceDate: '2024-11-15',
    nextMaintenanceDate: '2024-12-15',
    insuranceExpireDate: '2025-03-10',
    createTime: '2024-03-10T11:20:00',
    updateTime: '2024-11-15T10:30:00'
  },
  {
    id: 4,
    vin: 'LSGKF53A8KA456789',
    brand: '阿维塔',
    series: '11',
    configName: '90kWh 长续航版',
    color: '天青蓝',
    status: 'RETIRED', // 已退役
    orgId: 3,
    orgName: '广州销售中心',
    purchaseDate: '2023-12-05',
    purchasePrice: 34980000, // 349800元
    mileage: 8500,
    lastMaintenanceDate: '2024-10-01',
    nextMaintenanceDate: '2024-11-01',
    insuranceExpireDate: '2024-12-05',
    createTime: '2023-12-05T14:30:00',
    updateTime: '2024-10-01T09:15:00'
  }
]

/**
 * 试乘试驾车管理 API 服务 - RESTful风格
 */
export const trialVehiclesApi = {
  /**
   * 获取试驾车列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.vin] - VIN码
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.series] - 车型系列
   * @param {String} [params.status] - 车辆状态
   * @param {Number} [params.orgId] - 所属单位ID
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回试驾车列表的 Promise
   */
  getTrialVehiclesList(params) {
    // 临时使用模拟数据进行开发测试
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...mockData]

        // 模拟筛选逻辑
        if (params.status) {
          filteredData = filteredData.filter(item => item.status === params.status)
        }

        if (params.orgId) {
          filteredData = filteredData.filter(item => item.orgId === params.orgId)
        }

        if (params.keywords) {
          const keyword = params.keywords.toLowerCase()
          filteredData = filteredData.filter(item =>
            item.vin.toLowerCase().includes(keyword) ||
            item.brand.toLowerCase().includes(keyword) ||
            item.series.toLowerCase().includes(keyword) ||
            item.configName.toLowerCase().includes(keyword)
          )
        }

        const total = filteredData.length
        const pageSize = params.pageSize || 20
        const pageNum = params.pageNum || 1
        const start = (pageNum - 1) * pageSize
        const end = start + pageSize
        const list = filteredData.slice(start, end)

        resolve({
          code: 200,
          message: '获取成功',
          data: {
            list,
            total,
            pageNum,
            pageSize
          }
        })
      }, 500) // 模拟网络延迟
    })

    // 正式环境使用真实API
    // return doGet('/trial/vehicles', params)
  },

  /**
   * 获取试驾车详情
   * @param {Number} id - 试驾车记录ID
   * @returns {Promise} 返回试驾车详情的 Promise
   */
  getTrialVehicleDetail(id) {
    return doGet(`/trial/vehicles/${id}`)
  },

  /**
   * 新增试驾车记录
   * @param {Object} data - 试驾车数据
   * @param {String} data.vin - VIN码
   * @param {String} data.brand - 车辆品牌
   * @param {String} data.series - 车型系列
   * @param {String} data.configName - 配置名称
   * @param {String} data.color - 车辆颜色
   * @param {Number} data.orgId - 所属单位ID
   * @param {Number} data.purchasePrice - 采购价格(分)
   * @param {String} data.purchaseDate - 采购日期
   * @returns {Promise} 返回新增结果的 Promise
   */
  createTrialVehicle(data) {
    return doPost('/trial/vehicles', data)
  },

  /**
   * 更新试驾车信息
   * @param {Number} id - 试驾车记录ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateTrialVehicle(id, data) {
    return doPut(`/trial/vehicles/${id}`, data)
  },

  /**
   * 批量更新试驾车状态
   * @param {Array} ids - 试驾车ID数组
   * @param {String} status - 新状态
   * @returns {Promise} 返回批量更新结果的 Promise
   */
  batchUpdateStatus(ids, status) {
    return doPut('/trial/vehicles/batch/status', { ids, status })
  },

  /**
   * 删除试驾车记录
   * @param {Number} id - 试驾车记录ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteTrialVehicle(id) {
    return doDelete(`/trial/vehicles/${id}`)
  },

  /**
   * 获取试驾车使用记录
   * @param {Number} vehicleId - 试驾车ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回使用记录的 Promise
   */
  getTrialRecords(vehicleId, params) {
    return doGet(`/trial/vehicles/${vehicleId}/records`, params)
  },

  /**
   * 试驾车出库
   * @param {Number} stockId - 库存ID
   * @param {Object} data - 出库数据
   * @param {Number} data.amount - 出库金额(分)
   * @param {Number} data.customerId - 客户ID
   * @param {String} data.vin - 车架号
   * @param {String} data.customerName - 客户姓名
   * @param {String} [data.remark] - 备注信息
   * @returns {Promise} 返回出库结果的 Promise
   */
  outboundTrialVehicle(stockId, data) {
    return doPut(`/trial/vehicles/${stockId}`, data)
  }
}

export default trialVehiclesApi
