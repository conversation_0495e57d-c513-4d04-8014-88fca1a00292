import { doGet, doPost, doPut, doDelete } from '@/utils/requests'
import { dictList, dictOptions } from '@/mock/dictData'

// 前端维护的字典项列表（这些字典项从前端dictData.js读取）
const FRONTEND_DICT_GROUPS = ['province_city', 'city_district']

/**
 * 获取字典列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回字典列表的Promise
 */
export function getDictList(params) {
  // 总是从后端API获取字典列表，因为需要获取完整的字典组信息
  return doGet('/system/options', params)
}

/**
 * 获取字典详情
 * @param {String} dictCode - 字典编码
 * @returns {Promise} 返回字典详情的Promise
 */
export function getDictDetail(dictCode) {
  // 如果是前端维护的字典组，从本地数据获取
  if (FRONTEND_DICT_GROUPS.includes(dictCode)) {
    const dict = dictList.find(item => item.dict_code === dictCode)
    return Promise.resolve({
      code: 200,
      data: dict || null,
      message: dict ? 'success' : 'not found'
    })
  }
  // 其他字典组从后端API获取
  return doGet(`/system/dict/${dictCode}`)
}

/**
 * 获取字典选项列表
 * @param {String} optionGroup - 字典组编码
 * @returns {Promise} 返回字典选项列表的Promise
 */
export function getDictOptions(optionGroup) {
  // 如果是前端维护的字典组，从本地数据获取
  if (FRONTEND_DICT_GROUPS.includes(optionGroup)) {
    const options = dictOptions[optionGroup] || []
    const transformedData = options.map((option, index) => ({
      id: index + 1,
      optionComment: option.remark || option.optionLabel,
      optionGroup: optionGroup,
      optionGroupName: dictList.find(dict => dict.dict_code === optionGroup)?.dict_name || '',
      optionGroupType: 'CUSTOMIZE',
      optionLabel: option.optionLabel,
      optionOrder: option.sort || 0,
      optionOrgRange: "57,58,59", // 默认值
      optionValue: option.optionValue,
      parent: option.parent // 保留父级关联字段，用于级联字典
    }))

    return Promise.resolve({
      code: 200,
      data: transformedData,
      message: 'SUCCESS'
    })
  }

  // 其他字典组从后端API获取
  return doGet(`/system/options/${optionGroup}`)
}

/**
 * 创建字典
 * @param {Object} dictData - 字典数据
 * @returns {Promise} 返回创建结果的Promise
 */
export function createDict(dictData) {
  // 前端维护的字典组不允许通过API创建
  if (FRONTEND_DICT_GROUPS.includes(dictData.dict_code)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API创建'
    })
  }

  // 其他字典组通过后端API创建
  return doPost('/system/dict', dictData)
}

/**
 * 更新字典
 * @param {Object} dictData - 字典数据
 * @returns {Promise} 返回更新结果的Promise
 */
export function updateDict(dictData) {
  // 前端维护的字典组不允许通过API更新
  if (FRONTEND_DICT_GROUPS.includes(dictData.dict_code)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API更新'
    })
  }

  // 其他字典组通过后端API更新
  return doPut(`/system/dict/${dictData.dict_code}`, dictData)
}

/**
 * 删除字典
 * @param {String} dictCode - 字典编码
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteDict(dictCode) {
  // 前端维护的字典组不允许通过API删除
  if (FRONTEND_DICT_GROUPS.includes(dictCode)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API删除'
    })
  }

  // 其他字典组通过后端API删除
  return doDelete(`/system/dict/${dictCode}`)
}

/**
 * 创建字典项（字典组）
 * @param {Object} dictGroupData - 字典项数据
 * @param {String} dictGroupData.optionGroup - 字典组编码
 * @param {String} dictGroupData.optionGroupName - 字典组名称
 * @param {String} [dictGroupData.optionOrgRange] - 组织范围（可选，集团通用时不需要）
 * @returns {Promise} 返回创建结果的Promise
 */
export function createDictGroup(dictGroupData) {
  // 前端维护的字典组不允许通过API创建
  if (FRONTEND_DICT_GROUPS.includes(dictGroupData.optionGroup)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API创建'
    })
  }

  // 其他字典组通过后端API创建
  return doPost('/system/option/group', dictGroupData)
}

/**
 * 创建字典选项
 * @param {Object} optionData - 选项数据（包含optionGroup等完整信息）
 * @returns {Promise} 返回创建结果的Promise
 */
export function createDictOption(optionData) {
  // 前端维护的字典组不允许通过API创建选项
  if (FRONTEND_DICT_GROUPS.includes(optionData.optionGroup)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API创建选项'
    })
  }

  // 其他字典组通过后端API创建选项
  return doPost('/system/option', optionData)
}

/**
 * 更新字典选项
 * @param {Number} optionId - 选项ID
 * @param {Object} optionData - 选项数据
 * @returns {Promise} 返回更新结果的Promise
 */
export function updateDictOption(optionId, optionData) {
  // 前端维护的字典组不允许通过API更新选项
  if (FRONTEND_DICT_GROUPS.includes(optionData.optionGroup)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API更新选项'
    })
  }

  // 其他字典组通过后端API更新选项
  return doPut(`/system/option/${optionId}`, optionData)
}

/**
 * 删除字典选项
 * @param {Number} optionId - 选项ID
 * @param {String} optionGroup - 字典组编码（用于判断是否为前端维护）
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteDictOption(optionId, optionGroup) {
  // 前端维护的字典组不允许通过API删除选项
  if (optionGroup && FRONTEND_DICT_GROUPS.includes(optionGroup)) {
    return Promise.reject({
      code: 400,
      message: '该字典组由前端维护，不允许通过API删除选项'
    })
  }

  // 其他字典组通过后端API删除选项
  return doDelete(`/system/option/${optionId}`)
}
