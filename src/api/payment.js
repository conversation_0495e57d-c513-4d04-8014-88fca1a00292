import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 付款单管理 API 服务 - RESTful风格
 */
export const paymentApi = {
  /**
   * 获取付款单列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.paymentNo] - 付款单号
   * @param {String} [params.paymentUnit] - 付款单位
   * @param {String} [params.paymentAccount] - 付款账户
   * @param {String} [params.operator] - 经办人
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回付款单列表的 Promise
   */
  getPaymentList(params) {
    return doGet('/financial/payment/bill', params)
  },

  /**
   * 获取付款单详情
   * @param {Number} id - 付款单ID
   * @returns {Promise} 返回付款单详情的 Promise
   */
  getPaymentDetail(id) {
    return doGet(`/financial/payment/bill/${id}`)
  },

  /**
   * 新增付款单
   * @param {Object} data - 付款单数据
   * @param {String} [data.paymentUnit] - 付款单位
   * @param {String} [data.paymentAccount] - 付款账户
   * @param {Number} [data.paymentAmount] - 付款金额（分）
   * @param {Array} [data.details] - 付款明细数组
   * @returns {Promise} 返回新增结果的 Promise
   */
  createPayment(data) {
    return doPost('/financial/payment/bill', data)
  },

  /**
   * 更新付款单
   * @param {Object} data - 付款单数据
   * @param {Number} data.id - 付款单ID
   * @param {String} [data.paymentUnit] - 付款单位
   * @param {String} [data.paymentAccount] - 付款账户
   * @param {Number} [data.paymentAmount] - 付款金额（分）
   * @param {Array} [data.details] - 付款明细数组
   * @returns {Promise} 返回更新结果的 Promise
   */
  updatePayment(data) {
    return doPut(`/financial/payment/bill`, data)
  },

  /**
   * 删除付款单
   * @param {Number} id - 付款单ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deletePayment(id) {
    return doDelete(`/financial/payment/bill/${id}`)
  },

  /**
   * 批量删除付款单
   * @param {Array} ids - 付款单ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeletePayments(ids) {
    return doPost('/financial/payment/bill/batch', { ids })
  },

  /**
   * 导出付款单数据
   * @param {Object} params - 查询参数，同getPaymentList
   * @returns {Promise} 返回导出结果的 Promise
   */
  exportPayments(params) {
    return doGet('/financial/payment/bill/export', params, { responseType: 'blob' })
  },

  /**
   * 导入付款单数据
   * @param {FormData} formData - 包含文件的表单数据
   * @returns {Promise} 返回导入结果的 Promise
   */
  importPayments(formData) {
    return doPost('/financial/payment/bill/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 处理API返回的数据，添加子项明细
   * @param {Array} list - API返回的原始数据列表
   * @returns {Array} 处理后的数据，包含父行和子行
   */
  processPaymentData(list) {
    if (!list || !Array.isArray(list)) {
      return [];
    }

    return list.map(payment => {
      // 创建父行数据
      const parentRow = {
        id: payment.id,
        paymentNo: payment.paymentNo,
        paymentTime: payment.paymentTime,
        paymentUnit: payment.paymentUnit,
        paymentAccount: payment.paymentAccount,
        operator: payment.operator,
        feeAmount: payment.totalAmount || 0, // 总金额
        children: []
      };

      // 添加子行数据（付款明细）
      if (payment.details && payment.details.length > 0) {
        parentRow.children = payment.details.map(detail => ({
          id: `detail-${detail.id}`,
          parentId: payment.id,
          feeName: detail.feeName,
          feeAmount: detail.feeAmount,
          summary: detail.summary,
          isDetail: true // 标记为明细行
        }));
      }

      return parentRow;
    });
  },

  /**
   * 模拟数据 - 用于开发测试
   * @returns {Object} 模拟的付款单数据
   */
  getMockData() {
    const mockData = {
      total: 5,
      list: [
        {
          id: 1,
          paymentNo: 'SK20230001',
          paymentTime: Date.now() - ******** * 5, // 5天前
          paymentUnit: '北京销售中心',
          paymentAccount: '工商银行-1234',
          operator: '张三',
          totalAmount: 350000, // 单位：分
          details: [
            {
              id: 101,
              feeName: '车辆销售款',
              feeAmount: 300000, // 单位：分
              summary: '奔驰C级销售款'
            },
            {
              id: 102,
              feeName: '保险费',
              feeAmount: 30000, // 单位：分
              summary: '车辆保险费'
            },
            {
              id: 103,
              feeName: '上牌费',
              feeAmount: 20000, // 单位：分
              summary: '车辆上牌费用'
            }
          ]
        },
        {
          id: 2,
          paymentNo: 'SK20230002',
          paymentTime: Date.now() - ******** * 3, // 3天前
          paymentUnit: '上海销售中心',
          paymentAccount: '建设银行-5678',
          operator: '李四',
          totalAmount: 420000, // 单位：分
          details: [
            {
              id: 201,
              feeName: '车辆销售款',
              feeAmount: 380000, // 单位：分
              summary: '宝马3系销售款'
            },
            {
              id: 202,
              feeName: '保险费',
              feeAmount: 40000, // 单位：分
              summary: '车辆保险费'
            }
          ]
        },
        {
          id: 3,
          paymentNo: 'SK20230003',
          paymentTime: Date.now() - ******** * 2, // 2天前
          paymentUnit: '广州销售中心',
          paymentAccount: '招商银行-9012',
          operator: '王五',
          totalAmount: 380000, // 单位：分
          details: [
            {
              id: 301,
              feeName: '车辆销售款',
              feeAmount: 350000, // 单位：分
              summary: '奥迪A4销售款'
            },
            {
              id: 302,
              feeName: '上牌费',
              feeAmount: 30000, // 单位：分
              summary: '车辆上牌费用'
            }
          ]
        },
        {
          id: 4,
          paymentNo: 'SK20230004',
          paymentTime: Date.now() - ********, // 1天前
          paymentUnit: '深圳销售中心',
          paymentAccount: '中国银行-3456',
          operator: '赵六',
          totalAmount: 520000, // 单位：分
          details: [
            {
              id: 401,
              feeName: '车辆销售款',
              feeAmount: 450000, // 单位：分
              summary: '雷克萨斯ES销售款'
            },
            {
              id: 402,
              feeName: '保险费',
              feeAmount: 50000, // 单位：分
              summary: '车辆保险费'
            },
            {
              id: 403,
              feeName: '上牌费',
              feeAmount: 20000, // 单位：分
              summary: '车辆上牌费用'
            }
          ]
        },
        {
          id: 5,
          paymentNo: 'SK20230005',
          paymentTime: Date.now(), // 今天
          paymentUnit: '成都销售中心',
          paymentAccount: '农业银行-7890',
          operator: '钱七',
          totalAmount: 450000, // 单位：分
          details: [
            {
              id: 501,
              feeName: '车辆销售款',
              feeAmount: 400000, // 单位：分
              summary: '本田雅阁销售款'
            },
            {
              id: 502,
              feeName: '保险费',
              feeAmount: 30000, // 单位：分
              summary: '车辆保险费'
            },
            {
              id: 503,
              feeName: '上牌费',
              feeAmount: 20000, // 单位：分
              summary: '车辆上牌费用'
            }
          ]
        }
      ]
    };

    return mockData;
  }
};

export default paymentApi;
