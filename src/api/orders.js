import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 销售订单 API 服务
 */
export const ordersApi = {
  /**
   * 获取销售订单列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.orderNo] - 订单号
   * @param {String} [params.saleOrg] - 销售单位
   * @param {String} [params.vehicleCategory] - 车辆类别
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回销售订单列表的 Promise
   */
  getOrdersList(params) {
    return doGet('/sales/orders', params)
  },

  /**
   * 获取销售订单详情
   * @param {Number} id - 订单 ID
   * @returns {Promise} 返回销售订单详情的 Promise
   */
  getOrderDetail(id) {
    return doGet(`/sales/orders/${id}`)
  },

  /**
   * 新增销售订单
   * @param {Object} data - 订单数据
   * @returns {Promise} 返回新增结果的 Promise
   */
  addOrder(data) {
    return doPost('/sales/orders', data)
  },

  /**
   * 更新销售订单
   * @param {Object} data - 订单数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateOrder(data) {
    const { id, ...updateData } = data
    return doPut(`/sales/orders/${id}`, updateData)
  },

  /**
   * 删除销售订单
   * @param {Number} id - 订单 ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteOrder(id) {
    return doDelete(`/sales/orders/${id}`)
  },

  /**
   * 批量删除销售订单
   * @param {Array} ids - 订单 ID 数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteOrders(ids) {
    return doPost('/sales/orders/batch', { ids })
  },

  /**
   * 导入销售订单
   * @param {String} fileKey - 文件路径
   * @returns {Promise} 返回导入结果的 Promise
   */
  importOrders(fileKey) {
    return doPost('/sales/orders/import', { fileKey })
  }
}

export default ordersApi
