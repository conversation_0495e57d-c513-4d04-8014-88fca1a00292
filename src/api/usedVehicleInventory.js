import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 二手车库存 API 服务 - RESTful风格
 */
export const usedVehicleInventoryApi = {
  /**
   * 获取二手车库存列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.vehicleId] - 车牌号
   * @param {String} [params.vin] - 车辆VIN
   * @param {String} [params.brand] - 车辆品牌
   * @param {String} [params.series] - 车型系列
   * @param {String} [params.stockStatus] - 库存状态
   * @param {Number} [params.stockOrgId] - 库存机构ID
   * @param {String} [params.vehicleSource] - 车辆来源
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回二手车库存列表的 Promise
   */
  getInventoryList(params) {
    return doGet('/used/vehicle/inventory', params)
  },

  /**
   * 获取二手车库存详情
   * @param {Number} id - 库存记录ID
   * @returns {Promise} 返回二手车库存详情的 Promise
   */
  getInventoryDetail(id) {
    return doGet(`/used/vehicle/inventory/${id}`)
  },

  /**
   * 获取二手车库存详情(通过VIN)
   * @param {String} vin - 车辆VIN
   * @returns {Promise} 返回二手车库存详情的 Promise
   */
  getInventoryByVin(vin) {
    return doGet(`/used/vehicle/inventory/vin/${vin}`)
  },

  /**
   * 获取二手车库存详情(通过车牌号)
   * @param {String} vehicleId - 车牌号
   * @returns {Promise} 返回二手车库存详情的 Promise
   */
  getInventoryByVehicleId(vehicleId) {
    return doGet(`/used/vehicle/inventory/vehicle/${vehicleId}`)
  },

  /**
   * 新增二手车库存
   * @param {Object} data - 库存数据
   * @param {String} data.vehicleId - 车牌号
   * @param {String} data.vin - 车辆VIN
   * @param {String} data.brand - 车辆品牌
   * @param {String} data.series - 车型系列
   * @param {String} data.vehicleType - 车辆类型
   * @param {String} data.color - 车辆颜色
   * @param {Number} data.mileage - 里程数
   * @param {String} data.registrationDate - 注册日期
   * @param {String} data.insuranceDeadline - 保险到期日
   * @param {String} data.checkDeadline - 年检到期日
   * @param {Number} data.inboundAmount - 入库金额(分)
   * @param {String} data.inboundPaymentMethod - 入库付款方式
   * @param {String} data.inboundDate - 入库日期
   * @param {Number} data.inboundOrgId - 入库机构ID
   * @param {Number} data.inboundCustomerId - 入库客户ID
   * @param {Number} data.inboundAgentId - 入库经办人ID
   * @param {Number} data.stockOrgId - 库存机构ID
   * @param {String} data.stockStatus - 库存状态
   * @param {String} data.vehicleSource - 车辆来源
   * @param {String} data.leadsSource - 线索来源
   * @param {String} data.leadsProviderName - 线索提供人姓名
   * @param {String} data.leadsProviderPhone - 线索提供人电话
   * @param {Number} data.refOrderId - 关联订单ID
   * @param {String} data.remark - 备注
   * @returns {Promise} 返回新增结果的 Promise
   */
  createInventory(data) {
    return doPost('/used/vehicle/inventory', data)
  },

  /**
   * 更新二手车库存
   * @param {Number} id - 库存记录ID
   * @param {Object} data - 库存数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateInventory(id, data) {
    return doPut(`/used/vehicle/inventory/${id}`, data)
  },

  /**
   * 删除二手车库存
   * @param {Number} id - 库存记录ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteInventory(id) {
    return doDelete(`/used/vehicle/inventory/${id}`)
  },

  /**
   * 批量删除二手车库存
   * @param {Array} ids - 库存记录ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteInventory(ids) {
    return doPost('/used/vehicle/inventory/batch', { ids })
  },

  /**
   * 更新二手车库存状态
   * @param {Number} id - 库存记录ID
   * @param {String} status - 新状态
   * @returns {Promise} 返回状态更新结果的 Promise
   */
  updateInventoryStatus(id, status) {
    return doPut(`/used/vehicle/inventory/${id}/status`, { status })
  },

  /**
   * 车辆出库
   * @param {Number} id - 库存记录ID
   * @param {Object} data - 出库数据
   * @param {Number} data.outboundAmount - 出库金额(分)
   * @param {String} data.outboundPaymentMethod - 出库付款方式
   * @param {String} data.outboundDate - 出库日期
   * @param {Number} data.outboundOrgId - 出库机构ID
   * @param {Number} data.outboundCustomerId - 出库客户ID
   * @param {Number} data.outboundAgentId - 出库经办人ID
   * @param {String} data.outboundMethod - 出库方式
   * @returns {Promise} 返回出库结果的 Promise
   */
  outboundVehicle(id, data) {
    return doPut(`/used/vehicle/inventory/${id}/outbound`, data)
  },

  /**
   * 导入二手车库存
   * @param {String} fileKey - 文件路径
   * @returns {Promise} 返回导入结果的 Promise
   */
  importInventory(fileKey) {
    return doPost('/used/vehicle/inventory/import', { fileKey })
  },

  /**
   * 导出二手车库存
   * @param {Object} params - 查询参数，同getInventoryList
   * @returns {Promise} 返回导出结果的 Promise
   */
  exportInventory(params) {
    return doGet('/used/vehicle/inventory/export', params, { responseType: 'blob' })
  }
}

export default usedVehicleInventoryApi
