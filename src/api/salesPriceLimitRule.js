import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 销售限价规则API
 */
const salesPriceLimitRuleApi = {
  /**
   * 创建销售限价规则
   * @param {Object} data - 规则数据
   * @param {String} data.ruleName - 规则名称
   * @param {Number} data.effectiveTime - 生效时间（时间戳）
   * @param {Number} data.expiryTime - 失效时间（时间戳）
   * @param {String} data.ruleType - 规则类型（sku/vin）
   * @param {Number} data.priceLimit - 销售限价（单位：分）
   * @param {String} data.status - 状态（draft/active）
   * @param {String} data.effectiveOrg - 生效机构（机构ID用逗号拼接，空字符串表示全部机构）
   * @param {String} data.effectiveOrgNames - 生效机构名称（机构名称用逗号拼接，冗余存储）
   * @param {String} data.ruleDetails - 规则明细（JSON字符串）
   * @returns {Promise}
   */
  createRule(data) {
    return doPost('/operation/sales/price/limit/rule', data)
  },

  /**
   * 获取销售限价规则列表
   * @param {Object} params - 查询参数
   * @param {Number} [params.page] - 页码
   * @param {Number} [params.size] - 每页条数
   * @param {String} [params.ruleName] - 规则名称
   * @param {String} [params.ruleType] - 规则类型
   * @param {String} [params.status] - 状态
   * @returns {Promise}
   */
  getRuleList(params) {
    return doGet('/operation/sales/price/limit/rule', params)
  },

  /**
   * 获取销售限价规则详情
   * @param {string|number} id - 规则ID
   * @returns {Promise}
   */
  getRuleDetail(id) {
    return doGet(`/operation/sales/price/limit/rule/${id}`)
  },

  /**
   * 更新销售限价规则
   * @param {string|number} id - 规则ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateRule(id, data) {
    return doPut(`/operation/sales/price/limit/rule/${id}`, data)
  },

  /**
   * 删除销售限价规则
   * @param {string|number} id - 规则ID
   * @returns {Promise}
   */
  deleteRule(id) {
    return doDelete(`/operation/sales/price/limit/rule/${id}`)
  },

  /**
   * 批量删除销售限价规则
   * @param {Array} ids - 规则ID数组
   * @returns {Promise}
   */
  batchDeleteRules(ids) {
    return doPost('/operation/sales/price/limit/rule/batch', { ids })
  },

  /**
   * 启用/禁用销售限价规则
   * @param {string|number} id - 规则ID
   * @param {string} status - 状态：active/inactive
   * @returns {Promise}
   */
  updateRuleStatus(id, status) {
    return doPut(`/operation/sales/price/limit/rule/${id}/status`, { status })
  },

  /**
   * 根据SKU和销售机构查询限价
   * @param {string|number} skuId - SKU ID
   * @param {string|number} orgId - 销售机构ID
   * @returns {Promise}
   */
  getPriceLimitBySku(skuId, orgId) {
    return doGet(`/operation/sales/price/limit/rule/sku/${skuId}`, { orgId })
  },

  /**
   * 根据VIN和销售机构查询限价
   * @param {string} vin - VIN码
   * @param {string|number} orgId - 销售机构ID
   * @returns {Promise}
   */
  getPriceLimitByVin(vin, orgId) {
    return doGet(`/operation/sales/price/limit/rule/vin/${vin}`, { orgId })
  }
}

export default salesPriceLimitRuleApi
