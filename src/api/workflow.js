import { doGet, doPost } from '@/utils/requests'
import { workflowStats } from '@/mock/workflowData'

// 判断是否为开发环境
const isDev = true //import.meta.env.MODE === 'development'

/**
 * 工作流程 API 服务
 */
export const workflowApi = {
  /**
   * 获取我的待办任务
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回待办任务列表的 Promise
   */
  getMyTasks(params) {
    if (isDev) {
      // 开发环境使用模拟数据
      return Promise.resolve({
        code: 200,
        data: {
          list: myTasksList,
          total: myTasksList.length
        },
        message: 'success'
      })
    }
    return doGet('/workflow/tasks/my', params)
  },

  /**
   * 获取我发起的流程
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回我发起的流程列表的 Promise
   */
  getMyInitiated(params) {
    if (isDev) {
      // 开发环境使用模拟数据
      return Promise.resolve({
        code: 200,
        data: {
          list: myInitiatedList,
          total: myInitiatedList.length
        },
        message: 'success'
      })
    }
    return doGet('/workflow/process/initiated', params)
  },

  /**
   * 获取我处理的流程
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回我处理的流程列表的 Promise
   */
  getMyProcessed(params) {
    if (isDev) {
      // 开发环境使用模拟数据
      return Promise.resolve({
        code: 200,
        data: {
          list: myProcessedList,
          total: myProcessedList.length
        },
        message: 'success'
      })
    }
    return doGet('/workflow/process/processed', params)
  },

  /**
   * 获取抄送我的流程
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回抄送我的流程列表的 Promise
   */
  getMyCopied(params) {
    if (isDev) {
      // 开发环境使用模拟数据
      return Promise.resolve({
        code: 200,
        data: {
          list: myCopiedList,
          total: myCopiedList.length
        },
        message: 'success'
      })
    }
    return doGet('/workflow/process/copied', params)
  },

  /**
   * 获取工作流程统计数据
   * @returns {Promise} 返回工作流程统计数据的 Promise
   */
  getWorkflowStats() {
    if (isDev) {
      // 开发环境使用模拟数据
      return Promise.resolve({
        code: 200,
        data: workflowStats,
        message: 'success'
      })
    }
    return doGet('/workflow/stats')
  },

  /**
   * 发起流程
   * @param {Object} data - 流程数据
   * @returns {Promise} 返回发起结果的 Promise
   */
  initiateProcess(data) {
    return doPost('/workflow/process/initiate', data)
  },

  /**
   * 处理任务
   * @param {Number} taskId - 任务ID
   * @param {Object} data - 处理数据
   * @returns {Promise} 返回处理结果的 Promise
   */
  processTask(taskId, data) {
    return doPost(`/workflow/task/${taskId}/process`, data)
  },

  /**
   * 委托任务
   * @param {Number} taskId - 任务ID
   * @param {Object} data - 委托数据
   * @returns {Promise} 返回委托结果的 Promise
   */
  delegateTask(taskId, data) {
    return doPost(`/workflow/task/${taskId}/delegate`, data)
  }
}

export default workflowApi
