import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 启票 API 服务
 */
export const startBillApi = {
  /**
   * 获取启票列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.batchCode] - 批次代码
   * @param {String} [params.orgName] - 单位名称
   * @param {String} [params.creatorName] - 经办人
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {Number} [params.minAmount] - 最小金额（分）
   * @param {Number} [params.maxAmount] - 最大金额（分）
   * @returns {Promise} 返回启票列表的 Promise
   */
  getStartBillList(params) {
    return doGet('/vehicle/start/bill', params)
  },

  /**
   * 获取启票详情
   * @param {Number} id - 启票 ID
   * @returns {Promise} 返回启票详情的 Promise
   */
  getStartBillDetail(id) {
    return doGet(`/vehicle/start/bill/${id}`)
  },

  /**
   * 新增启票
   * @param {Object} data - 启票数据
   * @param {String} data.batchCode - 批次代码
   * @param {String} data.orgName - 单位名称
   * @param {String} data.creatorName - 经办人
   * @param {Number} data.itemCount - 数量
   * @param {Number} data.totalPriceCent - 金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回新增结果的 Promise
   */
  addStartBill(data) {
    return doPost('/vehicle/start/bill', data)
  },

  /**
   * 更新启票
   * @param {Object} data - 启票数据
   * @param {Number} data.id - 启票 ID
   * @param {String} data.batchCode - 批次代码
   * @param {String} data.orgName - 单位名称
   * @param {String} data.creatorName - 经办人
   * @param {Number} data.itemCount - 数量
   * @param {Number} data.totalPriceCent - 金额（分）
   * @param {String} [data.remark] - 备注
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateStartBill(data) {
    const { id, ...updateData } = data
    return doPut(`/vehicle/start/bill/${id}`, updateData)
  },

  /**
   * 删除启票
   * @param {Number} id - 启票 ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteStartBill(id) {
    return doDelete(`/vehicle/start/bill/${id}`)
  },

  /**
   * 批量删除启票
   * @param {Array} ids - 启票 ID 数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteStartBill(ids) {
    return doPost('/vehicle/start/bill/batch', { ids })
  },

  /**
   * 导入启票
   * @param {String|Array} fileKey - 单个文件路径或文件路径数组
   * @returns {Promise} 返回导入结果的 Promise
   */
  importStartBill(fileKey) {
    // 如果是数组，则使用 fileKeys 字段；如果是字符串，则使用 fileKey 字段
    const requestBody = Array.isArray(fileKey)
      ? { fileKeys: fileKey }
      : { fileKey };

    return doPost('/vehicle/starting/bill/import-and-merge', requestBody)
  },

  /**
   * 启票完成
   * @param {Array} ids - 启票ID数组
   * @returns {Promise} 返回启票完成结果的 Promise
   */
  completeStartBill(ids) {
    return doPut('/vehicle/start/bill/done', ids)
  }
}

export default startBillApi
