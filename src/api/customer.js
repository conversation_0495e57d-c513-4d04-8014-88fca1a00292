import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 客户管理 API 服务
 */
export const customerApi = {
  /**
   * 获取客户列表
   * @param {Object} params - 查询参数
   * @param {Number} params.pageNum - 页码
   * @param {Number} params.pageSize - 每页条数
   * @param {String} [params.customerName] - 客户名称
   * @param {String} [params.customerType] - 客户类型
   * @param {String} [params.orgName] - 所属单位
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回客户列表的 Promise
   */
  getCustomerList(params) {
    return doGet('/crm/customer', params)
  },

  /**
   * 获取客户详情
   * @param {Number} id - 客户 ID
   * @returns {Promise} 返回客户详情的 Promise
   */
  getCustomerDetail(id) {
    return doGet(`/crm/customer/${id}`)
  },

  /**
   * 新增客户
   * @param {Object} data - 客户数据
   * @returns {Promise} 返回新增结果的 Promise
   */
  addCustomer(data) {
    return doPost('/crm/customer', data)
  },

  /**
   * 更新客户
   * @param {Object} data - 客户数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateCustomer(data) {
    const { id, ...updateData } = data
    return doPut(`/crm/customer/${id}`, updateData)
  },

  /**
   * 删除客户
   * @param {Number} id - 客户 ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteCustomer(id) {
    return doDelete(`/crm/customer/${id}`)
  },

  /**
   * 批量删除客户
   * @param {Array} ids - 客户 ID 数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteCustomers(ids) {
    return doPost('/crm/customer/batch', { ids })
  },

  /**
   * 导入客户
   * @param {String} fileKey - 文件路径
   * @returns {Promise} 返回导入结果的 Promise
   */
  importCustomers(fileKey) {
    return doPost('/crm/customer/import', { fileKey })
  },

  /**
   * 根据客户信息查询客户ID
   * @param {Object} params - 查询参数
   * @param {String} params.customerName - 客户名称
   * @param {String} params.customerPhone - 联系电话
   * @param {String} params.customerType - 客户类型
   * @returns {Promise} 返回客户信息的 Promise
   */
  getCustomerId(params) {
    return doGet('/crm/customer/id', params)
  }
}

export default customerApi
