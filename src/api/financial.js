import { doGet } from '@/utils/requests'

/**
 * 财务统计 API 服务
 */
export const financialApi = {
  /**
   * 获取财务统计数据
   * @param {Object} params - 查询参数
   * @param {String} [params.date_scope] - 日期范围，如：this_month, last_month, this_year等
   * @param {String} [params.orgId] - 机构ID
   * @returns {Promise} 返回财务统计数据的 Promise
   */
  getFinancialStats(params) {
    return doGet('/financial/stat/rp', params)
  },

  /**
   * 获取机构维度收支总额数据
   * @param {Object} params - 查询参数
   * @param {String} [params.date_scope] - 日期范围，如：this_month, last_month, this_year等
   * @param {String} [params.start_date] - 自定义开始日期（当date_scope为custom时使用）
   * @param {String} [params.end_date] - 自定义结束日期（当date_scope为custom时使用）
   * @returns {Promise} 返回机构维度收支总额数据的 Promise
   */
  getOrgIncomeExpenseData(params) {
    return doGet('/financial/stat/org/income-expense', params)
  },

  /**
   * 获取机构维度应收应付数据
   * @param {Object} params - 查询参数
   * @param {String} [params.date_scope] - 日期范围，如：this_month, last_month, this_year等
   * @param {String} [params.start_date] - 自定义开始日期（当date_scope为custom时使用）
   * @param {String} [params.end_date] - 自定义结束日期（当date_scope为custom时使用）
   * @returns {Promise} 返回机构维度应收应付数据的 Promise
   */
  getOrgReceivablePayableData(params) {
    return doGet('/financial/stat/org/receivable-payable', params)
  },

  /**
   * 获取应收应付趋势数据
   * @param {Object} params - 查询参数
   * @param {String} [params.date_scope] - 日期范围，如：this_month, last_month, this_year等
   * @param {String} [params.start_date] - 自定义开始日期（当date_scope为custom时使用）
   * @param {String} [params.end_date] - 自定义结束日期（当date_scope为custom时使用）
   * @param {String} [params.orgId] - 机构ID
   * @returns {Promise} 返回应收应付趋势数据的 Promise
   */
  getReceivablePayableTrend(params) {
    return doGet('/financial/stat/trend/receivable-payable', params)
  },

  /**
   * 获取收款付款趋势数据
   * @param {Object} params - 查询参数
   * @param {String} [params.date_scope] - 日期范围，如：this_month, last_month, this_year等
   * @param {String} [params.start_date] - 自定义开始日期（当date_scope为custom时使用）
   * @param {String} [params.end_date] - 自定义结束日期（当date_scope为custom时使用）
   * @param {String} [params.orgId] - 机构ID
   * @returns {Promise} 返回收款付款趋势数据的 Promise
   */
  getReceptPaymentTrend(params) {
    return doGet('/financial/stat/trend/recept-payment', params)
  }
}

export default financialApi
