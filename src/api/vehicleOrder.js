import { doGet, doPost, doPut, doDelete } from '@/utils/requests'
import messages from '@/utils/messages'
import { convertNumberToChinese } from '@/utils/money'

/**
 * 车辆销售订单 API 服务 - RESTful风格
 */
export const vehicleOrderApi = {
  /**
   * 获取销售订单列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.orderNo] - 订单号
   * @param {String} [params.customerName] - 客户名称
   * @param {Number} [params.salesOrgId] - 销售单位ID
   * @param {Number} [params.deliveryOrgId] - 交付单位ID
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回销售订单列表的 Promise
   */
  getOrderList(params) {
    return doGet('/vehicle/order', params)
  },

  /**
   * 获取销售订单详情
   * @param {Number} id - 订单 ID
   * @returns {Promise} 返回销售订单详情的 Promise
   */
  getOrderDetail(id) {
    return doGet(`/vehicle/order/${id}`)
  },

  /**
   * 新增销售订单
   * @param {Object} data - 订单数据
   * @param {Number} data.customerId - 客户ID，必填
   * @param {Number} data.salesOrgId - 销售单位ID，必填
   * @param {Number} data.salesAgentId - 销售顾问ID，必填
   * @param {Number} data.salesLeaderId - 销售主管ID，必填
   * @param {Number} data.skuId - 车辆SKU ID，必填
   * @param {Number} data.deliveryDate - 交付日期，必填，时间戳
   * @param {Number} data.deliveryOrgId - 交付单位ID，必填
   * @param {Number} data.sbAmount - 启票总价（分），必填
   * @param {Number} data.salesAmount - 销售总价（分），必填，默认为sbAmount加价35%
   * @param {Number} data.discountAmount - 优惠金额（分），必填，默认值0

   * @param {Number} data.dealAmount - 成交总价（分），必填，默认值salesAmount - discountAmount
   * @param {String} data.dealAmountCn - 成交总价大写，必填
   * @param {Number} data.grossProfitAmount - 毛利润（分），必填，自动计算
   * @param {Number} data.grossProfitRate - 毛利率，必填，自动计算
   * @param {String} data.paymentMethod - 付款方式（全款FULL/贷款LOAN），默认选中全款
   * @param {String} [data.loanChannel] - 贷款渠道，付款方式为贷款时必填
   * @param {Number} [data.loanAmount] - 贷款金额（分），付款方式为贷款时必填
   * @param {Number} [data.loanInitialAmount] - 首付金额（分），付款方式为贷款时必填
   * @param {Number} [data.loanInitialRatio] - 首付比例，付款方式为贷款时自动计算
   * @param {String} [data.usedVehicleId] - 二手车车牌号，选填
   * @param {String} [data.usedVehicleVin] - 二手车VIN，选填，二手车车牌号不为空时必填
   * @param {Number} [data.usedVehicleAmount] - 二手车置换金额（分），选填，默认值0
   * @param {Number} [data.usedVehicleDiscountAmount] - 二手车置换补贴（分），选填，默认值0
   * @param {String} [data.usedVehicleBound] - 二手车品牌，选填
   * @param {String} [data.usedVehicleModel] - 二手车车型，选填
   * @param {String} [data.usedVehicleColor] - 二手车颜色，选填
   * @returns {Promise} 返回新增结果的 Promise
   */
  createOrder(data) {
    // 处理必填字段的默认值
    const orderData = {
      ...data,
      discountAmount: data.discountAmount || 0,
      paymentMethod: data.paymentMethod || 'FULL',
    }

    // 如果没有提供成交总价，则自动计算
    if (!orderData.dealAmount) {
      orderData.dealAmount = Math.max(0, orderData.salesAmount - orderData.discountAmount)
    }

    // 如果没有提供成交总价大写，则自动计算
    if (!orderData.dealAmountCn) {
      // 将分转换为元进行转换
      const dealAmountYuan = orderData.dealAmount / 100
      orderData.dealAmountCn = convertNumberToChinese(dealAmountYuan)
    }

    // 计算毛利润和毛利率
    if (!orderData.grossProfitAmount) {
      // 毛利润 = 成交总价 - 启票总价（销售费用字段已移除）
      orderData.grossProfitAmount = orderData.dealAmount - orderData.sbAmount
    }

    if (!orderData.grossProfitRate) {
      // 毛利率 = 毛利润 / 启票总价 * 100%
      if (orderData.sbAmount > 0) {
        const rate = (orderData.grossProfitAmount / orderData.sbAmount) * 100
        // 如果毛利率为负，则显示为0
        orderData.grossProfitRate = rate < 0 ? 0 : parseFloat(rate.toFixed(2))
      } else {
        orderData.grossProfitRate = 0
      }
    }

    // 处理贷款相关字段
    if (orderData.paymentMethod === 'LOAN') {
      // 如果没有提供首付比例，则自动计算
      if (!orderData.loanInitialRatio && orderData.loanInitialAmount && orderData.dealAmount) {
        orderData.loanInitialRatio = parseFloat(((orderData.loanInitialAmount / orderData.dealAmount) * 100).toFixed(2))
      }
    }

    // 处理二手车相关字段的默认值
    if (orderData.usedVehicleId) {
      orderData.usedVehicleAmount = orderData.usedVehicleAmount || 0
      orderData.usedVehicleDiscountAmount = orderData.usedVehicleDiscountAmount || 0
    }

    return doPost('/vehicle/order', orderData)
  },

  /**
   * 更新销售订单
   * @param {Number} id - 订单ID
   * @param {Object} data - 订单数据，字段同createOrder
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateOrder(id, data) {
    return doPut(`/vehicle/order/${id}`, data)
  },

  /**
   * 删除销售订单
   * @param {Number} id - 订单 ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteOrder(id) {
    return doDelete(`/vehicle/order/${id}`)
  },

  /**
   * 批量删除销售订单
   * @param {Array} ids - 订单 ID 数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteOrders(ids) {
    return doPost('/vehicle/order/batch', { ids })
  },

  /**
   * 获取订单状态列表
   * @returns {Promise} 返回订单状态列表的 Promise
   */
  getOrderStatusList() {
    return doGet('/vehicle/order/status')
  },

  /**
   * 更新订单状态
   * @param {Number} id - 订单ID
   * @param {String} status - 新状态
   * @returns {Promise} 返回状态更新结果的 Promise
   */
  updateOrderStatus(id, status) {
    return doPut(`/vehicle/order/${id}/status`, { status })
  },

  /**
   * 导出订单数据
   * @param {Object} params - 查询参数，同getOrderList
   * @returns {Promise} 返回导出结果的 Promise
   */
  exportOrders(params) {
    return doGet('/vehicle/order/export', params, { responseType: 'blob' })
  },

  /**
   * 导入订单数据
   * @param {FormData} formData - 包含文件的表单数据
   * @returns {Promise} 返回导入结果的 Promise
   */
  importOrders(formData) {
    return doPost('/vehicle/order/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取定金订单列表
   * @param {Object} params - 查询参数，同getOrderList
   * @returns {Promise} 返回定金订单列表的 Promise
   */
  getDepositOrderList(params) {
    return doGet('/vehicle/order', params)
  },

  /**
   * 获取定金订单详情
   * @param {Number} id - 定金订单ID
   * @returns {Promise} 返回定金订单详情的 Promise
   */
  getDepositOrderDetail(id) {
    return doGet(`/vehicle/order/${id}`)
  },

  /**
   * 新增定金订单
   * @param {Object} data - 定金订单数据
   * @param {Number} data.customerId - 客户ID，必填
   * @param {Number} data.salesOrgId - 销售单位ID，必填
   * @param {Number} data.salesAgentId - 销售顾问ID，必填
   * @param {Number} data.depositAmount - 定金金额（分），必填
   * @param {Number} data.depositDate - 定金日期，必填，时间戳
   * @param {String} data.depositStatus - 定金状态，必填
   * @param {Boolean} data.depositDeductible - 是否可抵扣，必填
   * @param {String} [data.intentionModel] - 意向车型，选填
   * @param {Number} [data.expectedDeliveryDate] - 预计交付日期，选填，时间戳
   * @param {String} [data.paymentRemark] - 备注，选填
   * @returns {Promise} 返回新增结果的 Promise
   */
  createDepositOrder(data) {
    // 处理必填字段的默认值
    const orderData = {
      ...data,
      orderType: 'deposit' // 标记为定金订单
    }

    return doPost('/vehicle/order', orderData)
  },

  /**
   * 更新定金订单
   * @param {Number} id - 定金订单ID
   * @param {Object} data - 定金订单数据，字段同createDepositOrder
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateDepositOrder(id, data) {
    return doPut(`/vehicle/order/${id}`, data)
  },

  /**
   * 删除定金订单
   * @param {Number} id - 定金订单ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteDepositOrder(id) {
    return doDelete(`/vehicle/deposit/order/${id}`)
  },

  /**
   * 将定金订单转为销售订单
   * @param {Number} id - 定金订单ID
   * @param {Object} data - 销售订单数据
   * @returns {Promise} 返回转换结果的 Promise
   */
  convertDepositToOrder(id, data) {
    return doPost(`/vehicle/deposit/order/${id}/convert`, data)
  },

  /**
   * 根据客户ID获取定金订单数据
   * @param {Number} customerId - 客户ID
   * @returns {Promise} 返回客户定金订单数据的 Promise
   */
  getCustomerDepositOrders(customerId) {
    return doGet(`/vehicle/order/deposit/${customerId}`)
  },

  /**
   * 车辆出库专用更新接口
   * @param {Object} data - 出库数据
   * @param {Number} data.orderId - 订单ID
   * @param {Number} data.outboundId - 出库单ID
   * @param {String} [data.hasDerivativeIncome] - 是否有衍生收入，值为"YES"或"NO"
   * @param {Number} [data.loanRebateReceivableAmount] - 应收-机构-分期返利（分）
   * @param {Number} [data.exclusiveDiscountReceivableAmount] - 应收-厂家-专项优惠（分）
   * @param {Number} [data.usedVehicleDiscountReceivableAmount] - 应收-厂家-置换补贴（分）
   * @param {Number} [data.notaryFee] - 公证费（分）
   * @param {Number} [data.carefreeIncome] - 畅行无忧收入（分）
   * @param {Number} [data.extendedWarrantyIncome] - 延保收入（分）
   * @param {Number} [data.vpsIncome] - VPS收入（分）
   * @param {Number} [data.preInterest] - 前置利息（分）
   * @param {Number} [data.licensePlateFee] - 挂牌费（分）
   * @param {Number} [data.tempPlateFee] - 临牌费（分）
   * @param {Number} [data.deliveryEquipment] - 外卖装具收入（分）
   * @param {Number} [data.otherIncome] - 其他收入（分）
   * @param {String} data.vin - VIN码
   * @param {String} [data.remark] - 备注信息
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateVehicleOutbound(data) {
    return doPut('/vehicle/outbound', data)
  }
}

export default vehicleOrderApi
