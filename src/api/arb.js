import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

/**
 * 应收账款账单 API 服务 - RESTful风格
 */
export const arbApi = {
  /**
   * 获取应收账款账单列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码
   * @param {Number} params.size - 每页条数
   * @param {String} [params.orderSn] - 订单号
   * @param {String} [params.customerName] - 客户名称
   * @param {String} [params.salesOrgName] - 销售单位
   * @param {String} [params.startDate] - 开始日期
   * @param {String} [params.endDate] - 结束日期
   * @param {String} [params.keywords] - 关键词搜索
   * @returns {Promise} 返回应收账款账单列表的 Promise
   */
  getArbList(params) {
    return doGet('/financial/receivable/bill', params)
  },

  /**
   * 获取应收账款账单详情
   * @param {Number} id - 应收账款账单ID
   * @returns {Promise} 返回应收账款账单详情的 Promise
   */
  getArbDetail(id) {
    return doGet(`/financial/receivable/bill/${id}`)
  },

  /**
   * 新增应收账款账单
   * @param {Object} data - 应收账款账单数据
   * @returns {Promise} 返回新增结果的 Promise
   */
  createArb(data) {
    return doPost('/financial/receivable/bill', data)
  },

  /**
   * 更新应收账款账单
   * @param {Number} id - 应收账款账单ID
   * @param {Object} data - 应收账款账单数据
   * @returns {Promise} 返回更新结果的 Promise
   */
  updateArb(id, data) {
    return doPut(`/financial/receivable/bill`, data)
  },

  /**
   * 删除应收账款账单
   * @param {Number} id - 应收账款账单ID
   * @returns {Promise} 返回删除结果的 Promise
   */
  deleteArb(id) {
    return doDelete(`/financial/receivable/bill/${id}`)
  },

  /**
   * 批量删除应收账款账单
   * @param {Array} ids - 应收账款账单ID数组
   * @returns {Promise} 返回批量删除结果的 Promise
   */
  batchDeleteArbs(ids) {
    return doPost('/financial/receivable/bill/batch', { ids })
  },

  /**
   * 获取应收账款账单状态列表
   * @returns {Promise} 返回应收账款账单状态列表的 Promise
   */
  getArbStatusList() {
    return doGet('/financial/receivable/bill/status')
  },

  /**
   * 更新应收账款账单状态
   * @param {Number} id - 应收账款账单ID
   * @param {String} status - 新状态
   * @returns {Promise} 返回状态更新结果的 Promise
   */
  updateArbStatus(id, status) {
    return doPut(`/financial/receivable/bill/${id}/status`, { status })
  },

  /**
   * 更新应收账款账单对账状态
   * @param {Number} id - 应收账款账单ID
   * @param {Object} data - 更新数据
   * @param {Boolean} data.confirmed - 对账状态
   * @returns {Promise} 返回对账状态更新结果的 Promise
   */
  updateArbConfirmStatus(id, data) {
    return doPut(`/financial/receivable/bill/${id}/confirm`, data)
  },

  /**
   * 确认应收账款收款
   * @param {Object} data - 确认收款数据
   * @param {Number} data.id - 应收账款ID
   * @param {Number} data.accountId - 收款账户ID
   * @param {Number} data.feeAmount - 实收金额（分）
   * @param {String} [data.businessSerialNumber] - 业务流水号
   * @returns {Promise} 返回确认收款结果的 Promise
   */
  confirmReceivablePayment(data) {
    return doPut('/financial/receivable/bill/confirm', data)
  },

  /**
   * 批量确认应收账款收款
   * @param {Object} data - 批量确认收款数据
   * @param {Number} data.accountId - 收款账户ID
   * @param {Number} data.totalActualAmount - 总实收金额（分）
   * @param {String} [data.businessSerialNumber] - 业务流水号
   * @param {Array} data.receivableItems - 应收账款明细列表
   * @param {Number} data.receivableItems[].id - 应收账款ID
   * @param {Number} data.receivableItems[].feeAmount - 分配金额（分）
   * @returns {Promise} 返回批量确认收款结果的 Promise
   */
  batchConfirmReceivablePayment(data) {
    return doPut('/financial/receivable/bill/batch-confirm', data)
  },

  /**
   * 导出应收账款账单数据
   * @param {Object} params - 查询参数，同getArbList
   * @returns {Promise} 返回导出结果的 Promise
   */
  exportArbs(params) {
    return doGet('/financial/receivable/bill/export', params, { responseType: 'blob' })
  },

  /**
   * 导入应收账款账单数据
   * @param {FormData} formData - 包含文件的表单数据
   * @returns {Promise} 返回导入结果的 Promise
   */
  importArbs(formData) {
    return doPost('/financial/receivable/bill/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 处理API返回的数据，按订单号分组
   * @param {Object} response - API返回的原始数据
   * @returns {Object} 处理后的数据，包含分组后的列表和总数
   */
  processGroupedData(response) {
    if (!response || !response.data || !response.data.list) {
      return { list: [], total: 0 };
    }

    const { list } = response.data;

    // 按订单号分组
    const groupedMap = new Map();

    list.forEach(item => {
      if (!groupedMap.has(item.orderSn)) {
        // 创建订单组
        groupedMap.set(item.orderSn, {
          id: `order-${item.orderSn}`, // 创建唯一ID
          orderId: item.orderId, // 保存真正的订单ID
          orderSn: item.orderSn,
          orderDate: item.orderDate ? item.orderDate.split(' ')[0] : '', // 只保留日期部分
          customerName: item.customerName,
          mobile: item.mobile, // 保持与API返回字段名一致
          salesOrgName: item.salesOrgName,
          salesAgentName: item.salesAgentName,
          paymentMethod: item.paymentMethod,
          vin: item.vin, // 添加VIN字段到父行
          feeAmount: 0, // 将在下面累加所有子项的feeAmount
          feeName: '小计', // 父表显示"合计"
          feeTarget: '', // 父表feeTarget留空
          paidAmount: 0, // 假设初始为0，实际应从API获取
          unpaidAmount: 0, // 将在下面计算
          status: 'UNPAID', // 默认状态，将在下面更新
          children: []
        });
      }

      // 将当前项添加到对应订单的子项中
      const orderGroup = groupedMap.get(item.orderSn);

      // 累加父项的feeAmount
      const amount = item.feeAmount || 0;
      orderGroup.feeAmount += amount;

      // 添加子项，只保留必要的字段
      orderGroup.children.push({
        // 使用item.id作为唯一标识
        id: item.id || `fee-${item.feeId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        feeId: item.feeId,
        feeName: item.feeName || `费用${item.feeId}`,
        feeTarget: item.feeTarget || '-',
        feeAmount: amount,
        confirmed: item.confirmed === true, // 确保是布尔值
        vin: item.vin // 保留VIN字段，虽然子行不显示，但保留数据完整性
      });
    });

    // 转换为数组并计算未付金额
    const groupedList = Array.from(groupedMap.values()).map(order => {
      order.unpaidAmount = order.feeAmount - order.paidAmount;

      // 根据已付金额和总金额确定状态
      if (order.paidAmount === 0) {
        order.status = 'UNPAID';
      } else if (order.paidAmount < order.feeAmount) {
        order.status = 'PARTIAL_PAID';
      } else {
        order.status = 'PAID';
      }

      return order;
    });

    return {
      list: groupedList,
      total: groupedList.length // 返回分组后的总数
    };
  }
};

export default arbApi;
