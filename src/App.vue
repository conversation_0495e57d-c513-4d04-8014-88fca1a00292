<script setup>
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NModalProvider,
} from "naive-ui";
import { zhCN, dateZhCN } from "naive-ui";
import { onMounted, ref, provide } from "vue";
// 如果您需要使用 store，请使用 Pinia 的 useStore
// import { useStore } from 'pinia'

const isDebugMode = ref(false);
// const store = useStore()  // 如果您需要使用 store，请取消注释这行

// 提供 isDebugMode 给所有子组件
provide("isDebugMode", isDebugMode);
</script>

<template>
  <n-config-provider :locale="zhCN" :date-locale="dateZhCN">
    <n-message-provider>
      <n-dialog-provider>
        <n-modal-provider>
          <router-view></router-view>
        </n-modal-provider>
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script>
export default {
  name: "<PERSON><PERSON>",
  // 组件逻辑可以在这里添加
};
</script>

<style>
@font-face {
  font-family: "CustomChinese";
  src: url("https://lal.vooice.tech/main.woff2") format("woff2");
  crossorigin: anonymous;
}

@font-face {
  font-family: "CustomEnglish";
  src: url("https://lal.vooice.tech/english.woff2") format("woff2");
  crossorigin: anonymous;
}

:root {
  font-weight: 600 !important;
  font-style: normal !important;
  --font-chinese: "CustomChinese";
  --font-english: "CustomEnglish";
  --font-fallback: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

html {
  font-weight: 600 !important;
  font-style: normal !important;
  font-family: var(--font-chinese), var(--font-english), var(--font-fallback);
}

body,
#app,
input,
button,
textarea,
select {
  font-family: inherit;
}

/* 为英文内容设置字体 */
[lang="en"],
[lang="en-US"] {
  font-family: var(--font-english), var(--font-chinese), var(--font-fallback);
}

/* 为中文内容设置字体 */
[lang="zh"],
[lang="zh-CN"] {
  font-family: var(--font-chinese), var(--font-english), var(--font-fallback);
}

/* 其他样式保持不变 */
/* 为确保中文字符使用正确的字体 */
:lang(zh),
:lang(zh-CN) {
  font-family: "CustomChinese", "CustomEnglish", sans-serif;
}

/* 重置默认的边距和填充 */
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

/* 确保 #app 占满整个视口 */
#app {
  min-height: 100vh;
  width: 100%;
}

/* 调试模式样式 */
.debug-mode * {
  outline: 0.2px solid rgba(182, 174, 174, 0.5) !important;
  position: relative !important;
}

.debug-mode *::before {
  content: attr(class);
  position: absolute;
  top: -18px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0 5px;
  font-size: 12px;
  color: red;
  white-space: nowrap;
  z-index: 9999;
}

/* 修改消息样式 */
:deep(.n-message) {
  top: 24px !important;
  right: 24px !important;
}

:deep(.n-data-table) {
  height: 100% !important;
}

/* 添加消息动画样式 */
:deep(.n-message-container) {
  pointer-events: none;
}

:deep(.n-message) {
  pointer-events: all;
  transition: all 0.3s ease-in-out;
}

:deep(.n-message-enter-from),
:deep(.n-message-leave-to) {
  opacity: 0;
  transform: translateX(100%);
}

:deep(.n-message-enter-to),
:deep(.n-message-leave-from) {
  opacity: 1;
  transform: translateX(0);
}
</style>
