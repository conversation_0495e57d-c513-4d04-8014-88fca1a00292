/**
 * 文本链接渲染工具函数
 * 用于在表格中渲染包含订单号和车架号的文本，自动识别并转换为可点击的链接
 */
import { h } from "vue";
import { NButton, NIcon } from "naive-ui";
import { CopyOutline } from "@vicons/ionicons5";
import { copyToClipboard } from '@/utils/clipboard';

/**
 * 渲染包含各种业务编号链接的文本
 * @param {string} text - 要处理的文本
 * @param {Object} handlers - 处理函数对象
 * @param {Function} handlers.onOrderClick - 订单号点击处理函数
 * @param {Function} handlers.onVinClick - 车架号点击处理函数
 * @param {Function} handlers.onOutboundClick - 出库单号点击处理函数
 * @param {Function} handlers.onPurchaseClick - 采购单号点击处理函数
 * @param {Function} handlers.onSalesClick - 销售单号点击处理函数
 * @returns {VNode|string} 渲染结果
 */
export const renderTextWithLinks = (text, handlers = {}) => {
  if (!text || text === "-") {
    return "-";
  }

  // 业务编号正则表达式配置
  const businessNumberPatterns = [
    { regex: /(DO|VO)\d{17}/g, type: 'order', name: '订单号' },
    { regex: /[A-Z]{2}[A-Z0-9]{15}/g, type: 'vin', name: 'VIN码' },
    { regex: /P\d{17}[A-Z]{4}/g, type: 'payment', name: '支付单号' },
    { regex: /R\d{17}[A-Z]{4}/g, type: 'receipt', name: '收款单号' }
  ];

  // 收集所有匹配项
  const allMatches = [];
  let textForVinMatching = text;

  // 按顺序匹配各种业务编号
  businessNumberPatterns.forEach(pattern => {
    if (pattern.type === 'vin') {
      // VIN码需要特殊处理，避免与其他编号重叠
      const matches = textForVinMatching.match(pattern.regex) || [];
      matches.forEach(match => {
        allMatches.push({ text: match, type: pattern.type, name: pattern.name });
      });
    } else {
      // 其他业务编号直接匹配
      const matches = text.match(pattern.regex) || [];
      matches.forEach(match => {
        allMatches.push({ text: match, type: pattern.type, name: pattern.name });
        // 从VIN匹配文本中移除已匹配的编号，避免重叠
        textForVinMatching = textForVinMatching.replace(match, '');
      });
    }
  });

  // 如果没有匹配项，直接返回文本
  if (allMatches.length === 0) {
    return text;
  }

  // 按位置排序匹配项，避免重叠
  allMatches.sort((a, b) => text.indexOf(a.text) - text.indexOf(b.text));

  let lastIndex = 0;
  const textElements = [];

  allMatches.forEach(match => {
    const matchIndex = text.indexOf(match.text, lastIndex);

    // 添加匹配前的普通文本
    if (matchIndex > lastIndex) {
      textElements.push(text.substring(lastIndex, matchIndex));
    }

    // 创建包含超链接和复制按钮的容器
    textElements.push(createLinkWithCopyButton(match, handlers));

    lastIndex = matchIndex + match.text.length;
  });

  // 添加最后剩余的普通文本
  if (lastIndex < text.length) {
    textElements.push(text.substring(lastIndex));
  }

  return h("span", {
    style: {
      display: "inline-flex",
      alignItems: "center",
      flexWrap: "nowrap",
      gap: "0px",
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    }
  }, textElements);
};

/**
 * 创建带复制按钮的链接元素
 * @param {Object} match - 匹配项对象
 * @param {Object} handlers - 处理函数对象
 * @returns {VNode} 链接元素
 */
const createLinkWithCopyButton = (match, handlers) => {
  const {
    onOrderClick,
    onVinClick,
    onPaymentClick,
    onReceiptClick
  } = handlers;

  // 根据类型调用对应的处理函数
  const handleClick = (e) => {
    e.stopPropagation();
    switch (match.type) {
      case 'vin':
        onVinClick && onVinClick(match.text);
        break;
      case 'order':
        onOrderClick && onOrderClick(match.text);
        break;
      case 'payment':
        onPaymentClick && onPaymentClick(match.text);
        break;
      case 'receipt':
        onReceiptClick && onReceiptClick(match.text);
        break;
    }
  };

  // 根据类型生成提示文本
  const getTooltipText = () => {
    switch (match.type) {
      case 'vin':
        return `点击查看车架号 ${match.text} 的追踪信息`;
      case 'order':
        return `点击查看订单 ${match.text} 的详情`;
      case 'payment':
        return `点击查看支付单 ${match.text} 的详情`;
      case 'receipt':
        return `点击查看收款单 ${match.text} 的详情`;
      default:
        return `点击查看 ${match.name} ${match.text} 的详情`;
    }
  };

  return h("span", {
    style: {
      display: "inline-flex",
      alignItems: "center",
      gap: "2px", // 超链接和复制按钮之间的间距
    }
  }, [
    // 超链接
    h("a", {
      href: "javascript:void(0)",
      style: {
        color: "var(--primary-color)",
        textDecoration: "underline",
        cursor: "pointer",
        fontWeight: "500",
      },
      onClick: handleClick,
      title: getTooltipText(),
    }, match.text),
    // 复制按钮
    h(
      NButton,
      {
        size: "tiny",
        quaternary: true,
        circle: true,
        onClick: (e) => {
          e.stopPropagation();
          copyToClipboard(match.text);
        },
        style: {
          color: "var(--primary-color)",
          padding: "1px",
          minWidth: "18px",
          height: "18px",
          marginLeft: "0px",
        },
        title: `复制${match.name}: ${match.text}`,
      },
      {
        default: () =>
          h(NIcon, { size: 12 }, { default: () => h(CopyOutline) }),
      }
    )
  ]);
};

export default {
  renderTextWithLinks
};
