import { createDiscreteApi } from 'naive-ui'

// 创建一个全局的消息 API
const { message } = createDiscreteApi(['message'], {
    configProviderProps: {
        theme: null,
        themeOverrides: {
            Message: {
                padding: '12px 20px',
                maxWidth: '720px',
                errorColorSuppl: 'rgba(255, 0, 0, 0.1)', // 设置错误消息的背景色为带透明度的红色
                errorTextColor: '#ff0000', // 设置错误消息的文字颜色为红色
            }
        }
    },
    messageProviderProps: {
        // placement: 'top-center',
        duration: 5000,
        max: 3,
        containerStyle: {
            top: '24px',
            right: '24px'
        }
    }
})

export default {
    success(content) {
        message.success(content)
    },
    error(content) {
        message.error(content)
    },
    warning(content) {
        message.warning(content)
    },
    info(content) {
        message.info(content)
    }
}
