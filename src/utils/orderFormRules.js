/**
 * 订单表单验证规则
 */

/**
 * 获取订单表单验证规则
 * @param {Object} form - 表单数据对象的引用
 * @returns {Object} 验证规则对象
 */
export const getOrderFormRules = (form) => {
  return {
    // 订单编号已隐藏，不需要校验
    dealDate: {
      required: true,
      message: '请选择销售日期',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (!value) {
          return new Error('请选择销售日期')
        }
        return true
      }
    },
    salesOrgName: {
      required: true,
      message: '请输入销售单位',
      trigger: ['blur', 'input']
    },
    deliveryDate: {
      required: true,
      message: '请选择预计出库日期',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (!value) {
          return new Error('请选择预计出库日期')
        }
        return true
      }
    },
    outboundOrgName: {
      required: true,
      message: '请选择出库单位',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (!value) {
          return new Error('请选择出库单位')
        }
        return true
      }
    },

    // 客户信息
    customerId: {
      required: true,
      message: '请选择客户',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (!value) {
          return new Error('请选择客户')
        }
        return true
      }
    },
    customerName: {
      required: true,
      message: '客户名称不能为空',
      trigger: ['blur', 'change']
    },
    customerType: {
      required: true,
      message: '请选择客户类型',
      trigger: ['blur', 'change']
    },
    customerPhone: {
      required: true,
      message: '请输入联系电话',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (!value) {
          return new Error('请输入联系电话')
        }
        // 手机号码格式校验（支持11位手机号）
        const phoneRegex = /^1[3-9]\d{9}$/
        if (!phoneRegex.test(value)) {
          return new Error('请输入正确的手机号码格式')
        }
        return true
      }
    },
    salesStoreType: {
      required: true,
      message: '请选择销售地类型',
      trigger: ['blur', 'change']
    },

    // 车辆信息
    skuId: {
      required: true,
      message: '请选择车辆',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (!value) {
          return new Error('请选择车辆')
        }
        return true
      }
    },

    // 金额信息
    invoicePrice: {
      required: true,
      type: 'number',
      message: '开票价格必须大于零',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (value === null || value === undefined || value === '') {
          return new Error('开票价格不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('开票价格必须是数字')
        }
        if (value <= 0) {
          return new Error('开票价格必须大于零')
        }
        return true
      }
    },
    salesAmount: {
      required: true,
      type: 'number',
      message: '车辆售价必须大于零',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (value === null || value === undefined || value === '') {
          return new Error('车辆售价不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('车辆售价必须是数字')
        }
        if (value <= 0) {
          return new Error('车辆售价必须大于零')
        }
        return true
      }
    },
    dealAmount: {
      required: true,
      type: 'number',
      message: '成交总价必须大于零',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (value === null || value === undefined || value === '') {
          return new Error('成交总价不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('成交总价必须是数字')
        }
        if (value <= 0) {
          return new Error('成交总价必须大于零')
        }
        return true
      }
    },

    // 贷款信息 - 使用函数动态计算规则
    loanChannel: {
      required: () => form.paymentMethod === 'LOAN',
      message: '该字段为必选项',
      trigger: ['blur', 'change']
    },
    loanAmount: {
      required: () => form.paymentMethod === 'LOAN',
      type: 'number',
      message: '该字段为必填项',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.paymentMethod !== 'LOAN') return true;

        if (value === null || value === undefined || value === '') {
          return new Error('分期金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('分期金额必须是数字')
        }
        if (value < 0) {
          return new Error('分期金额不能为负数')
        }
        // 分期金额应该基于成交金额
        const dealAmount = form.dealAmount || 0
        if (value > dealAmount) {
          return new Error('分期金额不能大于成交金额')
        }
        // 检查贷款金额和首付金额之和是否等于成交金额
        const total = (value || 0) + (form.loanInitialAmount || 0)
        if (dealAmount > 0 && Math.abs(total - dealAmount) > 0.01) { // 允许0.01的误差
          return new Error('分期金额和首付金额之和必须等于成交金额')
        }
        return true
      }
    },
    loanInitialAmount: {
      required: () => form.paymentMethod === 'LOAN',
      type: 'number',
      message: '请输入首付金额',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.paymentMethod !== 'LOAN') return true;

        if (value === null || value === undefined || value === '') {
          return new Error('首付金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('首付金额必须是数字')
        }
        if (value < 0) {
          return new Error('首付金额不能为负数')
        }
        // 首付金额应该基于成交金额
        const dealAmount = form.dealAmount || 0
        if (value > dealAmount) {
          return new Error('首付金额不能大于成交金额')
        }
        // 检查贷款金额和首付金额之和是否等于成交金额
        const total = (value || 0) + (form.loanAmount || 0)
        if (dealAmount > 0 && Math.abs(total - dealAmount) > 0.01) { // 允许0.01的误差
          return new Error('分期金额和首付金额之和必须等于成交金额')
        }
        return true
      }
    },
    loanMonths: {
      required: () => form.paymentMethod === 'LOAN',
      message: '请选择分期期数',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.paymentMethod !== 'LOAN') return true;

        if (value === null || value === undefined || value === '') {
          return new Error('请选择分期期数')
        }
        // 确保值是有效的数字
        if (typeof value !== 'number' && !Number.isInteger(Number(value))) {
          return new Error('分期期数必须是有效数字')
        }
        return true
      }
    },

    // 专享优惠信息 - 使用函数动态计算规则
    exclusiveDiscountType: {
      required: () => form.hasExclusiveDiscount === 'YES',
      message: '请选择专享优惠类型',
      trigger: ['blur', 'change']
    },
    exclusiveDiscountAmount: {
      required: () => form.hasExclusiveDiscount === 'YES',
      type: 'number',
      message: '请输入专享优惠金额',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.hasExclusiveDiscount !== 'YES') return true;

        if (value === null || value === undefined || value === '') {
          return new Error('专享优惠金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('专享优惠金额必须是数字')
        }
        if (value <= 0) {
          return new Error('专享优惠金额必须大于零')
        }
        return true
      }
    },

    // 保险信息 - 使用函数动态计算规则
    insuranceOrgId: {
      required: () => form.hasInsurance === 'YES',
      message: '请选择保险经办机构',
      trigger: ['blur', 'change']
    },
    insuranceOrgName: {
      required: () => form.hasInsurance === 'YES',
      message: '请选择保险经办机构',
      trigger: ['blur', 'change']
    },

    // 二手车信息 - 使用函数动态计算规则
    usedVehicleId: {
      required: () => form.hasUsedVehicle === 'YES',
      message: '请输入二手车车牌号',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.hasUsedVehicle !== 'YES') return true;

        if (!value) {
          return new Error('请输入二手车车牌号')
        }

        // 车牌号必须是7位或8位
        if (value.length !== 7 && value.length !== 8) {
          return new Error('车牌号必须是7位或8位字符')
        }

        return true
      }
    },
    usedVehicleVin: {
      required: () => form.hasUsedVehicle === 'YES' && form.usedVehicleId,
      message: '请输入二手车VIN',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.hasUsedVehicle !== 'YES' || !form.usedVehicleId) return true;

        if (!value) {
          return new Error('请输入二手车VIN')
        }

        // VIN必须是17位字符
        if (value.length !== 17) {
          return new Error('车架号VIN必须是17位字符')
        }

        return true
      }
    },
    usedVehicleBrand: {
      required: false,
      message: '请输入置换车品牌',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.hasUsedVehicle !== 'YES') return true;

        // 如果没有填写，则通过验证（选填）
        if (!value || value.trim() === '') {
          return true
        }

        // 检查长度限制
        if (value.length > 20) {
          return new Error('置换车品牌不能超过20个字符')
        }

        return true
      }
    },
    usedVehicleModel: {
      required: false,
      message: '请输入置换车型',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.hasUsedVehicle !== 'YES') return true;

        // 如果没有填写，则通过验证（选填）
        if (!value || value.trim() === '') {
          return true
        }

        // 检查长度限制
        if (value.length > 20) {
          return new Error('置换车型不能超过20个字符')
        }

        return true
      }
    },
    usedVehicleAmount: {
      required: () => form.hasUsedVehicle === 'YES' && form.usedVehicleId,
      type: 'number',
      message: '请输入置换金额',
      trigger: ['blur', 'change'],
      validator: (_, value) => {
        if (form.hasUsedVehicle !== 'YES' || !form.usedVehicleId) return true;

        if (value === null || value === undefined || value === '') {
          return new Error('置换金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('置换金额必须是数字')
        }
        if (value <= 0) {
          return new Error('置换金额必须大于零')
        }
        return true
      }
    }
  }
}

export default getOrderFormRules
