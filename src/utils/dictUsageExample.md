# 字典数据自动加载使用指南

## 概述

新的字典数据管理系统支持从服务器端自动加载字典数据并缓存到本地，同时保持与现有代码的兼容性。

## 主要特性

1. **自动加载**: 模块加载时自动从服务器获取字典数据
2. **智能缓存**: 本地缓存字典数据，减少网络请求
3. **兼容性**: 保持与现有 `dictUtils.js` 调用方式的完全兼容
4. **异步支持**: 提供异步API确保数据加载完成
5. **缓存管理**: 支持缓存刷新和清理

## 使用方式

### 1. 现有代码无需修改

```javascript
// 现有的调用方式仍然有效
import { getDictOptions, vehicleBrandUtils } from '@/utils/dictUtils'

// 同步获取字典选项（如果数据未加载会触发异步加载）
const brandOptions = getDictOptions('vehicle_brand')
const statusOptions = getDictOptions('order_status')

// 使用专用工具函数
const brandList = vehicleBrandUtils.getOptions()
```

### 2. 推荐的异步使用方式

```javascript
import { getDictOptionsAsync, dictManagementUtils } from '@/utils/dictUtils'

// 异步获取字典选项（推荐）
const brandOptions = await getDictOptionsAsync('vehicle_brand')
const statusOptions = await getDictOptionsAsync('order_status')

// 在组件中等待初始化完成
await dictManagementUtils.waitForInitialization()
```

### 3. 在 Vue 组件中使用（推荐方式）

```vue
<script setup>
import { useDictOptions } from '@/utils/dictUtils'

// 使用响应式字典选项（推荐）
const { options: brandOptions, loading: brandLoading } = useDictOptions('vehicle_brand')
const { options: statusOptions, loading: statusLoading } = useDictOptions('order_status')

// 选项会自动响应字典数据的加载和更新
</script>

<template>
  <div>
    <n-select
      v-model:value="selectedBrand"
      :options="brandOptions"
      :loading="brandLoading"
      placeholder="选择品牌"
    />

    <n-select
      v-model:value="selectedStatus"
      :options="statusOptions"
      :loading="statusLoading"
      placeholder="选择状态"
    />
  </div>
</template>
```

### 4. 传统异步方式

```vue
<script setup>
import { ref, onMounted } from 'vue'
import { getDictOptionsAsync, dictManagementUtils } from '@/utils/dictUtils'

const brandOptions = ref([])
const loading = ref(true)

onMounted(async () => {
  try {
    // 等待字典数据初始化
    await dictManagementUtils.waitForInitialization()

    // 获取字典选项
    brandOptions.value = await getDictOptionsAsync('vehicle_brand')
  } catch (error) {
    console.error('加载字典数据失败:', error)
  } finally {
    loading.value = false
  }
})
</script>
```

### 5. 管理字典缓存

```javascript
import { dictManagementUtils } from '@/utils/dictUtils'

// 刷新指定字典组
await dictManagementUtils.refresh('vehicle_brand')

// 刷新所有字典数据
await dictManagementUtils.refreshAll()

// 清除缓存
dictManagementUtils.clearCache()

// 获取加载状态
const status = dictManagementUtils.getStatus()
console.log('字典状态:', status)
```

## 配置说明

### 缓存配置

在 `src/mock/dictData.js` 中可以调整缓存配置：

```javascript
const CACHE_CONFIG = {
  // 缓存过期时间（毫秒）- 默认30分钟
  CACHE_EXPIRE_TIME: 30 * 60 * 1000,
  // 缓存版本
  CURRENT_VERSION: '1.0.0'
}
```

### 前端维护的字典组

某些字典组仍由前端维护，不会从服务器加载：

```javascript
const FRONTEND_DICT_GROUPS = ['province_city', 'city_district']
```

## API 接口

系统会调用以下后端接口：

- `GET /system/options` - 获取字典列表
- `GET /system/options/{dictCode}` - 获取指定字典组的选项

## 数据格式

### 服务器返回的字典选项格式

```javascript
{
  "code": 200,
  "data": [
    {
      "optionValue": "value1",
      "optionLabel": "标签1",
      "optionOrder": 1,
      "optionComment": "备注",
      "type": "default",
      "color": "#409EFF"
    }
  ]
}
```

### 转换后的内部格式

```javascript
{
  "optionValue": "value1",
  "optionLabel": "标签1",
  "sort": 1,
  "remark": "备注",
  "type": "default",
  "color": "#409EFF"
}
```

## 错误处理

- 网络错误时会使用缓存数据
- 加载失败的字典组会被标记，避免重复请求
- 提供详细的控制台日志用于调试

## 性能优化

1. **缓存机制**: 减少重复的网络请求
2. **批量加载**: 一次性加载所有字典数据
3. **懒加载**: 按需加载未缓存的字典组
4. **版本控制**: 缓存版本变更时自动清理旧缓存

## 调试和测试

使用测试工具验证功能：

```javascript
import { runAllTests } from '@/utils/dictTest'

// 运行测试套件
await runAllTests()
```

## 注意事项

1. 首次加载可能需要一些时间，建议在应用启动时显示加载状态
2. 缓存数据存储在 localStorage 中，清除浏览器数据会导致缓存丢失
3. 网络异常时会降级使用缓存数据，可能不是最新的
4. 前端维护的字典组（如省市数据）不会从服务器加载
