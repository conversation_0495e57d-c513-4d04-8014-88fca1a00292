/**
 * 日期范围相关工具函数
 */

/**
 * 日期范围选项
 * 用于筛选条件中的日期范围选择
 */
export const dateRangeOptions = [
  { label: '不限', value: null },
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '本周', value: 'thisWeek' },
  { label: '上周', value: 'lastWeek' },
  { label: '本月', value: 'thisMonth' },
  { label: '上月', value: 'lastMonth' },
  { label: '本季', value: 'thisQuarter' },
  { label: '本年', value: 'thisYear' },
  { label: '往年', value: 'prevYears' },
  { label: '自定义', value: 'custom' }
]

/**
 * 格式化日期为 YYYY-MM-DD 格式，避免时区问题
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 获取日期范围参数
 * @param {string} rangeType - 日期范围类型
 * @param {Array} customRange - 自定义日期范围
 * @returns {Object} 包含startDate和endDate的对象
 */
export const getDateRangeParams = (rangeType, customRange) => {
  const result = { startDate: null, endDate: null }
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const todayStr = formatDate(today)

  switch (rangeType) {
    case 'today':
      result.startDate = todayStr
      result.endDate = todayStr
      break
    case 'yesterday':
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = formatDate(yesterday)
      result.startDate = yesterdayStr
      result.endDate = yesterdayStr
      break
    case 'thisWeek':
      const thisWeekStart = new Date(today)
      thisWeekStart.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)) // 周一开始
      const thisWeekEnd = new Date(today)
      thisWeekEnd.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? 0 : 7)) // 周日结束
      result.startDate = formatDate(thisWeekStart)
      result.endDate = formatDate(thisWeekEnd)
      break
    case 'lastWeek':
      const lastWeekStart = new Date(today)
      lastWeekStart.setDate(today.getDate() - today.getDay() - 6) // 上周一
      const lastWeekEnd = new Date(today)
      lastWeekEnd.setDate(today.getDate() - today.getDay() - 1) // 上周日
      result.startDate = formatDate(lastWeekStart)
      result.endDate = formatDate(lastWeekEnd)
      break
    case 'thisMonth':
      result.startDate = formatDate(new Date(now.getFullYear(), now.getMonth(), 1))
      // 当月最后一天：下个月的第0天
      result.endDate = formatDate(new Date(now.getFullYear(), now.getMonth() + 1, 0))
      break
    case 'lastMonth':
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)
      result.startDate = formatDate(lastMonthStart)
      result.endDate = formatDate(lastMonthEnd)
      break
    case 'thisQuarter':
      const quarterMonth = Math.floor(now.getMonth() / 3) * 3
      result.startDate = formatDate(new Date(now.getFullYear(), quarterMonth, 1))
      // 本季度最后一天：下个季度第一个月的第0天
      result.endDate = formatDate(new Date(now.getFullYear(), quarterMonth + 3, 0))
      break
    case 'lastQuarter':
      const lastQuarterMonth = Math.floor((now.getMonth() - 3) / 3) * 3
      const lastQuarterStart = new Date(now.getFullYear(), lastQuarterMonth, 1)
      const lastQuarterEnd = new Date(now.getFullYear(), lastQuarterMonth + 3, 0)
      result.startDate = formatDate(lastQuarterStart)
      result.endDate = formatDate(lastQuarterEnd)
      break
    case 'thisYear':
      result.startDate = formatDate(new Date(now.getFullYear(), 0, 1))
      // 本年最后一天：12月31日
      result.endDate = formatDate(new Date(now.getFullYear(), 11, 31))
      break
    case 'prevYears':
      result.startDate = '2000-01-01' // 设置一个足够早的日期
      result.endDate = formatDate(new Date(now.getFullYear() - 1, 11, 31))
      break
    case 'custom':
      if (customRange && Array.isArray(customRange) && customRange.length === 2) {
        result.startDate = formatDate(new Date(customRange[0]))
        result.endDate = formatDate(new Date(customRange[1]))
      }
      break
  }

  return result
}

/**
 * 处理日期范围变化
 * @param {string} value - 选择的日期范围值
 * @param {Object} filterForm - 筛选表单对象
 * @param {Function} handleSearch - 搜索处理函数
 */
export const handleDateRangeChange = (value, filterForm, handleSearch) => {
  if (value !== 'custom') {
    // 非自定义选项直接执行查询
    handleSearch()
  } else {
    // 如果是自定义选项，清空日期范围
    filterForm.customDateRange = null
  }
  // 如果是自定义选项，不执行查询，等待用户选择日期
}

/**
 * 处理自定义日期变化
 * @param {Array} dates - 日期数组
 * @param {Function} handleSearch - 搜索处理函数
 */
export const handleCustomDateChange = (dates, handleSearch) => {
  if (dates && dates.length === 2) {
    // 只有当用户选择了完整的日期范围才执行查询
    handleSearch()
  }
}

export default {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange,
  handleCustomDateChange
}
