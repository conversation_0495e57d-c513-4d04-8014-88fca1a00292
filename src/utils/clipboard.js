/**
 * 剪贴板工具函数
 */
import messages from '@/utils/messages';

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {string} [successMessage] - 成功提示消息，默认为 "{text} 已复制"
 * @param {string} [errorMessage] - 失败提示消息，默认为 "复制失败，请手动复制"
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyToClipboard = async (text, successMessage, errorMessage) => {
  if (!text) {
    messages.warning("内容为空，无法复制");
    return false;
  }

  const defaultSuccessMessage = successMessage || `${text} 已复制`;
  const defaultErrorMessage = errorMessage || "复制失败，请手动复制";

  try {
    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      messages.success(defaultSuccessMessage);
      return true;
    } else {
      // 备用方法：使用 document.execCommand
      return fallbackCopyTextToClipboard(text, defaultSuccessMessage, defaultErrorMessage);
    }
  } catch (error) {
    console.error('复制失败:', error);
    // 如果 Clipboard API 失败，使用备用方法
    return fallbackCopyTextToClipboard(text, defaultSuccessMessage, defaultErrorMessage);
  }
};

/**
 * 备用复制方法（使用 document.execCommand）
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示消息
 * @param {string} errorMessage - 失败提示消息
 * @returns {boolean} 复制是否成功
 */
const fallbackCopyTextToClipboard = (text, successMessage, errorMessage) => {
  const textArea = document.createElement("textarea");
  textArea.value = text;

  // 避免滚动到底部
  textArea.style.top = "0";
  textArea.style.left = "0";
  textArea.style.position = "fixed";
  textArea.style.opacity = "0";

  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand('copy');
    if (successful) {
      messages.success(successMessage);
      return true;
    } else {
      messages.error(errorMessage);
      return false;
    }
  } catch (err) {
    console.error('复制失败:', err);
    messages.error(errorMessage);
    return false;
  } finally {
    document.body.removeChild(textArea);
  }
};

/**
 * 复制订单号的便捷方法
 * @param {string} orderSn - 订单号
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyOrderSn = (orderSn) => {
  return copyToClipboard(orderSn, `订单号 ${orderSn} 已复制`, "复制订单号失败");
};

/**
 * 复制车架号的便捷方法
 * @param {string} vin - 车架号
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyVin = (vin) => {
  return copyToClipboard(vin, `车架号 ${vin} 已复制`, "复制车架号失败");
};

export default {
  copyToClipboard,
  copyOrderSn,
  copyVin
};
