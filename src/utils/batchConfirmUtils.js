/**
 * 批量确认工具函数
 */

/**
 * 检查批量确认是否可用
 * @param {Array} selectedRows - 选中的行ID数组
 * @param {Array} receivableData - 应收账款数据
 * @param {Function} getRowKey - 获取行键的函数
 * @returns {boolean} 是否可以批量确认
 */
export const checkBatchConfirmEnabled = (selectedRows, receivableData, getRowKey) => {
  if (!selectedRows || selectedRows.length === 0) {
    return false;
  }

  // 获取选中的行数据
  const selectedData = receivableData.filter(row =>
    selectedRows.includes(getRowKey(row))
  );

  // 过滤出未确认且未退回的数据
  const unconfirmedData = selectedData.filter(row =>
    row.billStatus !== 'CONFIRMED' &&
    row.billStatus !== 'RETURNED'
  );

  if (unconfirmedData.length === 0) {
    return false;
  }

  // 只检查是否为相同收款机构（允许不同科目）
  const receivableOrgIds = [...new Set(unconfirmedData.map(row => row.receivableOrgId))];
  if (receivableOrgIds.length > 1) {
    return false;
  }

  return true;
};

/**
 * 获取批量确认按钮的提示文案
 * @param {Array} selectedRows - 选中的行ID数组
 * @param {Array} receivableData - 应收账款数据
 * @param {Function} getRowKey - 获取行键的函数
 * @returns {string} 提示文案
 */
export const getBatchConfirmTooltip = (selectedRows, receivableData, getRowKey) => {
  if (!selectedRows || selectedRows.length === 0) {
    return "请先选择要确认的数据";
  }

  // 获取选中的行数据
  const selectedData = receivableData.filter(row =>
    selectedRows.includes(getRowKey(row))
  );

  // 过滤出未确认且未退回的数据
  const unconfirmedData = selectedData.filter(row =>
    row.billStatus !== 'CONFIRMED' &&
    row.billStatus !== 'RETURNED'
  );

  if (unconfirmedData.length === 0) {
    // 检查是否有已退回的数据
    const returnedData = selectedData.filter(row =>
      row.billStatus === 'RETURNED'
    );
    if (returnedData.length > 0) {
      return "选中的数据包含已退回记录，无法进行批量确认";
    }
    return "选中的数据都已确认，无需重复确认";
  }

  // 只检查是否为相同收款机构（允许不同科目）
  const receivableOrgIds = [...new Set(unconfirmedData.map(row => row.receivableOrgId))];
  if (receivableOrgIds.length > 1) {
    return "只能选择相同收款机构的应收账款进行批量确认收款";
  }

  // 获取科目信息用于提示
  const feeNames = [...new Set(unconfirmedData.map(row => row.feeName))];
  if (feeNames.length > 1) {
    return `批量确认收款（包含${feeNames.length}个科目）`;
  }

  return "批量确认收款";
};

/**
 * 准备批量确认数据
 * @param {Array} selectedRows - 选中的行ID数组
 * @param {Array} receivableData - 应收账款数据
 * @param {Function} getRowKey - 获取行键的函数
 * @returns {Object|null} 批量确认数据对象
 */
export const prepareBatchConfirmData = (selectedRows, receivableData, getRowKey) => {
  if (!checkBatchConfirmEnabled(selectedRows, receivableData, getRowKey)) {
    return null;
  }

  // 获取选中的行数据
  const selectedData = receivableData.filter(row =>
    selectedRows.includes(getRowKey(row))
  );

  // 过滤出未确认且未退回的数据
  const unconfirmedData = selectedData.filter(row =>
    row.billStatus !== 'CONFIRMED' &&
    row.billStatus !== 'RETURNED'
  );

  // 按科目分组
  const groupedBySubject = unconfirmedData.reduce((groups, row) => {
    const key = row.feeName || row.feeId;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(row);
    return groups;
  }, {});

  // 构造批量明细数据
  const batchDetails = Object.entries(groupedBySubject).map(([feeName, items]) => {
    const totalAmount = items.reduce((sum, item) => sum + (item.feeAmount || 0), 0);
    return {
      feeName,
      feeAmount: totalAmount,
      count: items.length,
      items: items,
      receivableSummary: `${feeName}`
    };
  });

  // 计算总金额
  const totalAmount = unconfirmedData.reduce((sum, row) => {
    return sum + (row.feeAmount || 0);
  }, 0);

  // 构造批量确认收款的数据
  return {
    id: 'batch', // 标识为批量操作
    feeName: `批量确认收款（${Object.keys(groupedBySubject).length}个科目）`, // 科目名称
    feeAmount: totalAmount, // 总金额（分）
    receivableOrgId: unconfirmedData[0].receivableOrgId, // 使用第一条数据的机构ID
    receivableSummary: `批量确认收款(${unconfirmedData.length}条)`, // 批量确认的默认摘要
    batchData: unconfirmedData, // 保存批量数据用于后续处理
    batchDetails: batchDetails, // 按科目分组的明细数据
    isBatch: true // 标识为批量操作
  };
};

export default {
  checkBatchConfirmEnabled,
  getBatchConfirmTooltip,
  prepareBatchConfirmData
};
