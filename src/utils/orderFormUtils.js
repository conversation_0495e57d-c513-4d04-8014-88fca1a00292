/**
 * 订单表单工具函数
 */
import { convertNumberToChinese, convertYuanToCents } from '@/utils/money'

/**
 * 批量转换金额字段从元到分
 * @param {Object} data - 源数据
 * @param {Array} fields - 需要转换的字段数组
 */
const convertAmountFieldsToCents = (data, fields) => {
  fields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      data[field] = convertYuanToCents(data[field])
    }
  })
}

/**
 * 处理日期转换为时间戳
 * @param {Date|Number|String} date - 日期对象、时间戳或日期字符串
 * @returns {Number|null} 时间戳或null
 */
export const convertDateToTimestamp = (date) => {
  if (!date) return null
  if (typeof date === 'number') return date
  if (date instanceof Date) return date.getTime()
  return null
}

/**
 * 生成订单编号
 * @returns {String} 订单编号
 */
export const generateOrderNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `SO${year}${month}${day}${random}`
}

/**
 * 计算成交总价
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const calculateFinalPrice = (form) => {
  // 基础成交价 = 销售价格 - 优惠金额
  let basePrice = Math.max(0, form.salesAmount - (form.discountAmount || 0))

  // 如果有专享优惠且勾选了转车款选项，减去专享优惠金额
  if (form.exclusiveDiscountAmount > 0 && form.exclusiveDiscountPayableDeductible) {
    basePrice = Math.max(0, basePrice - (form.exclusiveDiscountAmount || 0))
  }

  // 如果已付定金且勾选了转车款选项，减掉定金金额
  if (form.depositAmount > 0 && form.depositDeductible) {
    basePrice = Math.max(0, basePrice - (form.depositAmount || 0))
  }

  // 如果有车辆置换且填写了转车款金额，减掉转车款金额
  if (form.hasUsedVehicle === 'YES' && form.usedVehicleDiscountPayableDeductibleAmount > 0) {
    basePrice = Math.max(0, basePrice - (form.usedVehicleDiscountPayableDeductibleAmount || 0))
  }

  // 如果有衍生收入，加上衍生收入总金额
  if (form.hasDerivativeIncome === 'YES') {
    basePrice += (
      (form.notaryFee || 0) +
      (form.carefreeIncome || 0) +
      (form.extendedWarrantyIncome || 0) +
      (form.vpsIncome || 0) +
      (form.preInterest || 0) +
      (form.licensePlateFee || 0) +
      (form.tempPlateFee || 0) +
      (form.deliveryEquipment || 0) +
      (form.otherIncome || 0)
    )
  }

  // 注意：分期服务费是独立的收入项，不影响成交价格
  // 分期服务费在应收客户中单独体现，不计入成交金额

  form.dealAmount = basePrice
  // 更新大写金额
  form.dealAmountCn = convertNumberToChinese(form.dealAmount)

  return form
}

/**
 * 计算预估毛利率和毛利润
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const calculateProfitRate = (form) => {
  // 计算总成本 = 启票价格（销售费用字段已移除）
  const totalCost = form.sbAmount

  // 计算预估毛利润 = 成交总价 - 总成本
  form.grossProfitAmount = form.dealAmount - totalCost

  if (totalCost > 0) {
    // 毛利率 = 毛利润 / 总成本 * 100%
    let rate = (form.grossProfitAmount / totalCost) * 100
    // 如果毛利率为负，则显示为0
    rate = rate < 0 ? 0 : rate
    // 保留2位小数
    form.profitRate = parseFloat(rate.toFixed(2))
  } else {
    form.profitRate = 0
  }

  return form
}

/**
 * 处理贷款金额变化
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const handleLoanAmountChange = (form) => {
  if (form.salesAmount > 0) {
    // 当贷款金额变化时，自动计算首付金额 = 车辆销售价格 - 贷款金额
    form.loanInitialAmount = parseFloat((form.salesAmount - form.loanAmount).toFixed(2))
    // 确保首付金额不小于0
    if (form.loanInitialAmount < 0) {
      form.loanInitialAmount = 0
      // 如果首付金额被调整为0，则贷款金额等于车辆销售价格
      form.loanAmount = form.salesAmount
    }
    // 计算首付比例 = 首付金额 / 车辆销售价格 * 100%
    form.loanInitialRatio = parseFloat(((form.loanInitialAmount / form.salesAmount) * 100).toFixed(2))
  }
  return form
}

/**
 * 处理首付金额变化
 * @param {Object} form - 表单数据
 * @returns {Object} 更新后的表单数据
 */
export const handleLoanInitialAmountChange = (form) => {
  if (form.salesAmount > 0) {
    // 当首付金额变化时，自动计算贷款金额 = 车辆销售价格 - 首付金额
    form.loanAmount = parseFloat((form.salesAmount - form.loanInitialAmount).toFixed(2))
    // 确保贷款金额不小于0
    if (form.loanAmount < 0) {
      form.loanAmount = 0
      // 如果贷款金额被调整为0，则首付金额等于车辆销售价格
      form.loanInitialAmount = form.salesAmount
    }
    // 计算首付比例 = 首付金额 / 车辆销售价格 * 100%
    form.loanInitialRatio = parseFloat(((form.loanInitialAmount / form.salesAmount) * 100).toFixed(2))
  }
  return form
}

/**
 * 将表单数据转换为API请求数据
 * @param {Object} form - 表单数据
 * @returns {Object} API请求数据
 */
export const convertFormToApiData = (form) => {
  // 复制表单数据，避免修改原始数据
  const orderPayload = { ...form }

  // 处理业务逻辑默认值
  orderPayload.salesOrgId = form.salesOrgId || form.deliveryOrgId // 销售单位ID回退逻辑
  orderPayload.salesAgentId = form.salesAgentId || form.customerId // 销售顾问ID回退逻辑
  orderPayload.salesLeaderId = form.salesLeaderId || form.customerId // 销售主管ID回退逻辑
  orderPayload.paymentMethod = form.paymentMethod || 'FULL' // 默认全款

  // 定义需要转换的基础金额字段
  const basicAmountFields = [
    'sbAmount', 'salesAmount', 'depositAmount', 'discountAmount',
    'exclusiveDiscountAmount', 'exclusiveDiscountReceivableAmount', 'exclusiveDiscountPayableAmount',
    'dealAmount', 'grossProfitAmount'
  ]

  // 批量转换基础金额字段
  convertAmountFieldsToCents(orderPayload, basicAmountFields)

  // 处理条件性金额字段转换
  if (form.hasUsedVehicle === 'YES' && form.usedVehicleId) {
    const usedVehicleAmountFields = ['usedVehicleAmount', 'usedVehicleDiscountAmount']
    convertAmountFieldsToCents(orderPayload, usedVehicleAmountFields)
  }

  if (orderPayload.paymentMethod === 'LOAN') {
    const loanAmountFields = [
      'loanAmount', 'loanInitialAmount', 'loanRebateAmount', 'loanFee',
      'loanRebateReceivableAmount', 'loanRebatePayableAmount'
    ]
    convertAmountFieldsToCents(orderPayload, loanAmountFields)
  }

  if (form.hasDerivativeIncome === 'YES') {
    const derivativeIncomeAmountFields = [
      'notaryFee', 'carefreeIncome', 'extendedWarrantyIncome', 'vpsIncome',
      'preInterest', 'licensePlateFee', 'tempPlateFee', 'deliveryEquipment', 'otherIncome'
    ]
    convertAmountFieldsToCents(orderPayload, derivativeIncomeAmountFields)
  }

  // 处理赠品明细信息
  if (form.hasGiftItems === 'YES' && Array.isArray(form.giftItems) && form.giftItems.length > 0) {
    // 处理赠品明细数据，将单价从元转换为分
    orderPayload.giftItems = form.giftItems.map(item => ({
      ...item,
      unitPrice: Math.round((item.unitPrice || 0) * 100) // 将单价从元转换为分
    }))
  }

  return orderPayload
}
