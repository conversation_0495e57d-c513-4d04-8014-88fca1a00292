/**
 * 请求防抖功能测试工具
 */

import { doGet, debugDeduplicator } from './requests'

/**
 * 测试请求防抖功能
 */
export async function testRequestDeduplication() {
  console.log('=== 开始测试请求防抖功能 ===')
  
  // 清理之前的缓存
  debugDeduplicator.clearCache()
  debugDeduplicator.setDebugMode(true)
  
  const testUrl = '/api/vehicle-order/list'
  const testParams = { page: 1, size: 10, brand: 'BMW' }
  
  console.log('初始状态:', debugDeduplicator.getStats())
  
  // 同时发起3个相同的请求
  console.log('同时发起3个相同的请求...')
  const promises = [
    doGet(testUrl, testParams),
    doGet(testUrl, testParams),
    doGet(testUrl, testParams)
  ]
  
  console.log('请求发起后状态:', debugDeduplicator.getStats())
  
  try {
    const results = await Promise.all(promises)
    console.log('所有请求完成，结果数量:', results.length)
    console.log('最终状态:', debugDeduplicator.getStats())
    
    // 验证结果是否相同
    const firstResult = JSON.stringify(results[0])
    const allSame = results.every(result => JSON.stringify(result) === firstResult)
    console.log('所有结果是否相同:', allSame)
    
    return {
      success: true,
      requestCount: promises.length,
      resultsMatch: allSame,
      stats: debugDeduplicator.getStats()
    }
  } catch (error) {
    console.error('测试失败:', error)
    return {
      success: false,
      error: error.message,
      stats: debugDeduplicator.getStats()
    }
  }
}

/**
 * 测试缓存功能
 */
export async function testRequestCache() {
  console.log('=== 开始测试请求缓存功能 ===')
  
  debugDeduplicator.clearCache()
  
  const testUrl = '/api/vehicle-order/list'
  const testParams = { page: 1, size: 5, brand: 'AUDI' }
  
  console.log('发起第一个请求...')
  const startTime1 = Date.now()
  const result1 = await doGet(testUrl, testParams)
  const duration1 = Date.now() - startTime1
  console.log(`第一个请求完成，耗时: ${duration1}ms`)
  
  // 立即发起第二个相同请求（应该使用缓存）
  console.log('立即发起第二个相同请求...')
  const startTime2 = Date.now()
  const result2 = await doGet(testUrl, testParams)
  const duration2 = Date.now() - startTime2
  console.log(`第二个请求完成，耗时: ${duration2}ms`)
  
  const resultsMatch = JSON.stringify(result1) === JSON.stringify(result2)
  const cacheUsed = duration2 < duration1 / 2 // 缓存请求应该明显更快
  
  console.log('结果匹配:', resultsMatch)
  console.log('缓存被使用:', cacheUsed)
  console.log('最终状态:', debugDeduplicator.getStats())
  
  return {
    success: true,
    resultsMatch,
    cacheUsed,
    duration1,
    duration2,
    stats: debugDeduplicator.getStats()
  }
}

/**
 * 模拟页面初始化时的多次调用
 */
export async function simulatePageInitialization() {
  console.log('=== 模拟页面初始化时的多次调用 ===')
  
  debugDeduplicator.clearCache()
  
  const testUrl = '/api/vehicle-order/list'
  const baseParams = { page: 1, size: 100 }
  
  // 模拟页面初始化时的场景：
  // 1. onMounted 调用
  // 2. VehicleBrandSelector 自动选择触发
  // 3. 其他字典组件触发
  
  console.log('模拟页面初始化场景...')
  const promises = [
    doGet(testUrl, { ...baseParams }), // onMounted
    doGet(testUrl, { ...baseParams }), // VehicleBrandSelector
    doGet(testUrl, { ...baseParams })  // 其他组件
  ]
  
  const startTime = Date.now()
  const results = await Promise.all(promises)
  const totalDuration = Date.now() - startTime
  
  console.log(`所有请求完成，总耗时: ${totalDuration}ms`)
  console.log('实际网络请求数量:', debugDeduplicator.getStats().pendingCount)
  console.log('缓存命中数量:', debugDeduplicator.getStats().cacheCount)
  
  return {
    success: true,
    totalRequests: promises.length,
    totalDuration,
    stats: debugDeduplicator.getStats()
  }
}

/**
 * 在浏览器控制台中运行测试
 */
if (typeof window !== 'undefined') {
  window.testRequestDeduplication = {
    testDeduplication: testRequestDeduplication,
    testCache: testRequestCache,
    simulatePageInit: simulatePageInitialization,
    getStats: () => debugDeduplicator.getStats(),
    clearCache: () => debugDeduplicator.clearCache()
  }
  
  console.log('请求防抖测试工具已加载到 window.testRequestDeduplication')
  console.log('可用方法:')
  console.log('- testDeduplication(): 测试请求防抖')
  console.log('- testCache(): 测试请求缓存')
  console.log('- simulatePageInit(): 模拟页面初始化')
  console.log('- getStats(): 获取当前状态')
  console.log('- clearCache(): 清理缓存')
}
