/**
 * 业务规则配置
 * 用于管理各种业务计算规则和常量
 */

// 销售价格计算规则
export const SALES_PRICE_RULES = {
  // 默认销售价格倍数（相对于启票价格）
  DEFAULT_MULTIPLIER: 1.35,
  
  // 品牌特定的价格倍数
  BRAND_MULTIPLIERS: {
    '阿维塔': 1.35,
    '深蓝': 1.30,
    '引力': 1.40,
    '启源': 1.32,
    '凯程': 1.38
  },
  
  // 最小毛利率要求
  MIN_PROFIT_RATE: 5, // 5%
  
  // 最大优惠比例
  MAX_DISCOUNT_RATE: 20 // 20%
}

// 贷款相关规则
export const LOAN_RULES = {
  // 默认首付比例
  DEFAULT_DOWN_PAYMENT_RATIO: 30, // 30%
  
  // 最小首付比例
  MIN_DOWN_PAYMENT_RATIO: 20, // 20%
  
  // 默认贷款期数
  DEFAULT_LOAN_MONTHS: 12,
  
  // 可选贷款期数
  AVAILABLE_LOAN_MONTHS: [12, 24, 36, 48, 60]
}

// 金额计算工具函数
export const calculateSalesPrice = (sbPrice, brand = null) => {
  if (!sbPrice || sbPrice <= 0) return 0
  
  // 根据品牌获取对应的倍数
  const multiplier = brand && SALES_PRICE_RULES.BRAND_MULTIPLIERS[brand] 
    ? SALES_PRICE_RULES.BRAND_MULTIPLIERS[brand]
    : SALES_PRICE_RULES.DEFAULT_MULTIPLIER
  
  return parseFloat((sbPrice * multiplier).toFixed(2))
}

// 计算毛利率
export const calculateProfitRate = (finalPrice, totalCost) => {
  if (!totalCost || totalCost <= 0) return 0
  
  const profitAmount = finalPrice - totalCost
  const rate = (profitAmount / totalCost) * 100
  
  return Math.max(0, parseFloat(rate.toFixed(2)))
}

// 验证价格是否合理
export const validatePricing = (salesPrice, sbPrice, discountAmount = 0) => {
  const errors = []
  
  if (salesPrice < sbPrice) {
    errors.push('销售价格不能低于启票价格')
  }
  
  if (discountAmount > 0) {
    const discountRate = (discountAmount / salesPrice) * 100
    if (discountRate > SALES_PRICE_RULES.MAX_DISCOUNT_RATE) {
      errors.push(`优惠比例不能超过${SALES_PRICE_RULES.MAX_DISCOUNT_RATE}%`)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// 计算贷款相关金额
export const calculateLoanAmounts = (finalPrice, downPaymentRatio = null) => {
  if (!finalPrice || finalPrice <= 0) {
    return {
      downPaymentAmount: 0,
      loanAmount: 0,
      downPaymentRatio: 0
    }
  }
  
  const ratio = downPaymentRatio || LOAN_RULES.DEFAULT_DOWN_PAYMENT_RATIO
  const downPaymentAmount = parseFloat((finalPrice * ratio / 100).toFixed(2))
  const loanAmount = parseFloat((finalPrice - downPaymentAmount).toFixed(2))
  
  return {
    downPaymentAmount,
    loanAmount,
    downPaymentRatio: ratio
  }
}
