/**
 * OrderEditModal 到 ConfigurableOrderForm 迁移工具
 * 提供配置转换和兼容性支持
 */

/**
 * 将 OrderEditModal 的 props 转换为 ConfigurableOrderForm 的配置
 * @param {Object} props - OrderEditModal 的 props
 * @returns {Object} ConfigurableOrderForm 的配置对象
 */
export function convertOrderEditModalPropsToConfig(props) {
  const {
    isEdit = false,
    vehicleCategoryOptions = [],
    orderStatusOptions = [],
    ...otherProps
  } = props

  // 基础配置
  const config = {
    mode: isEdit ? 'edit' : 'create',
    sections: {
      // 产品信息部分
      'product-info-section': {
        visible: true,
        editable: true,
        'show-vehicle-selector': true,
        fields: {
          vehicleBrand: { visible: true, editable: true },
          vehicleSeries: { visible: true, editable: true },
          vehicleConfig: { visible: true, editable: true },
          vehicleColorCode: { visible: true, editable: true },
          sbAmount: { visible: true, editable: true },
          salesAmount: { visible: true, editable: true },
          invoicePrice: { visible: true, editable: true }
        }
      },

      // 客户信息部分
      'customer-info-section': {
        visible: true,
        editable: true,
        'show-customer-selector': true,
        fields: {
          customerName: { visible: true, editable: true },
          customerPhone: { visible: true, editable: true },
          customerType: { visible: true, editable: true },
          salesAgentName: { visible: true, editable: true },
          salesOrgName: { visible: true, editable: true }
        }
      },

      // 出库信息部分
      'outbound-info-section': {
        visible: true,
        editable: true,
        fields: {
          outboundOrgName: { visible: true, editable: true },
          expectedOutboundDate: { visible: true, editable: true }
        }
      },

      // 付款方式部分
      'payment-method-section': {
        visible: true,
        editable: true,
        fields: {
          paymentMethod: { visible: true, editable: true },
          depositAmount: { visible: true, editable: true },
          depositDeductible: { visible: true, editable: true },
          discountAmount: { visible: true, editable: true },
          loanChannel: { visible: true, editable: true },
          loanMonths: { visible: true, editable: true },
          loanAmount: { visible: true, editable: true },
          loanInitialAmount: { visible: true, editable: true },
          loanFee: { visible: true, editable: true }
        }
      },

      // 车辆置换部分
      'vehicle-exchange-section': {
        visible: true,
        editable: true,
        fields: {
          hasUsedVehicle: { visible: true, editable: true },
          usedVehicleVin: { visible: true, editable: true },
          usedVehicleAmount: { visible: true, editable: true }
        }
      },

      // 专享优惠部分
      'exclusive-discount-section': {
        visible: true,
        editable: true,
        fields: {
          hasExclusiveDiscount: { visible: true, editable: true },
          exclusiveDiscountType: { visible: true, editable: true },
          exclusiveDiscountAmount: { visible: true, editable: true },
          exclusiveDiscountPayableDeductible: { visible: true, editable: true }
        }
      },

      // 赠品明细部分
      'gift-items-section': {
        visible: true,
        editable: true,
        fields: {
          hasGiftItems: { visible: true, editable: true },
          giftItems: { visible: true, editable: true }
        }
      },

      // 订单备注部分
      'remark-section': {
        visible: true,
        editable: true,
        fields: {
          remark: { visible: true, editable: true }
        }
      },

      // 财务结算部分
      'financial-settlement-section': {
        visible: true,
        editable: true,
        fields: {
          dealAmount: { visible: true, editable: false }, // 通常是计算字段
          dealAmountCn: { visible: true, editable: false },
          profitRate: { visible: true, editable: false },
          grossProfitAmount: { visible: true, editable: false }
        }
      }
    }
  }

  return config
}

/**
 * 创建兼容 OrderEditModal 的 ConfigurableOrderForm 配置
 * @param {Object} options - 配置选项
 * @returns {Object} 配置对象
 */
export function createCompatibleOrderFormConfig(options = {}) {
  const {
    mode = 'create',
    showVehicleSelector = true,
    showCustomerSelector = true,
    enableAllSections = true,
    readOnlyFields = [],
    hiddenFields = [],
    hiddenSections = []
  } = options

  const config = convertOrderEditModalPropsToConfig({ isEdit: mode === 'edit' })

  // 处理隐藏的 section
  hiddenSections.forEach(sectionKey => {
    if (config.sections[sectionKey]) {
      config.sections[sectionKey].visible = false
    }
  })

  // 处理隐藏的字段
  hiddenFields.forEach(fieldPath => {
    const [sectionKey, fieldKey] = fieldPath.split('.')
    if (config.sections[sectionKey]?.fields?.[fieldKey]) {
      config.sections[sectionKey].fields[fieldKey].visible = false
    }
  })

  // 处理只读字段
  readOnlyFields.forEach(fieldPath => {
    const [sectionKey, fieldKey] = fieldPath.split('.')
    if (config.sections[sectionKey]?.fields?.[fieldKey]) {
      config.sections[sectionKey].fields[fieldKey].editable = false
    }
  })

  // 处理选择器显示
  if (config.sections['product-info-section']) {
    config.sections['product-info-section']['show-vehicle-selector'] = showVehicleSelector
  }
  if (config.sections['customer-info-section']) {
    config.sections['customer-info-section']['show-customer-selector'] = showCustomerSelector
  }

  return config
}

/**
 * 获取简化的订单表单配置（类似原 OrderEditModal 的功能）
 * @param {string} mode - 'create' 或 'edit'
 * @returns {Object} 配置对象
 */
export function getSimplifiedOrderFormConfig(mode = 'create') {
  return createCompatibleOrderFormConfig({
    mode,
    showVehicleSelector: true,
    showCustomerSelector: true,
    enableAllSections: true
  })
}

/**
 * 获取只读订单表单配置
 * @returns {Object} 配置对象
 */
export function getReadOnlyOrderFormConfig() {
  const config = createCompatibleOrderFormConfig({ mode: 'edit' })
  
  // 将所有 section 设置为不可编辑
  Object.keys(config.sections).forEach(sectionKey => {
    config.sections[sectionKey].editable = false
  })

  return config
}

/**
 * 迁移辅助函数：将 OrderEditModal 的事件处理器适配到 ConfigurableOrderForm
 * @param {Object} handlers - OrderEditModal 的事件处理器
 * @returns {Object} 适配后的事件处理器
 */
export function adaptOrderEditModalHandlers(handlers) {
  const {
    onSave,
    onCancel,
    onVehicleSelected,
    onCustomerSelected,
    ...otherHandlers
  } = handlers

  return {
    save: onSave,
    cancel: onCancel,
    'vehicle-selected': onVehicleSelected,
    'customer-selected': onCustomerSelected,
    ...otherHandlers
  }
}

/**
 * 检查是否可以安全迁移到 ConfigurableOrderForm
 * @param {Object} currentProps - 当前 OrderEditModal 的 props
 * @returns {Object} 迁移检查结果
 */
export function checkMigrationCompatibility(currentProps) {
  const issues = []
  const warnings = []

  // 检查是否使用了不支持的 props
  const unsupportedProps = ['vehicleCategoryOptions', 'orderStatusOptions']
  unsupportedProps.forEach(prop => {
    if (currentProps[prop]) {
      warnings.push(`${prop} 需要通过配置方式处理`)
    }
  })

  // 检查是否有自定义的事件处理
  const customEvents = Object.keys(currentProps).filter(key => key.startsWith('on'))
  if (customEvents.length > 0) {
    warnings.push(`发现自定义事件处理器: ${customEvents.join(', ')}`)
  }

  return {
    canMigrate: issues.length === 0,
    issues,
    warnings
  }
}

export default {
  convertOrderEditModalPropsToConfig,
  createCompatibleOrderFormConfig,
  getSimplifiedOrderFormConfig,
  getReadOnlyOrderFormConfig,
  adaptOrderEditModalHandlers,
  checkMigrationCompatibility
}
