/**
 * 字典抽象架构验证工具
 * 用于验证新架构的完整性和正确性
 */

import { DICT_CODES, getDictGroupConfig } from '@/constants/dictConstants'
import { getPageDictConfig, getDictCacheStrategy } from '@/config/dictConfig'
import { dictManager } from '@/utils/dictManager'
import { useAdvancedDictOptions } from '@/composables/useAdvancedDict'

/**
 * 验证字典常量定义
 */
export function validateDictConstants() {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    info: []
  }

  // 检查字典编码定义
  const dictCodes = Object.values(DICT_CODES)
  results.info.push(`发现 ${dictCodes.length} 个字典编码定义`)

  // 检查重复编码
  const duplicates = dictCodes.filter((code, index) => dictCodes.indexOf(code) !== index)
  if (duplicates.length > 0) {
    results.errors.push(`发现重复的字典编码: ${duplicates.join(', ')}`)
    results.success = false
  }

  // 检查编码格式
  dictCodes.forEach(code => {
    if (!/^[a-z_]+$/.test(code)) {
      results.warnings.push(`字典编码格式不规范: ${code}`)
    }
  })

  return results
}

/**
 * 验证字典配置完整性
 */
export function validateDictConfigs() {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    info: []
  }

  const dictCodes = Object.values(DICT_CODES)

  // 检查字典组配置覆盖率
  let configuredCount = 0
  dictCodes.forEach(code => {
    const config = getDictGroupConfig(code)
    if (config) {
      configuredCount++
    } else {
      results.warnings.push(`字典 ${code} 缺少组配置`)
    }
  })

  results.info.push(`${configuredCount}/${dictCodes.length} 个字典有组配置`)

  // 检查缓存策略覆盖率
  let cacheStrategyCount = 0
  dictCodes.forEach(code => {
    const strategy = getDictCacheStrategy(code)
    if (strategy) {
      cacheStrategyCount++
    }
  })

  results.info.push(`${cacheStrategyCount}/${dictCodes.length} 个字典有缓存策略`)

  return results
}

/**
 * 验证页面配置
 */
export function validatePageConfigs() {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    info: []
  }

  const pageKeys = ['ORDER_EDIT', 'INVENTORY_MANAGEMENT', 'QUERY_PAGE', 'BIZ_ORG_SELECTOR']
  
  pageKeys.forEach(pageKey => {
    const config = getPageDictConfig(pageKey)
    if (!config || Object.keys(config).length === 0) {
      results.warnings.push(`页面 ${pageKey} 缺少字典配置`)
    } else {
      results.info.push(`页面 ${pageKey} 配置了 ${Object.keys(config).length} 个字典`)
      
      // 检查配置中的字典编码是否有效
      Object.entries(config).forEach(([key, dictConfig]) => {
        if (!Object.values(DICT_CODES).includes(dictConfig.dictCode)) {
          results.errors.push(`页面 ${pageKey} 中的字典配置 ${key} 使用了无效的字典编码: ${dictConfig.dictCode}`)
          results.success = false
        }
      })
    }
  })

  return results
}

/**
 * 验证组合式函数
 */
export async function validateComposables() {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    info: []
  }

  try {
    // 测试基础字典选项功能
    const testDictCode = DICT_CODES.VEHICLE_BRAND
    
    // 在Vue组件外部测试可能会有限制，这里主要检查函数是否可调用
    if (typeof useAdvancedDictOptions === 'function') {
      results.info.push('useAdvancedDictOptions 函数可用')
    } else {
      results.errors.push('useAdvancedDictOptions 函数不可用')
      results.success = false
    }

  } catch (error) {
    results.errors.push(`组合式函数验证失败: ${error.message}`)
    results.success = false
  }

  return results
}

/**
 * 验证字典管理器
 */
export async function validateDictManager() {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    info: []
  }

  try {
    // 检查字典管理器是否可用
    if (!dictManager) {
      results.errors.push('字典管理器不可用')
      results.success = false
      return results
    }

    // 检查初始化状态
    const status = dictManager.getAllDictStatus()
    results.info.push(`字典管理器状态: ${status.overall.initialized ? '已初始化' : '未初始化'}`)
    results.info.push(`已加载字典数量: ${status.overall.loadedCount}`)
    results.info.push(`失败字典数量: ${status.overall.failedCount}`)

    // 检查核心方法是否可用
    const methods = ['initialize', 'loadDict', 'refreshDict', 'getDictStatus', 'clearCache']
    methods.forEach(method => {
      if (typeof dictManager[method] === 'function') {
        results.info.push(`字典管理器方法 ${method} 可用`)
      } else {
        results.errors.push(`字典管理器方法 ${method} 不可用`)
        results.success = false
      }
    })

  } catch (error) {
    results.errors.push(`字典管理器验证失败: ${error.message}`)
    results.success = false
  }

  return results
}

/**
 * 验证组件可用性
 */
export function validateComponents() {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    info: []
  }

  // 检查组件文件是否存在（这里只能检查导入是否成功）
  const components = [
    'DictSelector',
    'CascadingDictSelector', 
    'DictTag'
  ]

  // 在实际应用中，这些组件应该通过动态导入来验证
  results.info.push(`需要验证的组件: ${components.join(', ')}`)
  results.info.push('组件验证需要在Vue环境中进行')

  return results
}

/**
 * 运行完整的架构验证
 */
export async function runFullArchitectureValidation() {
  console.log('🚀 开始字典抽象架构验证')
  
  const allResults = {
    constants: validateDictConstants(),
    configs: validateDictConfigs(),
    pageConfigs: validatePageConfigs(),
    composables: await validateComposables(),
    dictManager: await validateDictManager(),
    components: validateComponents()
  }

  // 汇总结果
  let totalErrors = 0
  let totalWarnings = 0
  let totalInfo = 0

  Object.entries(allResults).forEach(([category, result]) => {
    console.log(`\n📋 ${category.toUpperCase()} 验证结果:`)
    
    if (result.errors.length > 0) {
      console.log('❌ 错误:')
      result.errors.forEach(error => console.log(`  - ${error}`))
      totalErrors += result.errors.length
    }
    
    if (result.warnings.length > 0) {
      console.log('⚠️ 警告:')
      result.warnings.forEach(warning => console.log(`  - ${warning}`))
      totalWarnings += result.warnings.length
    }
    
    if (result.info.length > 0) {
      console.log('ℹ️ 信息:')
      result.info.forEach(info => console.log(`  - ${info}`))
      totalInfo += result.info.length
    }
    
    console.log(`✅ ${category} 验证${result.success ? '通过' : '失败'}`)
  })

  // 总体结果
  const overallSuccess = Object.values(allResults).every(result => result.success)
  
  console.log('\n🎯 验证总结:')
  console.log(`  错误: ${totalErrors}`)
  console.log(`  警告: ${totalWarnings}`)
  console.log(`  信息: ${totalInfo}`)
  console.log(`  总体结果: ${overallSuccess ? '✅ 通过' : '❌ 失败'}`)

  return {
    success: overallSuccess,
    results: allResults,
    summary: {
      errors: totalErrors,
      warnings: totalWarnings,
      info: totalInfo
    }
  }
}

// 在开发环境下暴露验证工具到全局
if (import.meta.env.DEV) {
  window.dictArchitectureValidator = {
    validateDictConstants,
    validateDictConfigs,
    validatePageConfigs,
    validateComposables,
    validateDictManager,
    validateComponents,
    runFullArchitectureValidation
  }
  
  console.log('字典架构验证工具已加载，可在控制台使用:')
  console.log('- window.dictArchitectureValidator.runFullArchitectureValidation() - 运行完整验证')
}
