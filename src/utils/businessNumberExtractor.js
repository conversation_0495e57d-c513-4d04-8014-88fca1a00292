/**
 * 业务编号提取工具函数
 * 用于从文本中提取各种业务编号，支持搜索和筛选功能
 */

/**
 * 业务编号正则表达式配置
 */
export const BUSINESS_NUMBER_PATTERNS = [
  { regex: /(DO|VO)\d{17}/g, type: 'order', name: '订单号' },
  { regex: /[A-Z]{2}[A-Z0-9]{15}/g, type: 'vin', name: 'VIN码' },
  { regex: /P\d{17}[A-Z]{4}/g, type: 'payment', name: '支付单号' },
  { regex: /R\d{17}[A-Z]{4}/g, type: 'receipt', name: '收款单号' }
];

/**
 * 从文本中提取所有业务编号
 * @param {string} text - 要处理的文本
 * @returns {Array} 提取到的业务编号数组
 */
export const extractBusinessNumbers = (text) => {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const results = [];
  let textForVinMatching = text;

  // 按顺序匹配各种业务编号
  BUSINESS_NUMBER_PATTERNS.forEach(pattern => {
    if (pattern.type === 'vin') {
      // VIN码需要特殊处理，避免与其他编号重叠
      const matches = textForVinMatching.match(pattern.regex) || [];
      matches.forEach(match => {
        results.push({
          text: match,
          type: pattern.type,
          name: pattern.name,
          position: text.indexOf(match)
        });
      });
    } else {
      // 其他业务编号直接匹配
      const matches = text.match(pattern.regex) || [];
      matches.forEach(match => {
        results.push({
          text: match,
          type: pattern.type,
          name: pattern.name,
          position: text.indexOf(match)
        });
        // 从VIN匹配文本中移除已匹配的编号，避免重叠
        textForVinMatching = textForVinMatching.replace(match, '');
      });
    }
  });

  // 按在文本中的位置排序
  return results.sort((a, b) => a.position - b.position);
};

/**
 * 检查文本是否包含指定类型的业务编号
 * @param {string} text - 要检查的文本
 * @param {string|Array} types - 要检查的业务编号类型，可以是单个类型或类型数组
 * @returns {boolean} 是否包含指定类型的业务编号
 */
export const containsBusinessNumber = (text, types = null) => {
  const numbers = extractBusinessNumbers(text);

  if (!types) {
    return numbers.length > 0;
  }

  const targetTypes = Array.isArray(types) ? types : [types];
  return numbers.some(number => targetTypes.includes(number.type));
};

/**
 * 获取文本中所有业务编号的纯文本数组
 * @param {string} text - 要处理的文本
 * @returns {Array} 业务编号文本数组
 */
export const getBusinessNumberTexts = (text) => {
  return extractBusinessNumbers(text).map(number => number.text);
};

/**
 * 检查关键字是否匹配业务编号
 * @param {string} text - 要检查的文本
 * @param {string} keyword - 搜索关键字
 * @returns {boolean} 是否匹配
 */
export const matchesBusinessNumber = (text, keyword) => {
  if (!text || !keyword) {
    return false;
  }

  const numbers = getBusinessNumberTexts(text);
  const lowerKeyword = keyword.toLowerCase();

  return numbers.some(number =>
    number.toLowerCase().includes(lowerKeyword)
  );
};

export default {
  BUSINESS_NUMBER_PATTERNS,
  extractBusinessNumbers,
  containsBusinessNumber,
  getBusinessNumberTexts,
  matchesBusinessNumber
};
