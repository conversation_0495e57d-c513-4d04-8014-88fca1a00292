/**
 * 路由加载工具
 * 提供动态路由加载和组件加载的功能
 */
import { markRaw } from 'vue'
import router from '@/router'
import NotFoundPage from '@/views/system/NotFoundPage.vue'

/**
 * 添加动态路由
 * 根据菜单数据动态添加路由配置
 * @param {Array} menus - 菜单数据数组
 */
export function addDynamicRoutes(menus) {
  if (!menus || !Array.isArray(menus)) {
    console.error('无效的菜单数据:', menus)
    return
  }

  // 使用import.meta.glob预加载所有可能的视图组件
  // 这会在构建时创建一个路径映射，但组件仍然是按需加载的
  // 同时使用多种路径格式来确保能找到组件
  const viewModules = {
    ...import.meta.glob('@/views/**/*.vue'),
    ...import.meta.glob('/src/views/**/*.vue'),
    ...import.meta.glob('../views/**/*.vue')
  }

  // 手动排除已经静态导入的NotFoundPage组件
  const notFoundPaths = [
    '@/views/system/NotFoundPage.vue',
    '/src/views/system/NotFoundPage.vue',
    '../views/system/NotFoundPage.vue'
  ];

  notFoundPaths.forEach(path => {
    if (viewModules[path]) {
      delete viewModules[path];
    }
  });

  try {
    const addRoutes = (menuItems, parentPath = '') => {
      menuItems.forEach(menu => {
        if (menu.viewPath) {
          // 构建完整路径
          const fullPath = parentPath + (menu.menuPath?.startsWith('/') ? menu.menuPath : `/${menu.menuPath || ''}`)

          // 构建可能的组件路径
          const possiblePaths = [
            `@/views/${menu.viewPath}.vue`,
            `/src/views/${menu.viewPath}.vue`,
            `../views/${menu.viewPath}.vue`
          ];

          // 检查组件是否存在于预加载的映射中
          const componentPath = possiblePaths.find(path => viewModules[path]);

          if (componentPath) {
            // 创建路由配置
            const route = {
              path: fullPath,
              name: menu.menuLabel,
              component: viewModules[componentPath], // 使用预加载的懒加载函数
              meta: {
                requiresAuth: true,
                title: menu.menuLabel,
                menuId: menu.id,
                icon: menu.menuIcon
              }
            }

            // 检查路由是否已存在
            if (!router.hasRoute(route.name)) {
              router.addRoute('Main', route)
            }
          }
        }

        // 递归处理子菜单
        if (menu.subMenus && menu.subMenus.length) {
          const newParentPath = parentPath + (menu.menuPath ? (menu.menuPath.startsWith('/') ? menu.menuPath : `/${menu.menuPath}`) : '')
          addRoutes(menu.subMenus, newParentPath)
        } else if (menu.children && menu.children.length) {
          // 兼容不同的子菜单字段名
          const newParentPath = parentPath + (menu.menuPath ? (menu.menuPath.startsWith('/') ? menu.menuPath : `/${menu.menuPath}`) : '')
          addRoutes(menu.children, newParentPath)
        }
      })
    }

    // 开始添加路由
    addRoutes(menus)
  } catch (error) {
    console.error('添加动态路由时出错:', error)
  }
}

/**
 * 加载组件
 * 根据视图路径动态加载组件
 * @param {string} viewPath - 组件的视图路径
 * @param {Function} setComponent - 设置组件的回调函数
 * @param {Object} options - 配置选项
 * @param {Object} options.modules - 预加载的模块映射
 * @param {Object} options.fallbackComponent - 找不到组件时的回退组件
 */
export async function loadComponent(viewPath, setComponent, options = {}) {
  const {
    modules = import.meta.glob('/src/views/**/*.vue'),
    fallbackComponent = NotFoundPage,
    extraPaths = []
  } = options

  try {
    if (!viewPath) {
      console.warn('视图路径为空')
      setComponent(markRaw(fallbackComponent))
      return
    }

    // 规范化视图路径，移除查询参数
    let normalizedViewPath = viewPath.startsWith('/') ? viewPath.substring(1) : viewPath;

    // 移除查询参数（如果有）
    if (normalizedViewPath.includes('?')) {
      normalizedViewPath = normalizedViewPath.split('?')[0];
    }

    // 构建可能的路径映射
    const pathMappings = [
      // 精确匹配 - 完整路径
      `/src/views/${normalizedViewPath}.vue`,
      // 如果是相对路径（不包含模块前缀）
      `/src/views/${normalizedViewPath.split('/').pop()}.vue`,
      // 尝试直接使用视图路径
      normalizedViewPath.endsWith('.vue') ? `/src/views/${normalizedViewPath}` : `/src/views/${normalizedViewPath}.vue`,
      // 尝试不同的路径格式
      `/src/views/${normalizedViewPath}`,
      // 尝试不同的路径分隔符
      `/src/views/${normalizedViewPath.replace(/\\/g, '/')}.vue`,
      // 添加额外的路径尝试
      ...extraPaths
    ]

    // 尝试按优先级顺序加载组件
    for (const path of pathMappings) {
      if (modules[path]) {
        // 记录当前匹配的路径，用于调试
        window._currentMatchedPath = path;

        try {
          const globModule = await modules[path]()

          // 检查组件是否有效
          if (globModule && globModule.default) {
            setComponent(markRaw(globModule.default))
            return
          } else {
            console.warn('组件加载成功但无效:', path, globModule)
          }
        } catch (loadError) {
          console.error('加载模块失败:', path, loadError)
        }
      }
    }
    // 提取目标组件名称（不带路径和扩展名）
    const targetComponentName = normalizedViewPath.split('/').pop().replace('.vue', '')

    // 查找可能匹配的路径
    const possiblePaths = Object.keys(modules).filter(path => {
      const pathComponentName = path.split('/').pop().replace('.vue', '')
      const isMatch = pathComponentName.toLowerCase() === targetComponentName.toLowerCase()
      return isMatch
    })

    // 如果找到多个匹配项，尝试找到最匹配的一个
    if (possiblePaths.length > 1) {
      // 尝试找到包含完整路径的匹配项
      const bestMatch = possiblePaths.find(path => {
        const pathWithoutExt = path.replace('.vue', '').toLowerCase()
        const normalizedViewPathLower = normalizedViewPath.toLowerCase()
        return pathWithoutExt.includes(normalizedViewPathLower)
      })

      if (bestMatch) {
        // 记录当前匹配的路径，用于调试
        window._currentMatchedPath = bestMatch;

        try {
          const globModule = await modules[bestMatch]()
          setComponent(markRaw(globModule.default))
          return
        } catch (loadError) {
          console.error('加载最佳匹配模块失败:', bestMatch, loadError)
        }
      }
    }

    if (possiblePaths.length > 0) {
      // 记录当前匹配的路径，用于调试
      window._currentMatchedPath = possiblePaths[0];

      try {
        const globModule = await modules[possiblePaths[0]]()
        setComponent(markRaw(globModule.default))
        return
      } catch (loadError) {
        console.error('加载模糊匹配模块失败:', possiblePaths[0], loadError)
      }
    }

    // 如果仍然找不到，尝试直接动态导入
    try {
      // 直接动态导入组件
      const module = await import(`@/views/${normalizedViewPath}.vue`)

      setComponent(markRaw(module.default))
    } catch (importError) {
      console.error('动态导入失败:', importError)
      console.warn(`未找到组件: ${viewPath}`)
      setComponent(markRaw(fallbackComponent))
    }
  } catch (error) {
    console.error('加载组件失败:', error)
    setComponent(markRaw(fallbackComponent))
  }
}

export default {
  addDynamicRoutes,
  loadComponent
}
