/**
 * 动态组件加载器
 * 根据组件路径动态加载组件
 */
import { defineAsyncComponent } from 'vue'
import NotFoundPage from '@/views/system/NotFoundPage.vue'

/**
 * 动态加载组件
 * @param {string} viewPath - 组件路径，例如 'system/UsersPage'
 * @param {object} options - 加载选项
 * @returns {Component} Vue组件
 */
export default function loadComponent(viewPath, options = {}) {
  const {
    loadingComponent = null,
    errorComponent = NotFoundPage,
    delay = 200,
    timeout = 30000
  } = options

  if (!viewPath) {
    console.error('组件路径不能为空')
    return errorComponent
  }

  // 规范化路径
  const normalizedPath = viewPath.startsWith('/') ? viewPath.substring(1) : viewPath

  // 尝试加载组件
  return defineAsyncComponent({
    loader: () => {
      try {
        // 检查是否是NotFoundPage组件
        if (normalizedPath === 'system/NotFoundPage' || normalizedPath === 'views/system/NotFoundPage') {
          return Promise.resolve(errorComponent)
        }

        // 动态导入组件
        return import(`@/views/${normalizedPath}.vue`)
          .catch(error => {
            console.error(`加载组件 ${normalizedPath} 失败:`, error)
            // 如果找不到组件，返回 404 页面
            return Promise.resolve(errorComponent)
          })
      } catch (error) {
        console.error(`加载组件 ${normalizedPath} 出错:`, error)
        return Promise.resolve(errorComponent)
      }
    },
    loadingComponent,
    errorComponent,
    delay,
    timeout,
    onError: (error, retry, fail) => {
      console.error(`组件 ${normalizedPath} 加载错误:`, error)
      fail()
    }
  })
}
