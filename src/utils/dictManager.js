/**
 * 字典管理工具
 * 提供字典数据的统一管理和操作接口
 */

import { reactive, ref, computed } from 'vue'
import { dictManagementUtils } from '@/utils/dictUtils'
import { 
  DICT_CODES, 
  getDictGroupConfig, 
  isCascadingDict,
  isNumericDict 
} from '@/constants/dictConstants'
import { 
  getPageDictConfig,
  getDictCacheStrategy,
  shouldPreloadDict,
  createDictConfigBuilder 
} from '@/config/dictConfig'

/**
 * 字典管理器类
 */
class DictManager {
  constructor() {
    // 字典状态管理
    this.state = reactive({
      initialized: false,
      loading: false,
      loadedDicts: new Set(),
      failedDicts: new Set(),
      lastUpdateTime: null,
    })
    
    // 字典数据缓存
    this.cache = new Map()
    
    // 初始化标志
    this.initPromise = null
  }
  
  /**
   * 初始化字典管理器
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    if (this.initPromise) {
      return this.initPromise
    }
    
    this.initPromise = this._doInitialize(options)
    return this.initPromise
  }
  
  /**
   * 执行初始化
   * @private
   */
  async _doInitialize(options = {}) {
    const { preloadAll = false, preloadHighFrequency = true } = options
    
    try {
      this.state.loading = true
      
      // 等待基础字典系统初始化
      await dictManagementUtils.waitForInitialization()
      
      // 预加载高频字典
      if (preloadHighFrequency) {
        await this._preloadHighFrequencyDicts()
      }
      
      // 预加载所有字典（可选）
      if (preloadAll) {
        await this._preloadAllDicts()
      }
      
      this.state.initialized = true
      this.state.lastUpdateTime = new Date()
      
      console.log('字典管理器初始化完成')
      
    } catch (error) {
      console.error('字典管理器初始化失败:', error)
      throw error
    } finally {
      this.state.loading = false
    }
  }
  
  /**
   * 预加载高频字典
   * @private
   */
  async _preloadHighFrequencyDicts() {
    const highFrequencyDicts = Object.values(DICT_CODES).filter(shouldPreloadDict)
    
    const loadPromises = highFrequencyDicts.map(async (dictCode) => {
      try {
        await this.loadDict(dictCode)
        this.state.loadedDicts.add(dictCode)
      } catch (error) {
        console.warn(`预加载字典 ${dictCode} 失败:`, error)
        this.state.failedDicts.add(dictCode)
      }
    })
    
    await Promise.allSettled(loadPromises)
  }
  
  /**
   * 预加载所有字典
   * @private
   */
  async _preloadAllDicts() {
    const allDictCodes = Object.values(DICT_CODES)
    
    const loadPromises = allDictCodes.map(async (dictCode) => {
      try {
        await this.loadDict(dictCode)
        this.state.loadedDicts.add(dictCode)
      } catch (error) {
        console.warn(`预加载字典 ${dictCode} 失败:`, error)
        this.state.failedDicts.add(dictCode)
      }
    })
    
    await Promise.allSettled(loadPromises)
  }
  
  /**
   * 加载指定字典
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项
   */
  async loadDict(dictCode) {
    try {
      // 检查缓存
      if (this.cache.has(dictCode)) {
        const cached = this.cache.get(dictCode)
        const strategy = getDictCacheStrategy(dictCode)
        
        // 检查缓存是否过期
        if (Date.now() - cached.timestamp < strategy.cacheTime) {
          return cached.data
        }
      }
      
      // 从字典工具加载
      const options = await dictManagementUtils.refresh(dictCode)
      
      // 缓存数据
      this.cache.set(dictCode, {
        data: options,
        timestamp: Date.now(),
      })
      
      this.state.loadedDicts.add(dictCode)
      this.state.failedDicts.delete(dictCode)
      
      return options
      
    } catch (error) {
      this.state.failedDicts.add(dictCode)
      throw error
    }
  }
  
  /**
   * 刷新字典数据
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项
   */
  async refreshDict(dictCode) {
    // 清除缓存
    this.cache.delete(dictCode)
    this.state.loadedDicts.delete(dictCode)
    this.state.failedDicts.delete(dictCode)
    
    // 重新加载
    return this.loadDict(dictCode)
  }
  
  /**
   * 刷新所有字典
   * @returns {Promise<void>}
   */
  async refreshAllDicts() {
    // 清除所有缓存
    this.cache.clear()
    this.state.loadedDicts.clear()
    this.state.failedDicts.clear()
    
    // 重新初始化
    this.initPromise = null
    return this.initialize()
  }
  
  /**
   * 获取字典状态
   * @param {string} dictCode - 字典编码
   * @returns {Object} 字典状态
   */
  getDictStatus(dictCode) {
    return {
      loaded: this.state.loadedDicts.has(dictCode),
      failed: this.state.failedDicts.has(dictCode),
      cached: this.cache.has(dictCode),
      loading: this.state.loading,
    }
  }
  
  /**
   * 获取所有字典状态
   * @returns {Object} 所有字典状态
   */
  getAllDictStatus() {
    const status = {}
    
    Object.values(DICT_CODES).forEach(dictCode => {
      status[dictCode] = this.getDictStatus(dictCode)
    })
    
    return {
      overall: {
        initialized: this.state.initialized,
        loading: this.state.loading,
        totalDicts: Object.values(DICT_CODES).length,
        loadedCount: this.state.loadedDicts.size,
        failedCount: this.state.failedDicts.size,
        lastUpdateTime: this.state.lastUpdateTime,
      },
      details: status,
    }
  }
  
  /**
   * 清除字典缓存
   * @param {string} [dictCode] - 字典编码，不传则清除所有
   */
  clearCache(dictCode) {
    if (dictCode) {
      this.cache.delete(dictCode)
    } else {
      this.cache.clear()
    }
  }
  
  /**
   * 创建字典配置
   * @param {string} dictCode - 字典编码
   * @returns {Object} 配置构建器
   */
  createConfig(dictCode) {
    return createDictConfigBuilder(dictCode)
  }
  
  /**
   * 获取页面字典配置
   * @param {string} pageKey - 页面标识
   * @returns {Object} 页面字典配置
   */
  getPageConfig(pageKey) {
    return getPageDictConfig(pageKey)
  }
  
  /**
   * 验证字典配置
   * @param {string} dictCode - 字典编码
   * @param {Object} config - 配置对象
   * @returns {Object} 验证结果
   */
  validateConfig(dictCode, config) {
    const errors = []
    const warnings = []
    
    // 检查字典编码是否有效
    if (!Object.values(DICT_CODES).includes(dictCode)) {
      errors.push(`无效的字典编码: ${dictCode}`)
    }
    
    // 检查级联字典配置
    if (isCascadingDict(dictCode) && !config.parentCode) {
      warnings.push(`级联字典 ${dictCode} 缺少父级配置`)
    }
    
    // 检查数值字典配置
    if (isNumericDict(dictCode) && !config.transform) {
      warnings.push(`数值字典 ${dictCode} 建议添加数值转换器`)
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings,
    }
  }
}

// 创建全局字典管理器实例
export const dictManager = new DictManager()

// 导出字典管理器类
export { DictManager }

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  window.dictManager = dictManager
  
  console.log('字典管理器已加载，可在控制台使用:')
  console.log('- window.dictManager.getAllDictStatus() - 查看所有字典状态')
  console.log('- window.dictManager.refreshDict(dictCode) - 刷新指定字典')
  console.log('- window.dictManager.refreshAllDicts() - 刷新所有字典')
  console.log('- window.dictManager.clearCache() - 清除缓存')
}
