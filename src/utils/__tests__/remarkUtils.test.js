/**
 * 备注工具函数测试
 */

import { 
  sanitizeRemark, 
  parseRemarkHistory, 
  createRemarkEntry, 
  mergeRemarkHistory,
  validateRemarkFormat 
} from '../remarkUtils';

describe('remarkUtils', () => {
  describe('sanitizeRemark', () => {
    test('应该移除换行符并替换为空格', () => {
      const input = '这是第一行\n这是第二行\r\n这是第三行';
      const expected = '这是第一行 这是第二行 这是第三行';
      expect(sanitizeRemark(input)).toBe(expected);
    });

    test('应该处理空字符串', () => {
      expect(sanitizeRemark('')).toBe('');
      expect(sanitizeRemark(null)).toBe('');
      expect(sanitizeRemark(undefined)).toBe('');
    });

    test('应该去除首尾空格', () => {
      const input = '  \n  内容  \r\n  ';
      const expected = '内容';
      expect(sanitizeRemark(input)).toBe(expected);
    });
  });

  describe('parseRemarkHistory', () => {
    test('应该正确解析标准格式的备注', () => {
      const input = '2024-01-15 10:30|张三|这是第一条备注\n2024-01-14 15:20|李四|这是第二条备注';
      const result = parseRemarkHistory(input);
      
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        time: '2024-01-15 10:30',
        author: '张三',
        content: '这是第一条备注'
      });
      expect(result[1]).toEqual({
        time: '2024-01-14 15:20',
        author: '李四',
        content: '这是第二条备注'
      });
    });

    test('应该处理包含管道符的备注内容', () => {
      const input = '2024-01-15 10:30|张三|这是备注|包含管道符|的内容';
      const result = parseRemarkHistory(input);
      
      expect(result).toHaveLength(1);
      expect(result[0].content).toBe('这是备注|包含管道符|的内容');
    });

    test('应该处理格式不正确的行', () => {
      const input = '2024-01-15 10:30|张三|正确格式\n不正确的格式\n2024-01-14|只有两部分';
      const result = parseRemarkHistory(input);
      
      expect(result).toHaveLength(3);
      expect(result[1]).toEqual({
        time: '未知时间',
        author: '未知',
        content: '不正确的格式'
      });
    });

    test('应该处理空输入', () => {
      expect(parseRemarkHistory('')).toEqual([]);
      expect(parseRemarkHistory(null)).toEqual([]);
      expect(parseRemarkHistory(undefined)).toEqual([]);
    });
  });

  describe('createRemarkEntry', () => {
    test('应该创建正确格式的备注条目', () => {
      const content = '这是一条测试备注';
      const author = '测试用户';
      const result = createRemarkEntry(content, author);
      
      expect(result).toMatch(/^\d{4}\/\d{1,2}\/\d{1,2} \d{1,2}:\d{2}:\d{2}\|测试用户\|这是一条测试备注$/);
    });

    test('应该清理备注内容中的换行符', () => {
      const content = '这是第一行\n这是第二行';
      const author = '测试用户';
      const result = createRemarkEntry(content, author);
      
      expect(result).toContain('这是第一行 这是第二行');
    });

    test('应该处理空内容', () => {
      expect(createRemarkEntry('')).toBe('');
      expect(createRemarkEntry(null)).toBe('');
      expect(createRemarkEntry('   ')).toBe('');
    });
  });

  describe('mergeRemarkHistory', () => {
    test('应该正确合并新备注到现有历史', () => {
      const newContent = '新的备注内容';
      const existingRemark = '2024-01-14 15:20|李四|旧的备注';
      const result = mergeRemarkHistory(newContent, existingRemark, '张三');
      
      const lines = result.split('\n');
      expect(lines).toHaveLength(2);
      expect(lines[0]).toContain('张三|新的备注内容');
      expect(lines[1]).toBe('2024-01-14 15:20|李四|旧的备注');
    });

    test('应该处理空的现有备注', () => {
      const newContent = '第一条备注';
      const result = mergeRemarkHistory(newContent, '', '张三');
      
      expect(result).toContain('张三|第一条备注');
      expect(result.split('\n')).toHaveLength(1);
    });

    test('应该处理空的新内容', () => {
      const existingRemark = '2024-01-14 15:20|李四|旧的备注';
      const result = mergeRemarkHistory('', existingRemark);
      
      expect(result).toBe(existingRemark);
    });
  });

  describe('validateRemarkFormat', () => {
    test('应该验证有效的备注格式', () => {
      const validRemark = '2024-01-15 10:30|张三|这是备注';
      expect(validateRemarkFormat(validRemark)).toBe(true);
    });

    test('应该拒绝无效的备注格式', () => {
      expect(validateRemarkFormat('')).toBe(false);
      expect(validateRemarkFormat(null)).toBe(false);
      expect(validateRemarkFormat(undefined)).toBe(false);
    });
  });
});
