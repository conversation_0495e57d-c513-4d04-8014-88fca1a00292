/**
 * 角色数据缓存工具
 * 用于管理业务角色数据的本地缓存
 */

// 缓存键名
const ROLES_CACHE_KEY = 'business_roles_cache';
const ROLES_CACHE_TIMESTAMP_KEY = 'business_roles_cache_timestamp';

// 缓存有效期（毫秒）- 默认30分钟
const CACHE_DURATION = 30 * 60 * 1000;

/**
 * 缓存角色数据到localStorage
 * @param {Array} roles - 角色数据数组，格式：[{label: string, value: string|number}]
 */
export function cacheRoles(roles) {
  try {
    if (!Array.isArray(roles)) {
      console.warn('cacheRoles: 角色数据必须是数组格式');
      return;
    }

    const timestamp = Date.now();
    
    // 存储角色数据
    localStorage.setItem(ROLES_CACHE_KEY, JSON.stringify(roles));
    // 存储缓存时间戳
    localStorage.setItem(ROLES_CACHE_TIMESTAMP_KEY, timestamp.toString());
    
    console.log(`角色数据已缓存: ${roles.length} 个角色`);
  } catch (error) {
    console.error('缓存角色数据失败:', error);
  }
}

/**
 * 从localStorage获取缓存的角色数据
 * @param {boolean} ignoreExpiry - 是否忽略过期时间，默认false
 * @returns {Array|null} 角色数据数组或null
 */
export function getRolesFromCache(ignoreExpiry = false) {
  try {
    const cachedData = localStorage.getItem(ROLES_CACHE_KEY);
    const cachedTimestamp = localStorage.getItem(ROLES_CACHE_TIMESTAMP_KEY);
    
    if (!cachedData || !cachedTimestamp) {
      return null;
    }

    // 检查缓存是否过期
    if (!ignoreExpiry) {
      const timestamp = parseInt(cachedTimestamp, 10);
      const now = Date.now();
      
      if (now - timestamp > CACHE_DURATION) {
        console.log('角色缓存已过期，清除缓存数据');
        clearRolesCache();
        return null;
      }
    }

    const roles = JSON.parse(cachedData);
    
    // 验证数据格式
    if (!Array.isArray(roles)) {
      console.warn('缓存的角色数据格式不正确，清除缓存');
      clearRolesCache();
      return null;
    }

    return roles;
  } catch (error) {
    console.error('获取缓存角色数据失败:', error);
    clearRolesCache();
    return null;
  }
}

/**
 * 清除角色缓存
 */
export function clearRolesCache() {
  try {
    localStorage.removeItem(ROLES_CACHE_KEY);
    localStorage.removeItem(ROLES_CACHE_TIMESTAMP_KEY);
    console.log('角色缓存已清除');
  } catch (error) {
    console.error('清除角色缓存失败:', error);
  }
}

/**
 * 检查角色缓存是否存在且有效
 * @returns {boolean} 缓存是否有效
 */
export function isRolesCacheValid() {
  const cachedData = localStorage.getItem(ROLES_CACHE_KEY);
  const cachedTimestamp = localStorage.getItem(ROLES_CACHE_TIMESTAMP_KEY);
  
  if (!cachedData || !cachedTimestamp) {
    return false;
  }

  const timestamp = parseInt(cachedTimestamp, 10);
  const now = Date.now();
  
  return (now - timestamp) <= CACHE_DURATION;
}

/**
 * 根据角色ID获取角色名称
 * @param {string|number} roleId - 角色ID
 * @returns {string} 角色名称，如果未找到返回原ID
 */
export function getRoleNameById(roleId) {
  const cachedRoles = getRolesFromCache(true); // 忽略过期时间
  
  if (!cachedRoles || !Array.isArray(cachedRoles)) {
    return roleId?.toString() || '未知角色';
  }

  const role = cachedRoles.find(r => r.value === roleId || r.value?.toString() === roleId?.toString());
  return role ? role.label : (roleId?.toString() || '未知角色');
}

/**
 * 获取缓存信息（用于调试）
 * @returns {Object} 缓存信息
 */
export function getCacheInfo() {
  const cachedData = localStorage.getItem(ROLES_CACHE_KEY);
  const cachedTimestamp = localStorage.getItem(ROLES_CACHE_TIMESTAMP_KEY);
  
  if (!cachedData || !cachedTimestamp) {
    return {
      exists: false,
      count: 0,
      timestamp: null,
      isValid: false
    };
  }

  try {
    const roles = JSON.parse(cachedData);
    const timestamp = parseInt(cachedTimestamp, 10);
    const now = Date.now();
    const isValid = (now - timestamp) <= CACHE_DURATION;
    
    return {
      exists: true,
      count: Array.isArray(roles) ? roles.length : 0,
      timestamp: new Date(timestamp).toLocaleString(),
      isValid,
      ageMinutes: Math.floor((now - timestamp) / (1000 * 60))
    };
  } catch (error) {
    return {
      exists: true,
      count: 0,
      timestamp: null,
      isValid: false,
      error: error.message
    };
  }
}
