/**
 * 表格工具函数
 */

/**
 * 获取行的唯一标识（简化版本）
 * @param {Object} row - 行数据
 * @returns {string|number} 行的唯一标识
 */
export const getRowKey = (row) => {
  // 优先使用 id 字段
  if (row.id !== undefined && row.id !== null) {
    return row.id;
  }
  
  // 如果没有 id，使用其他可能的唯一字段
  if (row.feeId) {
    return `fee-${row.feeId}`;
  }
  
  if (row.orderSn) {
    return `order-${row.orderSn}`;
  }
  
  // 最后的备选方案
  return `row-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

export default {
  getRowKey
};
