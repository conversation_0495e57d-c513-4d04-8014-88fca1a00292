import { dictOptions, dictManager, getInitPromise } from '@/mock/dictData'
import { ref, computed } from 'vue'

/**
 * 字典数据工具函数和类型定义
 */

// ===== 字典类型定义 =====

/**
 * 字典项接口
 * @typedef {Object} DictOption
 * @property {string|number} value - 字典值
 * @property {string} label - 字典标签
 * @property {string} [type] - 显示类型 (success, warning, error, info, default)
 * @property {string} [color] - 显示颜色
 * @property {number} [sort] - 排序号
 * @property {string} [remark] - 备注
 * @property {string} [parent] - 父级值（用于级联字典）
 */

/**
 * 字典组配置接口
 * @typedef {Object} DictGroupConfig
 * @property {string} code - 字典编码
 * @property {string} name - 字典名称
 * @property {string} type - 字典类型 (SYSTEM, CUSTOMIZE)
 * @property {boolean} [cascading] - 是否级联字典
 * @property {string} [parentCode] - 父级字典编码
 * @property {boolean} [cacheable] - 是否可缓存
 * @property {number} [cacheTime] - 缓存时间（毫秒）
 */

/**
 * 字典使用配置接口
 * @typedef {Object} DictUsageConfig
 * @property {boolean} [includeAll] - 是否包含"不限"选项
 * @property {string} [allLabel] - "不限"选项的标签
 * @property {any} [allValue] - "不限"选项的值
 * @property {function} [filter] - 过滤函数
 * @property {function} [transform] - 转换函数
 * @property {boolean} [lazy] - 是否懒加载
 */

/**
 * 获取字典选项列表（同步版本，兼容现有代码）
 * @param {string} dictCode - 字典编码
 * @param {boolean} includeAll - 是否包含"不限"选项
 * @returns {Array} 选项列表
 */
export function getDictOptions(dictCode, includeAll = true) {
  const options = includeAll ? [{ label: '不限', value: null }] : []

  if (dictOptions[dictCode]) {
    const dictItems = dictOptions[dictCode].map(item => ({
      label: item.optionLabel,
      value: item.optionValue,
      type: item.type,
      color: item.color,
      sort: item.sort,
      remark: item.remark
    }))

    // 按sort字段排序
    dictItems.sort((a, b) => (a.sort || 0) - (b.sort || 0))

    options.push(...dictItems)
  } else {
    // 如果字典数据不存在，尝试触发异步加载（但不等待结果）
    dictManager.getDictOptions(dictCode).catch(error => {
      console.warn(`异步加载字典 ${dictCode} 失败:`, error)
    })

    // 在开发环境下给出提示
    if (import.meta.env.DEV) {
      console.warn(`字典数据 ${dictCode} 尚未加载完成，返回空选项。建议使用 getDictOptionsAsync 或等待字典初始化完成。`)
    }
  }

  return options
}

/**
 * 获取字典选项列表（异步版本，推荐使用）
 * @param {string} dictCode - 字典编码
 * @param {boolean} includeAll - 是否包含"不限"选项
 * @returns {Promise<Array>} 选项列表
 */
export async function getDictOptionsAsync(dictCode, includeAll = true) {
  // 确保字典数据已加载
  await dictManager.getDictOptions(dictCode)

  // 使用同步方法获取结果
  return getDictOptions(dictCode, includeAll)
}

/**
 * 创建响应式的字典选项（推荐在Vue组件中使用）
 * @param {string} dictCode - 字典编码
 * @param {boolean} includeAll - 是否包含"不限"选项
 * @returns {Object} 包含选项数组和加载状态的响应式对象
 */
export function useDictOptions(dictCode, includeAll = true) {
  const loading = ref(true)
  const options = ref(includeAll ? [{ label: '不限', value: null }] : [])

  // 创建计算属性，自动响应字典数据变化
  const computedOptions = computed(() => {
    if (dictOptions[dictCode]) {
      const baseOptions = includeAll ? [{ label: '不限', value: null }] : []
      const dictItems = dictOptions[dictCode].map(item => ({
        label: item.optionLabel,
        value: item.optionValue,
        type: item.type,
        color: item.color,
        sort: item.sort,
        remark: item.remark
      }))

      // 按sort字段排序
      dictItems.sort((a, b) => (a.sort || 0) - (b.sort || 0))

      return [...baseOptions, ...dictItems]
    }
    return includeAll ? [{ label: '不限', value: null }] : []
  })

  // 异步加载字典数据
  const loadData = async () => {
    try {
      loading.value = true
      await dictManager.getDictOptions(dictCode)
      options.value = computedOptions.value
    } catch (error) {
      console.warn(`加载字典选项失败: ${dictCode}`, error)
    } finally {
      loading.value = false
    }
  }

  // 立即开始加载
  loadData()

  return {
    options: computedOptions,
    loading,
    refresh: loadData
  }
}

/**
 * 根据字典值获取字典项配置
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @returns {Object|null} 字典项配置
 */
export function getDictItem(dictCode, value) {
  if (!dictOptions[dictCode]) {
    return null
  }

  return dictOptions[dictCode].find(item => item.optionValue === value) || null
}

/**
 * 根据字典值获取字典标签
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @param {string} defaultLabel - 默认标签
 * @returns {string} 字典标签
 */
export function getDictLabel(dictCode, value, defaultLabel = '未知') {
  // 首先尝试从内存中获取
  let dictItem = getDictItem(dictCode, value)

  // 如果内存中没有，尝试从localStorage缓存中获取
  if (!dictItem && !dictOptions[dictCode]) {
    try {
      const cacheKey = `dict_cache_options_${dictCode}`
      const cached = localStorage.getItem(cacheKey)
      if (cached) {
        const cacheData = JSON.parse(cached)
        if (cacheData.value && Array.isArray(cacheData.value)) {
          // 将缓存数据加载到内存中
          dictOptions[dictCode] = cacheData.value
          dictItem = getDictItem(dictCode, value)
        }
      }
    } catch (error) {
      console.warn(`从缓存加载字典 ${dictCode} 失败:`, error)
    }

    // 如果缓存中也没有，尝试异步加载
    if (!dictItem) {
      dictManager.getDictOptions(dictCode).catch(error => {
        console.warn(`异步加载字典 ${dictCode} 失败:`, error)
      })
    }
  }

  return dictItem?.optionLabel || defaultLabel
}

/**
 * 根据字典值获取字典颜色
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @param {string} defaultColor - 默认颜色
 * @returns {string} 字典颜色
 */
export function getDictColor(dictCode, value, defaultColor = '#909399') {
  const dictItem = getDictItem(dictCode, value)
  return dictItem?.color || defaultColor
}

/**
 * 根据字典值获取字典类型
 * @param {string} dictCode - 字典编码
 * @param {string|number} value - 字典值
 * @param {string} defaultType - 默认类型
 * @returns {string} 字典类型
 */
export function getDictType(dictCode, value, defaultType = 'default') {
  // 首先尝试从内存中获取
  let dictItem = getDictItem(dictCode, value)

  // 如果内存中没有，尝试从localStorage缓存中获取
  if (!dictItem && !dictOptions[dictCode]) {
    try {
      const cacheKey = `dict_cache_options_${dictCode}`
      const cached = localStorage.getItem(cacheKey)
      if (cached) {
        const cacheData = JSON.parse(cached)
        if (cacheData.value && Array.isArray(cacheData.value)) {
          // 将缓存数据加载到内存中
          dictOptions[dictCode] = cacheData.value
          dictItem = getDictItem(dictCode, value)
        }
      }
    } catch (error) {
      console.warn(`从缓存加载字典 ${dictCode} 失败:`, error)
    }

    // 如果缓存中也没有，尝试异步加载
    if (!dictItem) {
      dictManager.getDictOptions(dictCode).catch(error => {
        console.warn(`异步加载字典 ${dictCode} 失败:`, error)
      })
    }
  }

  return dictItem?.type || defaultType
}

/**
 * 车辆品牌相关工具函数
 */
export const vehicleBrandUtils = {
  /**
   * 获取车辆品牌选项（同步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 品牌选项列表
   */
  getOptions: (includeAll = true) => {
    // 从 localStorage 读取 brands 缓存
    const cachedBrands = vehicleBrandUtils.getCachedBrands()

    // 如果缓存为空，返回所有选项
    if (!cachedBrands || cachedBrands.length === 0) {
      return getDictOptions('vehicle_brand', includeAll)
    }

    // 先获取不包含"不限"的所有选项
    const allOptionsWithoutAll = getDictOptions('vehicle_brand', false)

    // 过滤选项：只返回缓存值与字典选项的交集
    const filteredOptions = allOptionsWithoutAll.filter(option => {
      // 检查选项的 label 是否在缓存的品牌列表中（brands 返回的是中文名称）
      return cachedBrands.includes(option.label)
    })

    // 如果用户只有一个可选品牌，不添加"不限"选项
    if (filteredOptions.length === 1) {
      return filteredOptions
    }

    // 如果用户有多个可选品牌且需要包含"不限"选项，则添加
    if (includeAll && filteredOptions.length > 1) {
      return [{ label: '不限', value: null }, ...filteredOptions]
    }

    return filteredOptions
  },

  /**
   * 获取车辆品牌选项（异步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Promise<Array>} 品牌选项列表
   */
  getOptionsAsync: async (includeAll = true) => {
    // 确保字典数据已加载
    await dictManager.getDictOptions('vehicle_brand')

    // 使用同步方法获取结果
    return vehicleBrandUtils.getOptions(includeAll)
  },

  /**
   * 获取缓存的品牌列表
   * @returns {Array} 缓存的品牌值数组
   */
  getCachedBrands: () => {
    try {
      const cached = localStorage.getItem('brands')
      return cached ? JSON.parse(cached) : []
    } catch (error) {
      console.error('读取品牌缓存失败:', error)
      return []
    }
  },

  /**
   * 设置品牌缓存
   * @param {Array|string} brands - 品牌值数组或逗号分割的字符串
   */
  setCachedBrands: (brands) => {
    try {
      let brandArray = []

      if (typeof brands === 'string' && brands.trim()) {
        // 处理逗号分割的字符串
        brandArray = brands.split(',').map(brand => brand.trim()).filter(brand => brand)
      } else if (Array.isArray(brands)) {
        // 处理数组格式
        brandArray = brands.filter(brand => brand && typeof brand === 'string')
      }

      if (brandArray.length > 0) {
        localStorage.setItem('brands', JSON.stringify(brandArray))
      } else {
        // 如果没有有效的品牌值，清除缓存
        localStorage.removeItem('brands')
      }
    } catch (error) {
      console.error('设置品牌缓存失败:', error)
    }
  },

  /**
   * 清除品牌缓存
   */
  clearCachedBrands: () => {
    localStorage.removeItem('brands')
  },

  /**
   * 检查用户是否有权限访问指定品牌
   * @param {string} brandLabel - 品牌中文名称
   * @returns {boolean} 是否有权限
   */
  checkBrandPermission: (brandLabel) => {
    const cachedBrands = vehicleBrandUtils.getCachedBrands()

    // 如果没有缓存，表示没有限制
    if (!cachedBrands || cachedBrands.length === 0) {
      return true
    }

    // 检查品牌中文名称是否在允许列表中
    return cachedBrands.includes(brandLabel)
  },

  /**
   * 根据品牌中文名称获取品牌值
   * @param {string} brandLabel - 品牌中文名称
   * @returns {string|null} 品牌值
   */
  getBrandValueByLabel: (brandLabel) => {
    const allOptions = getDictOptions('vehicle_brand', false) // 不包含"不限"选项
    const option = allOptions.find(opt => opt.label === brandLabel)
    return option ? option.value : null
  },

  /**
   * 获取品牌标签
   * @param {string} value - 品牌值
   * @returns {string} 品牌标签
   */
  getLabel: (value) => getDictLabel('vehicle_brand', value),

  /**
   * 获取品牌颜色
   * @param {string} value - 品牌值
   * @returns {string} 品牌颜色
   */
  getColor: (value) => getDictColor('vehicle_brand', value),

  /**
   * 获取品牌类型
   * @param {string} value - 品牌值
   * @returns {string} 品牌类型
   */
  getType: (value) => getDictType('vehicle_brand', value)
}

/**
 * 订单状态相关工具函数
 */
export const orderStatusUtils = {
  /**
   * 获取订单状态选项（同步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('order_status', includeAll),

  /**
   * 获取订单状态选项（异步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Promise<Array>} 状态选项列表
   */
  getOptionsAsync: (includeAll = true) => getDictOptionsAsync('order_status', includeAll),

  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('order_status', value),

  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('order_status', value),

  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('order_status', value)
}

/**
 * 库存状态相关工具函数
 */
export const stockStatusUtils = {
  /**
   * 获取库存状态选项
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('stock_status', includeAll),

  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('stock_status', value),

  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('stock_status', value),

  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('stock_status', value)
}

/**
 * 定金状态相关工具函数
 */
export const depositStatusUtils = {
  /**
   * 获取定金状态选项
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('deposit_status', includeAll),

  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('deposit_status', value),

  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('deposit_status', value),

  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('deposit_status', value)
}

/**
 * 收款状态相关工具函数
 */
export const receptStatusUtils = {
  /**
   * 获取收款状态选项
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('recept_status', includeAll),

  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('recept_status', value),

  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('recept_status', value),

  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('recept_status', value)
}

/**
 * 调拨状态相关工具函数
 */
export const transferStatusUtils = {
  /**
   * 获取调拨状态选项（同步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('transfer_status', includeAll),

  /**
   * 获取调拨状态选项（异步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Promise<Array>} 状态选项列表
   */
  getOptionsAsync: (includeAll = true) => getDictOptionsAsync('transfer_status', includeAll),

  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('transfer_status', value),

  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('transfer_status', value),

  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('transfer_status', value)
}

/**
 * 返利状态相关工具函数
 */
export const rebateStatusUtils = {
  /**
   * 获取返利状态选项（同步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Array} 状态选项列表
   */
  getOptions: (includeAll = true) => getDictOptions('rebate_status', includeAll),

  /**
   * 获取返利状态选项（异步版本）
   * @param {boolean} includeAll - 是否包含"不限"选项
   * @returns {Promise<Array>} 状态选项列表
   */
  getOptionsAsync: (includeAll = true) => getDictOptionsAsync('rebate_status', includeAll),

  /**
   * 获取状态标签
   * @param {string} value - 状态值
   * @returns {string} 状态标签
   */
  getLabel: (value) => getDictLabel('rebate_status', value),

  /**
   * 获取状态颜色
   * @param {string} value - 状态值
   * @returns {string} 状态颜色
   */
  getColor: (value) => getDictColor('rebate_status', value),

  /**
   * 获取状态类型
   * @param {string} value - 状态值
   * @returns {string} 状态类型
   */
  getType: (value) => getDictType('rebate_status', value)
}

/**
 * 字典管理工具函数
 */
export const dictManagementUtils = {
  /**
   * 初始化字典数据
   * @param {boolean} force - 是否强制重新加载
   * @returns {Promise<void>}
   */
  initialize: (force = false) => dictManager.initialize(force),

  /**
   * 刷新指定字典组
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项列表
   */
  refresh: (dictCode) => dictManager.refreshDictOptions(dictCode),

  /**
   * 刷新所有字典数据
   * @returns {Promise<void>}
   */
  refreshAll: () => dictManager.refreshAll(),

  /**
   * 获取字典数据状态
   * @returns {Object} 状态信息
   */
  getStatus: () => dictManager.getStatus(),

  /**
   * 清除所有缓存
   */
  clearCache: () => dictManager.clearCache(),

  /**
   * 等待字典数据初始化完成
   * @returns {Promise<void>}
   */
  waitForInitialization: () => getInitPromise() || Promise.resolve()
}
