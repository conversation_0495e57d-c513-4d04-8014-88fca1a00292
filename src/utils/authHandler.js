import eventBus from '@/utils/eventBus'
import router from '@/router'
import { useMainStore } from '@/stores/mainStore'

// 初始化认证事件处理
export function initAuthEventHandlers() {
  // 监听登出事件
  eventBus.on('auth:logout', () => {
    console.log('收到登出事件（401自动退出），准备清理状态并跳转到登录页')

    // 获取 mainStore 实例
    const mainStore = useMainStore()

    // 使用 clearAuthState 方法清理状态，不调用后端接口
    mainStore.clearAuthState()
    console.log('状态清理完成，准备跳转到登录页')

    // 跳转到登录页
    router.push('/login')
  })
}

export default {
  initAuthEventHandlers
}
