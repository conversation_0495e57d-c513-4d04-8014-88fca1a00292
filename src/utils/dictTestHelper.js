/**
 * 字典数据测试辅助工具
 * 用于测试字典数据修复后的效果
 */

import { dictManagementUtils, useDictOptions, getDictOptionsAsync } from '@/utils/dictUtils'

/**
 * 测试字典数据加载
 */
export async function testDictDataLoading() {
  console.log('=== 开始测试字典数据加载 ===')
  
  try {
    // 1. 等待字典初始化完成
    console.log('1. 等待字典初始化完成...')
    await dictManagementUtils.waitForInitialization()
    console.log('✅ 字典初始化完成')
    
    // 2. 测试关键字典组
    const testDicts = [
      'loan_channel',
      'loan_months', 
      'vehicle_brand',
      'order_status',
      'customer_type',
      'exclusive_discount_type'
    ]
    
    console.log('2. 测试关键字典组加载...')
    for (const dictCode of testDicts) {
      try {
        const options = await getDictOptionsAsync(dictCode, false)
      } catch (error) {
        console.error(`❌ ${dictCode}: 加载失败`, error)
      }
    }
    
    // 3. 测试响应式字典选项
    console.log('3. 测试响应式字典选项...')
    const { options: loanChannelOptions, loading } = useDictOptions('loan_channel', false)
    
    // 等待加载完成
    let attempts = 0
    while (loading.value && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }
    
    if (!loading.value && loanChannelOptions.value.length > 0) {
      console.log(`✅ 响应式字典选项测试成功: ${loanChannelOptions.value.length} 个选项`)
    } else {
      console.log('❌ 响应式字典选项测试失败')
    }
    
    console.log('=== 字典数据加载测试完成 ===')
    return true
    
  } catch (error) {
    console.error('字典数据加载测试失败:', error)
    return false
  }
}

/**
 * 测试订单编辑页面字典使用
 */
export async function testOrderEditDictUsage() {
  console.log('=== 开始测试订单编辑页面字典使用 ===')
  
  try {
    // 测试贷款渠道选项
    const { options: loanChannelOptions } = useDictOptions('loan_channel', false)
    
    // 测试贷款期限选项
    const { options: rawLoanMonthsOptions } = useDictOptions('loan_months', false)
    
    // 等待加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('贷款渠道选项:', loanChannelOptions.value.length)
    console.log('贷款期限选项:', rawLoanMonthsOptions.value.length)
    
    if (loanChannelOptions.value.length > 0 && rawLoanMonthsOptions.value.length > 0) {
      console.log('✅ 订单编辑页面字典使用测试成功')
      return true
    } else {
      console.log('❌ 订单编辑页面字典使用测试失败')
      return false
    }
    
  } catch (error) {
    console.error('订单编辑页面字典使用测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export async function runAllDictTests() {
  console.log('🚀 开始运行字典数据修复测试套件')
  
  const results = []
  
  // 测试1: 字典数据加载
  const test1 = await testDictDataLoading()
  results.push({ name: '字典数据加载', success: test1 })
  
  // 测试2: 订单编辑页面字典使用
  const test2 = await testOrderEditDictUsage()
  results.push({ name: '订单编辑页面字典使用', success: test2 })
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:')
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败'
    console.log(`  ${result.name}: ${status}`)
  })
  
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 个测试通过`)
  
  return {
    success: successCount === totalCount,
    results,
    summary: `${successCount}/${totalCount} 个测试通过`
  }
}

// 在开发环境下暴露测试函数到全局
if (import.meta.env.DEV) {
  window.dictTestHelper = {
    testDictDataLoading,
    testOrderEditDictUsage,
    runAllDictTests
  }
  
  console.log('字典测试工具已加载，可在控制台使用:')
  console.log('- window.dictTestHelper.runAllDictTests() - 运行所有测试')
  console.log('- window.dictTestHelper.testDictDataLoading() - 测试字典数据加载')
  console.log('- window.dictTestHelper.testOrderEditDictUsage() - 测试订单编辑页面字典使用')
}
