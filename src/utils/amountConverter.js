/**
 * 金额转换工具函数
 * 提供更优雅的金额字段批量转换方案
 */

/**
 * 将分转换为元
 * @param {number} cents - 分值
 * @returns {number} 元值
 */
export const convertCentsToYuan = (cents) => {
  if (cents === undefined || cents === null) return undefined
  return parseFloat((cents / 100).toFixed(2))
}

/**
 * 将元转换为分
 * @param {number} yuan - 元值
 * @returns {number} 分值
 */
export const convertYuanToCents = (yuan) => {
  if (yuan === undefined || yuan === null) return undefined
  return Math.round(yuan * 100)
}

/**
 * 方案一：配置驱动的字段转换
 * 使用配置对象定义转换规则，支持字段名映射
 */
export const convertAmountFieldsWithConfig = (sourceData, targetData, config) => {
  config.forEach(({ source, target, direction = 'centsToYuan' }) => {
    if (sourceData[source] !== undefined) {
      const converter = direction === 'centsToYuan' ? convertCentsToYuan : convertYuanToCents
      targetData[target] = converter(sourceData[source])
    }
  })
}

/**
 * 方案二：基于字段名模式的自动转换
 * 自动识别金额字段并进行转换
 */
export const autoConvertAmountFields = (sourceData, targetData, direction = 'centsToYuan') => {
  // 定义金额字段的模式
  const amountFieldPatterns = [
    /Amount$/,     // 以Amount结尾
    /Price$/,      // 以Price结尾
    /Fee$/,        // 以Fee结尾
    /Income$/,     // 以Income结尾
    /Cost$/,       // 以Cost结尾
  ]
  
  const converter = direction === 'centsToYuan' ? convertCentsToYuan : convertYuanToCents
  
  Object.keys(sourceData).forEach(key => {
    const isAmountField = amountFieldPatterns.some(pattern => pattern.test(key))
    if (isAmountField && sourceData[key] !== undefined) {
      targetData[key] = converter(sourceData[key])
    } else {
      // 非金额字段直接复制
      targetData[key] = sourceData[key]
    }
  })
}

/**
 * 方案三：使用Proxy的响应式转换
 * 创建一个代理对象，自动处理金额字段的读写转换
 */
export const createAmountProxy = (data, amountFields = []) => {
  return new Proxy(data, {
    get(target, prop) {
      const value = target[prop]
      // 如果是金额字段且值为分，自动转换为元显示
      if (amountFields.includes(prop) && typeof value === 'number') {
        return convertCentsToYuan(value)
      }
      return value
    },
    
    set(target, prop, value) {
      // 如果是金额字段且设置的是元，自动转换为分存储
      if (amountFields.includes(prop) && typeof value === 'number') {
        target[prop] = convertYuanToCents(value)
      } else {
        target[prop] = value
      }
      return true
    }
  })
}

/**
 * 方案四：函数式编程风格的转换器
 * 使用高阶函数和管道模式
 */
export const createFieldTransformer = (transformRules) => {
  return (data) => {
    return Object.keys(data).reduce((result, key) => {
      const rule = transformRules[key]
      if (rule && typeof rule === 'function') {
        result[key] = rule(data[key])
      } else {
        result[key] = data[key]
      }
      return result
    }, {})
  }
}

/**
 * 方案五：装饰器模式的字段转换
 * 使用装饰器函数包装转换逻辑
 */
export const withAmountConversion = (converter) => {
  return (target, propertyKey, descriptor) => {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      // 转换输入参数中的金额字段
      const convertedArgs = args.map(arg => {
        if (typeof arg === 'object' && arg !== null) {
          return converter(arg)
        }
        return arg
      })
      
      // 调用原方法
      const result = originalMethod.apply(this, convertedArgs)
      
      // 转换返回值中的金额字段
      if (typeof result === 'object' && result !== null) {
        return converter(result)
      }
      
      return result
    }
    
    return descriptor
  }
}

// 使用示例配置
export const ORDER_AMOUNT_CONFIG = [
  { source: 'sbAmount', target: 'sbAmount', direction: 'centsToYuan' },
  { source: 'salesAmount', target: 'salesAmount', direction: 'centsToYuan' },
  { source: 'invoiceAmount', target: 'invoicePrice', direction: 'centsToYuan' },
  { source: 'depositAmount', target: 'depositAmount', direction: 'centsToYuan' },
  { source: 'discountAmount', target: 'discountAmount', direction: 'centsToYuan' },
  { source: 'dealAmount', target: 'dealAmount', direction: 'centsToYuan' },
  { source: 'grossProfitAmount', target: 'grossProfitAmount', direction: 'centsToYuan' },
]

export const ORDER_AMOUNT_FIELDS = [
  'sbAmount', 'salesAmount', 'depositAmount', 'discountAmount', 
  'dealAmount', 'grossProfitAmount', 'exclusiveDiscountAmount',
  'exclusiveDiscountReceivableAmount', 'exclusiveDiscountPayableAmount'
]
