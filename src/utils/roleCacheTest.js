/**
 * 角色缓存功能测试文件
 * 用于验证角色缓存功能是否正常工作
 */

import { 
  cacheRoles, 
  getRolesFromCache, 
  clearRolesCache, 
  isRolesCacheValid, 
  getRoleNameById,
  getCacheInfo 
} from './roleCache.js';

// 模拟角色数据
const mockRoles = [
  { label: '销售经理', value: 'sales_manager' },
  { label: '仓库管理员', value: 'warehouse_keeper' },
  { label: '财务专员', value: 'finance_specialist' },
  { label: '客服代表', value: 'customer_service' },
  { label: '系统管理员', value: 'system_admin' }
];

/**
 * 测试角色缓存功能
 */
export function testRoleCache() {
  console.log('=== 开始测试角色缓存功能 ===');
  
  // 1. 清除现有缓存
  console.log('1. 清除现有缓存');
  clearRolesCache();
  console.log('缓存信息:', getCacheInfo());
  
  // 2. 测试缓存角色数据
  console.log('\n2. 缓存角色数据');
  cacheRoles(mockRoles);
  console.log('缓存信息:', getCacheInfo());
  
  // 3. 测试从缓存获取角色数据
  console.log('\n3. 从缓存获取角色数据');
  const cachedRoles = getRolesFromCache();
  console.log('缓存的角色数据:', cachedRoles);
  console.log('数据是否一致:', JSON.stringify(cachedRoles) === JSON.stringify(mockRoles));
  
  // 4. 测试缓存有效性检查
  console.log('\n4. 测试缓存有效性');
  console.log('缓存是否有效:', isRolesCacheValid());
  
  // 5. 测试根据ID获取角色名称
  console.log('\n5. 测试根据ID获取角色名称');
  console.log('sales_manager:', getRoleNameById('sales_manager'));
  console.log('warehouse_keeper:', getRoleNameById('warehouse_keeper'));
  console.log('unknown_role:', getRoleNameById('unknown_role'));
  console.log('null:', getRoleNameById(null));
  console.log('undefined:', getRoleNameById(undefined));
  
  // 6. 测试数字ID
  console.log('\n6. 测试数字ID');
  const numericRoles = [
    { label: '管理员', value: 1 },
    { label: '普通用户', value: 2 }
  ];
  cacheRoles(numericRoles);
  console.log('数字ID 1:', getRoleNameById(1));
  console.log('数字ID 2:', getRoleNameById(2));
  console.log('字符串ID "1":', getRoleNameById('1'));
  console.log('字符串ID "2":', getRoleNameById('2'));
  
  // 7. 测试过期缓存（模拟）
  console.log('\n7. 测试过期缓存处理');
  // 手动设置一个过期的时间戳
  const expiredTimestamp = Date.now() - (31 * 60 * 1000); // 31分钟前
  localStorage.setItem('business_roles_cache_timestamp', expiredTimestamp.toString());
  console.log('设置过期时间戳后的缓存信息:', getCacheInfo());
  console.log('过期缓存获取结果:', getRolesFromCache());
  console.log('忽略过期时间的获取结果:', getRolesFromCache(true));
  
  // 8. 测试错误数据处理
  console.log('\n8. 测试错误数据处理');
  localStorage.setItem('business_roles_cache', 'invalid json');
  console.log('无效JSON数据的获取结果:', getRolesFromCache());
  
  // 9. 恢复正常缓存
  console.log('\n9. 恢复正常缓存');
  cacheRoles(mockRoles);
  console.log('最终缓存信息:', getCacheInfo());
  
  console.log('\n=== 角色缓存功能测试完成 ===');
}

/**
 * 在浏览器控制台中运行测试
 * 使用方法：在浏览器控制台中输入 window.testRoleCache()
 */
if (typeof window !== 'undefined') {
  window.testRoleCache = testRoleCache;
  console.log('角色缓存测试函数已注册到 window.testRoleCache，可在控制台中调用');
}
