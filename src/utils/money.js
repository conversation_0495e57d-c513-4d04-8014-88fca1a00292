/**
 * 金额相关工具函数
 */

/**
 * 将数字转换为中文大写金额
 * @param {number} num - 要转换的数字
 * @returns {string} 中文大写金额
 * @example
 * // 返回 "壹亿贰仟叁佰肆拾伍万陆仟柒佰捌拾玖元整"
 * convertNumberToChinese(123456789)
 * // 返回 "零元整"
 * convertNumberToChinese(0)
 */
export const convertNumberToChinese = (num) => {
  if (num === 0) return '零元整'

  // 处理小数部分
  const numStr = num.toString()
  const parts = numStr.split('.')
  const intPart = parseInt(parts[0], 10)
  const decPart = parts.length > 1 ? parts[1].padEnd(2, '0').substring(0, 2) : '00'

  // 中文数字
  const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  // 单位
  const cnIntUnits = ['', '拾', '佰', '仟']
  const cnBigUnits = ['', '万', '亿', '兆']
  // 小数单位
  const cnDecUnits = ['角', '分']

  // 转换整数部分
  const convertInteger = (integer) => {
    if (integer === 0) return '零'

    let result = ''
    let bigUnitPos = 0 // 大单位位置
    let needZero = false // 是否需要补零

    // 从个位开始处理
    const intStr = integer.toString()
    for (let i = 0; i < intStr.length; i++) {
      const pos = intStr.length - i - 1 // 当前位置（从右往左，个位为0）
      const digit = parseInt(intStr[i], 10) // 当前数字

      // 大单位（万、亿）处理
      const bigUnitIndex = Math.floor(pos / 4)
      const smallUnitIndex = pos % 4

      if (digit !== 0) {
        if (needZero) {
          result += cnNums[0] // 补零
          needZero = false
        }
        result += cnNums[digit] + cnIntUnits[smallUnitIndex]
        needZero = false
      } else {
        // 当前位为0
        if (smallUnitIndex === 0 && bigUnitIndex > 0 && bigUnitIndex !== bigUnitPos) {
          // 为整万、整亿等，需要加单位
          result += cnBigUnits[bigUnitIndex]
          bigUnitPos = bigUnitIndex
          needZero = false
        } else if (
          (smallUnitIndex > 0 && intStr[i - 1] !== '0') || // 非个位上的0，且前一位不为0
          (smallUnitIndex === 0 && i > 0 && intStr[i - 1] !== '0') // 个位上的0，且前一位不为0
        ) {
          needZero = true
        }
      }

      // 处理万、亿等大单位
      if (smallUnitIndex === 0 && digit !== 0) {
        result += cnBigUnits[bigUnitIndex]
        bigUnitPos = bigUnitIndex
      }
    }

    return result
  }

  // 转换小数部分
  const convertDecimal = (decimal) => {
    if (decimal === '00') return ''

    let result = ''
    for (let i = 0; i < 2; i++) {
      const digit = parseInt(decimal[i], 10)
      if (digit !== 0) {
        result += cnNums[digit] + cnDecUnits[i]
      }
    }

    return result
  }

  // 组合整数和小数部分
  const intResult = convertInteger(intPart)
  const decResult = convertDecimal(decPart)

  if (decResult === '') {
    return intResult + '元整'
  } else {
    return intResult + '元' + decResult
  }
}

/**
 * 格式化金额，添加千分位分隔符
 * @param {number} amount - 金额数值
 * @param {number} [precision=2] - 小数位数
 * @param {string} [prefix='¥'] - 前缀符号
 * @returns {string} 格式化后的金额字符串
 */
export const formatMoney = (amount, precision = 2, prefix = '¥') => {
  if (amount === null || amount === undefined) return `${prefix}0.00`

  // 确保amount是数字类型
  const numAmount = Number(amount)
  if (isNaN(numAmount)) return `${prefix}0.00`

  // 转换为固定小数位的字符串
  const fixedAmount = numAmount.toFixed(precision)

  // 分割整数和小数部分
  const parts = fixedAmount.split('.')
  const intPart = parts[0]
  const decPart = parts.length > 1 ? '.' + parts[1] : ''

  // 添加千分位分隔符
  const formattedIntPart = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  return `${prefix}${formattedIntPart}${decPart}`
}

/**
 * 将金额转换为万元单位并保留两位小数
 * @param {number} amount - 金额数值（元）
 * @returns {string} 转换后的万元金额字符串，如"10.53万元"
 * @example
 * // 返回 "10.53万元"
 * convertToWanYuan(105321)
 * // 返回 "0.01万元"
 * convertToWanYuan(100)
 */
export const convertToWanYuan = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) return '0.00万元'

  // 转换为万元并保留两位小数
  const inWan = amount / 10000
  return inWan.toFixed(2) + '万元'
}

/**
 * 将分转换为元
 * @param {number} cents - 金额数值（分）
 * @returns {number} 转换后的金额数值（元）
 * @example
 * // 返回 123.45
 * convertCentsToYuan(12345)
 * // 返回 0
 * convertCentsToYuan(null)
 */
export const convertCentsToYuan = (cents) => {
  if (cents === null || cents === undefined || isNaN(cents)) return 0
  return Number((cents / 100).toFixed(2))
}

/**
 * 将元转换为分
 * @param {number} yuan - 金额数值（元）
 * @returns {number} 转换后的金额数值（分）
 * @example
 * // 返回 12345
 * convertYuanToCents(123.45)
 * // 返回 0
 * convertYuanToCents(null)
 */
export const convertYuanToCents = (yuan) => {
  if (yuan === null || yuan === undefined || isNaN(yuan)) return 0
  return Math.round(yuan * 100)
}

/**
 * 渲染应收账款金额（分转元，支持用户偏好格式）
 * @param {number} amount - 金额数值（分）
 * @param {string} [prefix='￥'] - 前缀符号
 * @returns {string} 格式化后的金额字符串
 */
export const renderReceivableAmount = (amount, prefix = '￥') => {
  // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
  if (amount === 0 || amount === null || amount === undefined) {
    return `${prefix}0`;
  }

  // 将分转换为元
  const yuanAmount = amount / 100;

  // 判断是否为整数，决定小数位数
  const precision = yuanAmount % 1 === 0 ? 0 : 2;

  // 使用 formatMoney 函数格式化金额（包含千分位分隔符）
  return formatMoney(yuanAmount, precision, prefix);
};

/**
 * 渲染金额大写
 * @param {number} amount - 金额数值（元）
 * @returns {string} 中文大写金额
 */
export const renderAmountInChinese = (amount) => {
  if (!amount || amount <= 0) {
    return "零元整";
  }
  return convertNumberToChinese(amount);
};

/**
 * 将金额转换为万或亿为单位的字符串
 * @param {number} amount - 金额数值（元）
 * @returns {string} 转换后的金额字符串，如"12.35万"、"1.23亿"
 * @example
 * // 返回 "12.35万"
 * convertToWanOrYi(123456)
 * // 返回 "1.23亿"
 * convertToWanOrYi(123456789)
 * // 返回 "1234"
 * convertToWanOrYi(1234)
 */
export const convertToWanOrYi = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) return '0';

  const numAmount = Math.abs(amount);

  // 大于等于1亿，转换为亿
  if (numAmount >= 100000000) {
    const inYi = numAmount / 100000000;
    return inYi.toFixed(2).replace(/\.?0+$/, '') + '亿';
  }

  // 大于等于1万，转换为万
  if (numAmount >= 10000) {
    const inWan = numAmount / 10000;
    return inWan.toFixed(2).replace(/\.?0+$/, '') + '万';
  }

  // 小于1万，直接显示
  return numAmount.toString();
};

export default {
  convertNumberToChinese,
  formatMoney,
  convertToWanYuan,
  convertCentsToYuan,
  convertYuanToCents,
  renderReceivableAmount,
  renderAmountInChinese,
  convertToWanOrYi
}
