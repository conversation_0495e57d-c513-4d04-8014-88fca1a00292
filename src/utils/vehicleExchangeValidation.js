/**
 * 车辆置换转车款金额验证工具
 */

/**
 * 验证车辆置换转车款金额是否合理
 * @param {Object} form - 订单表单数据
 * @returns {Object} 验证结果 { valid: boolean, message?: string, details?: Array }
 */
export function validateVehicleExchangeDeductible(form) {
  // 如果没有车辆置换，直接通过验证
  if (form.hasUsedVehicle !== 'YES') {
    return { valid: true }
  }

  // 计算所有转车款选项勾选后的总金额
  let totalDeductibleAmount = 0
  const deductibleItems = []

  // 1. 已付定金（已转车款）
  if (form.depositAmount > 0 && form.depositDeductible) {
    totalDeductibleAmount += form.depositAmount
    deductibleItems.push(`已付定金: ${form.depositAmount}元`)
  }

  // 2. 现金优惠（已转车款）
  if (form.discountAmount > 0 && form.discountDeductible) {
    totalDeductibleAmount += form.discountAmount
    deductibleItems.push(`现金优惠: ${form.discountAmount}元`)
  }

  // 3. 应付-客户-分期返利（已转车款）
  if (form.loanRebatePayableAmount > 0 && form.loanRebatePayableDeductible) {
    totalDeductibleAmount += form.loanRebatePayableAmount
    deductibleItems.push(`分期返利: ${form.loanRebatePayableAmount}元`)
  }

  // 4. 应付-客户-置换补贴（已转车款）
  if (form.usedVehicleDiscountPayableAmount > 0 && form.usedVehicleDiscountPayableDeductible) {
    totalDeductibleAmount += form.usedVehicleDiscountPayableAmount
    deductibleItems.push(`置换补贴: ${form.usedVehicleDiscountPayableAmount}元`)
  }

  // 5. 置换转车款
  if (form.usedVehicleDeductibleAmount > 0) {
    totalDeductibleAmount += form.usedVehicleDeductibleAmount
    deductibleItems.push(`置换转车款: ${form.usedVehicleDeductibleAmount}元`)
  }

  // 6. 应付-客户-专项优惠（已转车款）
  if (form.exclusiveDiscountPayableAmount > 0 && form.exclusiveDiscountPayableDeductible) {
    totalDeductibleAmount += form.exclusiveDiscountPayableAmount
    deductibleItems.push(`专项优惠: ${form.exclusiveDiscountPayableAmount}元`)
  }

  // 获取车辆售价
  const vehicleSalesAmount = form.salesAmount || 0

  // 检查转车款总金额是否超过车辆售价
  if (totalDeductibleAmount > vehicleSalesAmount) {
    return {
      valid: false,
      message: `转车款总金额(${totalDeductibleAmount.toFixed(2)}元)不能超过车辆售价(${vehicleSalesAmount.toFixed(2)}元)`,
      details: deductibleItems,
      totalDeductibleAmount,
      vehicleSalesAmount
    }
  }

  // 特别检查：置换转车款不能超过车辆售价
  if (form.usedVehicleDeductibleAmount > vehicleSalesAmount) {
    return {
      valid: false,
      message: `置换转车款金额(${form.usedVehicleDeductibleAmount}元)不能超过车辆售价(${vehicleSalesAmount}元)`,
      details: [`置换转车款: ${form.usedVehicleDeductibleAmount}元`],
      totalDeductibleAmount: form.usedVehicleDeductibleAmount,
      vehicleSalesAmount
    }
  }

  // 新增检查：置换转车款不能超过置换金额
  const usedVehicleAmount = form.usedVehicleAmount || 0
  if (form.usedVehicleDeductibleAmount > usedVehicleAmount) {
    return {
      valid: false,
      message: `置换转车款金额(${form.usedVehicleDeductibleAmount}元)不能超过置换金额(${usedVehicleAmount}元)`,
      details: [`置换转车款: ${form.usedVehicleDeductibleAmount}元`, `置换金额: ${usedVehicleAmount}元`],
      totalDeductibleAmount: form.usedVehicleDeductibleAmount,
      vehicleSalesAmount,
      usedVehicleAmount
    }
  }

  return {
    valid: true,
    totalDeductibleAmount,
    vehicleSalesAmount,
    details: deductibleItems
  }
}

/**
 * 格式化验证错误消息
 * @param {Object} validationResult - 验证结果
 * @returns {string} 格式化的错误消息
 */
export function formatValidationErrorMessage(validationResult) {
  if (validationResult.valid) {
    return ''
  }

  let message = validationResult.message

  if (validationResult.details && validationResult.details.length > 0) {
    message += '\n\n请调整转车款金额或取消部分转车款选项。'
  }

  return message
}

/**
 * 计算置换差价（置换金额 - 转车款金额）
 * @param {Object} form - 订单表单数据
 * @returns {number} 置换差价
 */
export function calculateExchangeDifference(form) {
  if (form.hasUsedVehicle !== 'YES') {
    return 0
  }

  const usedVehicleAmount = form.usedVehicleAmount || 0
  const usedVehicleDeductibleAmount = form.usedVehicleDeductibleAmount || 0

  return Math.max(0, usedVehicleAmount - usedVehicleDeductibleAmount)
}

/**
 * 获取转车款建议值（不超过车辆售价和置换金额的合理值）
 * @param {Object} form - 订单表单数据
 * @returns {number} 建议的转车款金额
 */
export function getSuggestedDeductibleAmount(form) {
  if (form.hasUsedVehicle !== 'YES') {
    return 0
  }

  const vehicleSalesAmount = form.salesAmount || 0
  const usedVehicleAmount = form.usedVehicleAmount || 0

  // 建议转车款金额不超过车辆售价，也不超过置换金额
  // 取两者中的较小值作为建议上限
  return Math.min(vehicleSalesAmount, usedVehicleAmount)
}
