/**
 * 备注工具函数
 * 用于统一处理备注字段的格式化和清理
 */

/**
 * 清理备注内容中的换行符
 * 将换行符替换为空格，避免在按换行符解析时出现错误
 * @param {string} remark - 原始备注内容
 * @returns {string} 清理后的备注内容
 */
export function sanitizeRemark(remark) {
  if (!remark || typeof remark !== 'string') {
    return '';
  }
  
  // 替换所有类型的换行符为单个空格
  return remark.trim().replace(/[\r\n]+/g, ' ');
}

/**
 * 解析备注历史记录
 * 按换行符分割，然后按 | 分割为时间-作者-内容的格式
 * @param {string} remarkData - 备注数据字符串
 * @returns {Array} 解析后的备注历史数组
 */
export function parseRemarkHistory(remarkData) {
  if (!remarkData || typeof remarkData !== 'string') {
    return [];
  }

  try {
    // 如果是JSON数组格式
    if (remarkData.startsWith('[') && remarkData.endsWith(']')) {
      return JSON.parse(remarkData);
    }

    // 按换行符分割备注内容
    const lines = remarkData.split(/\r?\n/).filter(line => line.trim());
    const remarkList = [];

    for (const line of lines) {
      // 每行按 | 分割为 时间-作者-内容
      if (line.includes('|')) {
        const parts = line.split('|');
        if (parts.length >= 3) {
          remarkList.push({
            time: parts[0].trim(),
            author: parts[1].trim(),
            content: parts.slice(2).join('|').trim()
          });
        } else {
          // 如果分割后不足3部分，作为普通内容处理
          remarkList.push({
            time: '未知时间',
            author: '未知',
            content: line.trim()
          });
        }
      } else {
        // 没有 | 分隔符的行，作为普通内容处理
        remarkList.push({
          time: '未知时间',
          author: '未知',
          content: line.trim()
        });
      }
    }

    return remarkList;
  } catch (error) {
    console.warn('解析备注数据失败:', error);
    return [{
      time: '未知时间',
      author: '未知',
      content: remarkData
    }];
  }
}

/**
 * 创建新的备注条目
 * @param {string} content - 备注内容
 * @param {string} author - 作者名称（可选，默认从localStorage获取）
 * @returns {string} 格式化的备注条目
 */
export function createRemarkEntry(content, author = null) {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // 清理备注内容中的换行符
  const sanitizedContent = sanitizeRemark(content);
  
  if (!sanitizedContent) {
    return '';
  }

  const currentTime = new Date().toLocaleString('zh-CN');
  
  // 获取作者名称
  let agentName = author || '当前用户';
  if (!author) {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        agentName = user.nickname || '当前用户';
      }
    } catch (error) {
      console.warn('获取用户信息失败:', error);
    }
  }

  // 格式：时间|用户名|备注内容
  return `${currentTime}|${agentName}|${sanitizedContent}`;
}

/**
 * 合并新备注到现有备注历史
 * @param {string} newContent - 新备注内容
 * @param {string} existingRemark - 现有备注历史
 * @param {string} author - 作者名称（可选）
 * @returns {string} 合并后的备注历史
 */
export function mergeRemarkHistory(newContent, existingRemark = '', author = null) {
  if (!newContent || typeof newContent !== 'string' || !newContent.trim()) {
    return existingRemark || '';
  }

  const newRemarkEntry = createRemarkEntry(newContent, author);
  
  if (!newRemarkEntry) {
    return existingRemark || '';
  }

  // 如果已有备注，在前面添加新备注，用换行分隔
  if (existingRemark && existingRemark.trim()) {
    return `${newRemarkEntry}\n${existingRemark}`;
  } else {
    return newRemarkEntry;
  }
}

/**
 * 验证备注格式是否正确
 * @param {string} remarkData - 备注数据
 * @returns {boolean} 是否为有效格式
 */
export function validateRemarkFormat(remarkData) {
  if (!remarkData || typeof remarkData !== 'string') {
    return false;
  }

  try {
    const parsed = parseRemarkHistory(remarkData);
    return Array.isArray(parsed) && parsed.length > 0;
  } catch (error) {
    return false;
  }
}
