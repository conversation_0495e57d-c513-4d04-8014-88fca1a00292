/**
 * 字典数据调试工具
 * 用于诊断字典数据加载问题
 */

import { dictOptions, dictManager } from '@/mock/dictData'
import { dictManagementUtils } from '@/utils/dictUtils'

/**
 * 检查字典数据加载状态
 */
export async function debugDictStatus() {
  console.log('=== 字典数据调试信息 ===')

  // 1. 检查字典管理器状态
  const status = dictManagementUtils.getStatus()
  console.log('字典管理器状态:', status)

  // 3. 检查特定字典组
  const targetDicts = ['stock_status', 'transfer_status', 'rebate_status']
  console.log('检查目标字典组:')

  for (const dictCode of targetDicts) {
    console.log(`\n检查字典组: ${dictCode}`)

    // 检查是否已加载
    if (dictOptions[dictCode]) {
      console.log(`  ✅ 已加载，共 ${dictOptions[dictCode].length} 个选项`)
      dictOptions[dictCode].forEach((option, index) => {
        console.log(`    ${index + 1}. ${option.optionLabel} (${option.optionValue})`)
      })
    } else {
      console.log(`  ❌ 未加载`)

      // 尝试手动加载
      try {
        console.log(`  🔄 尝试手动加载...`)
        const options = await dictManager.getDictOptions(dictCode)
        console.log(`  ✅ 手动加载成功，共 ${options.length} 个选项`)
        options.forEach((option, index) => {
          console.log(`    ${index + 1}. ${option.optionLabel} (${option.optionValue})`)
        })
      } catch (error) {
        console.log(`  ❌ 手动加载失败:`, error)
      }
    }
  }

  // 4. 检查缓存状态
  console.log('\n检查缓存状态:')
  const cacheKeys = []
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && key.startsWith('dict_cache_')) {
      cacheKeys.push(key)
    }
  }

  console.log(`缓存键数量: ${cacheKeys.length}`)
  cacheKeys.forEach(key => {
    try {
      const cached = localStorage.getItem(key)
      const data = JSON.parse(cached)
      const age = Date.now() - data.timestamp
      console.log(`  - ${key}: ${Math.round(age / 1000)}秒前缓存`)
    } catch (error) {
      console.log(`  - ${key}: 缓存数据解析失败`)
    }
  })

  console.log('=== 调试信息结束 ===')
}

/**
 * 测试字典API调用
 */
export async function testDictAPI() {
  console.log('=== 测试字典API调用 ===')

  const testDicts = ['stock_status', 'transfer_status', 'order_status', 'vehicle_brand']

  for (const dictCode of testDicts) {
    console.log(`\n测试字典: ${dictCode}`)

    try {
      // 测试直接API调用
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/system/options/${dictCode}`, {
        headers: {
          'Authorization': localStorage.getItem('access_token') || ''
        }
      })

      if (response.ok) {
        const data = await response.json()
        console.log(`  ✅ API调用成功:`, data)
      } else {
        console.log(`  ❌ API调用失败: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.log(`  ❌ API调用异常:`, error)
    }
  }

  console.log('=== API测试结束 ===')
}

/**
 * 强制刷新指定字典
 */
export async function forceRefreshDict(dictCode) {
  console.log(`强制刷新字典: ${dictCode}`)

  try {
    const options = await dictManagementUtils.refresh(dictCode)
    return options
  } catch (error) {
    console.error(`刷新失败:`, error)
    return []
  }
}

/**
 * 清除所有字典缓存并重新加载
 */
export async function resetAllDicts() {
  console.log('清除所有字典缓存并重新加载...')

  try {
    await dictManagementUtils.refreshAll()
    console.log('重新加载完成')

    // 显示加载结果
    await debugDictStatus()
  } catch (error) {
    console.error('重新加载失败:', error)
  }
}

/**
 * 测试特定字典的加载
 */
export async function testSpecificDicts() {
  console.log('=== 测试特定字典加载 ===')

  const testDicts = ['stock_status', 'transfer_status']

  for (const dictCode of testDicts) {
    console.log(`\n测试字典: ${dictCode}`)

    try {
      // 强制刷新
      const options = await forceRefreshDict(dictCode)
      console.log(`✅ 加载成功，共 ${options.length} 个选项:`)
      options.forEach((option, index) => {
        console.log(`  ${index + 1}. ${option.label} (${option.value})`)
      })
    } catch (error) {
      console.log(`❌ 加载失败:`, error)
    }
  }

  console.log('=== 测试完成 ===')
}

/**
 * 测试响应式字典更新
 */
export async function testReactiveUpdate() {
  console.log('=== 测试响应式字典更新 ===')

  // 强制刷新一个字典，看看是否能触发响应式更新
  console.log('强制刷新 vehicle_brand 字典...')
  await forceRefreshDict('vehicle_brand')

  console.log('等待2秒，观察页面是否自动更新...')
  setTimeout(() => {
    console.log('如果页面的车辆品牌选项自动更新了，说明响应式机制工作正常')
  }, 2000)

  console.log('=== 测试完成 ===')
}

/**
 * 测试返利状态字典加载
 */
export async function testRebateStatus() {
  console.log('=== 测试返利状态字典加载 ===')

  try {
    // 检查返利状态字典是否已加载
    console.log('检查返利状态字典...')
    if (dictOptions.rebate_status) {
      console.log(`✅ 返利状态字典已加载，共 ${dictOptions.rebate_status.length} 个选项:`)
      dictOptions.rebate_status.forEach((option, index) => {
        console.log(`  ${index + 1}. ${option.optionLabel} (${option.optionValue}) - ${option.type}`)
      })
    } else {
      console.log('❌ 返利状态字典未加载，尝试手动加载...')
      const options = await forceRefreshDict('rebate_status')
      if (options.length > 0) {
        console.log(`✅ 手动加载成功，共 ${options.length} 个选项`)
      } else {
        console.log('❌ 手动加载失败')
      }
    }

    // 测试工具函数
    console.log('\n测试返利状态工具函数...')
    const { rebateStatusUtils } = await import('@/utils/dictUtils')

    const statusOptions = rebateStatusUtils.getOptions()
    console.log(`工具函数获取到 ${statusOptions.length} 个选项:`)
    statusOptions.forEach((option, index) => {
      console.log(`  ${index + 1}. ${option.label} (${option.value})`)
    })

    // 测试标签获取
    if (statusOptions.length > 1) {
      const firstStatus = statusOptions[1] // 跳过"不限"选项
      const label = rebateStatusUtils.getLabel(firstStatus.value)
      const color = rebateStatusUtils.getColor(firstStatus.value)
      const type = rebateStatusUtils.getType(firstStatus.value)

      console.log(`\n测试状态 ${firstStatus.value}:`)
      console.log(`  标签: ${label}`)
      console.log(`  颜色: ${color}`)
      console.log(`  类型: ${type}`)
    }

  } catch (error) {
    console.error('测试返利状态字典失败:', error)
  }

  console.log('=== 返利状态测试完成 ===')
}

/**
 * 在控制台中暴露调试函数
 */
if (import.meta.env.DEV) {
  window.dictDebugger = {
    debugStatus: debugDictStatus,
    testAPI: testDictAPI,
    forceRefresh: forceRefreshDict,
    resetAll: resetAllDicts,
    testReactive: testReactiveUpdate,
    testRebateStatus: testRebateStatus
  }

  console.log('字典调试工具已加载，可在控制台使用:')
  console.log('- window.dictDebugger.debugStatus() - 查看字典状态')
  console.log('- window.dictDebugger.testAPI() - 测试API调用')
  console.log('- window.dictDebugger.forceRefresh("dict_code") - 强制刷新指定字典')
  console.log('- window.dictDebugger.resetAll() - 重置所有字典')
  console.log('- window.dictDebugger.testReactive() - 测试响应式更新')
  console.log('- window.dictDebugger.testRebateStatus() - 测试返利状态字典')
}
