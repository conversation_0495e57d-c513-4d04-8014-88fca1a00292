/**
 * 字典常量定义
 * 统一管理所有字典编码和配置
 */

// ===== 字典编码常量 =====

/**
 * 业务字典编码
 */
export const DICT_CODES = {
  // 基础数据
  GIFT_CATEGORY: 'gift_category',           // 赠品类别
  VEHICLE_BRAND: 'vehicle_brand',           // 在售品牌
  VEHICLE_STATUS: 'vehicle_status',         // 车辆状态
  
  // 用户权限
  USER_PERMISSIONS: 'user_permissions',     // 用户权限
  
  // 财务相关
  RECEPT_ACCOUNT: 'recept_account',         // 收款方式
  PAYMENT_ACCOUNT: 'payment_account',       // 付款账户
  RECEIVABLE_SUBJECT: 'receivable_subject', // 应收科目
  PAYABLE_SUBJECT: 'payable_subject',       // 应付科目
  RECEIVABLE_TARGET: 'receivable_target',   // 应收对象
  PAYABLE_TARGET: 'payable_target',         // 应付对象
  
  // 贷款相关
  LOAN_CHANNEL: 'loan_channel',             // 贷款渠道
  LOAN_MONTHS: 'loan_months',               // 贷款期限
  
  // 订单相关
  ORDER_STATUS: 'order_status',             // 订单状态
  DEAL_STATUS: 'deal_status',               // 成交状态
  CUSTOMER_TYPE: 'customer_type',           // 客户类型
  SALES_STORE_TYPE: 'sales_store_type',     // 销售地类型
  
  // 优惠相关
  EXCLUSIVE_DISCOUNT_TYPE: 'exclusive_discount_type', // 专项优惠类型
  
  // 库存相关
  STOCK_STATUS: 'stock_status',             // 仓储状态
  VEHICLE_SOURCE: 'vehicle_source',         // 车辆来源
  TRANSFER_STATUS: 'transfer_status',       // 调拨状态
  
  // 返利相关
  REBATE_STATUS: 'rebate_status',           // 返利状态
  REBATE_TYPE: 'rebate_type',               // 返利类型
  
  // 资金相关
  FUND_TYPE: 'fund_type',                   // 资金类型
  RECEPT_STATUS: 'recept_status',           // 收款状态
  
  // 地理位置
  PROVINCE_CITY: 'province_city',           // 省/直辖市
  CITY_DISTRICT: 'city_district',           // 市/区
}

/**
 * 字典组配置
 */
export const DICT_GROUP_CONFIGS = {
  [DICT_CODES.GIFT_CATEGORY]: {
    code: DICT_CODES.GIFT_CATEGORY,
    name: '赠品类别',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 30 * 60 * 1000, // 30分钟
  },
  
  [DICT_CODES.VEHICLE_BRAND]: {
    code: DICT_CODES.VEHICLE_BRAND,
    name: '在售品牌',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 60 * 60 * 1000, // 1小时
  },
  
  [DICT_CODES.VEHICLE_STATUS]: {
    code: DICT_CODES.VEHICLE_STATUS,
    name: '车辆状态',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 30 * 60 * 1000,
  },
  
  [DICT_CODES.LOAN_CHANNEL]: {
    code: DICT_CODES.LOAN_CHANNEL,
    name: '贷款渠道',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 60 * 60 * 1000,
  },
  
  [DICT_CODES.LOAN_MONTHS]: {
    code: DICT_CODES.LOAN_MONTHS,
    name: '贷款期限',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 60 * 60 * 1000,
  },
  
  [DICT_CODES.ORDER_STATUS]: {
    code: DICT_CODES.ORDER_STATUS,
    name: '订单状态',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 30 * 60 * 1000,
  },
  
  [DICT_CODES.CUSTOMER_TYPE]: {
    code: DICT_CODES.CUSTOMER_TYPE,
    name: '客户类型',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 60 * 60 * 1000,
  },
  
  [DICT_CODES.EXCLUSIVE_DISCOUNT_TYPE]: {
    code: DICT_CODES.EXCLUSIVE_DISCOUNT_TYPE,
    name: '专项优惠类型',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 30 * 60 * 1000,
  },
  
  [DICT_CODES.STOCK_STATUS]: {
    code: DICT_CODES.STOCK_STATUS,
    name: '仓储状态',
    type: 'SYSTEM',
    cacheable: true,
    cacheTime: 30 * 60 * 1000,
  },
  
  [DICT_CODES.PROVINCE_CITY]: {
    code: DICT_CODES.PROVINCE_CITY,
    name: '省/直辖市',
    type: 'CUSTOMIZE',
    cascading: true,
    cacheable: true,
    cacheTime: 24 * 60 * 60 * 1000, // 24小时
  },
  
  [DICT_CODES.CITY_DISTRICT]: {
    code: DICT_CODES.CITY_DISTRICT,
    name: '市/区',
    type: 'CUSTOMIZE',
    cascading: true,
    parentCode: DICT_CODES.PROVINCE_CITY,
    cacheable: true,
    cacheTime: 24 * 60 * 60 * 1000,
  },
}

/**
 * 字典显示类型常量
 */
export const DICT_DISPLAY_TYPES = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  INFO: 'info',
  DEFAULT: 'default',
}

/**
 * 字典颜色常量
 */
export const DICT_COLORS = {
  SUCCESS: '#18a058',
  WARNING: '#f0a020',
  ERROR: '#d03050',
  INFO: '#2080f0',
  DEFAULT: '#909399',
}

/**
 * 常用字典使用配置
 */
export const COMMON_DICT_CONFIGS = {
  // 包含"不限"选项的配置
  WITH_ALL: {
    includeAll: true,
    allLabel: '不限',
    allValue: null,
  },
  
  // 不包含"不限"选项的配置
  WITHOUT_ALL: {
    includeAll: false,
  },
  
  // 懒加载配置
  LAZY_LOAD: {
    lazy: true,
    includeAll: false,
  },
  
  // 品牌选择器配置
  BRAND_SELECTOR: {
    includeAll: true,
    allLabel: '全部品牌',
    allValue: null,
    filter: (options, userBrands) => {
      if (!userBrands || userBrands.length === 0) return options
      return options.filter(option => 
        option.value === null || userBrands.includes(option.value)
      )
    },
  },
  
  // 级联选择器配置
  CASCADING: {
    includeAll: false,
    lazy: true,
  },
}

/**
 * 字典验证规则
 */
export const DICT_VALIDATION_RULES = {
  // 必填字典项
  REQUIRED_DICTS: [
    DICT_CODES.VEHICLE_BRAND,
    DICT_CODES.ORDER_STATUS,
    DICT_CODES.CUSTOMER_TYPE,
  ],
  
  // 数值类型字典项
  NUMERIC_DICTS: [
    DICT_CODES.LOAN_MONTHS,
  ],
  
  // 级联字典项
  CASCADING_DICTS: [
    {
      parent: DICT_CODES.PROVINCE_CITY,
      child: DICT_CODES.CITY_DISTRICT,
    },
  ],
}

/**
 * 获取字典组配置
 * @param {string} dictCode - 字典编码
 * @returns {DictGroupConfig|null} 字典组配置
 */
export function getDictGroupConfig(dictCode) {
  return DICT_GROUP_CONFIGS[dictCode] || null
}

/**
 * 检查字典是否为级联字典
 * @param {string} dictCode - 字典编码
 * @returns {boolean} 是否为级联字典
 */
export function isCascadingDict(dictCode) {
  const config = getDictGroupConfig(dictCode)
  return config?.cascading === true
}

/**
 * 检查字典是否为数值类型
 * @param {string} dictCode - 字典编码
 * @returns {boolean} 是否为数值类型
 */
export function isNumericDict(dictCode) {
  return DICT_VALIDATION_RULES.NUMERIC_DICTS.includes(dictCode)
}

/**
 * 获取字典的父级编码
 * @param {string} dictCode - 字典编码
 * @returns {string|null} 父级字典编码
 */
export function getParentDictCode(dictCode) {
  const config = getDictGroupConfig(dictCode)
  return config?.parentCode || null
}
