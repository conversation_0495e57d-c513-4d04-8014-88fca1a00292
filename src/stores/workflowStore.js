import { defineStore } from 'pinia'

export const useWorkflowStore = defineStore('workflow', {
  state: () => ({
    workflows: [
      { id: 1, name: '新客户开发流程', description: '针对新客户的销售流程', group: '销售', enabled: true },
      { id: 2, name: '老客户维护流程', description: '针对老客户的维护流程', group: '销售', enabled: true },
      { id: 3, name: '客户投诉处理流程', description: '处理客户投诉的标准流程', group: '客户服务', enabled: true },
      { id: 4, name: '产品退换货流程', description: '处理产品退换货的流程', group: '客户服务', enabled: false },
      { id: 5, name: '新员工入职流程', description: '新员工入职的标准流程', group: '人力资源', enabled: true },
      { id: 6, name: '员工绩效评估流程', description: '定期进行的员工绩效评估流程', group: '人力资源', enabled: true },
      { id: 7, name: '供应商筛选流程', description: '筛选新供应商的标准流程', group: '采购', enabled: true },
      { id: 8, name: '采购审批流程', description: '大额采购的审批流程', group: '采购', enabled: true },
      { id: 9, name: '新产品开发流程', description: '新产品从构思到上市的开发流程', group: '研发', enabled: true },
      { id: 10, name: '质量控制流程', description: '产品质量控制的标准流程', group: '生产', enabled: true },
    ]
  }),
  actions: {
    fetchWorkflows() {
      // 由于数据已经在 state 中，我们不需要异步操作
      // 但为了保持接口一致，我们返回一个 resolved promise
      return Promise.resolve(this.workflows)
    },
    addWorkflow(workflow) {
      const newId = Math.max(...this.workflows.map(w => w.id)) + 1
      this.workflows.push({ ...workflow, id: newId })
    },
    updateWorkflow(updatedWorkflow) {
      const index = this.workflows.findIndex(w => w.id === updatedWorkflow.id)
      if (index !== -1) {
        this.workflows[index] = updatedWorkflow
      }
    },
    deleteWorkflow(id) {
      this.workflows = this.workflows.filter(w => w.id !== id)
    }
  }
})
