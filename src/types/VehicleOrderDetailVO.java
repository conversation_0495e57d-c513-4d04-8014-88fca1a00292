package com.example.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 车辆订单详情视图对象
 * 用于订单详情展示，字段名采用驼峰命名法以兼容前端组件
 */
public class VehicleOrderDetailVO {
    
    // ==================== 基本信息 ====================
    private Long id;
    private String orderType;
    private String orderSn;
    private LocalDateTime orderDate;
    private String orderStatus;
    private LocalDateTime deliveryDate;
    private String paymentMethod;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // ==================== 客户信息 ====================
    private String customerName;
    private String customerType;
    private String dealStatus;
    private String customerIdCode;
    private String mobile;
    private String address;
    private String customerRemark;
    private String ownerOrgName;
    
    // ==================== 销售信息 ====================
    private String salesOrgName;
    private String salesAgentName;
    private String salesLeaderName;
    private String deliveryOrgName;
    private String creatorName;
    private String editorName;
    
    // ==================== 车辆信息 ====================
    private String brand;
    private String series;
    private String modelCode;
    private String modelName;
    private String configCode;
    private String configName;
    private String colorCode;
    private String skuId;
    
    // ==================== 金额信息 ====================
    private Long sbAmount;
    private Long dealAmount;
    private String dealAmountCn;
    private Long discountAmount;
    private Boolean discountDeductible;
    
    // ==================== 专享优惠信息 ====================
    private String exclusiveDiscountType;
    private Long exclusiveDiscountAmount;
    private Boolean exclusiveDiscountPayableDeductible;
    private String exclusiveDiscountRemark;
    
    // ==================== 定金信息 ====================
    private String depositType;
    private Long depositAmount;
    private Boolean depositDeductible;
    
    // ==================== 贷款信息 ====================
    private String loanChannel;
    private Integer loanMonths;
    private Long loanAmount;
    private Long loanInitialAmount;
    private BigDecimal loanInitialRatio;
    private Boolean loanRebatePayableDeductible;
    private Long loanFee;
    private Long loanRebateReceivableAmount;
    private Long loanRebatePayableAmount;
    
    // ==================== 二手车置换信息 ====================
    private String usedVehicleId;
    private String usedVehicleVin;
    private Long usedVehicleAmount;
    private Long usedVehicleDiscountPayableDeductibleAmount;
    private Long usedVehicleDiscountReceivableAmount;
    private Long usedVehicleDiscountPayableAmount;
    private Boolean usedVehicleDiscountPayableDeductible;
    private String usedVehicleBrand;
    private String usedVehicleModel;
    private String usedVehicleColor;
    
    // ==================== 其他衍生收入信息 ====================
    private String hasDerivativeIncome;
    private Long notaryFee;
    private Long carefreeIncome;
    private Long extendedWarrantyIncome;
    private Long vpsIncome;
    private Long preInterest;
    private Long licensePlateFee;
    private Long tempPlateFee;
    private Long deliveryEquipment;
    private Long otherIncome;
    
    // ==================== 保险和赠品信息 ====================
    private String hasInsurance;
    private String hasGiftItems;
    
    // ==================== Getter 和 Setter 方法 ====================
    
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getOrderType() { return orderType; }
    public void setOrderType(String orderType) { this.orderType = orderType; }
    
    public String getOrderSn() { return orderSn; }
    public void setOrderSn(String orderSn) { this.orderSn = orderSn; }
    
    public LocalDateTime getOrderDate() { return orderDate; }
    public void setOrderDate(LocalDateTime orderDate) { this.orderDate = orderDate; }
    
    public String getOrderStatus() { return orderStatus; }
    public void setOrderStatus(String orderStatus) { this.orderStatus = orderStatus; }
    
    public LocalDateTime getDeliveryDate() { return deliveryDate; }
    public void setDeliveryDate(LocalDateTime deliveryDate) { this.deliveryDate = deliveryDate; }
    
    public String getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }
    
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public String getCustomerType() { return customerType; }
    public void setCustomerType(String customerType) { this.customerType = customerType; }
    
    public String getDealStatus() { return dealStatus; }
    public void setDealStatus(String dealStatus) { this.dealStatus = dealStatus; }
    
    public String getCustomerIdCode() { return customerIdCode; }
    public void setCustomerIdCode(String customerIdCode) { this.customerIdCode = customerIdCode; }
    
    public String getMobile() { return mobile; }
    public void setMobile(String mobile) { this.mobile = mobile; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public String getCustomerRemark() { return customerRemark; }
    public void setCustomerRemark(String customerRemark) { this.customerRemark = customerRemark; }
    
    public String getOwnerOrgName() { return ownerOrgName; }
    public void setOwnerOrgName(String ownerOrgName) { this.ownerOrgName = ownerOrgName; }
    
    public String getSalesOrgName() { return salesOrgName; }
    public void setSalesOrgName(String salesOrgName) { this.salesOrgName = salesOrgName; }
    
    public String getSalesAgentName() { return salesAgentName; }
    public void setSalesAgentName(String salesAgentName) { this.salesAgentName = salesAgentName; }
    
    public String getSalesLeaderName() { return salesLeaderName; }
    public void setSalesLeaderName(String salesLeaderName) { this.salesLeaderName = salesLeaderName; }
    
    public String getDeliveryOrgName() { return deliveryOrgName; }
    public void setDeliveryOrgName(String deliveryOrgName) { this.deliveryOrgName = deliveryOrgName; }
    
    public String getCreatorName() { return creatorName; }
    public void setCreatorName(String creatorName) { this.creatorName = creatorName; }
    
    public String getEditorName() { return editorName; }
    public void setEditorName(String editorName) { this.editorName = editorName; }
    
    public String getBrand() { return brand; }
    public void setBrand(String brand) { this.brand = brand; }
    
    public String getSeries() { return series; }
    public void setSeries(String series) { this.series = series; }
    
    public String getModelCode() { return modelCode; }
    public void setModelCode(String modelCode) { this.modelCode = modelCode; }
    
    public String getModelName() { return modelName; }
    public void setModelName(String modelName) { this.modelName = modelName; }
    
    public String getConfigCode() { return configCode; }
    public void setConfigCode(String configCode) { this.configCode = configCode; }
    
    public String getConfigName() { return configName; }
    public void setConfigName(String configName) { this.configName = configName; }
    
    public String getColorCode() { return colorCode; }
    public void setColorCode(String colorCode) { this.colorCode = colorCode; }
    
    public String getSkuId() { return skuId; }
    public void setSkuId(String skuId) { this.skuId = skuId; }
    
    // 金额信息的getter/setter方法
    public Long getSbAmount() { return sbAmount; }
    public void setSbAmount(Long sbAmount) { this.sbAmount = sbAmount; }
    
    public Long getDealAmount() { return dealAmount; }
    public void setDealAmount(Long dealAmount) { this.dealAmount = dealAmount; }
    
    public String getDealAmountCn() { return dealAmountCn; }
    public void setDealAmountCn(String dealAmountCn) { this.dealAmountCn = dealAmountCn; }
    
    public Long getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(Long discountAmount) { this.discountAmount = discountAmount; }
    
    public Boolean getdiscountDeductible() { return discountDeductible; }
    public void setdiscountDeductible(Boolean discountDeductible) { this.discountDeductible = discountDeductible; }
    
    // 其他字段的getter/setter方法省略，按照相同模式添加...
    
}
