# VehicleStockPayload 使用说明

## 概述

`VehicleStockPayload` 是一个用于车辆库存出库操作的数据传输对象（DTO），包含了完整的Bean Validation注解验证。

## 字段说明

### 基本信息字段

| 字段名 | 类型 | 验证规则 | 说明 |
|--------|------|----------|------|
| `orderId` | Long | @NotNull, @Min(1) | 订单ID，必填且必须大于0 |
| `outboundId` | Long | @NotNull, @Min(1) | 出库单ID，必填且必须大于0 |
| `vin` | String | @NotBlank, @Size(17,17) | 车架号，必填且必须为17位 |

### 衍生收入字段（所有金额以分为单位）

| 字段名 | 类型 | 验证规则 | 说明 |
|--------|------|----------|------|
| `loanRebateReceivableAmount` | Long | @Min(0) | 应收-机构-分期返利，不能为负数 |
| `notaryFee` | Long | @Min(0) | 公证费，不能为负数 |
| `carefreeIncome` | Long | @Min(0) | 畅行无忧收入，不能为负数 |
| `extendedWarrantyIncome` | Long | @Min(0) | 延保收入，不能为负数 |
| `vpsIncome` | Long | @Min(0) | VPS收入，不能为负数 |
| `preInterest` | Long | @Min(0) | 前置利息，不能为负数 |
| `licensePlateFee` | Long | @Min(0) | 挂牌费，不能为负数 |
| `tempPlateFee` | Long | @Min(0) | 临牌费，不能为负数 |
| `deliveryEquipment` | Long | @Min(0) | 外卖装具收入，不能为负数 |
| `otherIncome` | Long | @Min(0) | 其他收入，不能为负数 |

## 验证注解说明

### 使用的验证注解

- `@NotNull`: 字段不能为null
- `@NotBlank`: 字符串字段不能为null、空字符串或只包含空白字符
- `@Min(value)`: 数值字段的最小值限制
- `@Size(min, max)`: 字符串长度限制

### 验证错误消息

所有验证注解都包含了中文错误消息，便于用户理解：

```java
@NotNull(message = "订单ID不能为空")
@Min(value = 1, message = "订单ID必须大于0")
private Long orderId;

@NotBlank(message = "车架号不能为空")
@Size(min = 17, max = 17, message = "车架号必须为17位")
private String vin;

@Min(value = 0, message = "畅行无忧收入不能为负数")
private Long carefreeIncome;
```

## 使用示例

### 1. 创建有效的对象

```java
VehicleStockPayload payload = new VehicleStockPayload(
    297L,                    // orderId
    297L,                    // outboundId
    "LS6C3E2S3SF017982",    // vin (17位)
    100L,                    // loanRebateReceivableAmount
    100L,                    // notaryFee
    100L,                    // carefreeIncome
    100L,                    // extendedWarrantyIncome
    100L,                    // vpsIncome
    100L,                    // preInterest
    100L,                    // licensePlateFee
    100L,                    // tempPlateFee
    100L,                    // deliveryEquipment
    200L                     // otherIncome
);
```

### 2. 手动验证对象

```java
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

// 获取验证器
ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
Validator validator = factory.getValidator();

// 验证对象
Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);

if (violations.isEmpty()) {
    System.out.println("验证通过！");
} else {
    System.out.println("验证失败：");
    violations.forEach(violation -> 
        System.out.println(violation.getPropertyPath() + ": " + violation.getMessage())
    );
}
```

### 3. 在Spring Boot中使用

在Spring Boot控制器中，可以使用`@Valid`注解自动验证：

```java
@RestController
public class VehicleController {
    
    @PutMapping("/vehicle/outbound")
    public ResponseEntity<?> updateVehicleOutbound(@Valid @RequestBody VehicleStockPayload payload) {
        // 如果验证失败，Spring会自动返回400错误和验证错误信息
        // 验证通过的代码逻辑
        return ResponseEntity.ok("更新成功");
    }
}
```

### 4. 处理验证错误

```java
@ControllerAdvice
public class ValidationExceptionHandler {
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, String>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        return ResponseEntity.badRequest().body(errors);
    }
}
```

## 常见验证错误

### 1. 必填字段为空
```
orderId: 订单ID不能为空
outboundId: 出库单ID不能为空
vin: 车架号不能为空
```

### 2. 数值验证错误
```
orderId: 订单ID必须大于0
carefreeIncome: 畅行无忧收入不能为负数
```

### 3. 字符串长度错误
```
vin: 车架号必须为17位
```

## 最佳实践

1. **总是验证输入数据**: 在处理业务逻辑之前，确保数据通过验证
2. **提供清晰的错误消息**: 使用中文错误消息，便于用户理解
3. **分组验证**: 对于复杂场景，可以使用验证分组
4. **自定义验证器**: 对于复杂的业务规则，可以创建自定义验证注解

## 依赖要求

确保项目中包含以下依赖：

```xml
<dependency>
    <groupId>javax.validation</groupId>
    <artifactId>validation-api</artifactId>
    <version>2.0.1.Final</version>
</dependency>
<dependency>
    <groupId>org.hibernate.validator</groupId>
    <artifactId>hibernate-validator</artifactId>
    <version>6.2.0.Final</version>
</dependency>
```

## 运行示例

可以运行 `VehicleStockPayloadExample.java` 来查看各种验证场景的演示。
