package com.example.types;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

/**
 * 车辆库存出库数据载荷类
 * 用于车辆出库操作的数据传输
 */
public class VehicleStockPayload {

    // ==================== 基本信息 ====================
    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Min(value = 1, message = "订单ID必须大于0")
    private Long orderId;

    /**
     * 出库单ID
     */
    @NotNull(message = "出库单ID不能为空")
    @Min(value = 1, message = "出库单ID必须大于0")
    private Long outboundId;

    /**
     * 车架号
     */
    @NotBlank(message = "车架号不能为空")
    @Size(min = 17, max = 17, message = "车架号必须为17位")
    private String vin;

    // ==================== 衍生收入信息 ====================
    /**
     * 应收-机构-分期返利（分）
     */
    @Min(value = 0, message = "应收-机构-分期返利不能为负数")
    private Long loanRebateReceivableAmount;

    /**
     * 公证费（分）
     */
    @Min(value = 0, message = "公证费不能为负数")
    private Long notaryFee;

    /**
     * 畅行无忧收入（分）
     */
    @Min(value = 0, message = "畅行无忧收入不能为负数")
    private Long carefreeIncome;

    /**
     * 延保收入（分）
     */
    @Min(value = 0, message = "延保收入不能为负数")
    private Long extendedWarrantyIncome;

    /**
     * VPS收入（分）
     */
    @Min(value = 0, message = "VPS收入不能为负数")
    private Long vpsIncome;

    /**
     * 前置利息（分）
     */
    @Min(value = 0, message = "前置利息不能为负数")
    private Long preInterest;

    /**
     * 挂牌费（分）
     */
    @Min(value = 0, message = "挂牌费不能为负数")
    private Long licensePlateFee;

    /**
     * 临牌费（分）
     */
    @Min(value = 0, message = "临牌费不能为负数")
    private Long tempPlateFee;

    /**
     * 外卖装具收入（分）
     */
    @Min(value = 0, message = "外卖装具收入不能为负数")
    private Long deliveryEquipment;

    /**
     * 其他收入（分）
     */
    @Min(value = 0, message = "其他收入不能为负数")
    private Long otherIncome;
    
    // ==================== 构造方法 ====================
    
    /**
     * 默认构造方法
     */
    public VehicleStockPayload() {
    }
    
    /**
     * 全参数构造方法
     */
    public VehicleStockPayload(Long orderId, Long outboundId, String vin, 
                              Long loanRebateReceivableAmount, Long notaryFee, 
                              Long carefreeIncome, Long extendedWarrantyIncome, 
                              Long vpsIncome, Long preInterest, Long licensePlateFee, 
                              Long tempPlateFee, Long deliveryEquipment, Long otherIncome) {
        this.orderId = orderId;
        this.outboundId = outboundId;
        this.vin = vin;
        this.loanRebateReceivableAmount = loanRebateReceivableAmount;
        this.notaryFee = notaryFee;
        this.carefreeIncome = carefreeIncome;
        this.extendedWarrantyIncome = extendedWarrantyIncome;
        this.vpsIncome = vpsIncome;
        this.preInterest = preInterest;
        this.licensePlateFee = licensePlateFee;
        this.tempPlateFee = tempPlateFee;
        this.deliveryEquipment = deliveryEquipment;
        this.otherIncome = otherIncome;
    }
    
    // ==================== Getter 和 Setter 方法 ====================
    
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public Long getOutboundId() {
        return outboundId;
    }
    
    public void setOutboundId(Long outboundId) {
        this.outboundId = outboundId;
    }
    
    public String getVin() {
        return vin;
    }
    
    public void setVin(String vin) {
        this.vin = vin;
    }
    
    public Long getLoanRebateReceivableAmount() {
        return loanRebateReceivableAmount;
    }
    
    public void setLoanRebateReceivableAmount(Long loanRebateReceivableAmount) {
        this.loanRebateReceivableAmount = loanRebateReceivableAmount;
    }
    
    public Long getNotaryFee() {
        return notaryFee;
    }
    
    public void setNotaryFee(Long notaryFee) {
        this.notaryFee = notaryFee;
    }
    
    public Long getCarefreeIncome() {
        return carefreeIncome;
    }
    
    public void setCarefreeIncome(Long carefreeIncome) {
        this.carefreeIncome = carefreeIncome;
    }
    
    public Long getExtendedWarrantyIncome() {
        return extendedWarrantyIncome;
    }
    
    public void setExtendedWarrantyIncome(Long extendedWarrantyIncome) {
        this.extendedWarrantyIncome = extendedWarrantyIncome;
    }
    
    public Long getVpsIncome() {
        return vpsIncome;
    }
    
    public void setVpsIncome(Long vpsIncome) {
        this.vpsIncome = vpsIncome;
    }
    
    public Long getPreInterest() {
        return preInterest;
    }
    
    public void setPreInterest(Long preInterest) {
        this.preInterest = preInterest;
    }
    
    public Long getLicensePlateFee() {
        return licensePlateFee;
    }
    
    public void setLicensePlateFee(Long licensePlateFee) {
        this.licensePlateFee = licensePlateFee;
    }
    
    public Long getTempPlateFee() {
        return tempPlateFee;
    }
    
    public void setTempPlateFee(Long tempPlateFee) {
        this.tempPlateFee = tempPlateFee;
    }
    
    public Long getDeliveryEquipment() {
        return deliveryEquipment;
    }
    
    public void setDeliveryEquipment(Long deliveryEquipment) {
        this.deliveryEquipment = deliveryEquipment;
    }
    
    public Long getOtherIncome() {
        return otherIncome;
    }
    
    public void setOtherIncome(Long otherIncome) {
        this.otherIncome = otherIncome;
    }
    
    // ==================== toString 方法 ====================
    
    @Override
    public String toString() {
        return "VehicleStockPayload{" +
                "orderId=" + orderId +
                ", outboundId=" + outboundId +
                ", vin='" + vin + '\'' +
                ", loanRebateReceivableAmount=" + loanRebateReceivableAmount +
                ", notaryFee=" + notaryFee +
                ", carefreeIncome=" + carefreeIncome +
                ", extendedWarrantyIncome=" + extendedWarrantyIncome +
                ", vpsIncome=" + vpsIncome +
                ", preInterest=" + preInterest +
                ", licensePlateFee=" + licensePlateFee +
                ", tempPlateFee=" + tempPlateFee +
                ", deliveryEquipment=" + deliveryEquipment +
                ", otherIncome=" + otherIncome +
                '}';
    }
}
