/**
 * 页面初始化管理器
 * 统一处理列表页面的初始化逻辑，避免重复API调用
 */

import { ref, nextTick } from 'vue'

/**
 * 页面初始化状态管理
 */
const globalInitState = {
  // 全局初始化状态
  isGlobalInitialized: ref(false),
  // 页面级初始化状态
  pageStates: new Map(),
}

/**
 * 页面初始化组合式函数
 * @param {string} pageKey - 页面唯一标识
 * @param {Object} options - 配置选项
 * @returns {Object} 初始化相关的状态和方法
 */
export function usePageInitialization(pageKey, options = {}) {
  const {
    // 初始化延迟时间（ms）
    initDelay = 150,
    // 是否启用调试日志
    debug = false,
    // 字典依赖列表
    dictDependencies = [],
    // 自定义初始化检查函数
    customInitCheck = null,
  } = options

  // 页面级初始化状态
  if (!globalInitState.pageStates.has(pageKey)) {
    globalInitState.pageStates.set(pageKey, {
      isInitialized: ref(false),
      isInitializing: ref(false),
      initPromise: null,
    })
  }

  const pageState = globalInitState.pageStates.get(pageKey)

  /**
   * 检查是否可以执行搜索操作
   * @returns {boolean}
   */
  const canPerformSearch = () => {
    const initialized = pageState.isInitialized.value
    if (debug) {
      console.log(`[PageInit] ${pageKey} - canPerformSearch:`, initialized)
    }
    return initialized
  }

  /**
   * 安全的搜索函数包装器
   * @param {Function} searchFn - 原始搜索函数
   * @returns {Function} 包装后的搜索函数
   */
  const createSafeSearchHandler = (searchFn) => {
    return (...args) => {
      if (!canPerformSearch()) {
        if (debug) {
          console.log(`[PageInit] ${pageKey} - 搜索被阻止，页面未初始化完成`)
        }
        return
      }
      if (debug) {
        console.log(`[PageInit] ${pageKey} - 执行搜索`, args)
      }
      return searchFn(...args)
    }
  }

  /**
   * 等待字典数据加载完成
   * @returns {Promise<void>}
   */
  const waitForDictionaries = async () => {
    if (dictDependencies.length === 0) {
      return
    }

    try {
      // 动态导入字典工具
      const { dictManagementUtils } = await import('@/utils/dictUtils')
      
      // 等待字典系统初始化
      await dictManagementUtils.waitForInitialization()
      
      if (debug) {
        console.log(`[PageInit] ${pageKey} - 字典数据加载完成`)
      }
    } catch (error) {
      console.warn(`[PageInit] ${pageKey} - 字典数据加载失败:`, error)
    }
  }

  /**
   * 执行页面初始化
   * @param {Function} initialDataLoader - 初始数据加载函数
   * @returns {Promise<void>}
   */
  const initializePage = async (initialDataLoader) => {
    if (pageState.isInitialized.value || pageState.isInitializing.value) {
      return pageState.initPromise
    }

    pageState.isInitializing.value = true
    
    pageState.initPromise = (async () => {
      try {
        if (debug) {
          console.log(`[PageInit] ${pageKey} - 开始初始化`)
        }

        // 1. 等待字典数据加载
        await waitForDictionaries()

        // 2. 执行自定义初始化检查
        if (customInitCheck && typeof customInitCheck === 'function') {
          await customInitCheck()
        }

        // 3. 加载初始数据
        if (initialDataLoader && typeof initialDataLoader === 'function') {
          await initialDataLoader()
        }

        // 4. 延迟一段时间确保所有组件完全挂载
        await new Promise(resolve => setTimeout(resolve, initDelay))

        // 5. 标记初始化完成
        pageState.isInitialized.value = true
        globalInitState.isGlobalInitialized.value = true

        if (debug) {
          console.log(`[PageInit] ${pageKey} - 初始化完成`)
        }

      } catch (error) {
        console.error(`[PageInit] ${pageKey} - 初始化失败:`, error)
        throw error
      } finally {
        pageState.isInitializing.value = false
      }
    })()

    return pageState.initPromise
  }

  /**
   * 重置页面初始化状态
   */
  const resetInitialization = () => {
    pageState.isInitialized.value = false
    pageState.isInitializing.value = false
    pageState.initPromise = null
    
    if (debug) {
      console.log(`[PageInit] ${pageKey} - 初始化状态已重置`)
    }
  }

  /**
   * 创建安全的字典选择器配置
   * @param {Object} baseConfig - 基础配置
   * @returns {Object} 安全的配置
   */
  const createSafeDictSelectorConfig = (baseConfig = {}) => {
    return {
      ...baseConfig,
      // 禁用自动选择，避免触发搜索
      autoSelectFirst: false,
    }
  }

  return {
    // 状态
    isInitialized: pageState.isInitialized,
    isInitializing: pageState.isInitializing,
    canPerformSearch,

    // 方法
    initializePage,
    resetInitialization,
    createSafeSearchHandler,
    createSafeDictSelectorConfig,

    // 工具方法
    waitForDictionaries,
  }
}

/**
 * 全局初始化状态
 */
export const useGlobalInitState = () => {
  return {
    isGlobalInitialized: globalInitState.isGlobalInitialized,
    getAllPageStates: () => {
      const states = {}
      globalInitState.pageStates.forEach((state, key) => {
        states[key] = {
          isInitialized: state.isInitialized.value,
          isInitializing: state.isInitializing.value,
        }
      })
      return states
    }
  }
}

/**
 * 调试工具
 */
export const pageInitDebugger = {
  /**
   * 打印所有页面的初始化状态
   */
  printAllStates() {
    console.log('=== 页面初始化状态 ===')
    console.log('全局初始化状态:', globalInitState.isGlobalInitialized.value)
    
    globalInitState.pageStates.forEach((state, key) => {
      console.log(`页面 ${key}:`, {
        isInitialized: state.isInitialized.value,
        isInitializing: state.isInitializing.value,
      })
    })
  },

  /**
   * 重置所有页面状态
   */
  resetAllStates() {
    globalInitState.isGlobalInitialized.value = false
    globalInitState.pageStates.forEach((state) => {
      state.isInitialized.value = false
      state.isInitializing.value = false
      state.initPromise = null
    })
    console.log('所有页面初始化状态已重置')
  }
}
