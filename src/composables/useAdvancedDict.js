/**
 * 高级字典组合式函数
 * 提供更强大的字典数据管理功能
 */

import { ref, computed, watch, unref } from 'vue'
import { useDictOptions, getDictOptionsAsync, getDictLabel, getDictItem } from '@/utils/dictUtils'
import { DICT_CODES, getDictGroupConfig, isCascadingDict, isNumericDict } from '@/constants/dictConstants'

/**
 * 高级字典选项组合式函数
 * @param {string} dictCode - 字典编码
 * @param {Object} config - 配置选项
 * @returns {Object} 字典数据和操作方法
 */
export function useAdvancedDictOptions(dictCode, config = {}) {
  const {
    includeAll = true,
    allLabel = '不限',
    allValue = null,
    filter = null,
    transform = null,
    lazy = false,
    dependencies = [],
  } = config

  // 基础字典数据
  const { options: baseOptions, loading, refresh } = useDictOptions(dictCode, false)
  
  // 过滤后的选项
  const filteredOptions = computed(() => {
    let options = baseOptions.value
    
    // 应用过滤器
    if (filter && typeof filter === 'function') {
      options = filter(options, ...dependencies.map(dep => unref(dep)))
    }
    
    // 应用转换器
    if (transform && typeof transform === 'function') {
      options = transform(options)
    }
    
    // 数值类型转换
    if (isNumericDict(dictCode)) {
      options = options.map(option => ({
        ...option,
        value: typeof option.value === 'string' ? parseInt(option.value) : option.value
      }))
    }
    
    // 添加"不限"选项
    if (includeAll) {
      return [{ label: allLabel, value: allValue }, ...options]
    }
    
    return options
  })

  // 监听依赖变化，重新计算选项
  if (dependencies.length > 0) {
    watch(dependencies, () => {
      // 依赖变化时可以触发重新加载
      if (!lazy) {
        refresh()
      }
    }, { deep: true })
  }

  return {
    options: filteredOptions,
    loading,
    refresh,
    // 辅助方法
    getLabel: (value) => getDictLabel(dictCode, value),
    getItem: (value) => getDictItem(dictCode, value),
    isEmpty: computed(() => filteredOptions.value.length === (includeAll ? 1 : 0)),
  }
}

/**
 * 级联字典组合式函数
 * @param {string} parentDictCode - 父级字典编码
 * @param {string} childDictCode - 子级字典编码
 * @param {Object} config - 配置选项
 * @returns {Object} 级联字典数据和操作方法
 */
export function useCascadingDict(parentDictCode, childDictCode, config = {}) {
  const {
    includeAll = true,
    allLabel = '不限',
    allValue = null,
  } = config

  // 父级选项
  const { options: parentOptions, loading: parentLoading } = useDictOptions(parentDictCode, includeAll)
  
  // 当前选中的父级值
  const selectedParentValue = ref(allValue)
  
  // 子级选项（根据父级值过滤）
  const { options: allChildOptions, loading: childLoading } = useDictOptions(childDictCode, false)
  
  // 过滤后的子级选项
  const childOptions = computed(() => {
    if (!selectedParentValue.value || selectedParentValue.value === allValue) {
      return includeAll ? [{ label: allLabel, value: allValue }] : []
    }
    
    const filtered = allChildOptions.value.filter(option => 
      option.parent === selectedParentValue.value
    )
    
    return includeAll ? [{ label: allLabel, value: allValue }, ...filtered] : filtered
  })

  // 设置父级值
  const setParentValue = (value) => {
    selectedParentValue.value = value
  }

  // 重置子级选择
  const resetChildValue = () => {
    return allValue
  }

  return {
    // 父级相关
    parentOptions,
    parentLoading,
    selectedParentValue,
    setParentValue,
    
    // 子级相关
    childOptions,
    childLoading,
    resetChildValue,
    
    // 整体状态
    loading: computed(() => parentLoading.value || childLoading.value),
  }
}

/**
 * 多字典组合式函数
 * @param {Array<string>} dictCodes - 字典编码数组
 * @param {Object} config - 配置选项
 * @returns {Object} 多字典数据
 */
export function useMultipleDict(dictCodes, config = {}) {
  const {
    includeAll = true,
    lazy = false,
  } = config

  const dictData = {}
  const loadingStates = {}

  // 为每个字典创建响应式数据
  dictCodes.forEach(dictCode => {
    const { options, loading, refresh } = useDictOptions(dictCode, includeAll)
    dictData[dictCode] = {
      options,
      loading,
      refresh,
      getLabel: (value) => getDictLabel(dictCode, value),
      getItem: (value) => getDictItem(dictCode, value),
    }
    loadingStates[dictCode] = loading
  })

  // 整体加载状态
  const loading = computed(() => 
    Object.values(loadingStates).some(state => state.value)
  )

  // 刷新所有字典
  const refreshAll = () => {
    Object.values(dictData).forEach(dict => dict.refresh())
  }

  return {
    ...dictData,
    loading,
    refreshAll,
  }
}

/**
 * 字典搜索组合式函数
 * @param {string} dictCode - 字典编码
 * @param {Object} config - 配置选项
 * @returns {Object} 搜索功能和结果
 */
export function useDictSearch(dictCode, config = {}) {
  const {
    includeAll = false,
    searchFields = ['label', 'value', 'remark'],
    caseSensitive = false,
  } = config

  const { options: allOptions, loading } = useDictOptions(dictCode, includeAll)
  const searchQuery = ref('')

  // 搜索结果
  const searchResults = computed(() => {
    if (!searchQuery.value.trim()) {
      return allOptions.value
    }

    const query = caseSensitive ? searchQuery.value : searchQuery.value.toLowerCase()
    
    return allOptions.value.filter(option => {
      return searchFields.some(field => {
        const fieldValue = option[field]
        if (!fieldValue) return false
        
        const searchValue = caseSensitive ? String(fieldValue) : String(fieldValue).toLowerCase()
        return searchValue.includes(query)
      })
    })
  })

  // 搜索方法
  const search = (query) => {
    searchQuery.value = query
  }

  // 清空搜索
  const clearSearch = () => {
    searchQuery.value = ''
  }

  return {
    searchQuery,
    searchResults,
    search,
    clearSearch,
    loading,
    hasResults: computed(() => searchResults.value.length > 0),
    resultCount: computed(() => searchResults.value.length),
  }
}

/**
 * 字典缓存组合式函数
 * @param {string} dictCode - 字典编码
 * @param {Object} config - 配置选项
 * @returns {Object} 缓存管理功能
 */
export function useDictCache(dictCode, config = {}) {
  const {
    cacheKey = `dict_cache_${dictCode}`,
    expireTime = 30 * 60 * 1000, // 30分钟
  } = config

  // 从缓存获取数据
  const getFromCache = () => {
    try {
      const cached = localStorage.getItem(cacheKey)
      if (!cached) return null

      const data = JSON.parse(cached)
      const now = Date.now()
      
      if (data.expireAt && now > data.expireAt) {
        localStorage.removeItem(cacheKey)
        return null
      }
      
      return data.options
    } catch (error) {
      console.warn('读取字典缓存失败:', error)
      return null
    }
  }

  // 保存到缓存
  const saveToCache = (options) => {
    try {
      const data = {
        options,
        expireAt: Date.now() + expireTime,
        timestamp: Date.now(),
      }
      localStorage.setItem(cacheKey, JSON.stringify(data))
    } catch (error) {
      console.warn('保存字典缓存失败:', error)
    }
  }

  // 清除缓存
  const clearCache = () => {
    localStorage.removeItem(cacheKey)
  }

  // 检查缓存是否存在
  const hasCache = () => {
    return getFromCache() !== null
  }

  return {
    getFromCache,
    saveToCache,
    clearCache,
    hasCache,
  }
}
