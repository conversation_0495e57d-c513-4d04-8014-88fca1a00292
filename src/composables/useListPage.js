/**
 * 列表页面基础组合式函数
 * 统一处理列表页面的通用逻辑，避免重复API调用
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { usePageInitialization } from './usePageInitialization'

/**
 * 列表页面组合式函数
 * @param {string} pageKey - 页面唯一标识
 * @param {Object} options - 配置选项
 * @returns {Object} 列表页面相关的状态和方法
 */
export function useListPage(pageKey, options = {}) {
  const {
    // API调用函数
    apiFunction = null,
    // 初始分页配置
    initialPagination = {
      page: 1,
      pageSize: 100,
      showSizePicker: true,
      pageSizes: [20, 50, 100, 200],
      showQuickJumper: false,
    },
    // 字典依赖
    dictDependencies = [],
    // 是否启用调试
    debug = false,
    // 自定义参数构建函数
    buildParams = null,
    // 自定义数据处理函数
    processData = null,
  } = options

  // 使用页面初始化管理器
  const {
    isInitialized,
    isInitializing,
    initializePage,
    createSafeSearchHandler,
    createSafeDictSelectorConfig,
  } = usePageInitialization(pageKey, {
    dictDependencies,
    debug,
  })

  // 基础状态
  const loading = ref(false)
  const tableData = ref([])
  const selectedRows = ref([])
  const windowHeight = ref(window.innerHeight)

  // 分页配置
  const pagination = reactive({
    ...initialPagination,
    pageCount: 1,
    itemCount: 0,
  })

  // 筛选表单 - 由具体页面传入
  const filterForm = reactive({})

  // 计算表格最大高度
  const tableMaxHeight = computed(() => {
    const screenHeight = windowHeight.value
    const pagepadding = 32
    const filterHeight = 140
    const toolbarHeight = 60
    const paginationHeight = 50
    const margin = 20

    const calculatedHeight = screenHeight - pagepadding - filterHeight - toolbarHeight - paginationHeight - margin

    let finalHeight
    if (screenHeight >= 1440) {
      finalHeight = Math.max(calculatedHeight, 700)
    } else if (screenHeight >= 1080) {
      finalHeight = Math.max(calculatedHeight, 500)
    } else if (screenHeight >= 768) {
      finalHeight = Math.max(calculatedHeight, 400)
    } else {
      finalHeight = Math.max(calculatedHeight, 300)
    }

    return finalHeight - 195
  })

  /**
   * 构建查询参数
   * @returns {Object} 查询参数
   */
  const buildQueryParams = () => {
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
    }

    // 使用自定义参数构建函数
    if (buildParams && typeof buildParams === 'function') {
      return buildParams(params, filterForm)
    }

    return params
  }

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    if (!apiFunction) {
      console.warn(`[ListPage] ${pageKey} - 未提供API函数`)
      return
    }

    loading.value = true
    try {
      const params = buildQueryParams()
      
      if (debug) {
        console.log(`[ListPage] ${pageKey} - 调用API`, params)
      }

      const response = await apiFunction(params)

      if (response.code === 200) {
        let data = response.data.list || response.data || []
        
        // 使用自定义数据处理函数
        if (processData && typeof processData === 'function') {
          data = processData(data)
        }

        tableData.value = data
        pagination.itemCount = response.data.total || data.length
        pagination.pageCount = response.data.pages || Math.ceil(pagination.itemCount / pagination.pageSize)

        // 确保当前页码与API返回的一致
        if (response.data.pageNum && pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > pagination.pageCount && pagination.pageCount > 0) {
          pagination.page = pagination.pageCount
          refreshData()
          return
        }

        if (debug) {
          console.log(`[ListPage] ${pageKey} - 数据加载成功`, {
            dataLength: data.length,
            total: pagination.itemCount,
            pages: pagination.pageCount,
          })
        }
      } else {
        console.error(`[ListPage] ${pageKey} - API返回错误:`, response.message)
      }
    } catch (error) {
      console.error(`[ListPage] ${pageKey} - 数据加载失败:`, error)
    } finally {
      loading.value = false
    }
  }

  // 创建安全的搜索处理函数
  const handleSearch = createSafeSearchHandler(() => {
    pagination.page = 1
    refreshData()
  })

  /**
   * 处理页码变化
   */
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  /**
   * 处理页面大小变化
   */
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  /**
   * 处理选择变化
   */
  const handleSelectionChange = (keys) => {
    selectedRows.value = tableData.value.filter(item => keys.includes(item.id))
  }

  /**
   * 窗口大小变化处理
   */
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  /**
   * 初始化页面
   */
  const initPage = async () => {
    await initializePage(refreshData)
  }

  // 生命周期
  onMounted(() => {
    initPage()
    window.addEventListener('resize', handleResize, { passive: true })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    // 状态
    loading,
    tableData,
    selectedRows,
    pagination,
    filterForm,
    windowHeight,
    isInitialized,
    isInitializing,

    // 计算属性
    tableMaxHeight,

    // 方法
    refreshData,
    handleSearch,
    handlePageChange,
    handlePageSizeChange,
    handleSelectionChange,
    buildQueryParams,

    // 工具方法
    createSafeDictSelectorConfig,
    
    // 内部方法（供高级用法）
    initPage,
  }
}

/**
 * 创建字典筛选器配置
 * @param {Object} baseConfig - 基础配置
 * @returns {Object} 安全的字典筛选器配置
 */
export function createDictFilterConfig(baseConfig = {}) {
  return {
    includeAll: true,
    autoSelectFirst: false, // 默认禁用自动选择
    autoSelectDelay: 200,   // 如果需要自动选择，延迟200ms
    ...baseConfig,
  }
}

/**
 * 创建标准的筛选参数构建函数
 * @param {Object} fieldMappings - 字段映射配置
 * @returns {Function} 参数构建函数
 */
export function createStandardParamsBuilder(fieldMappings = {}) {
  return (baseParams, filterForm) => {
    const params = { ...baseParams }

    // 处理关键字搜索
    if (filterForm.keywords) {
      params.keywords = filterForm.keywords
    }

    // 处理日期范围
    if (filterForm.dateRange && fieldMappings.dateRange) {
      const { getDateRangeParams } = require('@/utils/dateRange')
      const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
      if (dateRange.startDate) params[fieldMappings.dateRange.startField || 'startDate'] = dateRange.startDate
      if (dateRange.endDate) params[fieldMappings.dateRange.endField || 'endDate'] = dateRange.endDate
    }

    // 处理其他字段映射
    Object.entries(fieldMappings).forEach(([formField, apiField]) => {
      if (formField !== 'dateRange' && filterForm[formField]) {
        if (typeof apiField === 'string') {
          params[apiField] = filterForm[formField]
        } else if (typeof apiField === 'function') {
          const result = apiField(filterForm[formField], filterForm)
          if (result && typeof result === 'object') {
            Object.assign(params, result)
          }
        }
      }
    })

    return params
  }
}
