/**
 * Naive UI 全局配置插件
 * 在应用启动时设置所有组件的默认属性
 */

import { numberInputConfig } from '@/config/inputConfig'

/**
 * 安装 Naive UI 全局配置
 * @param {Object} app - Vue 应用实例
 */
export function installNaiveUIGlobalConfig(app) {
  // 设置全局属性，可以在所有组件中访问
  app.config.globalProperties.$naiveUIDefaults = {
    // 数字输入框默认配置
    numberInput: {
      min: numberInputConfig.min,
      max: numberInputConfig.max,
      precision: numberInputConfig.precision,
      step: numberInputConfig.step,
      buttonPlacement: 'both',
      showButton: true
    },
    
    // 金额输入框配置
    amountInput: {
      ...numberInputConfig.amount,
      buttonPlacement: 'both',
      showButton: true
    },
    
    // 数量输入框配置
    quantityInput: {
      ...numberInputConfig.quantity,
      buttonPlacement: 'both',
      showButton: true
    },
    
    // 百分比输入框配置
    percentageInput: {
      ...numberInputConfig.percentage,
      buttonPlacement: 'both',
      showButton: true
    },
    
    // 价格输入框配置
    priceInput: {
      ...numberInputConfig.price,
      buttonPlacement: 'both',
      showButton: true
    }
  }
  
  // 提供全局混入方法
  app.provide('naiveUIDefaults', app.config.globalProperties.$naiveUIDefaults)
}

/**
 * 组合式函数：在组件中使用默认配置
 */
export function useNaiveUIDefaults() {
  const { inject } = require('vue')
  return inject('naiveUIDefaults', {})
}

/**
 * 创建带有默认配置的数字输入框属性
 * @param {string} type - 输入框类型 (amount, quantity, percentage, price)
 * @param {Object} customProps - 自定义属性
 * @returns {Object} 合并后的属性对象
 */
export function createNumberInputProps(type = 'default', customProps = {}) {
  const config = numberInputConfig[type] || numberInputConfig
  
  return {
    min: config.min,
    max: config.max,
    precision: config.precision,
    step: config.step,
    placeholder: config.placeholder,
    buttonPlacement: 'both',
    showButton: true,
    ...customProps
  }
}

/**
 * 快捷方法：创建金额输入框属性
 */
export const createAmountInputProps = (customProps = {}) => 
  createNumberInputProps('amount', customProps)

/**
 * 快捷方法：创建数量输入框属性
 */
export const createQuantityInputProps = (customProps = {}) => 
  createNumberInputProps('quantity', customProps)

/**
 * 快捷方法：创建百分比输入框属性
 */
export const createPercentageInputProps = (customProps = {}) => 
  createNumberInputProps('percentage', customProps)

/**
 * 快捷方法：创建价格输入框属性
 */
export const createPriceInputProps = (customProps = {}) => 
  createNumberInputProps('price', customProps)
