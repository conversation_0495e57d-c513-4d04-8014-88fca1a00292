/**
 * 菜单工具函数
 */
import { useMainStore } from '@/stores/mainStore'

/**
 * 根据路径查找菜单项
 * @param {Array} menus - 菜单数据数组
 * @param {string} path - 要查找的路径
 * @returns {Object|null} 匹配的菜单项或null
 */
export function findMenuByPath(menus, path) {
  if (!menus || !Array.isArray(menus) || !path) {
    return null
  }

  // 规范化路径，确保以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`

  // 递归查找函数
  const findMenu = (menuItems, targetPath) => {
    for (const menu of menuItems) {
      // 构建完整的菜单路径
      const menuPath = menu.menuPath || ''
      const fullMenuPath = menuPath.startsWith('/') ? menuPath : `/${menuPath}`

      // 检查路径是否匹配
      if (fullMenuPath === targetPath) {
        return menu
      }

      // 递归检查子菜单
      if (menu.subMenus && menu.subMenus.length > 0) {
        const found = findMenu(menu.subMenus, targetPath)
        if (found) return found
      } else if (menu.children && menu.children.length > 0) {
        // 兼容不同的子菜单字段名
        const found = findMenu(menu.children, targetPath)
        if (found) return found
      }
    }
    return null
  }

  return findMenu(menus, normalizedPath)
}

/**
 * 加载动态路由
 * 从服务器获取菜单数据并加载动态路由
 * @returns {Promise<boolean>} 是否成功加载路由
 */
export async function loadDynamicRoutes() {
  try {
    const mainStore = useMainStore()

    // 如果菜单数据已经加载，直接返回
    if (mainStore.menusLoaded) {
      console.log('菜单数据已经加载，使用缓存数据')
      return true
    }

    // 从服务器获取菜单数据
    console.log('开始从服务器获取菜单数据')
    await mainStore.fetchMenus()

    // 检查是否成功获取菜单数据
    if (mainStore.menusLoaded) {
      console.log('成功加载动态路由')
      return true
    } else {
      console.error('加载动态路由失败')
      return false
    }
  } catch (error) {
    console.error('加载动态路由时出错:', error)
    return false
  }
}

export default {
  findMenuByPath,
  loadDynamicRoutes
}
