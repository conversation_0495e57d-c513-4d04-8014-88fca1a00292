/**
 * 增强版动态路由加载器
 * 支持Hash模式下的动态路由加载
 */

import router from './index.js'

/**
 * 动态路由加载器类
 */
export class DynamicRouteLoader {
  constructor() {
    // 预加载所有可能的视图组件
    this.viewModules = {
      ...import.meta.glob('@/views/**/*.vue'),
      ...import.meta.glob('/src/views/**/*.vue'),
      ...import.meta.glob('../views/**/*.vue')
    }
    
    // 路由缓存
    this.routeCache = new Map()
    
    // 路径转换规则
    this.pathTransformRules = [
      // 标准路径转换：inventory/orders-page -> inventory/OrdersPage.vue
      {
        pattern: /^([a-z]+)\/([a-z-]+)$/,
        transform: (match) => {
          const [, module, page] = match
          const pageName = this.kebabToPascal(page)
          return `${module}/${pageName}.vue`
        }
      },
      // 简单页面：orders-page -> OrdersPage.vue
      {
        pattern: /^([a-z-]+)$/,
        transform: (match) => {
          const [, page] = match
          const pageName = this.kebabToPascal(page)
          return `${pageName}.vue`
        }
      },
      // 系统页面：system/users -> system/UsersPage.vue
      {
        pattern: /^(system|admin)\/([a-z-]+)$/,
        transform: (match) => {
          const [, module, page] = match
          const pageName = this.kebabToPascal(page)
          return `${module}/${pageName}Page.vue`
        }
      }
    ]
  }

  /**
   * 将kebab-case转换为PascalCase
   * @param {string} str - kebab-case字符串
   * @returns {string} PascalCase字符串
   */
  kebabToPascal(str) {
    return str
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('')
  }

  /**
   * 根据路径查找对应的Vue组件
   * @param {string} routePath - 路由路径，如 'inventory/orders-page'
   * @returns {Promise<Object|null>} Vue组件或null
   */
  async findComponentByPath(routePath) {
    // 清理路径
    const cleanPath = routePath.replace(/^\/+|\/+$/g, '')
    
    // 检查缓存
    if (this.routeCache.has(cleanPath)) {
      return this.routeCache.get(cleanPath)
    }

    // 尝试不同的路径转换规则
    for (const rule of this.pathTransformRules) {
      const match = cleanPath.match(rule.pattern)
      if (match) {
        const componentPath = rule.transform(match)
        const component = await this.loadComponent(componentPath)
        if (component) {
          this.routeCache.set(cleanPath, component)
          return component
        }
      }
    }

    // 如果没有匹配的规则，尝试直接加载
    const directComponent = await this.loadComponent(`${cleanPath}.vue`)
    if (directComponent) {
      this.routeCache.set(cleanPath, directComponent)
      return directComponent
    }

    return null
  }

  /**
   * 加载Vue组件
   * @param {string} componentPath - 组件路径
   * @returns {Promise<Object|null>} Vue组件或null
   */
  async loadComponent(componentPath) {
    const possiblePaths = [
      `@/views/${componentPath}`,
      `/src/views/${componentPath}`,
      `../views/${componentPath}`
    ]

    for (const path of possiblePaths) {
      if (this.viewModules[path]) {
        try {
          const module = await this.viewModules[path]()
          return module.default
        } catch (error) {
          console.warn(`加载组件失败: ${path}`, error)
        }
      }
    }

    return null
  }

  /**
   * 动态添加路由
   * @param {string} routePath - 路由路径
   * @param {Object} options - 路由选项
   * @returns {Promise<boolean>} 是否成功添加路由
   */
  async addDynamicRoute(routePath, options = {}) {
    const component = await this.findComponentByPath(routePath)
    
    if (!component) {
      console.warn(`未找到路径 ${routePath} 对应的组件`)
      return false
    }

    const route = {
      path: `/${routePath}`,
      name: options.name || this.generateRouteName(routePath),
      component,
      meta: {
        requiresAuth: options.requiresAuth !== false,
        title: options.title || this.generateTitle(routePath),
        dynamicRoute: true,
        ...options.meta
      }
    }

    try {
      // 检查路由是否已存在
      if (!router.hasRoute(route.name)) {
        router.addRoute('Main', route)
        console.log(`✅ 动态路由已添加: ${routePath} -> ${route.name}`)
        return true
      } else {
        console.log(`⚠️ 路由已存在: ${route.name}`)
        return true
      }
    } catch (error) {
      console.error(`❌ 添加动态路由失败: ${routePath}`, error)
      return false
    }
  }

  /**
   * 生成路由名称
   * @param {string} routePath - 路由路径
   * @returns {string} 路由名称
   */
  generateRouteName(routePath) {
    return routePath
      .split('/')
      .map(segment => this.kebabToPascal(segment))
      .join('')
  }

  /**
   * 生成页面标题
   * @param {string} routePath - 路由路径
   * @returns {string} 页面标题
   */
  generateTitle(routePath) {
    return routePath
      .split('/')
      .map(segment => 
        segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
      )
      .join(' - ')
  }

  /**
   * 批量添加动态路由
   * @param {Array} routes - 路由配置数组
   * @returns {Promise<number>} 成功添加的路由数量
   */
  async addBatchRoutes(routes) {
    let successCount = 0
    
    for (const routeConfig of routes) {
      const { path, ...options } = routeConfig
      const success = await this.addDynamicRoute(path, options)
      if (success) successCount++
    }
    
    return successCount
  }

  /**
   * 清除动态路由缓存
   */
  clearCache() {
    this.routeCache.clear()
  }

  /**
   * 获取所有可用的组件路径
   * @returns {Array} 组件路径数组
   */
  getAvailableComponents() {
    return Object.keys(this.viewModules).map(path => 
      path.replace(/^(@\/views\/|\/src\/views\/|\.\.\/views\/)/, '').replace(/\.vue$/, '')
    )
  }
}

// 创建全局实例
export const dynamicRouteLoader = new DynamicRouteLoader()

// 便捷函数
export const addDynamicRoute = (path, options) => dynamicRouteLoader.addDynamicRoute(path, options)
export const findComponentByPath = (path) => dynamicRouteLoader.findComponentByPath(path)

export default dynamicRouteLoader
