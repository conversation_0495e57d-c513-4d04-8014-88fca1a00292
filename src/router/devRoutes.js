import { createRouter, createWebHistory } from 'vue-router'
import MainPage from '@/views/MainPage.vue'
import DepositOrderPage from '@/views/inventory/DepositOrderPage.vue'
import BizOrgMemberSelectorExample from '@/views/examples/BizOrgMemberSelectorExample.vue'
import SalesPriceLimit from '@/views/operation/SalesPriceLimit.vue'

// 开发环境路由配置
const devRoutes = [
  {
    path: '/inventory/deposit-order',
    component: DepositOrderPage,
    meta: { title: '定金订单管理' }
  },
  {
    path: '/examples/biz-org-member-selector',
    component: BizOrgMemberSelectorExample,
    meta: { title: '业务机构成员选择器示例' }
  },
  {
    path: '/operation/sales-price-limit',
    component: SalesPriceLimit,
    meta: { title: '销售限价规则管理' }
  }
]

// 添加开发路由到主应用
export function addDevRoutes(router) {
  devRoutes.forEach(route => {
    if (!router.hasRoute(route.path)) {
      router.addRoute('Main', route)
      console.log(`Added dev route: ${route.path}`)
    }
  })
}

export default devRoutes
