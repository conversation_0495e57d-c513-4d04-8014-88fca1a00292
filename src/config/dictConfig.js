/**
 * 字典配置中心
 * 统一管理所有字典的配置信息和使用规则
 */

import { DICT_CODES, COMMON_DICT_CONFIGS } from '@/constants/dictConstants'

/**
 * 页面字典配置映射
 * 定义每个页面/组件使用的字典及其配置
 */
export const PAGE_DICT_CONFIGS = {
  // 订单编辑页面
  ORDER_EDIT: {
    // 贷款相关
    loanChannel: {
      dictCode: DICT_CODES.LOAN_CHANNEL,
      config: COMMON_DICT_CONFIGS.WITHOUT_ALL,
      required: true,
    },
    loanMonths: {
      dictCode: DICT_CODES.LOAN_MONTHS,
      config: COMMON_DICT_CONFIGS.WITHOUT_ALL,
      required: true,
    },
    
    // 客户相关
    customerType: {
      dictCode: DICT_CODES.CUSTOMER_TYPE,
      config: COMMON_DICT_CONFIGS.WITHOUT_ALL,
      required: true,
    },
    salesStoreType: {
      dictCode: DICT_CODES.SALES_STORE_TYPE,
      config: COMMON_DICT_CONFIGS.WITHOUT_ALL,
      required: false,
    },
    
    // 优惠相关
    exclusiveDiscountType: {
      dictCode: DICT_CODES.EXCLUSIVE_DISCOUNT_TYPE,
      config: COMMON_DICT_CONFIGS.WITHOUT_ALL,
      required: false,
    },
  },
  
  // 库存管理页面
  INVENTORY_MANAGEMENT: {
    stockStatus: {
      dictCode: DICT_CODES.STOCK_STATUS,
      config: COMMON_DICT_CONFIGS.WITH_ALL,
      required: false,
    },
    vehicleSource: {
      dictCode: DICT_CODES.VEHICLE_SOURCE,
      config: COMMON_DICT_CONFIGS.WITH_ALL,
      required: false,
    },
    vehicleBrand: {
      dictCode: DICT_CODES.VEHICLE_BRAND,
      config: COMMON_DICT_CONFIGS.BRAND_SELECTOR,
      required: false,
    },
  },
  
  // 查询页面
  QUERY_PAGE: {
    orderStatus: {
      dictCode: DICT_CODES.ORDER_STATUS,
      config: COMMON_DICT_CONFIGS.WITH_ALL,
      required: false,
    },
    dealStatus: {
      dictCode: DICT_CODES.DEAL_STATUS,
      config: COMMON_DICT_CONFIGS.WITH_ALL,
      required: false,
    },
    vehicleBrand: {
      dictCode: DICT_CODES.VEHICLE_BRAND,
      config: COMMON_DICT_CONFIGS.BRAND_SELECTOR,
      required: false,
    },
  },
  
  // 业务组织选择器
  BIZ_ORG_SELECTOR: {
    province: {
      dictCode: DICT_CODES.PROVINCE_CITY,
      config: COMMON_DICT_CONFIGS.CASCADING,
      required: false,
    },
    city: {
      dictCode: DICT_CODES.CITY_DISTRICT,
      config: COMMON_DICT_CONFIGS.CASCADING,
      required: false,
      parentCode: DICT_CODES.PROVINCE_CITY,
    },
    vehicleBrand: {
      dictCode: DICT_CODES.VEHICLE_BRAND,
      config: COMMON_DICT_CONFIGS.BRAND_SELECTOR,
      required: false,
    },
  },
}

/**
 * 字典验证规则配置
 */
export const DICT_VALIDATION_CONFIGS = {
  // 订单相关验证
  ORDER_VALIDATION: {
    // 贷款渠道和期限必须同时选择或同时为空
    loanConsistency: {
      fields: ['loanChannel', 'loanMonths'],
      rule: 'all_or_none',
      message: '贷款渠道和贷款期限必须同时选择'
    },
    
    // 客户类型必选
    customerTypeRequired: {
      fields: ['customerType'],
      rule: 'required',
      message: '请选择客户类型'
    },
  },
  
  // 库存相关验证
  INVENTORY_VALIDATION: {
    // 车辆品牌权限验证
    brandPermission: {
      fields: ['vehicleBrand'],
      rule: 'permission_check',
      message: '您没有该品牌的操作权限'
    },
  },
}

/**
 * 字典联动配置
 * 定义字典之间的联动关系
 */
export const DICT_LINKAGE_CONFIGS = {
  // 地理位置联动
  LOCATION_LINKAGE: {
    parent: DICT_CODES.PROVINCE_CITY,
    child: DICT_CODES.CITY_DISTRICT,
    type: 'cascading',
    autoReset: true,
  },
  
  // 贷款信息联动
  LOAN_LINKAGE: {
    trigger: DICT_CODES.LOAN_CHANNEL,
    affected: [DICT_CODES.LOAN_MONTHS],
    type: 'filter',
    filterRule: (channel, months) => {
      // 根据贷款渠道过滤可用期限
      const channelMonthsMap = {
        '车贷通担保': [12, 24, 36],
        '建行': [12, 24, 36, 48],
        '长安金融': [12, 24, 36],
        '上汽金融': [12, 24],
      }
      return channelMonthsMap[channel] || []
    },
  },
}

/**
 * 字典缓存策略配置
 */
export const DICT_CACHE_STRATEGIES = {
  // 高频使用字典 - 长期缓存
  HIGH_FREQUENCY: {
    dictCodes: [
      DICT_CODES.VEHICLE_BRAND,
      DICT_CODES.ORDER_STATUS,
      DICT_CODES.CUSTOMER_TYPE,
    ],
    cacheTime: 24 * 60 * 60 * 1000, // 24小时
    preload: true,
  },
  
  // 中频使用字典 - 中期缓存
  MEDIUM_FREQUENCY: {
    dictCodes: [
      DICT_CODES.LOAN_CHANNEL,
      DICT_CODES.LOAN_MONTHS,
      DICT_CODES.STOCK_STATUS,
    ],
    cacheTime: 60 * 60 * 1000, // 1小时
    preload: false,
  },
  
  // 低频使用字典 - 短期缓存
  LOW_FREQUENCY: {
    dictCodes: [
      DICT_CODES.EXCLUSIVE_DISCOUNT_TYPE,
      DICT_CODES.FUND_TYPE,
    ],
    cacheTime: 30 * 60 * 1000, // 30分钟
    preload: false,
  },
}

/**
 * 获取页面字典配置
 * @param {string} pageKey - 页面标识
 * @returns {Object} 页面字典配置
 */
export function getPageDictConfig(pageKey) {
  return PAGE_DICT_CONFIGS[pageKey] || {}
}

/**
 * 获取字典验证配置
 * @param {string} validationKey - 验证标识
 * @returns {Object} 验证配置
 */
export function getDictValidationConfig(validationKey) {
  return DICT_VALIDATION_CONFIGS[validationKey] || {}
}

/**
 * 获取字典联动配置
 * @param {string} linkageKey - 联动标识
 * @returns {Object} 联动配置
 */
export function getDictLinkageConfig(linkageKey) {
  return DICT_LINKAGE_CONFIGS[linkageKey] || {}
}

/**
 * 获取字典缓存策略
 * @param {string} dictCode - 字典编码
 * @returns {Object} 缓存策略
 */
export function getDictCacheStrategy(dictCode) {
  for (const strategy of Object.values(DICT_CACHE_STRATEGIES)) {
    if (strategy.dictCodes.includes(dictCode)) {
      return strategy
    }
  }
  return DICT_CACHE_STRATEGIES.LOW_FREQUENCY // 默认策略
}

/**
 * 检查字典是否需要预加载
 * @param {string} dictCode - 字典编码
 * @returns {boolean} 是否需要预加载
 */
export function shouldPreloadDict(dictCode) {
  const strategy = getDictCacheStrategy(dictCode)
  return strategy.preload === true
}

/**
 * 获取字典缓存时间
 * @param {string} dictCode - 字典编码
 * @returns {number} 缓存时间（毫秒）
 */
export function getDictCacheTime(dictCode) {
  const strategy = getDictCacheStrategy(dictCode)
  return strategy.cacheTime
}

/**
 * 创建字典配置构建器
 * @param {string} dictCode - 字典编码
 * @returns {Object} 配置构建器
 */
export function createDictConfigBuilder(dictCode) {
  let config = {
    dictCode,
    includeAll: true,
    allLabel: '不限',
    allValue: null,
  }
  
  return {
    // 设置是否包含"不限"选项
    withAll(includeAll = true, label = '不限', value = null) {
      config.includeAll = includeAll
      config.allLabel = label
      config.allValue = value
      return this
    },
    
    // 设置过滤器
    withFilter(filterFn) {
      config.filter = filterFn
      return this
    },
    
    // 设置转换器
    withTransform(transformFn) {
      config.transform = transformFn
      return this
    },
    
    // 设置依赖项
    withDependencies(...deps) {
      config.dependencies = deps
      return this
    },
    
    // 应用预设配置
    withPreset(presetKey) {
      if (COMMON_DICT_CONFIGS[presetKey]) {
        config = { ...config, ...COMMON_DICT_CONFIGS[presetKey] }
      }
      return this
    },
    
    // 构建配置
    build() {
      return config
    }
  }
}
