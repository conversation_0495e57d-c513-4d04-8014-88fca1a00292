/**
 * 全局输入组件配置
 */

// 数字输入框默认配置
export const numberInputConfig = {
  // 基础配置
  min: 0,
  max: 1000000, // 100万
  precision: 2, // 默认保留2位小数
  step: 1,
  
  // 金额相关配置
  amount: {
    min: 0,
    max: 1000000,
    precision: 2,
    step: 0.01,
    placeholder: '请输入金额'
  },
  
  // 数量相关配置
  quantity: {
    min: 0,
    max: 999999,
    precision: 0,
    step: 1,
    placeholder: '请输入数量'
  },
  
  // 百分比配置
  percentage: {
    min: 0,
    max: 100,
    precision: 2,
    step: 0.01,
    placeholder: '请输入百分比'
  },
  
  // 价格配置
  price: {
    min: 0,
    max: 1000000,
    precision: 2,
    step: 0.01,
    placeholder: '请输入价格'
  }
}

// 文本输入框默认配置
export const textInputConfig = {
  maxlength: 200,
  placeholder: '请输入内容'
}

// 文本域默认配置
export const textareaConfig = {
  maxlength: 500,
  placeholder: '请输入内容',
  autosize: {
    minRows: 3,
    maxRows: 6
  }
}

/**
 * 获取数字输入框配置
 * @param {string} type - 配置类型 (amount, quantity, percentage, price)
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 合并后的配置
 */
export function getNumberInputConfig(type = 'default', customConfig = {}) {
  const baseConfig = numberInputConfig[type] || numberInputConfig
  return {
    ...baseConfig,
    ...customConfig
  }
}

/**
 * 获取文本输入框配置
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 合并后的配置
 */
export function getTextInputConfig(customConfig = {}) {
  return {
    ...textInputConfig,
    ...customConfig
  }
}

/**
 * 获取文本域配置
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 合并后的配置
 */
export function getTextareaConfig(customConfig = {}) {
  return {
    ...textareaConfig,
    ...customConfig
  }
}
