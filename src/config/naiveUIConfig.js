/**
 * Naive UI 全局配置
 * 为所有 Naive UI 组件设置默认属性
 */

import { numberInputConfig } from './inputConfig'

/**
 * 创建全局配置提供者
 * 可以在 main.js 中使用，为整个应用设置默认配置
 */
export const naiveUIGlobalConfig = {
  // 数字输入框全局配置
  NInputNumber: {
    min: numberInputConfig.min,
    max: numberInputConfig.max,
    precision: numberInputConfig.precision,
    step: numberInputConfig.step,
    buttonPlacement: 'both',
    showButton: true
  },
  
  // 输入框全局配置
  NInput: {
    clearable: true,
    maxlength: 200
  },
  
  // 文本域全局配置
  NInput: {
    type: 'textarea',
    maxlength: 500,
    autosize: {
      minRows: 3,
      maxRows: 6
    }
  },
  
  // 选择器全局配置
  NSelect: {
    clearable: true,
    filterable: true
  },
  
  // 日期选择器全局配置
  NDatePicker: {
    clearable: true,
    format: 'yyyy-MM-dd'
  },
  
  // 按钮全局配置
  NButton: {
    size: 'medium'
  }
}

/**
 * 为特定组件类型创建配置混入
 */
export const createComponentMixin = (componentType, customConfig = {}) => {
  const baseConfig = naiveUIGlobalConfig[componentType] || {}
  return {
    ...baseConfig,
    ...customConfig
  }
}

/**
 * 数字输入框配置混入
 */
export const numberInputMixin = (type = 'default', customConfig = {}) => {
  const config = numberInputConfig[type] || numberInputConfig
  return {
    min: config.min,
    max: config.max,
    precision: config.precision,
    step: config.step,
    placeholder: config.placeholder,
    buttonPlacement: 'both',
    showButton: true,
    ...customConfig
  }
}

/**
 * 金额输入框配置混入
 */
export const amountInputMixin = (customConfig = {}) => {
  return numberInputMixin('amount', customConfig)
}

/**
 * 数量输入框配置混入
 */
export const quantityInputMixin = (customConfig = {}) => {
  return numberInputMixin('quantity', customConfig)
}

/**
 * 百分比输入框配置混入
 */
export const percentageInputMixin = (customConfig = {}) => {
  return numberInputMixin('percentage', customConfig)
}

/**
 * 价格输入框配置混入
 */
export const priceInputMixin = (customConfig = {}) => {
  return numberInputMixin('price', customConfig)
}
