<template>
  <div class="example-container">
    <h2>业务机构列表布局组件示例</h2>
    
    <!-- 示例1: 不传入业务权限参数 -->
    <n-card title="示例1: 显示所有机构" style="margin-bottom: 20px;">
      <biz-org-list-layout
        title="所有机构数据"
        :columns="columns"
        :api-service="apiService"
        search-placeholder="搜索机构数据..."
      />
    </n-card>

    <!-- 示例2: 传入业务权限参数 -->
    <n-card title="示例2: 只显示有结算权限的机构">
      <biz-org-list-layout
        title="有结算权限的机构数据"
        :columns="columns"
        :api-service="apiService"
        business-permission="can_settle"
        search-placeholder="搜索有结算权限的机构数据..."
      />
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { NCard } from 'naive-ui';
import BizOrgListLayout from '@/components/layout/BizOrgListLayout.vue';

// 示例表格列配置
const columns = ref([
  {
    title: 'ID',
    key: 'id',
    width: 80,
  },
  {
    title: '机构名称',
    key: 'orgName',
    width: 200,
  },
  {
    title: '联系人',
    key: 'contactPerson',
    width: 120,
  },
  {
    title: '联系电话',
    key: 'contactPhone',
    width: 150,
  },
  {
    title: '地址',
    key: 'address',
    width: 300,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
  },
]);

// 示例API服务对象
const apiService = {
  // 这里应该是实际的API调用方法
  getList: async (params) => {
    console.log('API调用参数:', params);
    // 模拟API响应
    return {
      code: 200,
      data: {
        list: [
          {
            id: 1,
            orgName: '示例机构1',
            contactPerson: '张三',
            contactPhone: '13800138001',
            address: '北京市朝阳区',
            status: 'active'
          },
          {
            id: 2,
            orgName: '示例机构2',
            contactPerson: '李四',
            contactPhone: '13800138002',
            address: '上海市浦东新区',
            status: 'active'
          }
        ],
        total: 2,
        pageNum: 1,
        pages: 1
      }
    };
  }
};
</script>

<style scoped>
.example-container {
  padding: 20px;
}

.example-container h2 {
  margin-bottom: 20px;
  color: #333;
}
</style>
