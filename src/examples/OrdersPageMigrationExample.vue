<template>
  <div class="orders-page-migration-example">
    <n-card title="OrdersPage 迁移示例">
      <n-space vertical>
        <n-alert type="info">
          这个示例展示了如何将 OrdersPage.vue 从使用 OrderEditModal 迁移到使用 ConfigurableOrderForm
        </n-alert>

        <n-space>
          <n-button type="primary" @click="showCreateForm">
            新增订单（ConfigurableOrderForm）
          </n-button>
          <n-button @click="showEditForm">
            编辑订单（ConfigurableOrderForm）
          </n-button>
          <n-button @click="showOldCreateForm">
            新增订单（OrderEditModal - 旧版）
          </n-button>
          <n-button @click="showOldEditForm">
            编辑订单（OrderEditModal - 旧版）
          </n-button>
        </n-space>

        <!-- 迁移对比表格 -->
        <n-card title="迁移对比" size="small">
          <n-table :bordered="false" :single-line="false">
            <thead>
              <tr>
                <th>方面</th>
                <th>OrderEditModal（旧版）</th>
                <th>ConfigurableOrderForm（新版）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>组件导入</td>
                <td><n-code>import OrderEditModal from '@/components/orders/OrderEditModal.vue'</n-code></td>
                <td><n-code>import ConfigurableOrderForm from '@/components/orders/ConfigurableOrderForm.vue'</n-code></td>
              </tr>
              <tr>
                <td>配置方式</td>
                <td>通过 props 传递选项</td>
                <td>通过配置对象控制显示和行为</td>
              </tr>
              <tr>
                <td>备注处理</td>
                <td>✅ 正确处理历史备注</td>
                <td>✅ 正确处理历史备注（已修复）</td>
              </tr>
              <tr>
                <td>可扩展性</td>
                <td>❌ 硬编码结构</td>
                <td>✅ 灵活的配置化结构</td>
              </tr>
            </tbody>
          </n-table>
        </n-card>

        <!-- 新版 ConfigurableOrderForm -->
        <configurable-order-form
          v-model:visible="newFormVisible"
          :config="orderFormConfig"
          :title="newFormTitle"
          :initial-data="initialData"
          @save="handleNewFormSave"
          @cancel="handleNewFormCancel"
        />

        <!-- 旧版 OrderEditModal（用于对比） -->
        <order-edit-modal
          v-model:visible="oldFormVisible"
          :is-edit="isEdit"
          :title="oldFormTitle"
          :initial-data="initialData"
          @save="handleOldFormSave"
          @cancel="handleOldFormCancel"
        />

        <!-- 保存结果展示 -->
        <n-card title="保存结果对比" size="small" v-if="saveResults.length > 0">
          <n-space vertical>
            <div v-for="(result, index) in saveResults" :key="index">
              <n-tag :type="result.type === 'new' ? 'success' : 'warning'">
                {{ result.type === 'new' ? 'ConfigurableOrderForm' : 'OrderEditModal' }}
              </n-tag>
              <n-code :code="JSON.stringify(result.data, null, 2)" language="json" style="margin-top: 8px;" />
            </div>
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NCard, NSpace, NButton, NAlert, NTable, NCode, NTag } from 'naive-ui'
import ConfigurableOrderForm from '@/components/orders/ConfigurableOrderForm.vue'
import OrderEditModal from '@/components/orders/OrderEditModal.vue'
import { getSimplifiedOrderFormConfig } from '@/utils/orderEditModalMigration.js'

// 响应式数据
const newFormVisible = ref(false)
const oldFormVisible = ref(false)
const newFormTitle = ref('')
const oldFormTitle = ref('')
const isEdit = ref(false)
const initialData = ref({})
const saveResults = ref([])

// 订单表单配置
const orderFormConfig = ref({})

// 模拟的订单数据
const mockOrderData = {
  id: 1,
  customerName: '张三',
  customerPhone: '13800138000',
  vehicleBrand: '奔驰',
  vehicleSeries: 'C级',
  vehicleConfig: 'C200L 运动版',
  salesAmount: 350000,
  remark: '2024/1/15 10:30:00|李四|这是一条历史备注\n2024/1/14 15:20:00|王五|这是另一条历史备注'
}

// 显示新增表单（ConfigurableOrderForm）
const showCreateForm = () => {
  orderFormConfig.value = getSimplifiedOrderFormConfig('create')
  newFormTitle.value = '新增订单（ConfigurableOrderForm）'
  initialData.value = {}
  newFormVisible.value = true
}

// 显示编辑表单（ConfigurableOrderForm）
const showEditForm = () => {
  orderFormConfig.value = getSimplifiedOrderFormConfig('edit')
  newFormTitle.value = '编辑订单（ConfigurableOrderForm）'
  initialData.value = { ...mockOrderData }
  newFormVisible.value = true
}

// 显示旧版新增表单
const showOldCreateForm = () => {
  isEdit.value = false
  oldFormTitle.value = '新增订单（OrderEditModal）'
  initialData.value = {}
  oldFormVisible.value = true
}

// 显示旧版编辑表单
const showOldEditForm = () => {
  isEdit.value = true
  oldFormTitle.value = '编辑订单（OrderEditModal）'
  initialData.value = { ...mockOrderData }
  oldFormVisible.value = true
}

// 处理新版表单保存
const handleNewFormSave = (formData) => {
  console.log('ConfigurableOrderForm 保存数据:', formData)
  saveResults.value.unshift({
    type: 'new',
    timestamp: new Date().toLocaleString(),
    data: formData
  })
  newFormVisible.value = false
}

// 处理新版表单取消
const handleNewFormCancel = () => {
  newFormVisible.value = false
}

// 处理旧版表单保存
const handleOldFormSave = (formData) => {
  console.log('OrderEditModal 保存数据:', formData)
  saveResults.value.unshift({
    type: 'old',
    timestamp: new Date().toLocaleString(),
    data: formData
  })
  oldFormVisible.value = false
}

// 处理旧版表单取消
const handleOldFormCancel = () => {
  oldFormVisible.value = false
}
</script>

<style scoped>
.orders-page-migration-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.n-table {
  margin-top: 16px;
}

.n-table th,
.n-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.n-table th {
  background-color: #fafafa;
  font-weight: 600;
}
</style>
