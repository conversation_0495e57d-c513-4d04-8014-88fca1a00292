package com.example.examples;

import com.example.types.VehicleStockPayload;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * VehicleStockPayload验证示例
 * 演示如何使用Bean Validation注解进行数据验证
 */
public class VehicleStockPayloadExample {
    
    private static final Validator validator;
    
    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }
    
    public static void main(String[] args) {
        System.out.println("=== VehicleStockPayload验证示例 ===\n");
        
        // 示例1：有效数据
        testValidData();
        
        // 示例2：无效数据 - 空值
        testInvalidDataWithNulls();
        
        // 示例3：无效数据 - 负数
        testInvalidDataWithNegativeValues();
        
        // 示例4：无效数据 - VIN格式错误
        testInvalidVin();
        
        // 示例5：根据您的JSON数据创建对象
        testWithYourJsonData();
    }
    
    /**
     * 测试有效数据
     */
    private static void testValidData() {
        System.out.println("1. 测试有效数据:");
        
        VehicleStockPayload payload = new VehicleStockPayload(
            297L,                    // orderId
            297L,                    // outboundId
            "LS6C3E2S3SF017982",    // vin (17位)
            100L,                    // loanRebateReceivableAmount
            100L,                    // notaryFee
            100L,                    // carefreeIncome
            100L,                    // extendedWarrantyIncome
            100L,                    // vpsIncome
            100L,                    // preInterest
            100L,                    // licensePlateFee
            100L,                    // tempPlateFee
            100L,                    // deliveryEquipment
            200L                     // otherIncome
        );
        
        Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);
        
        if (violations.isEmpty()) {
            System.out.println("✅ 验证通过！数据有效。");
        } else {
            System.out.println("❌ 验证失败：");
            violations.forEach(violation -> 
                System.out.println("  - " + violation.getPropertyPath() + ": " + violation.getMessage())
            );
        }
        System.out.println();
    }
    
    /**
     * 测试包含空值的无效数据
     */
    private static void testInvalidDataWithNulls() {
        System.out.println("2. 测试包含空值的无效数据:");
        
        VehicleStockPayload payload = new VehicleStockPayload();
        // 所有字段都是null或空
        
        Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);
        
        if (violations.isEmpty()) {
            System.out.println("✅ 验证通过！");
        } else {
            System.out.println("❌ 验证失败，发现 " + violations.size() + " 个错误：");
            violations.forEach(violation -> 
                System.out.println("  - " + violation.getPropertyPath() + ": " + violation.getMessage())
            );
        }
        System.out.println();
    }
    
    /**
     * 测试包含负数的无效数据
     */
    private static void testInvalidDataWithNegativeValues() {
        System.out.println("3. 测试包含负数的无效数据:");
        
        VehicleStockPayload payload = new VehicleStockPayload();
        payload.setOrderId(297L);
        payload.setOutboundId(297L);
        payload.setVin("LS6C3E2S3SF017982");
        
        // 设置负数值
        payload.setCarefreeIncome(-100L);
        payload.setNotaryFee(-50L);
        payload.setOtherIncome(-200L);
        
        Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);
        
        if (violations.isEmpty()) {
            System.out.println("✅ 验证通过！");
        } else {
            System.out.println("❌ 验证失败，发现 " + violations.size() + " 个错误：");
            violations.forEach(violation -> 
                System.out.println("  - " + violation.getPropertyPath() + ": " + violation.getMessage())
            );
        }
        System.out.println();
    }
    
    /**
     * 测试无效的VIN码
     */
    private static void testInvalidVin() {
        System.out.println("4. 测试无效的VIN码:");
        
        VehicleStockPayload payload = new VehicleStockPayload();
        payload.setOrderId(297L);
        payload.setOutboundId(297L);
        payload.setVin("INVALID_VIN");  // 不是17位
        
        Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);
        
        if (violations.isEmpty()) {
            System.out.println("✅ 验证通过！");
        } else {
            System.out.println("❌ 验证失败，发现 " + violations.size() + " 个错误：");
            violations.forEach(violation -> 
                System.out.println("  - " + violation.getPropertyPath() + ": " + violation.getMessage())
            );
        }
        System.out.println();
    }
    
    /**
     * 使用您提供的JSON数据测试
     */
    private static void testWithYourJsonData() {
        System.out.println("5. 使用您的JSON数据测试:");
        
        VehicleStockPayload payload = new VehicleStockPayload();
        
        // 根据您提供的JSON数据设置值
        payload.setOrderId(297L);
        payload.setOutboundId(297L);
        payload.setVin("LS6C3E2S3SF017982");
        payload.setLoanRebateReceivableAmount(100L);
        payload.setNotaryFee(100L);
        payload.setCarefreeIncome(100L);
        payload.setExtendedWarrantyIncome(100L);
        payload.setVpsIncome(100L);
        payload.setPreInterest(100L);
        payload.setLicensePlateFee(100L);
        payload.setTempPlateFee(100L);
        payload.setDeliveryEquipment(100L);
        payload.setOtherIncome(200L);
        
        Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);
        
        if (violations.isEmpty()) {
            System.out.println("✅ 验证通过！您的数据完全有效。");
            System.out.println("对象详情: " + payload.toString());
        } else {
            System.out.println("❌ 验证失败：");
            violations.forEach(violation -> 
                System.out.println("  - " + violation.getPropertyPath() + ": " + violation.getMessage())
            );
        }
        System.out.println();
    }
    
    /**
     * 验证单个对象的工具方法
     * @param payload 要验证的对象
     * @return 是否验证通过
     */
    public static boolean validatePayload(VehicleStockPayload payload) {
        Set<ConstraintViolation<VehicleStockPayload>> violations = validator.validate(payload);
        
        if (!violations.isEmpty()) {
            System.err.println("VehicleStockPayload验证失败：");
            violations.forEach(violation -> 
                System.err.println("  - " + violation.getPropertyPath() + ": " + violation.getMessage())
            );
            return false;
        }
        
        return true;
    }
}
