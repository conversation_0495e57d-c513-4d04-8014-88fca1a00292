<template>
  <div class="payment-detail-container">
    <!-- 付款单基本信息部分 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>付款信息</h3>
      </div>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">付款单号</div>
          <div class="info-value code-value">
            {{ paymentData.paymentSn || "暂无" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">付款单位</div>
          <div class="info-value org-value">
            {{ paymentData.paymentOrgName || "暂无" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">付款方式</div>
          <div class="info-value method-value">
            {{ getPaymentMethodLabel(paymentData.paymentMethod) }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">付款时间</div>
          <div class="info-value time-value">
            {{ paymentData.paymentTime || "暂无" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">付款金额</div>
          <div class="info-value amount">
            ¥{{ formatMoney(paymentData.paymentAmount / 100) }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">付款金额（大写）</div>
          <div class="info-value amount-cn">
            {{
              paymentData.paymentAmountCn ||
              convertNumberToChinese(paymentData.paymentAmount / 100)
            }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">业务流水号</div>
          <div class="info-value code-value">
            {{ paymentData.bizNo || "暂无" }}
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">经办人</div>
          <div class="info-value person-value">
            {{ paymentData.paymentAgentName || "暂无" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">付款摘要</div>
          <div class="info-value summary-value">
            {{ paymentData.paymentSummary || "暂无" }}
          </div>
        </div>
      </div>
    </div>

    <!-- 付款明细数据列表部分 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>付款明细</h3>
      </div>

      <!-- 付款明细数据表格 -->
      <n-data-table
        :columns="paymentDetailColumns"
        :data="paymentData.items || []"
        :bordered="false"
        :single-line="false"
        :pagination="false"
        size="small"
      >
        <template #empty>
          <n-empty description="暂无付款明细数据" />
        </template>
      </n-data-table>

      <!-- 合计行 -->
      <div v-if="(paymentData.items || []).length > 0" class="total-row">
        <span>合计：</span>
        <span class="total-amount">¥{{ formatMoney(totalAmount) }}</span>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="detail-footer">
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
        <n-button type="primary" @click="handlePrint" v-if="showPrintButton"
          >打印</n-button
        >
      </n-space>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, h } from "vue";
import { NButton, NSpace, NDataTable, NEmpty, NTag } from "naive-ui";
import { convertNumberToChinese } from "@/utils/money";
import { getDictOptions } from "@/api/dict";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  // 付款单数据
  paymentData: {
    type: Object,
    default: () => ({
      id: null,
      paymentSn: "",
      paymentOrgId: 0,
      paymentOrgName: "",
      paymentMethod: null,
      paymentAmount: 0,
      paymentAmountCn: "",
      paymentTime: null,
      bizNo: "",
      paymentSummary: null,
      paymentAgentName: "",
      createTime: "",
      updateTime: "",
      creatorId: null,
      editorId: null,
      items: [],
    }),
  },
  // 是否显示打印按钮
  showPrintButton: {
    type: Boolean,
    default: true,
  },
});

// 定义组件事件
const emit = defineEmits([
  "close", // 关闭详情
  "print", // 打印详情
]);

// 付款方式选项
const paymentMethodOptions = ref([]);

// 定义几种颜色用于标签
const tagColors = ["success", "info", "warning", "error", "default"];

// 根据科目ID获取固定的颜色
function getTagColorByFeeId(feeId) {
  // 使用科目ID对颜色数组长度取模，确保同一科目ID总是获得相同颜色
  const colorIndex = (feeId || 0) % tagColors.length;
  return tagColors[colorIndex];
}

// 付款明细表格列定义
const paymentDetailColumns = [
  {
    title: "付款科目",
    key: "feeName",
    width: 200,
    render(row) {
      return h(
        NTag,
        {
          type: getTagColorByFeeId(row.feeId),
          bordered: false,
          size: "medium",
        },
        { default: () => row.feeName }
      );
    },
  },
  {
    title: "付款金额",
    key: "feeAmount",
    width: 150,
    render(row) {
      const amount = row.feeAmount || 0;
      return `￥${formatMoney(amount / 100)}`;
    },
  },
  {
    title: "付款摘要",
    key: "feeSummary",
    width: 200,
    render(row) {
      return row.feeSummary || "暂无";
    },
  },
  {
    title: "付款类型",
    key: "payableId",
    width: 120,
    render(row) {
      return row.payableId ? "应收账款" : "其他付款";
    },
  },
];

// 计算属性：总金额
const totalAmount = computed(() => {
  if (!props.paymentData.items || props.paymentData.items.length === 0) {
    return 0;
  }
  return (
    props.paymentData.items.reduce(
      (sum, item) => sum + (item.feeAmount || 0),
      0
    ) / 100
  );
});

// 生命周期钩子
onMounted(() => {
  // 获取付款方式选项
  fetchDictOptions();
});

// 方法
// 获取字典选项
async function fetchDictOptions() {
  try {
    // 获取付款方式选项
    const res = await getDictOptions("payment_account");
    paymentMethodOptions.value = (res.data || []).map((item) => ({
      label: item.optionLabel,
      value: item.optionValue,
    }));
  } catch (error) {
    console.error("获取字典选项失败:", error);
    messages.error("获取字典选项失败");
  }
}

// 获取付款方式标签
function getPaymentMethodLabel(value) {
  if (!value) return "暂无";
  const option = paymentMethodOptions.value.find((opt) => opt.value === value);
  return option ? option.label : value;
}

// 格式化金额显示
function formatMoney(amount) {
  if (amount === undefined || amount === null) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 处理关闭按钮
function handleClose() {
  emit("close");
}

// 处理打印按钮
function handlePrint() {
  emit("print", props.paymentData);
}
</script>

<style scoped>
.payment-detail-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  height: 100%;
}

.detail-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color, #18a058);
  margin: 0;
  position: relative;
  padding-left: 12px;
}

.section-header h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: var(--primary-color, #18a058);
  border-radius: 2px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  border-bottom: 1px dashed var(--primary-color, #18a058);
  padding-bottom: 4px;
  display: inline-block;
  min-width: 120px;
  transition: all 0.3s;
}

.info-value:hover {
  border-bottom-style: solid;
}

.info-value.amount {
  color: #f5222d;
  font-weight: 600;
  font-size: 16px;
  border-bottom-color: #f5222d;
}

.total-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;
  margin-top: 8px;
  background-color: rgba(24, 160, 88, 0.05);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color, #18a058);
}

.total-amount {
  font-weight: 600;
  color: #f5222d;
  margin-left: 8px;
  font-size: 18px;
  border-bottom: 1px dashed #f5222d;
  padding-bottom: 2px;
}

.detail-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
}
</style>
