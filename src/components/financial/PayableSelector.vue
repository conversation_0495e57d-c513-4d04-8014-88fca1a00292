<template>
  <n-modal
    :show="modelVisible"
    @update:show="updateVisible"
    :title="title || '选择对账单'"
    preset="card"
    :style="isMaximized ? { width: '90%', height: '90%' } : modalSize"
    :mask-closable="false"
    :auto-focus="false"
    class="payable-selector-modal"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component
              :is="isMaximized ? ContractOutlineIcon : ExpandOutlineIcon"
            />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div class="payable-selector-content">
      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-inputs-wrapper">
          <n-input
            v-model:value="searchKeyword"
            placeholder="请输入订单编号、客户名称或手机号码"
            clearable
            class="search-input"
            @keydown.enter="handleSearch"
          >
            <template #prefix>
              <n-icon><component :is="SearchOutlineIcon" /></n-icon>
            </template>
          </n-input>

          <n-select
            v-model:value="selectedSubjects"
            placeholder="请选择应付科目"
            multiple
            clearable
            class="subject-select"
            :options="subjectOptions"
            @update:value="handleSubjectsChange"
            :max-tag-count="3"
            size="medium"
            :render-tag="renderSubjectTag"
          />
        </div>
      </div>

      <!-- 数据列表 -->
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="payableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        :row-props="rowProps"
        :checked-row-keys="selectedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      >
        <template #empty>
          <n-empty description="暂无车辆数据，请点击➕新增" />
        </template>
      </n-data-table>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="selectedRowKeys.length === 0"
          @click="handleConfirm"
        >
          确定{{
            selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : ""
          }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import {
  ref,
  reactive,
  computed,
  markRaw,
  watch,
  h,
  onMounted,
  onUnmounted,
} from "vue";
import {
  NModal,
  NInput,
  NButton,
  NSpace,
  NDataTable,
  NIcon,
  NSelect,
  NTag,
} from "naive-ui";
import {
  SearchOutline,
  ExpandOutline,
  ContractOutline,
} from "@vicons/ionicons5";
import payableSelectorApi from "@/api/payableSelector";
import messages from "@/utils/messages";
import { getDictOptions } from "@/api/dict";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchOutlineIcon = markRaw(SearchOutline);
const ExpandOutlineIcon = markRaw(ExpandOutline);
const ContractOutlineIcon = markRaw(ContractOutline);

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "应付账款选择器",
  },
  // 可选的初始选中应付账款
  initialPayable: {
    type: Object,
    default: null,
  },
  // 可选的初始选中应付账款列表（多选模式）
  initialSelected: {
    type: Array,
    default: () => [],
  },
  // 可选的查询参数
  params: {
    type: Object,
    default: () => ({}),
  },
  // 是否允许多选
  multiple: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits([
  "update:visible",
  "select",
  "selectMultiple",
  "cancel",
]);

// 组件状态
const isMaximized = ref(false);
const loading = ref(false);
const searchKeyword = ref("");
const payableData = ref([]);
const selectedPayableId = ref(null);
const selectedPayable = ref(null);
const tableRef = ref(null);
const windowWidth = ref(window.innerWidth); // 当前窗口宽度

// 多选相关状态
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 应付科目相关状态
const subjectOptions = ref([]);
const selectedSubjects = ref([]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page;
    fetchData();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    fetchData();
  },
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
});

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 计算属性：根据窗口宽度计算模态框尺寸
const modalSize = computed(() => {
  // 当窗口宽度大于768px时，宽高为浏览器尺寸的60%
  if (windowWidth.value > 768) {
    return {
      width: "60%",
      height: "60%",
      maxWidth: "90%",
      maxHeight: "90%",
    };
  }
  // 当窗口宽度小于等于768px时，宽高为浏览器尺寸的100%
  else {
    return {
      width: "100%",
      height: "100%",
      maxWidth: "100%",
      maxHeight: "100%",
    };
  }
});

// 表格列配置
const columns = computed(() => {
  const cols = [];

  // 添加选择列（无论单选还是多选模式）
  cols.push({
    type: "selection",
    width: 40,
    align: "center",
  });

  // 添加其他列
  cols.push(
    {
      title: "订单编号",
      key: "orderSn",
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "客户名称",
      key: "customerName",
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "客户手机号",
      key: "mobile",
      width: 120,
      render(row) {
        // 手机号脱敏处理
        if (!row.mobile) return "-";
        if (row.mobile.length === 11) {
          return row.mobile.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
        }
        return row.mobile;
      },
    },
    {
      title: "应付科目",
      key: "feeName",
      width: 120,
    },
    {
      title: "应付金额",
      key: "feeAmount",
      width: 120,
      align: "right",
      render(row) {
        // 将金额从分转为元并格式化显示
        const amount = row.feeAmount || 0;
        return `￥${(amount / 100).toLocaleString("zh-CN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`;
      },
    }
  );

  return cols;
});

// 更新可见性
const updateVisible = (val) => {
  emit("update:visible", val);
};

// 切换最大化状态
const toggleSize = () => {
  isMaximized.value = !isMaximized.value;
};

// 获取应付科目选项
const fetchSubjectOptions = async () => {
  try {
    const response = await getDictOptions("payable_subject");
    if (response.code === 200 && response.data) {
      subjectOptions.value = response.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
      }));
    } else {
      console.error("获取应付科目选项失败:", response.message);
    }
  } catch (error) {
    console.error("获取应付科目选项失败:", error);
  }
};

// 处理应付科目变化
const handleSubjectsChange = () => {
  pagination.page = 1;
  fetchData();
};

// 自定义渲染标签
const renderSubjectTag = ({ option, handleClose }) => {
  return h(
    NTag,
    {
      closable: true,
      onClose: (e) => {
        e.stopPropagation();
        handleClose();
      },
      style: {
        padding: "0 8px",
      },
    },
    {
      default: () => option.label,
    }
  );
};

// 监听visible属性变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时，重置搜索条件并加载数据
      searchKeyword.value = "";
      selectedSubjects.value = [];
      pagination.page = 1;

      // 获取应付科目选项
      fetchSubjectOptions();

      // 加载数据
      fetchData();

      if (props.multiple) {
        // 多选模式：如果有初始选中的应付账款列表，则设置选中状态
        if (props.initialSelected && props.initialSelected.length > 0) {
          selectedRowKeys.value = props.initialSelected.map((item) => item.id);
          selectedRows.value = [...props.initialSelected];
        } else {
          selectedRowKeys.value = [];
          selectedRows.value = [];
        }
      } else {
        // 单选模式：如果有初始选中应付账款，则设置选中状态
        if (props.initialPayable) {
          selectedPayableId.value = props.initialPayable.id;
          selectedPayable.value = props.initialPayable;
          selectedRowKeys.value = [props.initialPayable.id];
          selectedRows.value = [props.initialPayable];
        } else {
          selectedPayableId.value = null;
          selectedPayable.value = null;
          selectedRowKeys.value = [];
          selectedRows.value = [];
        }
      }
    }
  }
);

// 获取应付账款列表
const fetchData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page, // 使用pageNum而不是page
      size: pagination.pageSize, // 使用pageSize而不是size
      ...props.params,
    };

    // 添加搜索关键字
    if (searchKeyword.value) {
      params.keywords = searchKeyword.value;
    }

    // 添加应付科目筛选条件
    if (selectedSubjects.value && selectedSubjects.value.length > 0) {
      params.feeIds = selectedSubjects.value.join(",");
    }

    // 调用API获取数据
    const response = await payableSelectorApi.getPayableList(params);

    if (response.code === 200 && response.data) {
      // 使用返回的list数据
      payableData.value = response.data.list || [];

      // 更新分页信息，使用新的接口返回结构
      pagination.itemCount = response.data.total || 0;
      pagination.pageCount =
        response.data.pages ||
        Math.ceil(pagination.itemCount / pagination.pageSize);

      console.log("应付账款选择器 - 处理后的数据:", payableData.value);

      // 如果是多选模式，更新选中状态
      if (props.multiple && selectedRowKeys.value.length > 0) {
        // 过滤掉不在当前页的选中项
        const currentPageIds = payableData.value.map((item) => item.id);
        selectedRows.value = selectedRows.value
          .filter(
            (item) =>
              selectedRowKeys.value.includes(item.id) &&
              !currentPageIds.includes(item.id)
          )
          .concat(
            payableData.value.filter((item) =>
              selectedRowKeys.value.includes(item.id)
            )
          );
      }
    } else {
      messages.error(response.message || "获取应付账款列表失败");
    }
  } catch (error) {
    console.error("获取应付账款列表失败:", error);
    messages.error("获取应付账款列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchData();
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  fetchData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchData();
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  emit("update:visible", false);
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;

  if (keys.length > 0) {
    // 更新选中的行数据
    selectedRows.value = payableData.value.filter((item) =>
      keys.includes(item.id)
    );

    // 单选模式下，只保留最后选中的一项
    if (!props.multiple && selectedRows.value.length > 0) {
      const lastSelected = selectedRows.value[selectedRows.value.length - 1];
      selectedPayableId.value = lastSelected.id;
      selectedPayable.value = lastSelected;
      selectedRowKeys.value = [lastSelected.id];
      selectedRows.value = [lastSelected];
    }
  } else {
    selectedRows.value = [];
    if (!props.multiple) {
      selectedPayableId.value = null;
      selectedPayable.value = null;
    }
  }
};

// 处理确认
const handleConfirm = () => {
  if (props.multiple) {
    // 多选模式
    if (selectedRows.value.length > 0) {
      emit("selectMultiple", selectedRows.value);
      emit("update:visible", false);
    } else {
      messages.warning("请至少选择一条应付账款");
    }
  } else {
    // 单选模式
    if (selectedRows.value.length > 0) {
      // 使用选中的行数据
      emit("select", selectedRows.value[0]);
      emit("update:visible", false);
    } else {
      messages.warning("请先选择一条应付账款");
    }
  }
};

// 行属性函数 - 添加选中样式
const rowProps = (row) => {
  // 多选模式
  if (props.multiple) {
    return {
      style: selectedRowKeys.value.includes(row.id)
        ? "background-color: rgba(24, 160, 88, 0.1);"
        : "",
    };
  }

  // 单选模式
  return {
    style:
      selectedPayableId.value === row.id
        ? "background-color: rgba(24, 160, 88, 0.1);"
        : "",
    onClick: () => {
      // 单击选中行
      selectedPayableId.value = row.id;
      selectedPayable.value = row;
    },
  };
};

// 处理窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

// 组件挂载时添加窗口大小变化监听器
onMounted(() => {
  window.addEventListener("resize", handleResize);
});

// 组件卸载时移除窗口大小变化监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.payable-selector-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-area {
  margin-bottom: 16px;
}

.search-inputs-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  flex-wrap: nowrap;
}

.search-input {
  width: 300px;
  min-width: 200px;
  flex-shrink: 1;
}

.subject-select {
  width: 450px;
  min-width: 200px;
  flex-grow: 1;
}

:deep(.n-data-table .n-data-table-td) {
  padding: 8px 12px;
}

:deep(.n-tag) {
  margin: 0;
}

/* 应付科目选择器样式 */
.subject-select :deep(.n-base-selection-tags) {
  padding: 0 8px;
  flex-wrap: nowrap;
  overflow-x: auto;
  max-width: 100%;
}

.subject-select :deep(.n-tag) {
  margin: 4px 4px 4px 0;
  max-width: none;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.subject-select :deep(.n-base-selection-tag-wrapper) {
  max-width: none;
  flex-shrink: 0;
}

.subject-select :deep(.n-base-selection-input) {
  margin-top: 4px;
  margin-bottom: 4px;
}

.subject-select :deep(.n-base-selection) {
  max-width: 100%;
}
</style>
