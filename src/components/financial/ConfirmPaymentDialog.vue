<template>
  <n-modal
    :show="show"
    @update:show="$emit('update:show', $event)"
    title="确认付款"
    preset="card"
    style="width: 600px"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 付款明细表格 -->
      <n-form-item label="付款明细">
        <n-data-table
          :columns="detailColumnsWithColspan"
          :data="tableDataWithTotal"
          :pagination="false"
          :bordered="true"
          size="small"
          style="width: 100%"
          :max-height="200"
          :virtual-scroll="true"
          :row-class-name="getRowClassName"
          :row-props="
            (row) => ({
              style: row.isTotal
                ? 'background-color: #f5f5f5; font-weight: bold;'
                : '',
            })
          "
        />
      </n-form-item>

      <n-form-item label="应付金额">
        <n-input :value="payableAmountFormatted" disabled style="width: 100%" />
      </n-form-item>

      <!-- 实付金额说明信息 -->
      <n-alert
        v-if="isBatchMode"
        type="info"
        :show-icon="false"
        style="margin-bottom: 16px"
      >
        批量付款时实付金额不可修改，将按比例分配到各条应付账款
      </n-alert>
      <n-alert
        v-else
        type="info"
        :show-icon="false"
        style="margin-bottom: 16px"
      >
        实付金额不能超过应付金额，差额部分将继续保留在应付账款列表中
      </n-alert>

      <n-form-item label="实付金额（元）" path="actualAmount">
        <n-input-number
          v-model:value="formData.actualAmount"
          :min="0.01"
          :max="isBatchMode ? undefined : payableAmount"
          :precision="2"
          button-placement="both"
          :step="100"
          @update:value="handleActualAmountChange"
          style="width: 100%"
          :disabled="isBatchMode"
          clearable
        >
          <template #prefix> ¥ </template>
        </n-input-number>
      </n-form-item>

      <n-form-item label="付款账户" path="accountId">
        <n-select
          v-model:value="formData.accountId"
          :options="accountOptions"
          placeholder="请选择付款账户"
          :loading="accountLoading"
          @update:value="handleAccountChange"
        />
      </n-form-item>

      <!-- 业务流水号字段 -->
      <n-form-item label="业务流水号">
        <n-input
          v-model:value="formData.businessSerialNumber"
          placeholder="建议输入银行流水号"
          :maxlength="50"
          clearable
        />
      </n-form-item>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="$emit('update:show', false)">取消付款</n-button>
        <n-button type="primary" @click="handleConfirm" :loading="saving">
          确认付款
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NSelect,
  NInput,
  NInputNumber,
  NSpace,
  NButton,
  NDataTable,
  NAlert,
} from "naive-ui";
import { accountsApi } from "@/api/accounts";
import { formatMoney } from "@/utils/money";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  payableData: {
    type: Object,
    default: () => ({}),
  },
  saving: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:show", "confirm"]);

// 表单引用
const formRef = ref(null);

// 表单数据
const formData = reactive({
  accountId: null,
  actualAmount: null,
  businessSerialNumber: "", // 业务流水号
});

// 付款账户选项
const accountOptions = ref([]);
const accountLoading = ref(false);

// 判断是否为批量模式
const isBatchMode = computed(() => {
  // 如果有batchData且长度大于1，则为批量模式
  if (props.payableData.batchData && props.payableData.batchData.length > 1) {
    return true;
  }
  // 通过isBatch标识判断
  return props.payableData.isBatch === true;
});

// 应付金额（元）
const payableAmount = computed(() => {
  const amount = props.payableData.feeAmount || 0;

  // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
  if (amount === 0 || amount === null || amount === undefined) {
    return 0;
  }

  // 将分转换为元
  return amount / 100;
});

// 应付金额格式化显示
const payableAmountFormatted = computed(() => {
  const amount = props.payableData.feeAmount || 0;

  // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
  if (amount === 0 || amount === null || amount === undefined) {
    return "￥0";
  }

  // 将分转换为元
  const amountInYuan = amount / 100;
  return formatMoney(amountInYuan);
});

// 表格数据（包含总计行）
const tableDataWithTotal = computed(() => {
  let details = [];

  // 如果是批量数据，使用batchData；如果是单条数据，构造数组
  if (props.payableData.batchData) {
    // 批量数据：直接使用选中的原始数据
    details = [...props.payableData.batchData];
  } else {
    // 单条数据：构造为数组格式
    details = [
      {
        id: props.payableData.id,
        feeName: props.payableData.feeName,
        feeAmount: props.payableData.feeAmount,
        payableSummary: props.payableData.payableSummary || "",
      },
    ];
  }

  // 添加合计行
  const totalAmount = details.reduce(
    (sum, detail) => sum + (detail.feeAmount || 0),
    0
  );
  const totalRow = {
    id: "", // 合计行第一列为空
    feeName: "合计", // 第二列显示"合计"
    feeAmount: totalAmount,
    payableSummary: "", // 备注为空
    isTotal: true, // 标识为合计行
  };

  return [...details, totalRow];
});

// 明细表格列定义
const detailColumnsWithColspan = computed(() => [
  {
    title: "应付标识",
    key: "id",
    width: 100,
    align: "center",
    render: (row) => {
      // 合计行不显示内容
      if (row.isTotal) {
        return "";
      }
      // 显示应付标识ID
      return row.id || "";
    },
  },
  {
    title: "应付科目",
    key: "feeName",
    width: 150,
    align: "center",
    render: (row) => {
      // 合计行显示"合计"，并使用HTML方式实现跨列
      if (row.isTotal) {
        return "合计";
      }
      return row.feeName || "";
    },
  },
  {
    title: "应付金额",
    key: "feeAmount",
    width: 150,
    align: "center",
    render: (row) => {
      const amount = row.feeAmount || 0;

      // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
      if (amount === 0 || amount === null || amount === undefined) {
        return "￥0";
      }

      // 将分转换为元
      const yuanAmount = amount / 100;

      // 判断是否为整数，决定小数位数
      const precision = yuanAmount % 1 === 0 ? 0 : 2;

      // 使用 formatMoney 函数格式化金额（包含千分位分隔符）
      return formatMoney(yuanAmount, precision, "￥");
    },
  },
  {
    title: "付款摘要",
    key: "payableSummary",
    align: "left",
    ellipsis: {
      tooltip: true,
    },
  },
]);

// 行样式函数
function getRowClassName(row) {
  return row.isTotal ? "total-row" : "";
}

// 表单验证规则
const rules = computed(() => ({
  accountId: {
    required: true,
    message: "请选择付款账户",
    trigger: ["blur", "change"],
  },
  actualAmount: {
    required: true,
    type: "number",
    min: 0.01,
    max: isBatchMode.value ? undefined : payableAmount.value,
    message: isBatchMode.value
      ? "请输入有效的实付金额"
      : `实付金额必须大于0且不能超过应付金额 ¥${payableAmount.value}`,
    trigger: ["blur", "change"],
  },
}));

// 监听弹窗显示状态，重置表单数据
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 弹窗显示时重置表单数据
      resetFormData();
      // 根据应付机构ID加载账户选项
      const payableOrgId = getPayableOrgId();
      if (payableOrgId) {
        loadAccountOptions(payableOrgId);
      }
    }
  }
);

// 获取应付机构ID
function getPayableOrgId() {
  // 由于批量确认时被选中的数据必然是相同机构的，所以可以直接取第一条数据的机构ID
  if (props.payableData.batchData && props.payableData.batchData.length > 0) {
    return props.payableData.batchData[0].payableOrgId;
  } else if (props.payableData.payableOrgId) {
    return props.payableData.payableOrgId;
  }
  return null;
}

// 重置表单数据
function resetFormData() {
  formData.accountId = null;
  formData.actualAmount = payableAmount.value; // 默认填充应付金额
  formData.businessSerialNumber = "";
}

// 处理实付金额变化
function handleActualAmountChange(value) {
  formData.actualAmount = value;
}

// 处理账户变化
function handleAccountChange(value) {
  formData.accountId = value;
}

// 处理确认付款
function handleConfirm() {
  formRef.value?.validate(async (errors) => {
    if (errors) {
      console.error("表单验证错误:", errors);
      return;
    }

    try {
      // 构造提交数据
      const submitData = {
        accountId: formData.accountId,
        feeAmount: Math.round(formData.actualAmount * 100), // 转换为分
        businessSerialNumber: formData.businessSerialNumber,
      };

      console.log("=== 确认付款提交数据 ===");
      console.log(JSON.stringify(submitData, null, 2));

      // 触发确认事件
      emit("confirm", submitData);
    } catch (error) {
      console.error("确认付款失败:", error);
      messages.error("确认付款失败");
    }
  });
}

// 加载付款账户选项
async function loadAccountOptions(payableOrgId) {
  if (!payableOrgId) {
    accountOptions.value = [];
    return;
  }

  accountLoading.value = true;
  try {
    // 根据应付机构ID查询该机构的账户
    const response = await accountsApi.getList({
      ownerOrgId: payableOrgId,
      page: 1,
      size: 100, // 设置较大的size以获取所有账户
    });

    if (response.code === 200 && response.data && response.data.list) {
      // 过滤出可付款且状态为可用的账户
      const availableAccounts = response.data.list.filter(
        (account) => account.payable === true && account.usable === true
      );

      // 转换为下拉框选项格式
      accountOptions.value = availableAccounts.map((account) => ({
        label: account.abbr, // 账户名称作为显示标签
        value: account.id.toString(), // 账户ID作为值
        account: account, // 保存完整账户信息，以便需要时使用
      }));
    } else {
      accountOptions.value = [];
    }
  } catch (error) {
    console.error("加载付款账户选项失败:", error);
    accountOptions.value = [];
  } finally {
    accountLoading.value = false;
  }
}

// 暴露表单引用给父组件
defineExpose({
  formRef,
});
</script>

<style lang="scss" scoped>
.batch-item {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

:deep(.total-row) {
  background-color: #f5f5f5;
  font-weight: bold;
  border-top: 2px solid #d9d9d9;
}

:deep(.total-row td) {
  background-color: #f5f5f5 !important;
}

/* 合计行样式优化 */
:deep(.total-row td:first-child) {
  border-right: none !important;
  padding-right: 0 !important;
}

:deep(.total-row td:nth-child(2)) {
  border-left: none !important;
  text-align: center !important;
  padding-left: 0 !important;
}
</style>
