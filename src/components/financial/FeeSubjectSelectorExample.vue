<template>
  <div class="fee-subject-selector-example">
    <n-card title="费用科目选择器使用示例">
      <n-space vertical>
        <!-- 基础使用 -->
        <n-form-item label="选择费用科目">
          <n-input-group>
            <n-input
              v-model:value="selectedSubject.subjectName"
              placeholder="请选择费用科目"
              readonly
              style="flex: 1"
            />
            <n-button type="primary" @click="showSelector = true">
              选择科目
            </n-button>
          </n-input-group>
        </n-form-item>

        <!-- 只显示可收款科目 -->
        <n-form-item label="选择可收款科目">
          <n-input-group>
            <n-input
              v-model:value="selectedReceivableSubject.subjectName"
              placeholder="请选择可收款科目"
              readonly
              style="flex: 1"
            />
            <n-button type="primary" @click="showReceivableSelector = true">
              选择科目
            </n-button>
          </n-input-group>
        </n-form-item>

        <!-- 只显示可付款科目 -->
        <n-form-item label="选择可付款科目">
          <n-input-group>
            <n-input
              v-model:value="selectedPayableSubject.subjectName"
              placeholder="请选择可付款科目"
              readonly
              style="flex: 1"
            />
            <n-button type="primary" @click="showPayableSelector = true">
              选择科目
            </n-button>
          </n-input-group>
        </n-form-item>

        <!-- 显示选中的科目信息 -->
        <n-divider />
        <div v-if="selectedSubject.id">
          <h4>选中的科目信息：</h4>
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="科目ID">
              {{ selectedSubject.id }}
            </n-descriptions-item>
            <n-descriptions-item label="科目名称">
              {{ selectedSubject.subjectName }}
            </n-descriptions-item>
            <n-descriptions-item label="科目类别">
              {{ selectedSubject.subjectCategoryName }}
            </n-descriptions-item>
            <n-descriptions-item label="可收款">
              <n-tag :type="selectedSubject.receivable ? 'success' : 'default'">
                {{ selectedSubject.receivable ? '是' : '否' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="可付款">
              <n-tag :type="selectedSubject.payable ? 'success' : 'default'">
                {{ selectedSubject.payable ? '是' : '否' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="默认金额">
              {{ formatMoney(selectedSubject.defaultAmount / 100) }}
            </n-descriptions-item>
            <n-descriptions-item label="备注" :span="2">
              {{ selectedSubject.remark || '-' }}
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </n-space>
    </n-card>

    <!-- 费用科目选择器 - 基础使用 -->
    <FeeSubjectSelector
      v-model:visible="showSelector"
      @select="handleSubjectSelect"
    />

    <!-- 费用科目选择器 - 只显示可收款科目 -->
    <FeeSubjectSelector
      v-model:visible="showReceivableSelector"
      :receivable-only="true"
      @select="handleReceivableSubjectSelect"
    />

    <!-- 费用科目选择器 - 只显示可付款科目 -->
    <FeeSubjectSelector
      v-model:visible="showPayableSelector"
      :payable-only="true"
      @select="handlePayableSubjectSelect"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  NCard,
  NSpace,
  NFormItem,
  NInputGroup,
  NInput,
  NButton,
  NDivider,
  NDescriptions,
  NDescriptionsItem,
  NTag,
} from "naive-ui";
import FeeSubjectSelector from "./FeeSubjectSelector.vue";
import { formatMoney } from "@/utils/money";

// 选择器显示状态
const showSelector = ref(false);
const showReceivableSelector = ref(false);
const showPayableSelector = ref(false);

// 选中的科目
const selectedSubject = reactive({
  id: null,
  subjectName: "",
  subjectCategory: "",
  subjectCategoryName: "",
  receivable: false,
  payable: false,
  defaultAmount: 0,
  remark: "",
});

const selectedReceivableSubject = reactive({
  id: null,
  subjectName: "",
});

const selectedPayableSubject = reactive({
  id: null,
  subjectName: "",
});

// 处理科目选择
const handleSubjectSelect = (subject) => {
  console.log("选中的科目:", subject);
  
  // 更新选中的科目信息
  Object.assign(selectedSubject, subject);
};

// 处理可收款科目选择
const handleReceivableSubjectSelect = (subject) => {
  console.log("选中的可收款科目:", subject);
  
  Object.assign(selectedReceivableSubject, {
    id: subject.id,
    subjectName: subject.subjectName,
  });
};

// 处理可付款科目选择
const handlePayableSubjectSelect = (subject) => {
  console.log("选中的可付款科目:", subject);
  
  Object.assign(selectedPayableSubject, {
    id: subject.id,
    subjectName: subject.subjectName,
  });
};
</script>

<style scoped>
.fee-subject-selector-example {
  padding: 20px;
}

h4 {
  margin: 16px 0 8px 0;
  color: var(--text-color-1);
}
</style>
