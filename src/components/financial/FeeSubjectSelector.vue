<template>
  <n-modal
    :show="visible"
    @update:show="handleVisibleChange"
    preset="card"
    title="费用科目选择"
    style="width: 800px"
    :mask-closable="false"
    transform-origin="center"
  >
    <div class="selector-content">
      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-wrapper">
          <n-input
            v-model:value="searchKeyword"
            placeholder="请输入科目名称关键字进行搜索"
            clearable
            style="width: 300px"
            @keydown.enter="handleSearch"
          >
            <template #suffix>
              <n-button text @click="handleSearch">
                <template #icon>
                  <n-icon>
                    <SearchOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
          </n-input>

          <!-- 筛选条件 -->
          <n-space class="filter-area">
            <n-select
              v-model:value="filterParams.subjectCategory"
              :options="categoryOptions"
              placeholder="科目类别"
              clearable
              style="width: 150px"
              @update:value="handleSearch"
            />
          </n-space>

          <n-text depth="3" class="search-tip">
            <n-icon size="14" style="margin-right: 4px">
              <InformationCircleOutline />
            </n-icon>
            {{
              multiple
                ? "点击数据列表可以多选科目"
                : "双击数据列表可以选中该科目"
            }}
          </n-text>
        </div>
      </div>

      <!-- 数据列表 -->
      <div class="data-list">
        <n-spin :show="loading">
          <n-data-table
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :pagination="false"
            :row-key="(row) => row.id"
            :row-class-name="rowClassName"
            :row-props="rowProps"
            :checked-row-keys="selectedRowKeys"
            :max-height="tableMaxHeight"
            virtual-scroll
            :min-row-height="48"
            :height-for-row="() => 48"
            striped
            @update:checked-row-keys="handleCheckedRowKeysChange"
          />
        </n-spin>
      </div>
    </div>

    <template #footer>
      <n-space justify="space-between" align="center">
        <!-- 分页 -->
        <div v-if="pagination.itemCount > 0">
          <n-pagination
            v-model:page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :item-count="pagination.itemCount"
            :page-sizes="[10, 20, 50, 100]"
            show-size-picker
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
          />
        </div>
        <div v-else></div>

        <!-- 操作按钮 -->
        <n-space>
          <n-button @click="handleClose">取消</n-button>
          <n-button
            type="primary"
            @click="handleConfirm"
            :disabled="multiple ? selectedRowKeys.length === 0 : !selectedRow"
          >
            确定
          </n-button>
        </n-space>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, watch, h, computed, onMounted, onUnmounted } from "vue";
import {
  NModal,
  NButton,
  NIcon,
  NSpin,
  NDataTable,
  NInput,
  NSpace,
  NTag,
  NText,
  NPagination,
  NSelect,
} from "naive-ui";
import { SearchOutline, InformationCircleOutline } from "@vicons/ionicons5";
import { feeSubjectApi } from "@/api/feeSubject";
import messages from "@/utils/messages";
import { formatMoney } from "@/utils/money";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 筛选参数
  params: {
    type: Object,
    default: () => ({}),
  },
  // 可收款筛选：1-全部，2-仅可收款，3-仅不可收款
  receivable: {
    type: Number,
    default: 1,
  },
  // 可付款筛选：1-全部，2-仅可付款，3-仅不可付款
  payable: {
    type: Number,
    default: 1,
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 已选中的科目IDs（多选模式下使用）
  selectedSubjectIds: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update:visible", "select"]);

// 状态变量
const loading = ref(false);
const searchKeyword = ref("");
const selectedRowKeys = ref([]);
const selectedRow = ref(null);
const tableRef = ref(null);

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight);

// 计算表格容器动态高度 - 适应禁用外层滚动的情况
const tableMaxHeight = computed(() => {
  const screenHeight = windowHeight.value;

  // 模态框的各部分高度
  const modalHeaderHeight = 60; // 模态框头部
  const searchAreaHeight = 80; // 搜索区域
  const modalFooterHeight = 80; // 模态框底部按钮区域
  const modalPadding = 48; // 模态框内边距
  const margin = 40; // 额外边距

  // 计算可用高度
  const usedHeight =
    modalHeaderHeight +
    searchAreaHeight +
    modalFooterHeight +
    modalPadding +
    margin;
  const availableHeight = screenHeight - usedHeight;

  // 根据屏幕高度设置合适的表格高度
  if (screenHeight >= 1080) {
    // 大屏幕 - 最大高度600px
    return Math.min(availableHeight, 600);
  } else if (screenHeight >= 768) {
    // 中等屏幕 - 最大高度500px
    return Math.min(availableHeight, 500);
  } else {
    // 小屏幕 - 最大高度400px
    return Math.min(availableHeight, 400);
  }
});

// 筛选参数
const filterParams = reactive({
  subjectCategory: null,
});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  pageCount: 0,
});

// 表格数据
const tableData = ref([]);

// 科目类别选项
const categoryOptions = feeSubjectApi.getSubjectCategoryOptions();

// 表格列配置
const createColumns = () => {
  return [
    {
      type: "selection",
      width: 50,
      multiple: props.multiple,
    },
    {
      title: "科目ID",
      key: "id",
      width: 80,
    },
    {
      title: "科目名称",
      key: "subjectName",
      width: 180,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "科目类别",
      key: "subjectCategoryName",
      width: 150,
    },

    {
      title: "默认金额(元)",
      key: "defaultAmount",
      width: 120,
      render: (row) => {
        return h(
          "span",
          {
            style: {
              color: "#f0a020",
              fontWeight: "bold",
            },
          },
          formatMoney(row.defaultAmount / 100)
        );
      },
    },
  ];
};

const columns = createColumns();

// 处理弹窗显示状态变化
const handleVisibleChange = (value) => {
  emit("update:visible", value);
  if (!value) {
    // 关闭弹窗时重置状态
    resetState();
  }
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
};

// 确认选择
const handleConfirm = () => {
  if (props.multiple) {
    // 多选模式
    if (selectedRowKeys.value.length > 0) {
      const selectedSubjects = tableData.value.filter((item) =>
        selectedRowKeys.value.includes(item.id)
      );
      emit("select", selectedSubjects);
      handleClose();
    } else {
      messages.warning("请至少选择一个费用科目");
    }
  } else {
    // 单选模式
    if (selectedRow.value) {
      emit("select", selectedRow.value);
      handleClose();
    } else {
      messages.warning("请选择一个费用科目");
    }
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchData();
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  fetchData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchData();
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;

  if (props.multiple) {
    // 多选模式下不需要设置selectedRow
    selectedRow.value = null;
  } else {
    // 单选模式
    if (keys.length > 0) {
      selectedRow.value = tableData.value.find((item) => item.id === keys[0]);
    } else {
      selectedRow.value = null;
    }
  }

  // 强制更新视图
  tableData.value = [...tableData.value];
};

// 行样式
const rowClassName = (row) => {
  if (selectedRowKeys.value.includes(row.id)) {
    return "selected-row";
  }
  return "";
};

// 行属性函数 - 添加双击事件和选中样式
const rowProps = (row) => {
  return {
    onClick: () => {
      if (props.multiple) {
        // 多选模式：切换选中状态
        const currentKeys = [...selectedRowKeys.value];
        const index = currentKeys.indexOf(row.id);
        if (index > -1) {
          currentKeys.splice(index, 1);
        } else {
          currentKeys.push(row.id);
        }
        handleCheckedRowKeysChange(currentKeys);
      } else {
        // 单选模式：直接选中
        selectedRowKeys.value = [row.id];
        selectedRow.value = row;
      }
    },
    onDblclick: () => {
      if (props.multiple) {
        // 多选模式：双击不自动确认，避免误操作
        return;
      } else {
        // 单选模式：双击选中并确认
        selectedRowKeys.value = [row.id];
        selectedRow.value = row;
        handleConfirm();
      }
    },
  };
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
      keywords: searchKeyword.value || undefined,
      ...props.params,
    };

    // 添加筛选条件
    if (filterParams.subjectCategory) {
      params.subjectCategory = filterParams.subjectCategory;
    }

    // 根据props设置筛选条件
    if (props.receivable === 2) {
      params.receivable = true;
    } else if (props.receivable === 3) {
      params.receivable = false;
    }

    if (props.payable === 2) {
      params.payable = true;
    } else if (props.payable === 3) {
      params.payable = false;
    }

    const response = await feeSubjectApi.getFeeSubjectList(params);

    if (response.code === 200) {
      tableData.value = response.data.list;
      pagination.itemCount = response.data.total;
      pagination.pageCount =
        response.data.pages ||
        Math.ceil(response.data.total / pagination.pageSize);
    } else {
      messages.error(response.message || "获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    messages.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 重置状态
const resetState = () => {
  searchKeyword.value = "";
  selectedRowKeys.value = [];
  selectedRow.value = null;
  pagination.page = 1;
  filterParams.subjectCategory = null;
};

// 初始化选中状态
const initSelectedState = () => {
  // 清理所有选中状态
  selectedRowKeys.value = [];
  selectedRow.value = null;

  // 只有在多选模式且明确传入了选中ID时才保留选中状态
  if (
    props.multiple &&
    props.selectedSubjectIds &&
    props.selectedSubjectIds.length > 0
  ) {
    selectedRowKeys.value = [...props.selectedSubjectIds];
  }
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 打开弹窗时初始化选中状态
      initSelectedState();
      // 获取数据
      fetchData();
    }
  }
);

// 监听已选中的科目IDs变化（多选模式）
watch(
  () => props.selectedSubjectIds,
  (newIds) => {
    if (props.multiple && props.visible) {
      selectedRowKeys.value = [...newIds];
    }
  }
);

// 窗口大小变化监听器
const handleResize = () => {
  windowHeight.value = window.innerHeight;
};

// 组件挂载时添加监听器
onMounted(() => {
  window.addEventListener("resize", handleResize);
});

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.selector-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
}

.search-area {
  margin-bottom: 16px;
  width: 100%;
}

.search-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-area {
  margin-left: 12px;
}

.search-tip {
  margin-left: 12px;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.data-list {
  flex: 1;
  overflow: hidden;
  /* 动态高度，适应不同屏幕尺寸 */
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

:deep(.selected-row) {
  background-color: var(--primary-color-hover) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--primary-color-hover) !important;
}

:deep(.n-data-table-tbody .n-data-table-tr:hover) {
  background-color: var(--hover-color);
}

/* 确保表格容器正确处理虚拟滚动 */
:deep(.n-data-table) {
  height: 100%;
}

:deep(.n-data-table-wrapper) {
  height: 100%;
}

/* 表格行高度固定 */
:deep(.n-data-table-tr) {
  height: 48px;
}

/* 表头样式 */
:deep(.n-data-table-th) {
  padding: 10px 12px;
  background-color: #f5f7fa;
  height: 48px;
}

/* 表格单元格样式 */
:deep(.n-data-table-td) {
  padding: 8px 12px;
}

/* 虚拟滚动容器优化 */
:deep(.n-data-table-base-table-body) {
  /* 确保虚拟滚动体能够正确显示 */
  overflow: visible;
}

/* 确保表头始终可见 */
:deep(.n-data-table-thead) {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
}

/* 表格样式优化 */
:deep(.n-data-table-table) {
  cursor: pointer;
}
</style>
