<template>
  <n-modal
    :show="show"
    @update:show="$emit('update:show', $event)"
    :title="isEditMode ? '编辑应收账款' : '新增应收账款'"
    preset="card"
    style="width: 600px"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="应收机构" path="receivableOrgId">
        <n-space align="center" style="width: 100%">
          <n-input
            :value="selectedOrgName"
            placeholder="请选择应收机构"
            readonly
            style="flex: 1"
          />
          <n-button @click="showOrgSelector">选择机构</n-button>
        </n-space>
      </n-form-item>

      <n-form-item label="应收科目" path="receivableSubject">
        <n-select
          v-model:value="formData.receivableSubject"
          :options="filteredSubjectOptions"
          placeholder="请输入或选择应收科目"
          :loading="subjectLoading"
          filterable
          clearable
          remote
          :clear-filter-after-select="false"
          @search="handleSubjectSearch"
          @update:value="handleSubjectChange"
          @clear="handleSubjectClear"
        />
      </n-form-item>

      <n-form-item label="应收对象" path="receivableTarget">
        <n-select
          v-model:value="formData.receivableTarget"
          :options="targetOptions"
          placeholder="请输入或选择应收对象"
          filterable
          tag
          clearable
          :clear-filter-after-select="false"
          @update:value="handleTargetChange"
        />
      </n-form-item>

      <n-form-item label="应收金额（元）" path="receivableAmount">
        <n-input-number
          :min="amountInputConfig.min"
          :max="amountInputConfig.max"
          :step="amountInputConfig.step"
          v-model:value="formData.receivableAmount"
          :precision="2"
          button-placement="both"
          @update:value="handleAmountChange"
          style="width: 100%"
          :default-value="0.01"
          clearable
        >
          <template #prefix> ¥ </template>
        </n-input-number>
      </n-form-item>

      <n-form-item label="收款摘要" path="receivableSummary">
        <n-input
          v-model:value="formData.receivableSummary"
          type="textarea"
          placeholder="请输入收款摘要"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </n-form-item>

      <n-form-item label="应收金额（大写）">
        <n-input :value="amountInChinese" disabled />
      </n-form-item>
    </n-form>

    <!-- 应收机构选择器 -->
    <biz-org-selector
      :visible="orgSelectorVisible"
      @update:visible="orgSelectorVisible = $event"
      title="选择应收机构"
      :single="true"
      @select="handleOrgSelect"
      @cancel="orgSelectorVisible = false"
    />

    <template #footer>
      <n-space justify="end">
        <n-button @click="$emit('update:show', false)">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving">
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { getDictOptions } from "@/api/dict";
import { feeSubjectApi } from "@/api/feeSubject";
import { getNumberInputConfig } from "@/config/inputConfig";

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    required: true,
  },
  amountInChinese: {
    type: String,
    default: "",
  },
  saving: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: "add", // 'add' 或 'edit'
    validator: (value) => ["add", "edit"].includes(value),
  },
});

// Emits
const emit = defineEmits([
  "update:show",
  "save",
  "org-change",
  "subject-change",
  "target-change",
  "amount-change",
]);

// Refs
const formRef = ref(null);
const orgSelectorVisible = ref(false);
const selectedOrgName = ref("");
const subjectOptions = ref([]);
const filteredSubjectOptions = ref([]);
const subjectSearchKeyword = ref("");
const targetOptions = ref([]);
const subjectLoading = ref(false);

// Computed
const isEditMode = computed(() => props.mode === "edit");

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 表单验证规则
const rules = {
  receivableOrgId: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应收机构");
      }
      return true;
    },
    trigger: ["blur", "change"],
  },
  receivableSubject: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应收科目");
      }
      return true;
    },
    trigger: ["blur", "change"],
  },
  receivableTarget: {
    required: true,
    validator: (_, value) => {
      if (!value || (typeof value === "string" && value.trim() === "")) {
        return new Error("请输入或选择应收对象");
      }
      return true;
    },
    trigger: ["blur", "change", "input"],
  },
  receivableAmount: {
    required: true,
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入应收金额");
      }
      if (typeof value === "number" && value < 0.01) {
        return new Error("应收金额必须大于0");
      }
      return true;
    },
    trigger: ["blur", "change", "input"],
  },
  receivableSummary: {
    required: true,
    validator: (_, value) => {
      if (!value || value.trim() === "") {
        return new Error("请输入收款摘要");
      }
      return true;
    },
    trigger: ["blur", "input"],
  },
};

// 初始化数据
onMounted(async () => {
  await loadTargetOptions();
  await loadSubjectOptions();
});

// 监听对话框显示状态和模式，新增时清除表单数据
watch(
  () => [props.show, props.mode],
  ([newShow, newMode]) => {
    if (newShow && newMode === "add") {
      // 新增模式下清除表单数据
      clearFormData();
    }
  }
);

// Methods
function clearFormData() {
  // 清除表单数据
  props.formData.receivableOrgId = null;
  props.formData.receivableSubject = null;
  props.formData.receivableTarget = null;
  props.formData.receivableAmount = null;
  props.formData.receivableSummary = "";

  // 清除接口提交字段
  props.formData.feeId = null;
  props.formData.receivableOrgName = "";
  props.formData.feeTarget = "";
  props.formData.feeAmount = null;

  // 清除相关显示数据
  selectedOrgName.value = "";

  // 重置应收科目搜索状态
  subjectSearchKeyword.value = "";
  filteredSubjectOptions.value = [...subjectOptions.value];

  // 注意：不清除 subjectOptions，因为科目列表是全局的，不依赖机构
}

function showOrgSelector() {
  orgSelectorVisible.value = true;
}

function handleOrgSelect(selectedOrgs) {
  if (selectedOrgs && selectedOrgs.length > 0) {
    const org = selectedOrgs[0];
    props.formData.receivableOrgId = org.id;
    selectedOrgName.value = org.orgName;
    emit("org-change", org);
  }
  orgSelectorVisible.value = false;
}

function handleSubjectChange(value) {
  emit("subject-change", value);
}

function handleSubjectSearch(query) {
  subjectSearchKeyword.value = query;
  filterSubjectOptions(query);
}

function handleSubjectClear() {
  subjectSearchKeyword.value = "";
  filteredSubjectOptions.value = [...subjectOptions.value];
}

function filterSubjectOptions(query) {
  if (!query || query.trim() === "") {
    filteredSubjectOptions.value = [...subjectOptions.value];
    return;
  }

  const keyword = query.toLowerCase().trim();
  filteredSubjectOptions.value = subjectOptions.value.filter(
    (option) =>
      option.label.toLowerCase().includes(keyword) ||
      option.value.toString().includes(keyword)
  );
}

function handleTargetChange(value) {
  emit("target-change", value);
}

function handleAmountChange(value) {
  emit("amount-change", value);
}

function handleSave() {
  console.log("=== ReceivableDialog handleSave 开始 ===");
  console.log("提交前的表单数据:", JSON.stringify(props.formData, null, 2));

  // 在提交前，填充接口所需的字段
  if (props.formData.receivableSubject) {
    props.formData.feeId = props.formData.receivableSubject;
    console.log("设置 feeId:", props.formData.feeId);
  }

  // 填充应收机构ID和名称
  if (props.formData.receivableOrgId) {
    props.formData.receivableOrgName = selectedOrgName.value;
    console.log("设置 receivableOrgName:", props.formData.receivableOrgName);
  }

  // 填充应收对象名称（从选项中获取对应的label）
  if (props.formData.receivableTarget) {
    const targetOption = targetOptions.value.find(
      (option) => option.value === props.formData.receivableTarget
    );
    props.formData.feeTarget = targetOption
      ? targetOption.label
      : props.formData.receivableTarget;
    console.log("设置 feeTarget:", props.formData.feeTarget);
  }

  // 将金额从元转换为分
  if (props.formData.receivableAmount && props.formData.receivableAmount > 0) {
    const amountInYuan = Number(props.formData.receivableAmount);
    const amountInFen = Math.round(amountInYuan * 100);

    props.formData.feeAmount = amountInFen;
    console.log(`金额转换: ${amountInYuan}元 → ${amountInFen}分`);
  }

  console.log("=== 最终表单数据 ===");
  console.log(JSON.stringify(props.formData, null, 2));

  // 创建一个专门用于接口提交的数据对象
  const submitData = {
    feeId: props.formData.feeId,
    receivableOrgId: props.formData.receivableOrgId,
    receivableOrgName: props.formData.receivableOrgName,
    feeTarget: props.formData.feeTarget,
    feeAmount: props.formData.feeAmount,
    receivableSummary: props.formData.receivableSummary,
  };

  console.log("=== 接口提交数据 ===");
  console.log(JSON.stringify(submitData, null, 2));

  // 将提交数据也放到 formData 中，确保父组件能获取到
  Object.assign(props.formData, submitData);

  console.log("=== ReceivableDialog handleSave 结束 ===");
  emit("save");
}

// 加载应收对象选项
async function loadTargetOptions() {
  try {
    const response = await getDictOptions("receivable_target");
    if (response.code === 200) {
      targetOptions.value = response.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
      }));
    }
  } catch (error) {
    console.error("加载应收对象选项失败:", error);
  }
}

// 加载应收科目选项
async function loadSubjectOptions() {
  subjectLoading.value = true;
  try {
    const response = await feeSubjectApi.getFeeSubjectList({
      page: 1,
      size: 500, // 获取所有科目
    });
    if (response.code === 200) {
      subjectOptions.value = response.data.list
        .filter((item) => item.receivable) // 只获取可收款的科目
        .map((item) => ({
          label: item.subjectName,
          value: item.id,
        }));

      // 初始化过滤选项
      filteredSubjectOptions.value = [...subjectOptions.value];
    }
  } catch (error) {
    console.error("加载应收科目选项失败:", error);
    subjectOptions.value = [];
    filteredSubjectOptions.value = [];
  } finally {
    subjectLoading.value = false;
  }
}

// 暴露表单引用给父组件
defineExpose({
  formRef,
});
</script>

<style lang="scss" scoped>
// 组件样式可以根据需要添加
</style>
