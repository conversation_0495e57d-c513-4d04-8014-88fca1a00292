<template>
  <n-modal
    :show="show"
    @update:show="$emit('update:show', $event)"
    title="确认收款"
    preset="card"
    style="width: 600px"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 收款明细表格 -->
      <n-form-item label="收款明细">
        <n-data-table
          :columns="detailColumnsWithColspan"
          :data="tableDataWithTotal"
          :pagination="false"
          :bordered="true"
          size="small"
          style="width: 100%"
          :max-height="200"
          :virtual-scroll="true"
          :row-class-name="getRowClassName"
          :row-props="
            (row) => ({
              style: row.isTotal
                ? 'background-color: #f5f5f5; font-weight: bold;'
                : '',
            })
          "
        />
      </n-form-item>

      <n-form-item label="应收金额">
        <n-input
          :value="receivableAmountFormatted"
          disabled
          style="width: 100%"
        />
      </n-form-item>

      <!-- 实收金额说明信息 -->
      <n-alert
        v-if="isBatchMode"
        type="info"
        :show-icon="false"
        style="margin-bottom: 16px"
      >
        批量收款时实收金额不可修改，将按比例分配到各条应收账款
      </n-alert>
      <n-alert
        v-else
        type="info"
        :show-icon="false"
        style="margin-bottom: 16px"
      >
        实收金额不能超过应收金额，差额部分将继续保留在应收账款列表中
      </n-alert>

      <n-form-item label="实收金额（元）" path="actualAmount">
        <n-input-number
          v-model:value="formData.actualAmount"
          :min="0.01"
          :max="isBatchMode ? undefined : receivableAmount"
          :precision="2"
          button-placement="both"
          :step="100"
          @update:value="handleActualAmountChange"
          style="width: 100%"
          :disabled="isBatchMode"
          clearable
        >
          <template #prefix> ¥ </template>
        </n-input-number>
      </n-form-item>

      <n-form-item label="收款账户" path="accountId">
        <n-select
          v-model:value="formData.accountId"
          :options="accountOptions"
          placeholder="请选择收款账户"
          :loading="accountLoading"
          @update:value="handleAccountChange"
        />
      </n-form-item>

      <!-- 业务流水号字段 -->
      <n-form-item label="业务流水号">
        <n-input
          v-model:value="formData.businessSerialNumber"
          placeholder="建议输入银行流水号"
          :maxlength="50"
          clearable
        />
      </n-form-item>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="$emit('update:show', false)">取消收款</n-button>
        <n-button type="primary" @click="handleConfirm" :loading="saving">
          确认收款
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NSelect,
  NInput,
  NInputNumber,
  NSpace,
  NButton,
  NDataTable,
  NAlert,
} from "naive-ui";
import { accountsApi } from "@/api/accounts";
import { formatMoney } from "@/utils/money";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  receivableData: {
    type: Object,
    default: () => ({}),
  },
  saving: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:show", "confirm"]);

// 表单引用
const formRef = ref(null);

// 表单数据
const formData = reactive({
  accountId: null,
  actualAmount: null,
  businessSerialNumber: "", // 业务流水号
});

// 收款账户选项
const accountOptions = ref([]);
const accountLoading = ref(false);

// 判断是否为批量模式
const isBatchMode = computed(() => {
  // 如果有batchData且长度大于1，则为批量模式
  if (
    props.receivableData.batchData &&
    props.receivableData.batchData.length > 1
  ) {
    return true;
  }
  // 通过isBatch标识判断
  return props.receivableData.isBatch === true;
});

// 应收金额（元）
const receivableAmount = computed(() => {
  const amount = props.receivableData.feeAmount || 0;

  // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
  if (amount === 0 || amount === null || amount === undefined) {
    return 0;
  }

  // 将分转换为元
  return amount / 100;
});

// 应收金额格式化显示
const receivableAmountFormatted = computed(() => {
  const amount = props.receivableData.feeAmount || 0;

  // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
  if (amount === 0 || amount === null || amount === undefined) {
    return "￥0";
  }

  // 将分转换为元
  const yuanAmount = amount / 100;

  // 判断是否为整数，决定小数位数
  const precision = yuanAmount % 1 === 0 ? 0 : 2;

  // 使用 formatMoney 函数格式化金额（包含千分位分隔符）
  return formatMoney(yuanAmount, precision, "￥");
});

// 明细表格列定义
const detailColumnsWithColspan = computed(() => [
  {
    title: "应收标识",
    key: "id",
    width: 100,
    align: "center",
    render: (row) => {
      // 合计行不显示内容
      if (row.isTotal) {
        return "";
      }
      // 显示应收标识ID
      return row.id || "";
    },
  },
  {
    title: "应收科目",
    key: "feeName",
    width: 150,
    align: "center",
    render: (row) => {
      // 合计行显示"合计"，并使用HTML方式实现跨列
      if (row.isTotal) {
        return "合计";
      }
      return row.feeName || "";
    },
  },
  {
    title: "应收金额",
    key: "feeAmount",
    width: 150,
    align: "center",
    render: (row) => {
      const amount = row.feeAmount || 0;

      // 根据用户偏好：正值不显示加号，负值显示减号，零值显示0
      if (amount === 0 || amount === null || amount === undefined) {
        return "￥0";
      }

      // 将分转换为元
      const yuanAmount = amount / 100;

      // 判断是否为整数，决定小数位数
      const precision = yuanAmount % 1 === 0 ? 0 : 2;

      // 使用 formatMoney 函数格式化金额（包含千分位分隔符）
      return formatMoney(yuanAmount, precision, "￥");
    },
  },
  {
    title: "收款摘要",
    key: "receivableSummary",
    align: "left",
    ellipsis: {
      tooltip: true,
    },
  },
]);

// 表格数据（包含合计行）
const tableDataWithTotal = computed(() => {
  let details = [];

  // 如果是批量数据，使用batchData；如果是单条数据，构造数组
  if (props.receivableData.batchData) {
    // 批量数据：直接使用选中的原始数据
    details = [...props.receivableData.batchData];
  } else {
    // 单条数据：构造为数组格式
    details = [
      {
        id: props.receivableData.id,
        feeName: props.receivableData.feeName,
        feeAmount: props.receivableData.feeAmount,
        receivableSummary: props.receivableData.receivableSummary || "",
      },
    ];
  }

  // 添加合计行
  const totalAmount = details.reduce(
    (sum, detail) => sum + (detail.feeAmount || 0),
    0
  );
  const totalRow = {
    id: "", // 合计行第一列为空
    feeName: "合计", // 第二列显示"合计"
    feeAmount: totalAmount,
    receivableSummary: "", // 备注为空
    isTotal: true, // 标识为合计行
  };

  return [...details, totalRow];
});

// 行样式函数
function getRowClassName(row) {
  return row.isTotal ? "total-row" : "";
}

// 表单验证规则
const rules = computed(() => ({
  accountId: {
    required: true,
    message: "请选择收款账户",
    trigger: ["blur", "change"],
  },
  actualAmount: {
    required: true,
    type: "number",
    min: 0.01,
    max: isBatchMode.value ? undefined : receivableAmount.value,
    message: isBatchMode.value
      ? "请输入有效的实收金额"
      : `实收金额必须大于0且不能超过应收金额 ¥${receivableAmount.value}`,
    trigger: ["blur", "change"],
  },
}));

// 获取应收机构ID
function getReceivableOrgId() {
  // 由于批量确认时被选中的数据必然是相同机构的，所以可以直接取第一条数据的机构ID
  if (
    props.receivableData.batchData &&
    props.receivableData.batchData.length > 0
  ) {
    return props.receivableData.batchData[0].receivableOrgId;
  } else if (props.receivableData.receivableOrgId) {
    return props.receivableData.receivableOrgId;
  }
  return null;
}

// 获取机构的可用收款账户
async function fetchOrgAccounts(orgId) {
  if (!orgId) {
    accountOptions.value = [];
    return;
  }

  accountLoading.value = true;
  try {
    // 调用accounts.js的API获取机构账户列表
    const response = await accountsApi.getList({
      ownerOrgId: orgId,
      page: 1,
      size: 100, // 设置较大的size以获取所有账户
    });

    if (response.code === 200 && response.data && response.data.list) {
      // 过滤出可收款且状态为可用的账户
      const availableAccounts = response.data.list.filter(
        (account) => account.receivable === true && account.usable === true
      );

      // 转换为下拉框选项格式
      accountOptions.value = availableAccounts.map((account) => ({
        label: account.abbr, // 账户名称作为显示标签
        value: account.id.toString(), // 账户ID作为值
        account: account, // 保存完整账户信息，以便需要时使用
      }));
    } else {
      accountOptions.value = [];
      console.warn("获取机构账户列表失败或返回数据为空");
    }
  } catch (error) {
    console.error("获取机构可用收款账户失败:", error);
    messages.error("获取机构可用收款账户失败");
    accountOptions.value = [];
  } finally {
    accountLoading.value = false;
  }
}

// 处理收款账户变化
function handleAccountChange(value) {
  formData.accountId = value;
}

// 处理实收金额变化
function handleActualAmountChange(value) {
  // 批量模式下不允许修改实收金额
  if (isBatchMode.value) {
    formData.actualAmount = receivableAmount.value;
    return;
  }

  // 单条模式下，限制实收金额不能超过应收金额
  if (value && value > receivableAmount.value) {
    formData.actualAmount = receivableAmount.value;
    messages.warning(`实收金额不能超过应收金额 ¥${receivableAmount.value}`);
    return;
  }

  formData.actualAmount = value;
}

// 处理确认收款
function handleConfirm() {
  formRef.value?.validate((errors) => {
    if (errors) {
      console.error("表单验证错误:", errors);
      return;
    }

    // 构造提交数据
    const submitData = {
      id: props.receivableData.id,
      accountId: Number(formData.accountId), // 转换为数值型
      feeAmount: Math.round(formData.actualAmount * 100), // 元转分
      // 包含批量相关数据（如果存在）
      batchData: props.receivableData.batchData,
      isBatch: props.receivableData.isBatch || false,
    };

    // 添加业务流水号（如果有输入）
    if (formData.businessSerialNumber) {
      submitData.businessSerialNumber = formData.businessSerialNumber;
    }

    emit("confirm", submitData);
  });
}

// 监听弹窗显示状态，重置表单
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 重置表单数据
      Object.assign(formData, {
        accountId: null,
        actualAmount: receivableAmount.value, // 默认填充应收金额
        businessSerialNumber: "", // 重置业务流水号
      });

      // 批量模式下，实收金额锁定为应收金额，不允许修改
      if (isBatchMode.value) {
        formData.actualAmount = receivableAmount.value;
      }

      // 根据应收机构ID获取收款账户
      const receivableOrgId = getReceivableOrgId();
      if (receivableOrgId) {
        fetchOrgAccounts(receivableOrgId);
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.batch-item {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

:deep(.total-row) {
  background-color: #f5f5f5;
  font-weight: bold;
  border-top: 2px solid #d9d9d9;
}

:deep(.total-row td) {
  background-color: #f5f5f5 !important;
}

/* 合计行样式优化 */
:deep(.total-row td:first-child) {
  border-right: none !important;
  padding-right: 0 !important;
}

:deep(.total-row td:nth-child(2)) {
  border-left: none !important;
  text-align: center !important;
  padding-left: 0 !important;
}
</style>
