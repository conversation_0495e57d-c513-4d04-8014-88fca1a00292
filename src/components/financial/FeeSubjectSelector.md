# FeeSubjectSelector 费用科目选择器

## 概述

`FeeSubjectSelector` 是一个费用科目选择器弹窗组件，参考了 `VehicleSkuSelector` 的设计模式，提供了完整的费用科目选择功能。

## 功能特性

- ✅ 弹窗式选择界面，支持最大化/最小化
- ✅ 关键词搜索功能
- ✅ 多维度筛选（科目类别、可收款、可付款）
- ✅ 虚拟滚动支持大数据量
- ✅ 单选模式，支持单击选中、双击确认
- ✅ 分页功能
- ✅ 响应式设计

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `visible` | Boolean | `false` | 控制弹窗显示/隐藏 |
| `params` | Object | `{}` | 额外的查询参数 |
| `receivableOnly` | Boolean | `false` | 是否只显示可收款科目 |
| `payableOnly` | Boolean | `false` | 是否只显示可付款科目 |
| `multiple` | Boolean | `false` | 是否支持多选 |
| `selectedSubjectIds` | Array | `[]` | 已选中的科目IDs（多选模式下使用） |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:visible` | `(visible: boolean)` | 弹窗显示状态变化 |
| `select` | `(subject: Object \| Array)` | 选中科目时触发，单选返回对象，多选返回数组 |

## 使用方法

### 基础使用

```vue
<template>
  <div>
    <n-button @click="showSelector = true">选择费用科目</n-button>
    
    <FeeSubjectSelector
      v-model:visible="showSelector"
      @select="handleSubjectSelect"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FeeSubjectSelector from '@/components/financial/FeeSubjectSelector.vue'

const showSelector = ref(false)

const handleSubjectSelect = (subject) => {
  console.log('选中的科目:', subject)
  // subject 包含完整的科目信息
}
</script>
```

### 只显示可收款科目

```vue
<template>
  <FeeSubjectSelector
    v-model:visible="showReceivableSelector"
    :receivable-only="true"
    @select="handleReceivableSubjectSelect"
  />
</template>
```

### 只显示可付款科目

```vue
<template>
  <FeeSubjectSelector
    v-model:visible="showPayableSelector"
    :payable-only="true"
    @select="handlePayableSubjectSelect"
  />
</template>
```

### 多选模式

```vue
<template>
  <FeeSubjectSelector
    v-model:visible="showMultiSelector"
    :multiple="true"
    :receivable-only="true"
    :selected-subject-ids="selectedSubjectIds"
    @select="handleMultiSubjectSelect"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const selectedSubjects = ref([])
const selectedSubjectIds = computed(() =>
  selectedSubjects.value.map(subject => subject.id)
)

const handleMultiSubjectSelect = (subjects) => {
  console.log('选中的科目数组:', subjects)
  selectedSubjects.value = subjects
}
</script>
```

### 带额外查询参数

```vue
<template>
  <FeeSubjectSelector
    v-model:visible="showSelector"
    :params="{ subjectCategory: 'DEAL_FEE' }"
    @select="handleSubjectSelect"
  />
</template>
```

## 返回的科目数据结构

```javascript
{
  id: 1,                              // 科目ID
  subjectName: "车款",                // 科目名称
  subjectCategory: "DEAL_FEE",        // 科目类别代码
  subjectCategoryName: "销售关联费用", // 科目类别名称
  receivable: true,                   // 是否可收款
  payable: false,                     // 是否可付款
  usable: true,                       // 是否启用
  defaultAmount: 0,                   // 默认金额（分）
  remark: "车辆销售款项",             // 备注
  editorName: "system",               // 经办人
  createdTime: "2025-05-09 10:32:12", // 创建时间
  updateTime: "2025-05-09 10:49:07"   // 更新时间
}
```

## 筛选功能

组件提供了以下筛选功能：

1. **关键词搜索**：支持按科目名称进行模糊搜索
2. **科目类别筛选**：
   - 企业运营 (OPENING_FEE)
   - 销售关联费用 (DEAL_FEE)
   - 其他费用 (OTHER_FEE)
3. **可收款筛选**：是/否
4. **可付款筛选**：是/否

## 交互说明

### 单选模式
- **单击行**：选中该科目
- **双击行**：选中该科目并确认选择

### 多选模式
- **单击行**：切换该科目的选中状态
- **双击行**：无特殊操作（避免误操作）

### 通用操作
- **搜索框回车**：执行搜索
- **筛选条件变化**：自动执行搜索
- **弹窗右上角按钮**：切换最大化/最小化

## 样式说明

- 选中行会高亮显示
- 支持虚拟滚动，性能优秀
- 响应式设计，适配不同屏幕尺寸
- 与系统整体风格保持一致

## 注意事项

1. 组件依赖 `@/api/feeSubject` API 接口
2. 使用了 `@/utils/messages` 进行消息提示
3. 使用了 `@/utils/money` 进行金额格式化
4. 需要确保相关依赖已正确导入

## 完整示例

参考 `FeeSubjectSelectorExample.vue` 文件查看完整的使用示例。
