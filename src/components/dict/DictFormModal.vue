<template>
  <n-modal
    v-model:show="modalVisible"
    :title="isEdit ? '编辑字典' : '新增字典'"
    preset="card"
    :style="{ width: '600px' }"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 网格布局：3行x2列 -->
      <n-grid :cols="2" :x-gap="16" :y-gap="16">
        <!-- 第一行 -->
        <n-grid-item>
          <n-form-item label="字典编码" path="dict_code">
            <n-input
              v-model:value="form.dict_code"
              placeholder="请输入字典编码"
              :disabled="isEdit"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="字典名称" path="dict_name">
            <n-input
              v-model:value="form.dict_name"
              placeholder="请输入字典名称"
            />
          </n-form-item>
        </n-grid-item>

        <!-- 第二行 -->
        <n-grid-item :span="2">
          <n-form-item label="字典类型" path="scope">
            <n-select
              v-model:value="form.scope"
              :options="scopeOptions"
              placeholder="请选择字典类型"
            />
          </n-form-item>
        </n-grid-item>

        <!-- 第三行 -->
        <n-grid-item :span="2">
          <n-form-item label="备注" path="remark">
            <n-input
              v-model:value="form.remark"
              type="textarea"
              placeholder="请输入备注"
              :rows="3"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="loading">
          保存
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NButton,
  NSpace,
  NGrid,
  NGridItem,
} from "naive-ui";
// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 组件状态
const formRef = ref(null);

// 表单数据
const form = reactive({
  dict_code: "",
  dict_name: "",
  scope: "group_available", // 默认集团可用
  org_ids: "",
  org_names: "",
  remark: "",
});

// 可用范围选项
const scopeOptions = [
  { label: "集团通用", value: "group_available" },
  { label: "机构专用", value: "specific_org" },
];

// 表单验证规则
const rules = {
  dict_code: [
    { required: true, message: "请输入字典编码", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: "字典编码只能包含字母、数字和下划线",
      trigger: "blur",
    },
  ],
  dict_name: [{ required: true, message: "请输入字典名称", trigger: "blur" }],
  scope: [{ required: true, message: "请选择字典类型", trigger: "change" }],
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData) {
      Object.assign(form, {
        dict_code: newData.dict_code || "",
        dict_name: newData.dict_name || "",
        scope: newData.scope || "group_available",
        org_ids: newData.org_ids || "",
        org_names: newData.org_names || "",
        remark: newData.remark || "",
      });
    }
  },
  { immediate: true, deep: true }
);

// 处理保存
const handleSave = () => {
  formRef.value?.validate((errors) => {
    if (!errors) {
      emit("save", form);
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
};
</script>

<style scoped>
.org-selector-container {
  width: 100%;
}

:deep(.n-form-item-label) {
  font-weight: 500;
  margin-bottom: 8px;
}

:deep(.n-grid-item) {
  display: flex;
  flex-direction: column;
}

:deep(.n-form-item) {
  margin-bottom: 0;
  flex: 1;
}
</style>
