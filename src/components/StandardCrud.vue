<template>
  <div class="standard-crud">
    <!-- 筛选区域 -->
    <n-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span>筛选条件</span>
          <n-button quaternary @click="toggleFilterCollapse">
            {{ isFilterCollapsed ? "展开" : "收起" }}
            <template #icon>
              <n-icon>
                <ChevronDown v-if="isFilterCollapsed" />
                <ChevronUp v-else />
              </n-icon>
            </template>
          </n-button>
        </div>
      </template>
      <n-form
        inline
        :model="filterForm"
        class="filter-form"
        @keyup.enter="handleFilter"
        :class="{ 'is-collapsed': isFilterCollapsed }"
      >
        <div class="filter-content" ref="filterContent">
          <n-grid :cols="24" :x-gap="12">
            <template v-for="field in filterFields" :key="field.name">
              <n-grid-item :span="getColSpan(field)">
                <n-form-item :label="field.alias || field.name">
                  <template
                    v-if="field.type === 'string' || field.type === 'number'"
                  >
                    <n-input
                      v-model:value="filterForm[field.name]"
                      :placeholder="'请输入' + (field.alias || field.name)"
                    />
                  </template>
                  <template v-else-if="field.type === 'dict'">
                    <n-select
                      v-model:value="filterForm[field.name]"
                      :placeholder="'请选择' + (field.alias || field.name)"
                      :options="
                        field.dict.map((item) => ({
                          label: item.label,
                          value: item.value,
                        }))
                      "
                      style="width: 200px"
                    />
                  </template>
                  <template
                    v-else-if="
                      field.type === 'date' || field.type === 'datetime'
                    "
                  >
                    <n-date-picker
                      v-model:value="filterForm[`${field.name}Range`]"
                      :type="
                        field.type === 'date' ? 'daterange' : 'datetimerange'
                      "
                      :shortcuts="dateShortcuts"
                      clearable
                      style="width: 100%"
                    />
                  </template>
                </n-form-item>
              </n-grid-item>
            </template>
          </n-grid>
        </div>
        <div class="filter-buttons">
          <n-button type="primary" @click="handleFilter" round>
            <template #icon>
              <n-icon><Search /></n-icon>
            </template>
            筛选
          </n-button>
          <n-button @click="resetFilter" round>
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            重置
          </n-button>
        </div>
      </n-form>
    </n-card>

    <!-- 按钮工具栏 -->
    <div class="toolbar">
      <n-button
        class="toolbar-button"
        type="primary"
        @click="refreshData"
        round
      >
        <template #icon>
          <n-icon><Refresh /></n-icon>
        </template>
        刷新
      </n-button>
      <n-button
        v-if="config.addEnable !== false"
        class="toolbar-button"
        type="success"
        @click="handleAdd"
        round
      >
        <template #icon>
          <n-icon><Add /></n-icon>
        </template>
        新增
      </n-button>
      <n-button
        v-if="config.deleteEnable !== false"
        class="toolbar-button"
        type="error"
        @click="handleBatchDelete"
        :disabled="!selectedRows.length"
        round
      >
        <template #icon>
          <n-icon><TrashBin /></n-icon>
        </template>
        删除
      </n-button>
      <n-button
        v-for="button in config.buttons"
        :key="button.label"
        :type="button.type"
        @click="handleCustomAction(button.action)"
        class="toolbar-button"
        round
      >
        <template #icon v-if="button.icon">
          <n-icon>
            <component :is="button.icon" />
          </n-icon>
        </template>
        {{ button.label }}
      </n-button>
    </div>

    <!-- 数据列表和分页器容器 -->
    <div class="table-container">
      <!-- 数据列表 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :row-key="(row) => row.id"
        @update:checked-row-keys="handleSelectionChange"
      />

      <!-- 分页器 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50]"
          :item-count="total"
          show-size-picker
          show-quick-jumper
          @update:page="handleCurrentChange"
          @update:page-size="handleSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      :style="{ width: isSmallScreen ? '100%' : '600px' }"
      preset="card"
      :mask-closable="false"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item
          v-for="field in editableFields"
          :key="field.name"
          :label="field.alias || field.name"
          :path="field.name"
        >
          <n-input
            v-if="field.type === 'string'"
            v-model:value="formData[field.name]"
            :maxlength="field['max-len']"
            :minlength="field['min-len']"
          />
          <n-input
            v-else-if="field.type === 'text'"
            v-model:value="formData[field.name]"
            type="textarea"
            :maxlength="field['max-len']"
            :minlength="field['min-len']"
          />
          <n-input-number
            :min="amountInputConfig.min"
            :max="amountInputConfig.max"
            :step="amountInputConfig.step"
            v-else-if="field.type === 'number'"
            v-model:value="formData[field.name]"
          />
          <n-select
            v-else-if="field.type === 'dict'"
            v-model:value="formData[field.name]"
            :options="
              field.dict.map((item) => ({
                label: item.label,
                value: item.value,
              }))
            "
          />
          <n-date-picker
            v-else-if="field.type === 'date'"
            v-model:value="formData[field.name]"
            type="date"
            clearable
          />
          <n-date-picker
            v-else-if="field.type === 'datetime'"
            v-model:value="formData[field.name]"
            type="datetime"
            clearable
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 详情对话框 -->
    <n-modal
      v-model:show="detailDialogVisible"
      :title="detailDialogTitle"
      :style="{ width: '50%' }"
      preset="card"
      @close="handleDetailDialogClose"
    >
      <component
        v-if="detailComponent"
        :is="detailComponent"
        :detail-data="detailData"
      ></component>
      <RecursiveJsonRenderer v-else ref="jsonRenderer" :data="detailData" />
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, nextTick, h } from "vue";
import { useDialog } from "naive-ui";
import api from "@/utils/api";
import { formatDate } from "@/utils/dateUtils"; // 假设你有一个日期格式化的工具函数
import RecursiveJsonRenderer from "./RecursiveJsonRenderer.vue";
import messages from "@/utils/messages";
import { getNumberInputConfig } from "@/config/inputConfig";
import {
  Search,
  Refresh,
  ChevronDown,
  ChevronUp,
  Create as Edit,
  TrashBin,
  Eye as View,
  Add,
  LinkOutline,
} from "@vicons/ionicons5";

// 修改 props 定义
const props = defineProps({
  config: {
    type: Object,
    required: true,
  },
  detailApi: {
    type: Function,
    default: null,
  },
  detailComponent: {
    type: Object,
    default: null,
  },
  detailDialogTitle: {
    type: String,
    default: "详情",
  },
});

// 使用 Naive UI 的对话框
const dialog = useDialog();

// 响应式数据
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const selectedRows = ref([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formData = reactive({});
const filterForm = reactive({});

// 计算属性
const hasFilters = computed(() => filterFields.value.length > 0);
const visibleFields = computed(() =>
  props.config.fields.filter((field) => field.visible !== false)
);
const editableFields = computed(() =>
  props.config.fields.filter((field) => field.editable !== false)
);
const filterFields = computed(() =>
  props.config.fields.filter((field) => field.filter)
);
const isSmallScreen = computed(() => window.innerWidth < 768);

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 表单验证规则
const formRules = computed(() => {
  const rules = {};
  props.config.fields.forEach((field) => {
    if (field.required) {
      rules[field.name] = [
        {
          required: true,
          message: `请输入${field.alias || field.name}`,
          trigger: "blur",
        },
      ];
    }
    if (field.formatter) {
      if (!rules[field.name]) {
        rules[field.name] = [];
      }
      rules[field.name].push({
        pattern: new RegExp(field.formatter),
        message: `${field.alias || field.name}格式不正确`,
        trigger: "blur",
      });
    }
  });
  return rules;
});

// 日期快捷选项
const dateShortcuts = {
  今日: () => {
    const end = new Date();
    const start = new Date(end.getFullYear(), end.getMonth(), end.getDate());
    return [start, end];
  },
  昨日: () => {
    const end = new Date();
    end.setTime(end.getTime() - 3600 * 1000 * 24);
    const start = new Date(end.getFullYear(), end.getMonth(), end.getDate());
    return [start, end];
  },
  最近7天: () => {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    return [start, end];
  },
  最近30天: () => {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
    return [start, end];
  },
};

// 表格列配置
const columns = computed(() => {
  const cols = [
    {
      type: "selection",
      width: 50,
      fixed: "left",
    },
    ...visibleFields.value.map((field) => ({
      title: field.alias || field.name,
      key: field.name,
      sorter: field.sorted ? "default" : false,
      render: (row) => {
        if (field.type === "dict") {
          return h("span", null, getDictLabel(field, row[field.name]));
        } else if (field.type === "date" || field.type === "datetime") {
          return h("span", null, row[field.name]);
        } else if (field.type === "link") {
          return h(
            "a",
            {
              href: row[field.name],
              target: "_blank",
              style: {
                color: "var(--primary-color)",
                display: "inline-flex",
                alignItems: "center",
              },
            },
            [
              h(LinkOutline, { style: { marginRight: "4px" } }),
              field.alias || "",
            ]
          );
        } else {
          return h("span", null, row[field.name]);
        }
      },
    })),
  ];

  // 添加操作列
  if (
    props.config.editEnable !== false ||
    props.config.deleteEnable !== false
  ) {
    cols.push({
      title: "操作",
      key: "actions",
      width: 150,
      fixed: "right",
      render: (row) => {
        return h(
          "div",
          {
            style: {
              display: "flex",
              gap: "8px",
            },
          },
          [
            h(
              "div",
              {
                style: {
                  cursor: "pointer",
                  color: "var(--primary-color)",
                },
                onClick: () => handleDetail(row),
              },
              [h(View)]
            ),
            props.config.editEnable !== false
              ? h(
                  "div",
                  {
                    style: {
                      cursor: "pointer",
                      color: "var(--primary-color)",
                    },
                    onClick: () => handleEdit(row),
                  },
                  [h(Edit)]
                )
              : null,
            props.config.deleteEnable !== false
              ? h(
                  "div",
                  {
                    style: {
                      cursor: "pointer",
                      color: "var(--error-color)",
                    },
                    onClick: () => handleDelete(row),
                  },
                  [h(TrashBin)]
                )
              : null,
          ]
        );
      },
    });
  }

  return cols;
});

// 方法
const fetchData = async () => {
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      ...filterForm,
    };

    // 处理日期范围
    props.config.fields.forEach((field) => {
      if (
        (field.type === "date" || field.type === "datetime") &&
        field.filter
      ) {
        const range = filterForm[`${field.name}Range`];
        if (range && range.length === 2) {
          params[`${field.name}Begin`] = range[0];
          params[`${field.name}End`] = range[1];
        }
        delete params[`${field.name}Range`];
      }
    });

    const response = await api.get(
      `${props.config.requestPrefix}${props.config.entity}`,
      { params }
    );
    tableData.value = response.data.list;
    total.value = response.data.total;
  } catch (error) {
    messages.error("获取数据失败");
  }
};

const handleSelectionChange = (rowKeys) => {
  selectedRows.value = tableData.value.filter((row) =>
    rowKeys.includes(row.id)
  );
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchData();
};

const handleFilter = () => {
  currentPage.value = 1;
  fetchData();
};

const resetFilter = () => {
  props.config.fields.forEach((field) => {
    if (field.filter) {
      if (field.type === "date" || field.type === "datetime") {
        filterForm[`${field.name}Range`] = null;
      } else {
        filterForm[field.name] = "";
      }
    }
  });
  handleFilter();
};

const refreshData = () => {
  fetchData();
};

const handleAdd = () => {
  dialogTitle.value = "新增";
  Object.keys(formData).forEach((key) => {
    formData[key] = "";
  });
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  dialogTitle.value = "编辑";
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleDetail = async (row) => {
  try {
    let response;
    if (props.detailApi) {
      response = await props.detailApi(row.id);
    } else if (props.config.api && props.config.api.detail) {
      response = await props.config.api.detail(row.id);
    } else {
      // 使用默认 URL
      response = await api.get(
        `${props.config.requestPrefix}${props.config.entity}/${row.id}`
      );
    }
    detailData.value = response.data;
    detailDialogVisible.value = true;

    // 重置音频播放器
    nextTick(() => {
      if (jsonRenderer.value && jsonRenderer.value.resetAudioPlayer) {
        jsonRenderer.value.resetAudioPlayer();
      }
    });
  } catch (error) {
    console.error("获取详情失败", error);
    messages.error("获取详情失败");
  }
};

const handleDelete = (row) => {
  dialog.warning({
    title: "提示",
    content: "确定要删除这条记录吗？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        await api.delete(
          `${props.config.requestPrefix}${props.config.entity}/${row.id}`
        );
        messages.success("删除成功");
        fetchData();
      } catch (error) {
        messages.error("删除失败");
      }
    },
  });
};

const handleBatchDelete = () => {
  dialog.warning({
    title: "提示",
    content: `确定要删除这 ${selectedRows.value.length} 条记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const ids = selectedRows.value.map((row) => row.id).join(",");
        await api.delete(
          `${props.config.requestPrefix}${props.config.entity}?ids=${ids}`
        );
        messages.success("批量删除成功");
        fetchData();
      } catch (error) {
        messages.error("批量删除失败");
      }
    },
  });
};

const handleSave = async () => {
  try {
    // 确保 formRef 存在
    if (!formRef.value) {
      throw new Error("表单引用不存在");
    }
    await formRef.value.validate();
    let response;
    if (formData.id) {
      response = await api.put(
        `${props.config.requestPrefix}${props.config.entity}`,
        formData
      );
    } else {
      response = await api.post(
        `${props.config.requestPrefix}${props.config.entity}`,
        formData
      );
    }
    if (response.code === 200) {
      messages.success(formData.id ? "更新成功" : "新增成功");
      dialogVisible.value = false;
      fetchData();
    } else {
      throw new Error(response.message || "操作失败");
    }
  } catch (error) {
    console.error("Save error:", error);
    messages.error(error.message || (formData.id ? "更新失败" : "新增失败"));
  }
};

const handleCustomAction = (action) => {
  emit("customAction", action, selectedRows.value);
};

const getDictLabel = (field, value) => {
  const option = field.dict.find((item) => item.value === value);
  return option ? option.label : value;
};

const filterContent = ref(null);
const isFilterCollapsed = ref(false);

const toggleFilterCollapse = () => {
  isFilterCollapsed.value = !isFilterCollapsed.value;
};

onMounted(() => {
  fetchData();
  nextTick(() => {
    if (filterFields.value.length > 8) {
      // 如果筛字段超过8个，默认折叠
      isFilterCollapsed.value = true;
    }
  });
});

// 定义emit
const emit = defineEmits(["customAction"]);

// 添加这一行来定义 formRef
const formRef = ref(null);

// 添加这个方法来计算每个字段的 col span
const getColSpan = (field) => {
  if (field.type === "date" || field.type === "datetime") {
    return 12; // 日期和日期时间类型占据整行
  } else {
    return 6; // 其他类型占据半行
  }
};

const detailDialogVisible = ref(false);
const detailData = ref({});

const getFieldAlias = (key) => {
  const field = props.config.fields.find((f) => f.name === key);
  return field ? field.alias || field.name : key;
};

const formatDetailValue = (key, value) => {
  const field = props.config.fields.find((f) => f.name === key);
  if (!field) return value;

  switch (field.type) {
    case "dict":
      return getDictLabel(field, value);
    case "date":
    case "datetime":
      return formatDate(
        value,
        field.type === "date" ? "YYYY-MM-DD" : "YYYY-MM-DD HH:mm:ss"
      );
    case "link":
      return `<a href="${value}" target="_blank">${field.alias || "链接"}</a>`;
    default:
      return value;
  }
};

const jsonRenderer = ref(null);

const handleDetailDialogClose = () => {
  if (jsonRenderer.value && jsonRenderer.value.stopAudio) {
    jsonRenderer.value.stopAudio();
  }
  // 如果有自定义的详情组件，也可以在这里添加停止音频的逻辑
  window.dispatchEvent(new Event("stopAudio"));
};
</script>

<style scoped>
.standard-crud {
  padding: 0px;
  display: flex;
  flex-direction: column;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 这将在按钮之间添加间距 */
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-form {
  transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
  max-height: 1000px;
  opacity: 1;
  overflow: hidden;
}

.filter-form.is-collapsed {
  max-height: 0;
  opacity: 0;
}

.filter-content {
  margin-bottom: 20px;
}

.filter-buttons {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 10px;
}

.table-container {
  position: relative;
  margin-bottom: 60px; /* 为分页器留出空间 */
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
