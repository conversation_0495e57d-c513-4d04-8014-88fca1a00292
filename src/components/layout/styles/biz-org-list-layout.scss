/* 业务机构列表布局组件样式 */

.biz-org-list-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 主卡片样式
  .main-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    :deep(.n-card__content) {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }

    // 网格容器样式
    :deep(.n-grid) {
      height: 100%;
      min-height: 0;
    }

    // 左侧机构列表容器
    :deep(.n-grid-item:first-child) {
      // height: calc(100vh - 120px); // 减去页面边距和卡片头部
      min-height: 0;
      overflow: hidden;
    }
  }

  // 数据列表卡片样式
  .data-list-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    // 不设置 margin-bottom，让分页工具栏绝对定位

    :deep(.n-card__content) {
      padding: 16px;
      padding-bottom: 60px; // 为分页工具栏预留空间
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: var(--n-text-color-disabled);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
  }
}