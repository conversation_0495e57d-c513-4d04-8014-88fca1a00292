<template>
  <div class="org-tree-settings-layout">
    <n-card title="" class="main-card">
      <n-grid :cols="12" :x-gap="24">
        <!-- 左侧组织机构树 -->
        <n-grid-item :span="treeSpan">
          <organization-tree
            ref="orgTreeRef"
            :show-actions="showTreeActions"
            @select="handleOrgSelect"
            @action="handleOrgAction"
          />
        </n-grid-item>

        <!-- 右侧数据列表 -->
        <n-grid-item :span="12 - treeSpan">
          <n-card :title="dataListTitle" class="data-list-card">
            <template #header-extra>
              <n-space>
                <n-input v-model:value="searchParams.keywords" :placeholder="searchPlaceholder" clearable style="width: 300px;"
                  @keydown.enter="searchData">
                  <template #prefix>
                    <n-icon>
                      <SearchOutline />
                    </n-icon>
                  </template>
                </n-input>
                <n-button @click="searchData" type="primary">
                  <template #icon>
                    <n-icon>
                      <SearchOutline />
                    </n-icon>
                  </template>
                  搜索
                </n-button>
                <n-button @click="refreshData" secondary>
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
                <n-button @click="handleAddData" type="info" :disabled="isEditing">
                  <template #icon>
                    <n-icon>
                      <AddCircleOutline />
                    </n-icon>
                  </template>
                  新增
                </n-button>
                <slot name="extra-buttons"></slot>
              </n-space>
            </template>
            <div class="data-table-container">
              <n-data-table
                ref="tableRef"
                :columns="mergedColumns"
                :data="dataList"
                :loading="loading"
                :row-key="typeof rowKey === 'function' ? rowKey : (row) => row[rowKey]"
                :scroll-x="scrollX"
                :max-height="tableMaxHeight"
                :sticky="true"
                :pagination="false" />
            </div>
            <!-- 分页组件单独放置，固定在底部 -->
            <div class="pagination-container">
              <n-pagination
                v-model:page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :item-count="pagination.itemCount"
                :page-sizes="pagination.pageSizes"
                :show-size-picker="pagination.showSizePicker"
                @update:page="handlePageChange"
                @update:page-size="handlePageSizeChange" />
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, h, reactive, computed, watch } from 'vue'
import {
  NSpace, NCard, NGrid, NGridItem, NDataTable, NButton,
  NInput, NIcon, NSelect, useDialog, NPagination
} from 'naive-ui'
import {
  AddCircleOutline, SearchOutline, RefreshOutline,
  CheckmarkCircleOutline, CloseCircleOutline
} from '@vicons/ionicons5'
import { Edit, TrashCan } from '@vicons/carbon'
import OrganizationTree from '@/components/common/OrganizationTree.vue'
import messages from '@/utils/messages'

// Props 定义
const props = defineProps({
  // 基本配置
  title: { type: String, default: '数据列表' },
  searchPlaceholder: { type: String, default: '请输入关键词搜索' },
  treeSpan: { type: Number, default: 4 },
  showTreeActions: { type: Boolean, default: true },

  // 表格配置
  columns: { type: Array, required: true },
  rowKey: { type: [String, Function], default: 'id' },
  scrollX: { type: Number, default: 1200 },

  // API 配置
  apiService: { type: Object, required: true },
  defaultParams: { type: Object, default: () => ({}) },
  orgIdField: { type: String, default: 'orgId' },

  // 新增行默认值
  defaultNewRow: { type: Object, default: () => ({}) }
})

// Emits 定义
const emit = defineEmits([
  'select-org',
  'add-data',
  'edit-data',
  'save-data',
  'cancel-edit',
  'delete-data',
  'refresh-data'
])

// 状态变量
const dataList = ref([])
const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

const loading = ref(false)
const tableRef = ref(null)
const orgTreeRef = ref(null)
const dialog = useDialog()
const editableRowKeys = ref([]) // 当前可编辑的行的key集合
const isEditing = computed(() => editableRowKeys.value.length > 0) // 是否有正在编辑的行
const originalRowData = ref({}) // 存储编辑前的原始行数据，用于取消编辑时恢复

// 搜索参数
const searchParams = reactive({
  ...props.defaultParams,
  keywords: '',
  page: 1,
  size: 20
})

// 计算属性
const dataListTitle = computed(() => {
  return props.title
})

// 表格最大高度计算属性
const tableMaxHeight = computed(() => {
  return 'calc(90vh - 220px)'
})

// 合并操作列
const mergedColumns = computed(() => {
  // 检查是否已经有操作列
  const hasActionsColumn = props.columns.some(col => col.key === 'actions')

  if (hasActionsColumn) {
    return props.columns
  }

  // 添加默认的操作列
  return [...props.columns, {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right',
    render: (row, index) => {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row[props.rowKey])

      if (isEditing) {
        return h(NSpace, { align: 'center' }, {
          default: () => [
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'success',
              onClick: () => saveRow(row),
              style: 'color: #18a058; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
            h(NButton, {
              quaternary: true,
              circle: true,
              size: 'small',
              type: 'error',
              onClick: () => cancelEdit(row),
              style: 'color: #d03050; font-size: 18px;'
            }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
          ]
        })
      }

      return h(NSpace, { align: 'center' }, {
        default: () => [
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => editRow(row)
          }, { default: () => h(NIcon, { component: Edit }) }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => deleteRow(row, index)
          }, { default: () => h(NIcon, { component: TrashCan }) })
        ]
      })
    }
  }]
})

onMounted(async () => {
  // 组件挂载后，机构树组件会自动加载数据并触发select事件
  console.log('OrgTreeSettingsLayout mounted')

  // 不需要额外调用fetchData，因为OrganizationTree组件的onMounted已经调用了fetchDepartments
  // 并且在fetchDepartments中会触发select事件，该事件会被OrgTreeSettingsLayout捕获并调用handleOrgSelect
  // handleOrgSelect函数会调用fetchData，所以不需要在这里再次调用
})

// 处理机构选择
async function handleOrgSelect(orgId) {
  console.log('Organization selected:', orgId)
  emit('select-org', orgId)

  // 检查orgId是否有效
  if (!orgId) {
    console.warn('No organization ID provided')
    return
  }

  try {
    // 获取数据
    await fetchData(orgId)
  } catch (error) {
    console.error('Error in handleOrgSelect:', error)
    messages.error('处理机构选择时发生错误')
  }
}

// 处理机构操作
async function handleOrgAction({ key, nodeKey }) {
  if (key === 'add' && !isEditing.value) {
    await addNewRow(nodeKey)
  }
}

// 获取数据
async function fetchData(orgId) {
  loading.value = true
  try {
    // 更新搜索参数
    searchParams[props.orgIdField] = orgId
    searchParams.page = pagination.value.page
    searchParams.size = pagination.value.pageSize

    console.log('Fetching data with params:', searchParams)

    // 调用API - 修复API调用方式
    let response
    if (typeof props.apiService.getList === 'function') {
      response = await props.apiService.getList(searchParams)
    } else if (typeof props.apiService.getGiftStockList === 'function') {
      // 兼容赠品库存API
      response = await props.apiService.getGiftStockList(searchParams)
    } else {
      // 尝试通用的获取列表方法
      const getListMethod = Object.keys(props.apiService).find(key =>
        key.startsWith('get') && key.endsWith('List') && typeof props.apiService[key] === 'function'
      )

      if (getListMethod) {
        response = await props.apiService[getListMethod](searchParams)
      } else {
        throw new Error('API服务对象没有提供有效的获取列表方法')
      }
    }

    // 处理返回的数据结构
    if (response && response.code === 200 && response.data) {
      dataList.value = response.data.list || []
      pagination.value.itemCount = response.data.total || 0
      pagination.value.page = response.data.pageNum || 1
      pagination.value.pageCount = response.data.pages || 1
    } else {
      dataList.value = []
      pagination.value.itemCount = 0
    }

    emit('refresh-data', dataList.value)
  } catch (error) {
    console.error('Failed to fetch data:', error)
    messages.error('获取数据失败')
    dataList.value = []
  } finally {
    loading.value = false
  }
}

// 分页处理
function handlePageChange(page) {
  pagination.value.page = page
  searchParams.page = page
  const currentOrgId = orgTreeRef.value.getCurrentSelectedOrgId()
  fetchData(currentOrgId)
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  searchParams.page = 1
  searchParams.size = pageSize
  const currentOrgId = orgTreeRef.value.getCurrentSelectedOrgId()
  fetchData(currentOrgId)
}

// 添加新行
async function handleAddData() {
  const currentOrgId = orgTreeRef.value.getCurrentSelectedOrgId()
  await addNewRow(currentOrgId)
}

// 添加新行的通用方法
async function addNewRow(orgId) {
  try {
    // 创建一个新行对象，但不包含id
    const newRow = {
      [props.orgIdField]: orgId,
      isNew: true, // 标记为新行
      isEditing: true, // 标记为编辑状态，用于列渲染
      ...props.defaultNewRow
    }

    // 调用API创建新行并获取ID
    let createdRow = null

    // 尝试调用API创建新行
    if (typeof props.apiService.createEmpty === 'function') {
      // 如果有专门的创建空行方法
      const response = await props.apiService.createEmpty({ [props.orgIdField]: orgId })
      if (response && response.code === 200 && response.data) {
        createdRow = response.data
      }
    } else {
      // 使用默认行数据，但不立即保存到服务器
      createdRow = { ...newRow }

      // 如果没有id，生成一个临时id
      if (!createdRow[props.rowKey]) {
        createdRow[props.rowKey] = `temp_${Date.now()}`
      }
    }

    if (createdRow) {
      // 确保设置了编辑状态标记
      createdRow.isNew = true
      createdRow.isEditing = true

      // 添加到数据列表的开头
      dataList.value.unshift(createdRow)

      // 设置为编辑状态
      editableRowKeys.value.push(createdRow[props.rowKey])

      // 滚动到顶部并聚焦到新行
      scrollToFirstRow()

      // 触发事件
      emit('add-data', createdRow)
    } else {
      throw new Error('创建新行失败')
    }
  } catch (error) {
    console.error('Failed to add new row:', error)
    messages.error('添加新行失败')
  }
}

// 滚动到表格顶部并高亮最新行
const scrollToFirstRow = () => {
  setTimeout(() => {
    if (tableRef.value) {
      // 获取表格主体元素
      const tableBodyEl = tableRef.value.$el.querySelector('.n-data-table-base-table-body')
      if (tableBodyEl) {
        // 滚动到顶部
        tableBodyEl.scrollTop = 0

        // 尝试找到新添加的行并添加高亮效果
        const firstRowEl = tableBodyEl.querySelector('.n-data-table-tr:first-child')
        if (firstRowEl) {
          // 添加高亮类
          firstRowEl.classList.add('new-row-highlight')
          // 3秒后移除高亮效果
          setTimeout(() => {
            firstRowEl.classList.remove('new-row-highlight')
          }, 3000)
        }
      }
    }
  }, 100)
}

// 编辑行
function editRow(row) {
  const rowKey = row[props.rowKey]

  // 在编辑前保存原始数据的深拷贝，用于取消编辑时恢复
  originalRowData.value[rowKey] = JSON.parse(JSON.stringify(row))

  // 设置为编辑状态
  editableRowKeys.value.push(rowKey)
  // 标记为编辑状态，用于列渲染
  row.isEditing = true
  emit('edit-data', row)
}

// 取消编辑
function cancelEdit(row) {
  const rowKey = row[props.rowKey]

  // 从可编辑行集合中移除
  editableRowKeys.value = editableRowKeys.value.filter(id => id !== rowKey)

  // 移除编辑状态标记
  row.isEditing = false

  // 如果是新添加的行，则从数据中移除
  if (row.isNew) {
    dataList.value = dataList.value.filter(item => item[props.rowKey] !== rowKey)
  } else {
    // 如果是编辑现有行，恢复原始数据
    const originalData = originalRowData.value[rowKey]
    if (originalData) {
      // 找到当前行在数据列表中的索引
      const index = dataList.value.findIndex(item => item[props.rowKey] === rowKey)
      if (index !== -1) {
        // 使用原始数据替换当前行
        dataList.value[index] = { ...originalData }
      }
      // 删除已使用的原始数据
      delete originalRowData.value[rowKey]
    }
  }

  emit('cancel-edit', row)
}

// 保存行
async function saveRow(row) {
  try {
    // 检查是否是临时ID（以temp_开头）
    const isTemporaryId = typeof row[props.rowKey] === 'string' && row[props.rowKey].startsWith('temp_')
    const rowKey = row[props.rowKey]
    let response = null

    if (row.isNew || isTemporaryId) {
      // 创建新行数据的副本，移除id字段
      const newRowData = { ...row }

      // 删除id字段，避免在新增时传入id
      if (newRowData[props.rowKey]) {
        delete newRowData[props.rowKey]
      }

      // 创建新数据 - 修复API调用方式
      if (typeof props.apiService.create === 'function') {
        response = await props.apiService.create(newRowData)
      } else if (typeof props.apiService.createGiftStock === 'function') {
        // 兼容赠品库存API
        response = await props.apiService.createGiftStock(newRowData)
      } else {
        // 尝试通用的创建方法
        const createMethod = Object.keys(props.apiService).find(key =>
          key.startsWith('create') && typeof props.apiService[key] === 'function'
        )

        if (createMethod) {
          response = await props.apiService[createMethod](newRowData)
        } else {
          throw new Error('API服务对象没有提供有效的创建方法')
        }
      }

      // 检查响应状态
      if (response && response.code === 200) {
        messages.success('创建成功')

        // 清除原始数据缓存
        if (originalRowData.value[rowKey]) {
          delete originalRowData.value[rowKey]
        }

        // 必须刷新页面以获取最新的ID
        const currentOrgId = orgTreeRef.value.getCurrentSelectedOrgId()
        await fetchData(currentOrgId)
      } else {
        throw new Error(response?.message || '创建失败')
      }
    } else {
      // 更新现有数据 - 修复API调用方式
      if (typeof props.apiService.update === 'function') {
        response = await props.apiService.update(row)
      } else if (typeof props.apiService.updateGiftStock === 'function') {
        // 兼容赠品库存API
        response = await props.apiService.updateGiftStock(row)
      } else {
        // 尝试通用的更新方法
        const updateMethod = Object.keys(props.apiService).find(key =>
          key.startsWith('update') && typeof props.apiService[key] === 'function'
        )

        if (updateMethod) {
          response = await props.apiService[updateMethod](row)
        } else {
          throw new Error('API服务对象没有提供有效的更新方法')
        }
      }

      // 检查响应状态
      if (response && response.code === 200) {
        messages.success('更新成功')

        // 清除原始数据缓存
        if (originalRowData.value[rowKey]) {
          delete originalRowData.value[rowKey]
        }

        // 移除编辑状态标记
        row.isEditing = false

        // 从可编辑行集合中移除
        editableRowKeys.value = editableRowKeys.value.filter(id => id !== rowKey)

        // 更新本地数据，不需要重新请求服务器
        const index = dataList.value.findIndex(item => item[props.rowKey] === rowKey)
        if (index !== -1) {
          // 如果响应中包含更新后的数据，使用响应数据
          if (response.data) {
            dataList.value[index] = { ...response.data }
          } else {
            // 否则使用当前编辑的数据
            dataList.value[index] = { ...row, isEditing: false }
          }
        }
      } else {
        throw new Error(response?.message || '更新失败')
      }
    }

    emit('save-data', row)
  } catch (error) {
    console.error('Failed to save data:', error)
    messages.error(error.message || '保存失败')
  }
}

// 删除行
function deleteRow(row, index) {
  dialog.warning({
    title: '删除确认',
    content: `确定要删除此项吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        if (!row.isNew) {
          // 如果不是新行，则调用API删除 - 修复API调用方式
          if (typeof props.apiService.delete === 'function') {
            await props.apiService.delete(row[props.rowKey])
          } else if (typeof props.apiService.deleteGiftStock === 'function') {
            // 兼容赠品库存API
            await props.apiService.deleteGiftStock(row[props.rowKey])
          } else {
            // 尝试通用的删除方法
            const deleteMethod = Object.keys(props.apiService).find(key =>
              key.startsWith('delete') && typeof props.apiService[key] === 'function'
            )

            if (deleteMethod) {
              await props.apiService[deleteMethod](row[props.rowKey])
            } else {
              throw new Error('API服务对象没有提供有效的删除方法')
            }
          }
        }

        // 从数据中移除
        dataList.value.splice(index, 1)
        messages.success('删除成功')

        emit('delete-data', row)
      } catch (error) {
        console.error('Failed to delete data:', error)
        messages.error('删除失败')
      }
    }
  })
}

// 刷新数据
function refreshData() {
  pagination.value.page = 1
  searchParams.page = 1
  const currentOrgId = orgTreeRef.value.getCurrentSelectedOrgId()
  fetchData(currentOrgId)
}

// 搜索数据
function searchData() {
  pagination.value.page = 1
  searchParams.page = 1
  const currentOrgId = orgTreeRef.value.getCurrentSelectedOrgId()
  fetchData(currentOrgId)
}
</script>

<style lang="scss">
@use './styles/org-tree-settings-layout';

/* 表头居中对齐 */
:deep(.n-data-table-th) {
  text-align: center !important;
}

:deep(.n-data-table-th__title) {
  justify-content: center;
}

/* 表格数据居左对齐 */
:deep(.n-data-table-td) {
  text-align: left !important;
}

/* 操作列居中对齐 */
:deep(.n-data-table-td--last) {
  text-align: center !important;
}

:deep(.n-data-table-td--last .n-space) {
  justify-content: center;
}
</style>
