<template>
  <div class="biz-org-list-layout">
    <n-card title="" class="main-card">
      <n-grid :cols="16" :x-gap="24">
        <!-- 左侧业务机构列表 -->
        <n-grid-item :span="listSpan">
          <biz-org-list-selector
            ref="orgListRef"
            :business-permission="businessPermission"
            @select-org="handleOrgSelect"
          />
        </n-grid-item>

        <!-- 右侧数据列表 -->
        <n-grid-item :span="16 - listSpan">
          <div class="data-list-container">
            <n-card :title="dataListTitle" class="data-list-card">
              <template #header-extra>
                <n-space>
                  <n-input
                    v-model:value="searchParams.keywords"
                    :placeholder="searchPlaceholder"
                    clearable
                    style="width: 300px"
                    @keydown.enter="searchData"
                  >
                    <template #prefix>
                      <n-icon>
                        <SearchOutline />
                      </n-icon>
                    </template>
                  </n-input>
                  <n-button @click="searchData" type="primary">
                    <template #icon>
                      <n-icon>
                        <SearchOutline />
                      </n-icon>
                    </template>
                    搜索
                  </n-button>
                  <n-button @click="refreshData" secondary>
                    <template #icon>
                      <n-icon>
                        <RefreshOutline />
                      </n-icon>
                    </template>
                    刷新
                  </n-button>
                  <n-button
                    @click="handleAddData"
                    type="info"
                    :disabled="isEditing"
                  >
                    <template #icon>
                      <n-icon>
                        <AddCircleOutline />
                      </n-icon>
                    </template>
                    新增
                  </n-button>
                  <slot name="extra-buttons"></slot>
                </n-space>
              </template>
              <!-- 表格容器 -->
              <div class="table-wrapper">
                <n-data-table
                  ref="tableRef"
                  :columns="mergedColumns"
                  :data="dataList"
                  :loading="loading"
                  :row-key="(row) => row.id"
                  :max-height="virtualScrollHeight"
                  :scroll-x="scrollX"
                  :pagination="pagination"
                  :min-row-height="48"
                  :header-height="48"
                  :height-for-row="() => 48"
                  striped
                  remote
                  size="large"
                  virtual-scroll
                  @update:page="handlePageChange"
                  @update:page-size="handlePageSizeChange"
                />
              </div>
            </n-card>
          </div>
        </n-grid-item>
      </n-grid>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, h, reactive, computed } from "vue";
import {
  NSpace,
  NCard,
  NGrid,
  NGridItem,
  NDataTable,
  NButton,
  NInput,
  NIcon,
  useDialog,
} from "naive-ui";
import {
  AddCircleOutline,
  SearchOutline,
  RefreshOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
} from "@vicons/ionicons5";
import { Edit, TrashCan } from "@vicons/carbon";
import BizOrgListSelector from "@/components/bizOrg/BizOrgListSelector.vue";
import messages from "@/utils/messages";

// Props 定义
const props = defineProps({
  // 基本配置
  title: { type: String, default: "数据列表" },
  searchPlaceholder: { type: String, default: "请输入关键词搜索" },
  listSpan: { type: Number, default: 4 },

  // 表格配置
  columns: { type: Array, required: true },
  rowKey: { type: [String, Function], default: "id" },
  scrollX: { type: Number, default: 1130 },

  // API 配置
  apiService: { type: Object, required: true },
  defaultParams: { type: Object, default: () => ({}) },
  orgIdField: { type: String, default: "orgId" },

  // 业务机构查询条件
  businessPermission: { type: String, default: "" },

  // 新增行默认值
  defaultNewRow: { type: Object, default: () => ({}) },

  // 外部编辑状态控制
  externalEditing: { type: Boolean, default: false },
});

// Emits 定义
const emit = defineEmits([
  "select-org",
  "add-data",
  "edit-data",
  "save-data",
  "cancel-edit",
  "delete-data",
  "refresh-data",
]);

// 状态变量
const dataList = ref([]);
const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [20, 50, 100],
});

const loading = ref(false);
const tableRef = ref(null);
const orgListRef = ref(null);
const dialog = useDialog();
const editableRowKeys = ref([]); // 当前可编辑的行的key集合
const isEditing = computed(() => editableRowKeys.value.length > 0); // 是否有正在编辑的行
const originalRowData = ref({}); // 存储编辑前的原始行数据，用于取消编辑时恢复

// 搜索参数
const searchParams = reactive({
  ...props.defaultParams,
  keywords: "",
  page: 1,
  size: pagination.value.pageSize, // 与分页配置保持同步
});

// 计算属性
const dataListTitle = computed(() => {
  return props.title;
});

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight);

// 计算虚拟滚动的高度 - 这是关键配置
const virtualScrollHeight = computed(() => {
  // 固定区域高度：
  // - 页面顶部边距: 16px
  // - 主卡片内边距: 32px (上下各16px)
  // - 卡片头部(搜索栏等): 80px
  // - 内置分页组件高度: 60px
  // - 页面底部边距: 16px
  // - 其他边距和边框: 16px
  const fixedHeight = 260;

  // 虚拟滚动区域的可用高度
  // 确保最小高度为600px，以便显示足够的数据行
  const scrollHeight = Math.max(600, windowHeight.value - fixedHeight);
  return scrollHeight;
});

// 合并操作列
const mergedColumns = computed(() => {
  // 检查是否已经有操作列
  const hasActionsColumn = props.columns.some((col) => col.key === "actions");

  if (hasActionsColumn) {
    return props.columns;
  }

  // 添加默认的操作列
  return [
    ...props.columns,
    {
      title: "操作",
      key: "actions",
      width: 100,
      fixed: "right",
      render: (row, index) => {
        // 判断当前行是否处于编辑状态
        const isRowEditing = editableRowKeys.value.includes(row[props.rowKey]);

        if (isRowEditing) {
          return h(
            NSpace,
            { align: "center" },
            {
              default: () => [
                h(
                  NButton,
                  {
                    quaternary: true,
                    circle: true,
                    size: "small",
                    type: "success",
                    onClick: () => saveRow(row),
                    style: "color: #18a058; font-size: 18px;",
                  },
                  {
                    default: () =>
                      h(NIcon, { component: CheckmarkCircleOutline }),
                  }
                ),
                h(
                  NButton,
                  {
                    quaternary: true,
                    circle: true,
                    size: "small",
                    type: "error",
                    onClick: () => cancelEdit(row),
                    style: "color: #d03050; font-size: 18px;",
                  },
                  { default: () => h(NIcon, { component: CloseCircleOutline }) }
                ),
              ],
            }
          );
        }

        return h(
          NSpace,
          { align: "center" },
          {
            default: () => [
              h(
                NButton,
                {
                  quaternary: true,
                  circle: true,
                  size: "small",
                  disabled: editableRowKeys.value.length > 0, // 当有任何行在编辑时禁用其他行的编辑按钮
                  onClick: () => editRow(row),
                },
                { default: () => h(NIcon, { component: Edit }) }
              ),
              h(
                NButton,
                {
                  quaternary: true,
                  circle: true,
                  size: "small",
                  disabled: editableRowKeys.value.length > 0, // 当有任何行在编辑时禁用删除按钮
                  onClick: () => deleteRow(row, index),
                },
                { default: () => h(NIcon, { component: TrashCan }) }
              ),
            ],
          }
        );
      },
    },
  ];
});

// 窗口大小变化处理函数
const handleResize = () => {
  windowHeight.value = window.innerHeight;
};

onMounted(async () => {
  // 组件挂载后，机构列表组件会自动加载数据并触发select事件

  // 添加窗口大小变化监听器
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener("resize", handleResize);
});

// 处理机构选择
async function handleOrgSelect(orgId, orgData) {
  emit("select-org", orgId, orgData);

  // 检查orgId是否有效
  if (!orgId) {
    console.warn("No organization ID provided");
    return;
  }

  try {
    // 获取数据
    await fetchData(orgId);
  } catch (error) {
    console.error("Error in handleOrgSelect:", error);
    messages.error("处理机构选择时发生错误");
  }
}

// 获取数据
async function fetchData(orgId) {
  loading.value = true;
  try {
    // 更新搜索参数
    searchParams[props.orgIdField] = orgId;
    searchParams.page = pagination.value.page;
    searchParams.size = pagination.value.pageSize;

    console.log("fetchData 参数:", {
      orgId,
      page: searchParams.page,
      size: searchParams.size,
      searchParams: { ...searchParams },
    });

    // 调用API - 修复API调用方式
    let response;
    if (typeof props.apiService.getList === "function") {
      response = await props.apiService.getList(searchParams);
    } else if (typeof props.apiService.getGiftStockList === "function") {
      // 兼容赠品库存API
      response = await props.apiService.getGiftStockList(searchParams);
    } else {
      // 尝试通用的获取列表方法
      const getListMethod = Object.keys(props.apiService).find(
        (key) =>
          key.startsWith("get") &&
          key.endsWith("List") &&
          typeof props.apiService[key] === "function"
      );

      if (getListMethod) {
        response = await props.apiService[getListMethod](searchParams);
      } else {
        throw new Error("API服务对象没有提供有效的获取列表方法");
      }
    }

    // 处理返回的数据结构
    if (response && response.code === 200 && response.data) {
      dataList.value = response.data.list || [];
      pagination.value.itemCount = response.data.total || 0;
      pagination.value.page = response.data.pageNum || 1;
      pagination.value.pageCount = response.data.pages || 1;

      console.log("Data loaded successfully:", {
        dataCount: dataList.value.length,
        total: pagination.value.itemCount,
        data: dataList.value,
      });
    } else {
      dataList.value = [];
      pagination.value.itemCount = 0;
    }

    emit("refresh-data", dataList.value);
  } catch (error) {
    console.error("Failed to fetch data:", error);
    messages.error("获取数据失败");
    dataList.value = [];
  } finally {
    loading.value = false;
  }
}

// 分页处理
function handlePageChange(page) {
  console.log("分页变化:", page, "当前页:", pagination.value.page);
  pagination.value.page = page;
  searchParams.page = page;
  const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
  console.log("当前机构ID:", currentOrgId);
  if (currentOrgId) {
    fetchData(currentOrgId);
  } else {
    console.warn("没有选中的机构，无法获取数据");
  }
}

function handlePageSizeChange(pageSize) {
  console.log("页面大小变化:", pageSize);
  pagination.value.pageSize = pageSize;
  pagination.value.page = 1;
  searchParams.page = 1;
  searchParams.size = pageSize;
  const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
  if (currentOrgId) {
    fetchData(currentOrgId);
  } else {
    console.warn("没有选中的机构，无法获取数据");
  }
}

// 添加新行
async function handleAddData() {
  const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
  await addNewRow(currentOrgId);
}

// 添加新行的具体实现
async function addNewRow(orgId) {
  if (!orgId) {
    messages.warning("请先选择一个机构");
    return;
  }

  // 检查是否已有行在编辑状态
  if (editableRowKeys.value.length > 0) {
    messages.warning("请先完成当前行的编辑");
    return;
  }

  // 创建新行数据
  const newRow = {
    ...props.defaultNewRow,
    [props.orgIdField]: orgId,
    id: `temp_${Date.now()}`, // 临时ID
    isNew: true,
  };

  // 添加到数据列表的开头
  dataList.value.unshift(newRow);

  // 设置为唯一的编辑行
  editableRowKeys.value = [newRow.id];

  emit("add-data", newRow);
}

// 编辑行
function editRow(row) {
  const rowKey = row[props.rowKey];

  // 检查是否已有其他行在编辑状态
  if (editableRowKeys.value.length > 0) {
    console.warn("Another row is currently being edited");
    return false;
  }

  // 保存原始数据
  originalRowData.value[rowKey] = { ...row };

  // 设置为唯一的编辑行
  editableRowKeys.value = [rowKey];

  emit("edit-data", row);
  return true;
}

// 取消编辑
function cancelEdit(row) {
  const rowKey = row[props.rowKey];

  // 恢复原始数据
  if (originalRowData.value[rowKey]) {
    Object.assign(row, originalRowData.value[rowKey]);
    delete originalRowData.value[rowKey];
  }

  // 如果是新行，从列表中移除
  if (row.isNew) {
    const index = dataList.value.findIndex(
      (item) => item[props.rowKey] === rowKey
    );
    if (index !== -1) {
      dataList.value.splice(index, 1);
    }
  }

  // 从可编辑行集合中移除
  editableRowKeys.value = editableRowKeys.value.filter((id) => id !== rowKey);

  emit("cancel-edit", row);
}

// 保存行
async function saveRow(row) {
  try {
    const rowKey = row[props.rowKey];
    let response;

    // 检查是否是临时ID（以temp_开头）
    const isTemporaryId =
      typeof row.id === "string" && row.id.startsWith("temp_");

    if (row.isNew || isTemporaryId) {
      // 创建新行数据的副本，移除id字段
      const newRowData = { ...row };

      // 删除id字段，避免在新增时传入id
      if (newRowData[props.rowKey]) {
        delete newRowData[props.rowKey];
      }

      // 创建新数据 - 修复API调用方式
      if (typeof props.apiService.create === "function") {
        response = await props.apiService.create(newRowData);
      } else if (typeof props.apiService.createGiftStock === "function") {
        // 兼容赠品库存API
        response = await props.apiService.createGiftStock(newRowData);
      } else {
        // 尝试通用的创建方法
        const createMethod = Object.keys(props.apiService).find(
          (key) =>
            key.startsWith("create") &&
            typeof props.apiService[key] === "function"
        );

        if (createMethod) {
          response = await props.apiService[createMethod](newRowData);
        } else {
          throw new Error("API服务对象没有提供有效的创建方法");
        }
      }

      if (response && response.code === 200) {
        messages.success("创建成功");

        // 清除原始数据缓存
        if (originalRowData.value[rowKey]) {
          delete originalRowData.value[rowKey];
        }

        // 移除编辑状态标记
        row.isEditing = false;

        // 清空所有编辑状态，因为刷新数据后ID会改变
        editableRowKeys.value = [];

        // 刷新数据列表
        const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
        await fetchData(currentOrgId);
      } else {
        throw new Error(response?.message || "创建失败");
      }
    } else {
      // 更新现有数据 - 修复API调用方式
      if (typeof props.apiService.update === "function") {
        response = await props.apiService.update(row);
      } else if (typeof props.apiService.updateGiftStock === "function") {
        // 兼容赠品库存API
        response = await props.apiService.updateGiftStock(row);
      } else {
        // 尝试通用的更新方法
        const updateMethod = Object.keys(props.apiService).find(
          (key) =>
            key.startsWith("update") &&
            typeof props.apiService[key] === "function"
        );

        if (updateMethod) {
          response = await props.apiService[updateMethod](row);
        } else {
          throw new Error("API服务对象没有提供有效的更新方法");
        }
      }

      // 检查响应状态
      if (response && response.code === 200) {
        messages.success("更新成功");

        // 清除原始数据缓存
        if (originalRowData.value[rowKey]) {
          delete originalRowData.value[rowKey];
        }

        // 移除编辑状态标记
        row.isEditing = false;

        // 从可编辑行集合中移除
        editableRowKeys.value = editableRowKeys.value.filter(
          (id) => id !== rowKey
        );

        // 刷新数据列表以确保数据一致性
        const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
        await fetchData(currentOrgId);
      } else {
        throw new Error(response?.message || "更新失败");
      }
    }

    emit("save-data", row);
  } catch (error) {
    console.error("Failed to save data:", error);
    messages.error(error.message || "保存失败");
  }
}

// 删除行
function deleteRow(row, index) {
  dialog.warning({
    title: "删除确认",
    content: `确定要删除此项吗？此操作不可恢复。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        if (!row.isNew) {
          // 如果不是新行，则调用API删除 - 修复API调用方式
          if (typeof props.apiService.delete === "function") {
            await props.apiService.delete(row[props.rowKey]);
          } else if (typeof props.apiService.deleteGiftStock === "function") {
            // 兼容赠品库存API
            await props.apiService.deleteGiftStock(row[props.rowKey]);
          } else {
            // 尝试通用的删除方法
            const deleteMethod = Object.keys(props.apiService).find(
              (key) =>
                key.startsWith("delete") &&
                typeof props.apiService[key] === "function"
            );

            if (deleteMethod) {
              await props.apiService[deleteMethod](row[props.rowKey]);
            } else {
              throw new Error("API服务对象没有提供有效的删除方法");
            }
          }
        }

        // 从数据中移除
        dataList.value.splice(index, 1);
        messages.success("删除成功");

        emit("delete-data", row);
      } catch (error) {
        console.error("Failed to delete data:", error);
        messages.error("删除失败");
      }
    },
  });
}

// 刷新数据
function refreshData() {
  pagination.value.page = 1;
  searchParams.page = 1;
  const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
  fetchData(currentOrgId);
}

// 搜索数据
function searchData() {
  pagination.value.page = 1;
  searchParams.page = 1;
  const currentOrgId = orgListRef.value.getCurrentSelectedOrgId();
  fetchData(currentOrgId);
}

// 从列表中移除行（用于取消新增行）
function removeRowFromList(row) {
  const rowKey = row[props.rowKey];
  const index = dataList.value.findIndex(
    (item) => item[props.rowKey] === rowKey
  );

  if (index !== -1) {
    dataList.value.splice(index, 1);
  }

  // 从可编辑行集合中移除
  editableRowKeys.value = editableRowKeys.value.filter((id) => id !== rowKey);

  // 清除原始数据缓存
  if (originalRowData.value[rowKey]) {
    delete originalRowData.value[rowKey];
  }
}

// 清理编辑状态（用于外部调用）
function clearEditingState(rowKey = null) {
  if (rowKey) {
    // 清理指定行的编辑状态
    editableRowKeys.value = editableRowKeys.value.filter((id) => id !== rowKey);
    if (originalRowData.value[rowKey]) {
      delete originalRowData.value[rowKey];
    }
  } else {
    // 清理所有编辑状态
    editableRowKeys.value = [];
    originalRowData.value = {};
  }
}

// 暴露方法给父组件
defineExpose({
  saveRow,
  cancelEdit,
  editRow,
  deleteRow,
  refreshData,
  searchData,
  fetchData,
  dataList,
  removeRowFromList,
  clearEditingState,
});
</script>

<style lang="scss">
@use "./styles/biz-org-list-layout";
</style>
