<template>
  <n-layout position="absolute" class="page-layout">
    <!-- 顶部工具栏 -->
    <n-layout-header class="toolbar">
      <n-button @click="handleBack" class="back-button">
        <template #icon>
          <n-icon>
            <ArrowBack />
          </n-icon>
        </template>
        返回
      </n-button>
      <div class="page-title">{{ title }}</div>
    </n-layout-header>

    <!-- 下方内容区域 -->
    <n-layout has-sider position="absolute" style="top: 48px; bottom: 0">
      <!-- 左侧菜单栏 -->
      <n-layout-sider
        :collapsed="isCollapsed"
        :collapsed-width="64"
        :width="240"
        show-trigger
        @collapse="isCollapsed = true"
        @expand="isCollapsed = false"
      >
        <n-spin :show="loading">
          <n-menu
            v-if="menuOptions.length > 0"
            v-model:value="activeMenuKey"
            :collapsed="isCollapsed"
            :options="menuOptions"
            :collapsed-width="64"
            :collapsed-icon-size="22"
            :render-label="renderMenuLabel"
            :render-icon="renderMenuIcon"
            :expanded-keys="expandedKeys"
            :default-expanded-keys="expandedKeys"
            :accordion="false"
            @update:value="handleMenuSelect"
            @update:expanded-keys="handleExpandedKeysChange"
          />
          <div v-else class="no-menu-tip">
            {{ isCollapsed ? "" : "暂无子菜单" }}
          </div>
        </n-spin>
      </n-layout-sider>

      <!-- 右侧内容区 -->
      <n-layout-content class="main-content">
        <div class="component-container">
          <!-- 使用key强制组件重新渲染 -->
          <component :is="currentComponent" :key="componentUpdateCounter" />
          <!-- 添加调试信息 -->
          <div v-if="false" class="debug-info">
            当前组件更新计数: {{ componentUpdateCounter }} 当前加载的组件:
            {{ window._lastLoadedComponent }}
          </div>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script setup>
import { ref, onMounted, h, shallowRef, markRaw, watch } from "vue";
import { useRouter } from "vue-router";
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NLayoutHeader,
  NMenu,
  NCard,
  NIcon,
  NButton,
  NSpin,
} from "naive-ui";
import {
  ArrowBack,
  SpeedometerOutline,
  PersonOutline,
  BuildOutline,
  MenuOutline,
  WalletOutline,
  ChatbubbleOutline,
  StarOutline,
  HeadsetOutline,
  SearchOutline,
  SyncOutline,
  BookOutline,
  HelpCircleOutline,
  BriefcaseOutline,
  FilterOutline,
  SettingsSharp,
  CarSportOutline,
  BagHandleOutline,
  ArchiveOutline,
  CashOutline,
  CheckmarkDoneCircleOutline,
  SendSharp,
  DocumentTextOutline,
  CopyOutline,
  AddCircleOutline,
  PeopleOutline,
} from "@vicons/ionicons5";
import { FlowConnection } from "@vicons/carbon";
import NotFoundPage from "@/views/system/NotFoundPage.vue";
import { loadComponent } from "@/utils/routeLoader";

// 定义组件属性
const props = defineProps({
  // 菜单ID，用于构建菜单树
  menuId: {
    type: [Number, String],
    required: true,
  },
  // 页面标题
  title: {
    type: String,
    required: false,
  },
  // 默认选中的菜单key
  jumpTo: {
    type: String,
    default: null,
  },
});

console.log(
  "IndexPageLayout 组件初始化，菜单ID:",
  props.menuId,
  "标题:",
  props.title,
  "默认菜单Key:",
  props.jumpTo
);

const router = useRouter();
const isCollapsed = ref(false);
const currentMenu = ref(null);

// 当前选中的菜单项的key
const activeMenuKey = ref(null);

// 当前展开的菜单项keys
const expandedKeys = ref([]);

// 当前加载的组件
const currentComponent = shallowRef(markRaw(NCard));

// 添加一个计数器，用于强制更新组件
const componentUpdateCounter = ref(0);

// 菜单选项
const menuOptions = ref([]);

// 加载状态
const loading = ref(true);

// 图标映射表
const iconMap = {
  Odometer: SpeedometerOutline,
  UserFilled: PersonOutline,
  Tools: BuildOutline,
  Menu: MenuOutline,
  Money: WalletOutline,
  Message: ChatbubbleOutline,
  StarFilled: StarOutline,
  Service: HeadsetOutline,
  Search: SearchOutline,
  Refresh: SyncOutline,
  Reading: BookOutline,
  HelpFilled: HelpCircleOutline,
  Suitcase: BriefcaseOutline,
  Filter: FilterOutline,
  SettingsSharp: SettingsSharp,
  CarSportOutline: CarSportOutline,
  FlowConnection: FlowConnection,
  BagHandle: BagHandleOutline,
  Archive: ArchiveOutline,
  Cash: CashOutline,
  CheckmarkDoneCircleOutline,
  SendSharp,
  CarSportOutline,
  DocumentTextOutline,
  CopyOutline,
  AddCircleOutline,
  PeopleOutline,
  BagHandleOutline,
  ArchiveOutline,
};

/**
 * 将图标名称转换为图标组件
 * @param {String} iconName - 图标名称
 * @returns {Function|null} 图标渲染函数或null
 */
const getIconComponent = (iconName) => {
  if (!iconName) return null;
  return iconMap[iconName] || HelpCircleOutline;
};

/**
 * 从localStorage获取菜单数据
 * 包含重试机制，确保能够获取到数据
 * @returns {Promise<Array>} 菜单数据数组
 */
const getMenusFromStorage = async () => {
  let retryCount = 0;
  const maxRetries = 5;

  const tryGetMenus = async () => {
    const storedMenus = localStorage.getItem("menus");
    if (!storedMenus) {
      if (retryCount < maxRetries) {
        retryCount++;
        // 延迟200毫秒后重试
        await new Promise((resolve) => setTimeout(resolve, 200));
        return await tryGetMenus();
      } else {
        return null;
      }
    }

    try {
      return JSON.parse(storedMenus);
    } catch (error) {
      console.error("解析菜单数据失败:", error);
      return null;
    }
  };

  return await tryGetMenus();
};

/**
 * 根据菜单ID查找菜单项
 * @param {Array} menus - 菜单数据数组
 * @param {String|Number} menuId - 菜单ID
 * @returns {Object|null} 找到的菜单项或null
 */
const findMenuById = (menus, menuId) => {
  if (!menus || !Array.isArray(menus)) return null;

  // 尝试不同的比较方式
  let menuItem = menus.find((menu) => String(menu.id) === String(menuId));

  if (!menuItem) {
    // 尝试数字比较
    const menuIdNum = Number(menuId);
    menuItem = menus.find((menu) => menu.id === menuIdNum);
  }

  if (!menuItem) {
    // 尝试宽松比较
    menuItem = menus.find((menu) => menu.id == menuId);
  }

  return menuItem;
};

/**
 * 将菜单数据转换为NaiveUI菜单组件所需的格式
 * @param {Array} menus - 菜单数据数组
 * @returns {Array} 格式化后的菜单选项
 */
const formatMenuOptions = (menus) => {
  if (!menus || !Array.isArray(menus)) return [];

  // 添加调试日志

  return menus.map((menu) => {
    const option = {
      key: menu.menuPath || String(menu.id),
      label: menu.menuLabel || menu.name,
      // 不直接设置icon属性，而是在renderMenuIcon函数中处理
      // 保存图标名称，以便在renderMenuIcon中使用
      iconName: menu.menuIcon,
      viewPath: menu.viewPath,
    };

    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      option.children = formatMenuOptions(menu.children);
    } else if (menu.subMenus && menu.subMenus.length > 0) {
      option.children = formatMenuOptions(menu.subMenus);
    }

    return option;
  });
};

/**
 * 在菜单树中查找指定key的菜单项
 * @param {Array} menuItems - 菜单项数组
 * @param {String} key - 要查找的菜单key
 * @param {Array} path - 当前路径，用于记录查找路径
 * @returns {Object|null} 包含找到的菜单项和路径的对象，或null
 */
const findMenuItemByKey = (menuItems, key, path = []) => {
  if (!menuItems || !Array.isArray(menuItems)) return null;

  for (const item of menuItems) {
    // 创建当前路径的副本
    const currentPath = [...path, item.key];

    // 检查当前项是否匹配
    if (item.key === key) {
      return { item, path: currentPath };
    }

    // 如果有子菜单，递归查找
    if (item.children && item.children.length > 0) {
      const result = findMenuItemByKey(item.children, key, currentPath);
      if (result) return result;
    }
  }

  return null;
};

/**
 * 查找第一个有viewPath的菜单项
 * @param {Array} menuItems - 菜单项数组
 * @returns {Object|null} 找到的菜单项或null
 */
const findFirstViewPathMenuItem = (menuItems) => {
  if (!menuItems || !Array.isArray(menuItems)) return null;

  for (const item of menuItems) {
    if (item.viewPath) {
      return item;
    }

    if (item.children && item.children.length > 0) {
      const found = findFirstViewPathMenuItem(item.children);
      if (found) return found;
    }
  }

  return null;
};

/**
 * 更新URL中的jump_to参数
 * @param {String} menuPath - 菜单路径
 */
const updateJumpToParameter = (menuPath) => {
  try {
    // 获取当前路由信息
    const currentRoute = router.currentRoute.value;

    // 构建新的查询参数，保留其他参数，只更新jump_to
    const newQuery = {
      ...currentRoute.query,
      jump_to: menuPath,
    };

    // 使用replace方法更新URL，避免在浏览器历史记录中添加新条目
    router.replace({
      path: currentRoute.path,
      query: newQuery,
    });

    console.log("URL参数已更新，jump_to:", menuPath);
  } catch (error) {
    console.error("更新URL参数失败:", error);
  }
};

/**
 * 处理菜单选择
 * @param {String} key - 选中的菜单key
 */
const handleMenuSelect = (key) => {
  // 更新当前选中的菜单项
  activeMenuKey.value = key;

  // 查找菜单项
  const menuItemResult = findMenuItemByKey(menuOptions.value, key);

  if (!menuItemResult) {
    console.warn("未找到对应的菜单项");
    return;
  }

  const menuItem = menuItemResult.item;

  // 更新URL中的jump_to参数为菜单的menuPath
  updateJumpToParameter(key);

  // 不改变展开状态，保持所有菜单始终展开
  // 如果菜单项有viewPath，加载对应组件
  if (menuItem.viewPath) {
    loadComponentByPath(menuItem.viewPath);
  }
};

/**
 * 处理菜单展开状态变化
 * @param {Array} keys - 展开的菜单keys
 */
const handleExpandedKeysChange = (keys) => {
  // 更新展开的菜单keys，允许用户控制菜单的展开/折叠状态
  expandedKeys.value = keys;
  console.log("菜单展开状态已更新:", keys);
};

/**
 * 根据路径加载组件
 * @param {String} viewPath - 组件的视图路径
 */
const loadComponentByPath = async (viewPath) => {
  // 记录当前加载的组件路径，用于调试
  window._lastLoadedComponent = viewPath;

  try {
    // 预加载所有视图组件
    const modules = import.meta.glob("/src/views/**/*.vue");

    // 使用通用的组件加载工具函数
    await loadComponent(
      viewPath,
      (component) => {
        // 确保组件是新的实例
        if (component) {
          // 增加计数器，强制视图更新
          componentUpdateCounter.value++;

          // 直接设置组件，不使用setTimeout
          currentComponent.value = markRaw(component);
        } else {
          console.error("组件加载成功但为空:", viewPath);
          componentUpdateCounter.value++;
          currentComponent.value = markRaw(NotFoundPage);
        }
      },
      {
        modules,
        fallbackComponent: NotFoundPage,
        // 添加额外的路径映射尝试
        extraPaths: [
          `/src/views/${viewPath}.vue`,
          `/src/views/${viewPath}`,
          `/src/views/${viewPath.split("/").pop()}.vue`,
        ],
      }
    );
  } catch (error) {
    console.error("加载组件失败:", error);
    currentComponent.value = markRaw(NotFoundPage);
  }
};

/**
 * 自定义渲染菜单标签
 * @param {Object} option - 菜单选项
 * @returns {VNode} 渲染的标签节点
 */
const renderMenuLabel = (option) => {
  return h("div", { class: "menu-label" }, [h("span", option.label)]);
};

/**
 * 自定义渲染菜单图标
 * @param {Object} option - 菜单选项
 * @returns {VNode|null} 渲染的图标节点或null
 */
const renderMenuIcon = (option) => {
  if (option.iconName) {
    const IconComponent = getIconComponent(option.iconName);
    if (IconComponent) {
      return h(NIcon, null, { default: () => h(IconComponent) });
    }
  }
  return null;
};

/**
 * 处理返回按钮点击
 */
const handleBack = () => {
  window.location.href = "/"; //uter.back();
};

/**
 * 查找子菜单
 * @param {Array} menus - 菜单数据数组
 * @param {String|Number} parentId - 父菜单ID
 * @returns {Array} 子菜单数组
 */
const findChildrenByParentId = (menus, parentId) => {
  if (!menus || !Array.isArray(menus)) return [];

  return menus.filter((menu) => {
    // 尝试不同的比较方式
    return (
      String(menu.parentId) === String(parentId) ||
      menu.parentId === Number(parentId) ||
      menu.parentId == parentId
    );
  });
};

/**
 * 选择第一个有效的菜单项
 */
const selectFirstMenuItem = () => {
  if (!menuOptions.value || menuOptions.value.length === 0) {
    console.warn("没有可用的菜单选项");
    return;
  }

  // 查找第一个有viewPath的菜单项
  const firstMenuItem = findFirstViewPathMenuItem(menuOptions.value);

  if (firstMenuItem) {
    handleMenuSelect(firstMenuItem.key);
  } else {
    console.warn("未找到有效的菜单项");
  }
};

/**
 * 根据jumpTo参数查找并选择菜单项
 * @param {String} jumpTo - 要跳转到的菜单路径
 */
const selectMenuByJumpTo = (jumpTo) => {
  if (!jumpTo) return false;

  // 在所有菜单项中查找匹配的菜单项
  const findMenuByPath = (items, path) => {
    if (!items || !Array.isArray(items)) return null;

    for (const item of items) {
      if (item.key === path) {
        return item;
      }

      if (item.children && item.children.length > 0) {
        const found = findMenuByPath(item.children, path);
        if (found) return found;
      }
    }

    return null;
  };

  const menuItem = findMenuByPath(menuOptions.value, jumpTo);

  if (menuItem) {
    handleMenuSelect(menuItem.key);
    return true;
  }

  console.warn("未找到匹配的菜单项:", jumpTo);
  return false;
};

// 组件挂载后初始化
onMounted(async () => {
  // 从localStorage获取菜单数据
  const menus = await getMenusFromStorage();

  if (!menus) {
    console.error("无法获取菜单数据");
    loading.value = false;
    return;
  }

  // 查找当前菜单项
  const currentMenuItem = findMenuById(menus, props.menuId);

  if (currentMenuItem) {
    currentMenu.value = currentMenuItem;

    // 查找子菜单
    let children = [];

    // 优先使用children字段
    if (currentMenuItem.children && currentMenuItem.children.length > 0) {
      children = currentMenuItem.children;
    }
    // 其次使用subMenus字段
    else if (currentMenuItem.subMenus && currentMenuItem.subMenus.length > 0) {
      children = currentMenuItem.subMenus;
    }
    // 最后尝试根据parentId查找
    else {
      children = findChildrenByParentId(menus, props.menuId);
    }

    // 递归查找所有子菜单
    const buildMenuTree = (items, allMenus) => {
      if (!items || !Array.isArray(items)) return items;

      return items.map((item) => {
        // 创建新对象，避免修改原始数据
        const newItem = { ...item };

        // 查找子菜单
        let subItems = [];

        // 如果已有children或subMenus，直接使用
        if (newItem.children && newItem.children.length > 0) {
          subItems = newItem.children;
        } else if (newItem.subMenus && newItem.subMenus.length > 0) {
          subItems = newItem.subMenus;
        } else {
          // 否则根据parentId查找
          subItems = findChildrenByParentId(allMenus, newItem.id);
        }

        // 如果找到子菜单，递归构建子菜单树
        if (subItems && subItems.length > 0) {
          newItem.children = buildMenuTree(subItems, allMenus);
        }

        return newItem;
      });
    };

    // 构建完整的菜单树
    children = buildMenuTree(children, menus);

    if (children.length > 0) {
      // 将子菜单转换为菜单选项
      menuOptions.value = formatMenuOptions(children);

      // 默认展开所有菜单
      const allKeys = [];
      const collectKeys = (items) => {
        if (!items || !Array.isArray(items)) return;

        items.forEach((item) => {
          if (item.key) allKeys.push(item.key);
          if (item.children && item.children.length > 0) {
            collectKeys(item.children);
          }
        });
      };

      collectKeys(menuOptions.value);
      expandedKeys.value = allKeys;

      // 移除强制展开的监听器，允许用户自由控制菜单展开/折叠状态

      // 处理jumpTo参数
      if (props.jumpTo) {
        // 尝试根据jumpTo参数选择菜单项
        const selected = selectMenuByJumpTo(props.jumpTo);

        // 如果没有找到匹配的菜单项，选择第一个有效的菜单项
        if (!selected) {
          selectFirstMenuItem();
        }
      } else {
        // 没有jumpTo参数，选择第一个有效的菜单项
        selectFirstMenuItem();
      }
    } else {
      console.warn("未找到子菜单");
    }
  } else {
    console.error(`未找到ID为 ${props.menuId} 的菜单项`);
  }

  loading.value = false;
});
</script>

<style scoped>
.page-layout {
  height: 100vh;
  overflow: hidden; /* 关闭页面级滚动 */
}

.toolbar {
  display: flex;
  align-items: center;
  height: 48px;
  border-bottom: 1px solid #eee;
  padding: 0 16px;
  background-color: #fff;
  z-index: 10;
}

.back-button {
  margin-right: 16px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
}

.main-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止主内容区域产生滚动条 */
  height: 100%;
}

.component-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 48px); /* 限制最大高度，防止超出视口 */
  overflow: hidden; /* 防止组件容器产生滚动条 */
}

.no-menu-tip {
  padding: 12px;
  color: #999;
  text-align: center;
}

.menu-label {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 使用深度选择器覆盖NaiveUI样式 - 只对主内容区域关闭滚动 */
:deep(.n-layout-content),
:deep(.n-layout-content .n-layout-scroll-container) {
  overflow: hidden !important;
  overflow-y: hidden !important;
  overflow-x: hidden !important;
}

/* 确保主布局容器高度正确 */
:deep(.n-layout) {
  height: 100vh !important;
  max-height: 100vh !important;
}

:deep(.n-layout-content) {
  height: 100% !important;
  max-height: 100% !important;
}

/* 左侧菜单栏样式 - 确保可以滚动 */
:deep(.n-layout-sider) {
  height: 100% !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

:deep(.n-layout-sider .n-layout-scroll-container) {
  flex: 1 !important;
  height: 0 !important; /* 关键：设置为0让flex生效 */
  overflow-y: auto !important;
  overflow-x: hidden !important;
  min-height: 0 !important; /* 确保可以收缩 */
}

/* 菜单容器样式 */
:deep(.n-menu) {
  height: auto !important; /* 改为auto，让内容决定高度 */
  min-height: 100% !important; /* 确保至少填满容器 */
  overflow-y: visible !important; /* 让父容器处理滚动 */
  overflow-x: hidden !important;
}

/* 确保菜单项正确显示 */
:deep(.n-menu .n-menu-item),
:deep(.n-menu .n-submenu) {
  flex-shrink: 0 !important; /* 防止菜单项被压缩 */
}

/* 自定义滚动条样式 - 使其更细更美观 */
:deep(.n-layout-sider .n-layout-scroll-container::-webkit-scrollbar),
:deep(.n-menu::-webkit-scrollbar) {
  width: 6px;
}

:deep(.n-layout-sider .n-layout-scroll-container::-webkit-scrollbar-track),
:deep(.n-menu::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.n-layout-sider .n-layout-scroll-container::-webkit-scrollbar-thumb),
:deep(.n-menu::-webkit-scrollbar-thumb) {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

:deep(
    .n-layout-sider .n-layout-scroll-container::-webkit-scrollbar-thumb:hover
  ),
:deep(.n-menu::-webkit-scrollbar-thumb:hover) {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 修复展开收起按钮被遮挡的问题 - 温和的方式 */
:deep(.n-layout-sider .n-layout-toggle-bar) {
  z-index: 10 !important;
}

:deep(.n-layout-sider .n-layout-toggle-button) {
  z-index: 11 !important;
}
</style>