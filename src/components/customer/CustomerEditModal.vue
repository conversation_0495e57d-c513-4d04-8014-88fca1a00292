<template>
  <n-modal
    v-model:show="modelVisible"
    :title="title"
    preset="card"
    :style="
      isMaximized
        ? { width: '50%', height: '90%' }
        : { width: '600px' }
    "
    :mask-closable="false"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component
              :is="
                isMaximized
                  ? ContractOutlineIcon
                  : ExpandOutlineIcon
              "
            />
          </n-icon>
        </template>
      </n-button>
    </template>
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="isMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
        <n-grid-item>
          <n-form-item label="客户类型" path="customerType">
            <n-select
              v-model:value="formData.customerType"
              :options="customerTypeOptions.filter((item) => item.value !== null)"
              @update:value="handleCustomerTypeChange"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="客户名称" path="customerName">
            <n-input
              v-model:value="formData.customerName"
              placeholder="请输入客户名称"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-grid :cols="isMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
        <n-grid-item>
          <n-form-item
            :label="
              formData.customerType === 'institutional'
                ? '统一社会信用代码'
                : '身份证号码'
            "
            path="customerIdCode"
          >
            <n-input
              v-model:value="formData.customerIdCode"
              :placeholder="
                formData.customerType === 'institutional'
                  ? '请输入统一社会信用代码'
                  : '请输入身份证号码'
              "
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="手机号码" path="mobile">
            <n-input
              v-model:value="formData.mobile"
              placeholder="请输入手机号码"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-grid :cols="isMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
        <n-grid-item>
          <n-form-item label="所属单位" path="ownerOrgName">
            <n-input
              v-model:value="formData.ownerOrgName"
              placeholder="将根据销售顾问自动设置"
              readonly
              style="width: 100%"
            >
              <template #prefix>
                <n-icon>
                  <component :is="BuildingIcon" />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="销售顾问" path="ownerSellerName">
            <n-button
              type="primary"
              ghost
              @click="$emit('show-seller-selector')"
              style="width: 100%; justify-content: flex-start"
            >
              <template #icon>
                <n-icon>
                  <component :is="PersonIcon" />
                </n-icon>
              </template>
              {{ formData.ownerSellerName || "请选择销售顾问" }}
            </n-button>
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-grid :cols="isMaximized ? 2 : 1" :x-gap="24" :y-gap="16">
        <n-grid-item>
          <n-form-item label="成交状态" path="dealStatus">
            <n-select
              v-model:value="formData.dealStatus"
              :options="dealStatusOptions.filter((item) => item.value !== null)"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="客户地址" path="address">
            <n-input
              v-model:value="formData.address"
              placeholder="请输入客户地址"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <n-form-item label="备注" path="remark">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
        />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, markRaw } from 'vue';
import { NIcon } from 'naive-ui';
import { ContractOutline, ExpandOutline } from '@vicons/ionicons5';
import { Building } from '@vicons/tabler';
import { Person } from '@vicons/ionicons5';

// 图标组件
const ContractOutlineIcon = markRaw(ContractOutline);
const ExpandOutlineIcon = markRaw(ExpandOutline);
const BuildingIcon = markRaw(Building);
const PersonIcon = markRaw(Person);

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '编辑客户'
  },
  formData: {
    type: Object,
    required: true
  },
  customerTypeOptions: {
    type: Array,
    default: () => []
  },
  dealStatusOptions: {
    type: Array,
    default: () => []
  },
  rules: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits([
  'update:visible',
  'save',
  'cancel',
  'customer-type-change',
  'show-seller-selector'
]);

// 状态
const formRef = ref(null);
const isMaximized = ref(true);

// 计算属性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 方法
const toggleSize = () => {
  isMaximized.value = !isMaximized.value;
};

const handleCancel = () => {
  emit('cancel');
};

const handleSave = () => {
  formRef.value?.validate((errors) => {
    if (!errors) {
      emit('save');
    }
  });
};

const handleCustomerTypeChange = (value) => {
  emit('customer-type-change', value);
};

// 暴露方法给父组件
defineExpose({
  formRef
});
</script>

<style scoped>
/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* 表单样式 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  padding-bottom: 4px;
}

:deep(.n-form-item) {
  margin-bottom: 8px;
}

:deep(.n-input) {
  border-radius: 4px;
}

:deep(.n-select) {
  width: 100%;
}
</style>
