<template>
  <div class="energy-chart" ref="chartRef"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const chartRef = ref(null);
let chart = null;

const echartData = [
  {
    value: 2154,
    name: "阿维塔",
    percent: '18%',
    itemStyle: {
      normal: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#0175c4" },
          { offset: 1, color: "#03163a" },
        ]),
      },
    },
  },
  {
    value: 2258,
    name: "启源",
    percent: '25%',
    itemStyle: {
      normal: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#01a0c7" },
          { offset: 1, color: "#032748" },
        ]),
      },
    },
  },
  {
    value: 3515,
    name: "引力",
    percent: '16%',
    itemStyle: {
      normal: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#b99c0b" },
          { offset: 1, color: "#1a232b" },
        ]),
      },
    },
  },
  {
    value: 3515,
    name: "深蓝",
    percent: '38%',
    itemStyle: {
      normal: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#c16b27" },
          { offset: 1, color: "transparent" },
        ]),
      },
    },
  },
  {
    value: 3854,
    name: "凯程",
    percent: '12%',
    itemStyle: {
      normal: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#FF3939" },
          { offset: 1, color: "transparent" },
        ]),
      },
    },
  },
];

const colorArr = ['#0175c4', '#01a0c7', '#b99c0b', '#c16b27', '#FF3939'];

const legendRich = {
  name: {
    fontSize: 12,
    color: "#fff",
  },
  value: {
    fontSize: 12,
    color: "#fff",
  },
};

for (let index = 0; index < colorArr.length; index++) {
  const color = colorArr[index];
  legendRich['percent' + index] = {
    fontSize: 12,
    color: color,
  };
}

const option = {
  backgroundColor: 'transparent',
  legend: {
    orient: "vertical",
    x: "30%",
    y: "center",
    itemWidth: 20,
    textStyle: {
      rich: legendRich
    },
    itemGap: 12,
    formatter: function (name) {
      let str = '';
      echartData.map((item, index) => {
        if (item.name === name) {
          str = `{name|${name}}
{value|${(item.value)}kWh}

{${'percent' + index}|${item.percent}}`;
        }
      });
      return str;
    },
  },
  tooltip: {
    show: true,
    formatter: function (value) {
      let data = value.data;
      return `${data.name} ${data.value} (${data.percent})`;
    }
  },
  series: [
    {
      name: "能源分布",
      type: "pie",
      labelLine: {
        show: false
      },
      label: {
        show: false
      },
      radius: ["45%", "85%"],
      center: ['50%', '50%'],
      hoverAnimation: true,
      data: echartData,
    },
  ],
};

onMounted(() => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    chart.setOption(option);

    window.addEventListener('resize', handleResize);
  }
});

const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    window.removeEventListener('resize', handleResize);
  }
});
</script>

<style scoped>
.energy-chart {
  width: 25%;
  height: 45%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(13, 36, 52, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.energy-chart:hover {
  background: rgba(13, 36, 52, 0.5);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  transform: translateY(-50%) scale(1.02);
}
</style>