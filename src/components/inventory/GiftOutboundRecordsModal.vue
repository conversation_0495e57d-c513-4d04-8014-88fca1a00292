<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    title="赠品出库记录"
    preset="card"
    style="width: 1000px; max-height: 90vh"
    :mask-closable="false"
    transform-origin="center"
  >
    <div class="gift-outbound-records">
      <!-- 赠品信息 -->
      <div v-if="giftInfo" class="gift-info-section">
        <n-descriptions :column="3" bordered size="small">
          <n-descriptions-item label="赠品名称">
            {{ giftInfo.name }}
          </n-descriptions-item>
          <n-descriptions-item label="赠品类型">
            {{ giftInfo.category }}
          </n-descriptions-item>
          <n-descriptions-item label="规格型号">
            {{ giftInfo.spec || "-" }}
          </n-descriptions-item>
          <n-descriptions-item label="单位">
            {{ giftInfo.unit }}
          </n-descriptions-item>
          <n-descriptions-item label="单价">
            {{ formatMoney(giftInfo.price, 2) }}
          </n-descriptions-item>
          <n-descriptions-item label="当前库存">
            {{ giftInfo.quantity }}
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <!-- 出库记录表格 -->
      <div class="records-table-section">
        <n-data-table
          :columns="columns"
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          :bordered="true"
          :single-line="false"
          size="small"
          :max-height="400"
          :striped="true"
          remote
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { NModal, NDataTable, NDescriptions, NDescriptionsItem } from "naive-ui";
import { giftStockApi } from "@/api/giftStock.js";
import { formatMoney } from "@/utils/money.js";
import { formatDate } from "@/utils/dateUtils.js";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  giftInfo: {
    type: Object,
    default: null,
  },
});

// 定义事件
const emit = defineEmits(["update:visible"]);

// 响应式数据
const modelVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const loading = ref(false);
const tableData = ref([]);
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20],
  showQuickJumper: false,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
});

// 表格列定义
const columns = [
  {
    title: "序号",
    key: "index",
    width: 60,
    align: "center",
    render: (_, index) => {
      return (
        (pagination.value.page - 1) * pagination.value.pageSize + index + 1
      );
    },
  },
  {
    title: "订单编号",
    key: "orderSn",
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "出库数量",
    key: "quantity",
    width: 100,
    align: "center",
  },
  {
    title: "出库金额",
    key: "outboundAmount",
    width: 120,
    align: "right",
    render: (row) => {
      return formatMoney(row.outboundAmount, 2);
    },
  },
  {
    title: "出库日期",
    key: "createTime",
    width: 150,
    render: (row) => {
      return formatDate(row.createTime, "YYYY-MM-DD HH:mm:ss");
    },
  },
  {
    title: "出库机构",
    key: "outboundOrgName",
    width: 120,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "经办人",
    key: "creatorName",
    width: 100,
    ellipsis: {
      tooltip: true,
    },
  }
];

// 更新弹窗显示状态
const updateVisible = (value) => {
  emit("update:visible", value);
};

// 获取出库记录数据
const fetchOutboundRecords = async () => {
  if (!props.giftInfo?.id) return;

  try {
    loading.value = true;
    const params = {
      giftId: props.giftInfo.id,
      page: pagination.value.page,
      size: pagination.value.pageSize,
    };

    const response = await giftStockApi.getGiftOutboundRecords(params);

    if (response.code === 200) {
      tableData.value = response.data.list || [];
      pagination.value.itemCount = response.data.total || 0;
    } else {
      messages.error(response.message || "获取出库记录失败");
      tableData.value = [];
      pagination.value.itemCount = 0;
    }
  } catch (error) {
    console.error("获取出库记录失败:", error);
    messages.error("获取出库记录失败");
    tableData.value = [];
    pagination.value.itemCount = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  pagination.value.page = page;
  fetchOutboundRecords();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.value.pageSize = pageSize;
  pagination.value.page = 1;
  fetchOutboundRecords();
};

// 监听弹窗显示状态，当显示时获取数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.giftInfo?.id) {
      // 重置分页
      pagination.value.page = 1;
      fetchOutboundRecords();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.gift-outbound-records {
  .gift-info-section {
    margin-bottom: 16px;
  }

  .records-table-section {
    margin-top: 16px;
  }
}
</style>
