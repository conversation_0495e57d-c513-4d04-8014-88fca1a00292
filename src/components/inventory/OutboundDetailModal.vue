<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    title="出库详情"
    preset="card"
    style="width: 1200px; max-height: 90vh"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-spin :show="loading">
      <div v-if="detailData" class="outbound-detail-content">
        <!-- 基本信息 -->
        <div class="section-container">
          <div class="section-title">
            <span class="title-text">基本信息</span>
          </div>
          <n-divider class="section-divider"></n-divider>
          <n-grid :cols="4" :x-gap="16" :y-gap="12">
            <n-grid-item>
              <n-form-item label="订单编号">
                <div class="detail-item">
                  <span class="order-sn">{{ detailData.orderSn }}</span>
                  <n-button
                    quaternary
                    circle
                    size="small"
                    @click="copyToClipboard(detailData.orderSn)"
                  >
                    <template #icon>
                      <n-icon color="#18a058">
                        <component :is="CopyOutlineIcon" />
                      </n-icon>
                    </template>
                  </n-button>
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="订单日期">
                <div class="detail-item">
                  {{ formatDate(detailData.orderDate) }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="出库状态">
                <div class="detail-item">
                  <n-tag
                    :type="getOutboundStatusType(detailData.outboundStatus)"
                    size="small"
                  >
                    {{ getOutboundStatusText(detailData.outboundStatus) }}
                  </n-tag>
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="出库时间">
                <div class="detail-item">
                  {{ formatDate(detailData.outboundTime) }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="出库经办人">
                <div class="detail-item">
                  {{ detailData.outboundAgentName || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item :span="2">
              <n-form-item label="VIN">
                <div class="detail-item">
                  <span class="vin-code">{{ detailData.vin || "-" }}</span>
                  <n-button
                    v-if="detailData.vin"
                    quaternary
                    circle
                    size="small"
                    @click="copyToClipboard(detailData.vin)"
                  >
                    <template #icon>
                      <n-icon color="#18a058">
                        <component :is="CopyOutlineIcon" />
                      </n-icon>
                    </template>
                  </n-button>
                </div>
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </div>

        <!-- 客户信息 -->
        <div class="section-container">
          <div class="section-title">
            <span class="title-text">客户信息</span>
          </div>
          <n-divider class="section-divider"></n-divider>
          <n-grid :cols="4" :x-gap="16" :y-gap="12">
            <n-grid-item>
              <n-form-item label="客户名称">
                <div class="detail-item">
                  {{ detailData.customerName || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="联系电话">
                <div class="detail-item">
                  {{ formatMobile(detailData.mobile) }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="销售单位">
                <div class="detail-item">
                  {{ detailData.salesOrgName || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="销售顾问">
                <div class="detail-item">
                  {{ detailData.salesAgentName || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </div>

        <!-- 车辆信息 -->
        <div class="section-container">
          <div class="section-title">
            <span class="title-text">车辆信息</span>
          </div>
          <n-divider class="section-divider"></n-divider>
          <n-grid :cols="4" :x-gap="16" :y-gap="12">
            <n-grid-item>
              <n-form-item label="品牌">
                <div class="detail-item">
                  {{ detailData.brand || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="车系">
                <div class="detail-item">
                  {{ detailData.series || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="配置名称">
                <div class="detail-item">
                  {{ detailData.configName || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="颜色">
                <div class="detail-item">
                  {{ detailData.colorCode || "-" }}
                </div>
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </div>
      </div>
      <div v-else class="empty-data">
        <n-empty description="暂无详情数据" />
      </div>
    </n-spin>
  </n-modal>
</template>

<script setup>
import { ref, computed, markRaw } from "vue";
import {
  NModal,
  NSpin,
  NButton,
  NEmpty,
  NGrid,
  NGridItem,
  NFormItem,
  NDivider,
  NTag,
  NIcon,
} from "naive-ui";
import { CopyOutline } from "@vicons/ionicons5";
import messages from "@/utils/messages";

const CopyOutlineIcon = markRaw(CopyOutline);

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailData: {
    type: Object,
    default: null,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible"]);

// 组件状态
const loading = ref(false);

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 更新可见性
const updateVisible = (val) => {
  emit("update:visible", val);
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  if (typeof dateStr === "number") {
    return new Date(dateStr).toLocaleString("zh-CN");
  }
  return dateStr;
};

// 格式化手机号（脱敏处理）
const formatMobile = (mobile) => {
  if (!mobile) return "-";
  if (mobile.length === 11) {
    return mobile.substring(0, 3) + "****" + mobile.substring(7);
  }
  return mobile;
};

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      messages.success("复制成功");
    })
    .catch(() => {
      messages.error("复制失败");
    });
};

// 获取出库状态文本
const getOutboundStatusText = (status) => {
  const statusMap = {
    PENDING: "待出库",
    COMPLETED: "已出库",
  };
  return statusMap[status] || status || "未知";
};

// 获取出库状态类型
const getOutboundStatusType = (status) => {
  const typeMap = {
    PENDING: "warning",
    COMPLETED: "success",
  };
  return typeMap[status] || "default";
};
</script>

<style scoped>
.outbound-detail-content {
  padding: 0 10px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

.order-sn,
.vin-code {
  font-family: "Monaco", "Consolas", "Courier New", monospace;
  letter-spacing: 1px;
  font-weight: 600;
  margin-right: 8px;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
