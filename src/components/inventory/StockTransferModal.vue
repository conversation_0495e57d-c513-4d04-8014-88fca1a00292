<template>
  <n-modal
    :show="visible"
    @update:show="$emit('update:visible', $event)"
    :title="title"
    preset="card"
    style="width: 600px"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="调拨类型" path="transferType">
        <n-input
          v-model:value="form.transferType"
          readonly
          placeholder="调拨类型"
        />
      </n-form-item>

      <!-- 调拨类型说明文案 -->
      <div class="transfer-description">
        <n-alert
          v-if="form.transferType === '外部调拨'"
          type="info"
          :show-icon="false"
          style="margin-bottom: 16px"
        >
          <template #header>
            <span style="font-weight: 500">外部调拨说明</span>
          </template>
          系统将自动产生本机构的应收账款与调入机构的应付账款，金额比例由调出机构指定。
        </n-alert>

        <n-alert
          v-if="form.transferType === '内部调拨'"
          type="warning"
          :show-icon="false"
          style="margin-bottom: 16px"
        >
          <template #header>
            <span style="font-weight: 500">内部调拨说明</span>
          </template>
          如果无需产生财务往来，请使用内部调拨。
        </n-alert>
      </div>

      <n-form-item label="调出机构" path="sourceOrgName">
        <n-input
          v-model:value="form.sourceOrgName"
          readonly
          placeholder="调出机构"
        />
      </n-form-item>

      <n-form-item label="调入机构" path="targetOrgId">
        <n-button
          type="primary"
          ghost
          @click="showTargetOrgSelector = true"
          style="width: 100%; justify-content: flex-start"
        >
          <template #icon>
            <n-icon><Building /></n-icon>
          </template>
          {{ form.targetOrgName || "请选择调入机构" }}
        </n-button>
      </n-form-item>
      <n-form-item
        v-if="form.transferType === '外部调拨'"
        label="应收比例"
        path="receivableRatio"
      >
        <n-input-number
          v-model:value="form.receivableRatio"
          button-placement="both"
          placeholder="请输入应收比例"
          :min="0.01"
          :max="100"
          :precision="2"
          :step="0.01"
          style="width: 100%"
        >
          <template #suffix>%</template>
        </n-input-number>
        <n-text
          depth="3"
          style="
            width: 80px;
            font-size: 12px;
            margin-top: 4px;
            display: block;
            margin-left: 5px;
          "
        >
          x 启票价格
        </n-text>
      </n-form-item>
      <n-form-item label="调拨原因" path="transferReason">
        <n-input
          v-model:value="form.transferReason"
          type="textarea"
          placeholder="请输入调拨原因"
          :rows="3"
        />
      </n-form-item>
      <n-form-item label="备注" path="remark">
        <n-input
          v-model:value="form.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="2"
        />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSubmit">确定</n-button>
      </n-space>
    </template>

    <!-- 目标机构选择器 -->
    <biz-org-selector
      v-model:visible="showTargetOrgSelector"
      title="选择目标机构"
      business-permission="can_stock_in"
      :single="true"
      :list-all="props.transferType === '外部调拨'"
      @select="handleTargetOrgSelect"
      @cancel="handleTargetOrgCancel"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";
import { useMessage } from "naive-ui";
import { Building } from "@vicons/tabler";
import stocksApi from "@/api/stocks";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  transferType: {
    type: String,
    default: "内部调拨", // '内部调拨' 或 '外部调拨'
  },
  stockIds: {
    type: Array,
    default: () => [],
  },
  selectedStocks: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["update:visible", "success"]);

// 响应式数据
const message = useMessage();
const formRef = ref(null);
const showTargetOrgSelector = ref(false);
const loading = ref(false);

// 计算属性
const title = computed(() => props.transferType);

// 表单数据
const form = reactive({
  transferType: "",
  sourceOrgName: "", // 调出机构名称（只读）
  targetOrgId: null,
  targetOrgName: "",
  receivableRatio: 100,
  transferReason: "",
  remark: "",
  stockIds: [],
});

// 表单验证规则
const rules = {
  targetOrgId: {
    required: true,
    message: "请选择目标机构",
    trigger: ["blur", "change"],
    validator: (_, value) => {
      if (!value || value === null || value === undefined) {
        return new Error("请选择目标机构");
      }
      return true;
    },
  },
  receivableRatio: {
    required: true,
    type: "number",
    message: "请输入应收比例",
    trigger: ["blur", "change"],
    validator: (_, value) => {
      if (form.transferType === "外部调拨") {
        if (value === null || value === undefined || value === "") {
          return new Error("请输入应收比例");
        }
        if (value < 0.01 || value > 100) {
          return new Error("应收比例必须在0.01-100之间");
        }
      }
      return true;
    },
  },
  transferReason: {
    required: true,
    message: "请输入调拨原因",
    trigger: ["blur", "input"],
  },
};

// 重置表单
const resetForm = () => {
  form.transferType = props.transferType;
  form.sourceOrgName = getSourceOrgName();
  form.targetOrgId = null;
  form.targetOrgName = "";
  form.receivableRatio = 100;
  form.transferReason = "";
  form.remark = "";
  form.stockIds = [...props.stockIds];

  // 重置表单验证状态
  nextTick(() => {
    formRef.value?.restoreValidation();
  });
};

// 获取调出机构名称
const getSourceOrgName = () => {
  if (props.selectedStocks && props.selectedStocks.length > 0) {
    // 获取第一个库存的机构名称，如果所有库存都来自同一机构则显示机构名
    // 如果来自不同机构则显示"多个机构"
    const firstOrgName = props.selectedStocks[0].stockOrgName;
    const allSameOrg = props.selectedStocks.every(
      (stock) => stock.stockOrgName === firstOrgName
    );

    if (allSameOrg) {
      return firstOrgName || "未知机构";
    } else {
      return "多个机构";
    }
  }
  return "";
};

// 处理目标机构选择
const handleTargetOrgSelect = (orgs) => {
  showTargetOrgSelector.value = false;
  if (orgs && orgs.length > 0) {
    const org = orgs[0]; // 单选模式，取第一个
    form.targetOrgId = org.id;
    form.targetOrgName = org.orgName;

    // 清除目标机构的验证错误
    nextTick(() => {
      formRef.value?.restoreValidation("targetOrgId");
    });
  }
};

// 处理目标机构选择取消
const handleTargetOrgCancel = () => {
  showTargetOrgSelector.value = false;
};

// 提交调拨
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) {
      console.log("表单验证错误:", errors);
      return;
    }

    try {
      loading.value = true;

      // 确定调拨类型参数
      const transferTypeParam =
        form.transferType === "内部调拨" ? "INTERNAL" : "EXTERNAL";

      // 构建请求体数据，将所有参数封装到JSON body中
      const requestBody = {
        ids: form.stockIds,
        targetOrgId: form.targetOrgId,
        transferType: transferTypeParam,
        transferReason: form.transferReason,
        remark: form.remark ? form.remark.replace(/[\r\n]+/g, " ") : "",
      };

      // 如果是外部调拨，添加应收比例
      if (form.transferType === "外部调拨") {
        requestBody.receivableRatio = form.receivableRatio;
      }

      // 调用库存调拨API
      const response = await stocksApi.transferStock(requestBody);

      if (response.code === 200) {
        emit("success");
        handleCancel();
      }
    } catch (error) {
      console.error("保存失败:", error);
    } finally {
      loading.value = false;
    }
  });
};

// 取消操作
const handleCancel = () => {
  emit("update:visible", false);
  resetForm();
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm();
      // 确保表单值正确设置
      nextTick(() => {
        form.receivableRatio = 100;
      });
    }
  }
);

// 监听调拨类型变化
watch(
  () => props.transferType,
  (newVal) => {
    form.transferType = newVal;
  }
);

// 监听库存ID变化
watch(
  () => props.stockIds,
  (newVal) => {
    form.stockIds = [...newVal];
  }
);

// 监听选中库存变化，更新调出机构名称
watch(
  () => props.selectedStocks,
  () => {
    form.sourceOrgName = getSourceOrgName();
  }
);
</script>

<style lang="scss" scoped>
// 组件样式可以根据需要添加
</style>
