<template>
  <n-modal
    v-model:show="modalVisible"
    title="车辆流程追踪"
    preset="card"
    style="width: 800px; max-width: 90%"
    :mask-closable="true"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>

    <div v-else-if="error" class="error-container">
      <n-result status="error" :title="error" />
    </div>

    <div v-else class="tracking-content">
      <!-- 车辆基本信息 -->
      <div class="vehicle-info">
        <n-descriptions bordered :column="2" label-placement="left">
          <n-descriptions-item label="VIN">
            <div style="display: flex; align-items: center;">
              <span>{{ trackingData?.vin || '-' }}</span>
              <n-button v-if="trackingData?.vin" quaternary circle size="small" @click="copyToClipboard(trackingData.vin)" style="margin-left: 5px;" title="复制VIN">
                <template #icon>
                  <n-icon color="#18a058">
                    <CopyOutline />
                  </n-icon>
                </template>
              </n-button>
            </div>
          </n-descriptions-item>
          <n-descriptions-item label="车型名称">
            {{ trackingData?.vehicleModelName || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="车型系列">
            {{ trackingData?.vehicleSeries || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="车辆类别">
            {{ trackingData?.vehicleCategory || '-' }}
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <!-- 流程时间线 -->
      <div class="tracking-timeline">
        <h3>流程追踪</h3>
        <n-timeline>
          <n-timeline-item v-for="(item, index) in trackingData?.timeline" :key="index" :type="getTimelineItemType(item.status)" :title="item.title" :time="item.time" :content="item.content" />
        </n-timeline>
      </div>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ContractOutline, ExpandOutline, CopyOutline } from '@vicons/ionicons5'
import messages from '@/utils/messages'
import vehicleTrackingApi from '@/api/vehicleTracking'

const props = defineProps({
  vin: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

// 状态变量
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
const isMaximized = ref(true)
const loading = ref(false)
const error = ref(null)
const trackingData = ref(null)

// 切换弹窗最大化/最小化
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false
}

// 复制到剪贴板
const copyToClipboard = (text) => {
  if (!text) return

  navigator.clipboard.writeText(text)
    .then(() => {
      messages.success('复制成功')
    })
    .catch(err => {
      console.error('复制失败:', err)
      messages.error('复制失败')
    })
}

// 获取时间线项的类型
const getTimelineItemType = (status) => {
  const typeMap = {
    'completed': 'success',
    'processing': 'info',
    'pending': 'warning',
    'error': 'error'
  }
  return typeMap[status] || 'default'
}

// 加载车辆流程追踪数据
const loadTrackingData = async () => {
  if (!props.vin) return

  loading.value = true
  error.value = null

  try {
    // 尝试使用API获取数据
    try {
      const response = await vehicleTrackingApi.getVehicleTracking(props.vin)

      if (response.code === 200) {
        trackingData.value = response.data
        return
      }
    } catch (apiError) {
      console.warn('使用API获取数据失败，将使用模拟数据:', apiError)
    }

    // 如果API调用失败，使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟

    // 模拟数据
    trackingData.value = {
      vin: props.vin,
      vehicleModelName: '奥迪A6L 2023款',
      vehicleSeries: 'A6L',
      vehicleCategory: '轿车',
      timeline: [
        {
          title: '启票',
          time: '2023-06-01 10:30:00',
          content: '车辆启票完成，启票金额：¥380,000.00',
          status: 'completed'
        },
        {
          title: '生产',
          time: '2023-06-10 14:20:00',
          content: '车辆生产完成，质检合格',
          status: 'completed'
        },
        {
          title: '入库',
          time: '2023-06-15 09:15:00',
          content: '车辆入库，库位：A区-12-34',
          status: 'completed'
        },
        {
          title: '发运',
          time: '2023-06-20 11:30:00',
          content: '车辆发运，目的地：北京市朝阳区',
          status: 'completed'
        },
        {
          title: '到店',
          time: '2023-06-25 16:45:00',
          content: '车辆到达经销商，经销商：北京奥德汽车销售有限公司',
          status: 'completed'
        },
        {
          title: '销售',
          time: '2023-07-05 10:20:00',
          content: '车辆已售出',
          status: 'completed'
        },
        {
          title: '交付',
          time: '2023-07-10 14:30:00',
          content: '车辆已交付客户',
          status: 'completed'
        }
      ]
    }
  } catch (err) {
    console.error('获取车辆流程追踪数据失败:', err)
    error.value = '获取车辆流程追踪数据失败，请稍后重试'
    messages.error(error.value)
  } finally {
    loading.value = false
  }
}

// 监听 visible 和 vin 变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.vin) {
    loadTrackingData()
  }
})

watch(() => props.vin, (newVal) => {
  if (modalVisible.value && newVal) {
    loadTrackingData()
  }
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.error-container {
  min-height: 200px;
}

.tracking-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.vehicle-info {
  margin-bottom: 20px;
}

.tracking-timeline {
  margin-top: 20px;
}

.tracking-timeline h3 {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
</style>
