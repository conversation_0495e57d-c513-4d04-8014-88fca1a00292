<template>
  <n-modal
    v-model:show="modalVisible"
    :title="isEdit ? '编辑启票' : '新增启票'"
    preset="card"
    :style="{ width: '50vw', minWidth: '800px' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="4" :x-gap="16" :y-gap="16">
        <n-grid-item>
          <n-form-item label="VIN" path="vin">
            <n-input v-model:value="form.vin" placeholder="请输入VIN" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="启票日期" path="erpOrderDate">
            <n-date-picker
              v-model:value="dateValue"
              type="date"
              clearable
              style="width: 100%"
              @update:value="handleDateChange"
              value-format="yyyy-MM-dd"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="启票金额(元)" path="startBillPrice">
            <n-input-number
              :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
              v-model:value="form.startBillPrice"
              placeholder="请输入启票金额"
              style="width: 100%"
              button-placement="both"
              :precision="2"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="启票单位" path="invoiceOrgName">
            <n-button
              type="primary"
              ghost
              @click="showInvoiceOrgSelector = true"
              style="width: 100%; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ form.invoiceOrgName || "请选择启票单位" }}
            </n-button>
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="车辆品牌" path="vehicleBrand">
            <n-select
              v-model:value="form.vehicleBrand"
              placeholder="请选择车辆品牌"
              :options="vehicleBrandOptions"
              clearable
              @update:value="handleBrandChange"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="车型系列" path="vehicleSeries">
            <n-select
              v-model:value="form.vehicleSeries"
              placeholder="请选择车型系列"
              :options="vehicleModelOptions"
              :disabled="!form.vehicleBrand"
              :loading="loadingModels"
              clearable
              filterable
              @update:value="handleModelChange"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="车型配置" path="vehicleConfig">
            <n-select
              v-model:value="form.vehicleConfig"
              placeholder="请选择车型配置"
              :options="vehicleConfigOptions"
              :disabled="!form.vehicleSeries"
              :loading="loadingConfigs"
              clearable
              filterable
              @update:value="handleConfigChange"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="颜色代码" path="colorCode">
            <n-select
              v-model:value="form.colorCode"
              placeholder="请选择或输入颜色代码"
              :options="colorCodeOptions"
              :disabled="!form.vehicleConfig"
              :loading="loadingColors"
              clearable
              filterable
              tag
              @update:value="handleColorChange"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <!-- 资金信息 -->
      <n-grid :cols="4" :x-gap="16" :y-gap="16">
        <n-grid-item :span="2">
          <n-form-item label="资金类型" path="fundType">
            <n-select
              v-model:value="form.fundType"
              placeholder="请选择资金类型"
              :options="fundTypeOptions"
              clearable
              filterable
              :fallback-option="false"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item :span="2">
          <n-form-item label="启票备注" path="billRemark">
            <n-input
              v-model:value="form.billRemark"
              placeholder="请输入启票备注"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="loading"
          >确定</n-button
        >
      </n-space>
    </template>
  </n-modal>

  <!-- 启票单位选择器 -->
  <biz-org-selector
    v-model:visible="showInvoiceOrgSelector"
    title="选择启票单位"
    business-permission="can_stock_in"
    :single="true"
    @select="handleInvoiceOrgSelect"
    @cancel="handleInvoiceOrgCancel"
  />
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { Building } from "@vicons/tabler";
import startBillApi from "@/api/startBill";
import messages from "@/utils/messages";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { useDictOptions } from "@/utils/dictUtils";
import skuApi from "@/api/sku";
import { getNumberInputConfig } from "@/config/inputConfig";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "success"]);

// 状态变量
// 使用简单的ref而不是computed属性，避免可能的循环引用
const modalVisible = ref(false);

// 监听props.visible的变化
watch(
  () => props.visible,
  (newVal) => {
    modalVisible.value = newVal;
  }
);

// 监听modalVisible的变化
watch(modalVisible, (newVal) => {
  if (newVal !== props.visible) {
    emit("update:visible", newVal);
  }
});
const loading = ref(false);
const formRef = ref(null);
const dateValue = ref(null);

// BizOrgSelector 显示状态
const showInvoiceOrgSelector = ref(false);

// 联动下拉框相关状态
const loadingModels = ref(false);
const loadingConfigs = ref(false);
const loadingColors = ref(false);
const vehicleModelOptions = ref([]);
const vehicleConfigOptions = ref([]);
const colorCodeOptions = ref([]);
const colorCodeSkuMap = ref(new Map()); // 存储颜色代码与SKU数据的映射

// 表单数据
const form = reactive({
  id: null,
  erpOrderDate: "",
  invoiceOrgName: "",
  invoiceOrgId: "", // 启票单位ID
  vehicleBrand: null, // 车辆品牌
  vehicleSeries: null, // 车型系列
  vehicleConfig: null, // 车型配置
  colorCode: null, // 颜色代码
  skuId: null, // SKU ID，当颜色是从列表中选择时才有值
  vin: "",
  startBillPrice: null,
  fundType: null, // 资金类型
  billRemark: "", // 启票备注
});

// 车辆品牌选项（使用响应式字典数据）
const { options: vehicleBrandOptions } = useDictOptions("vehicle_brand", false);

// 资金类型选项（使用响应式字典数据）
const { options: fundTypeOptions } = useDictOptions("fund_type", false);

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 表单验证规则
const rules = {
  vin: {
    required: true,
    message: "请输入VIN",
    trigger: ["blur", "input"],
    validator: (_, value) => {
      if (!value) {
        return new Error("请输入VIN");
      }
      // VIN必须是17位字符
      if (value.length !== 17) {
        return new Error("VIN必须是17位字符");
      }
      return true;
    },
  },
  erpOrderDate: {
    required: true,
    message: "请选择启票日期",
    trigger: ["blur", "change"],
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择启票日期");
      }
      return true;
    },
  },
  startBillPrice: {
    required: true,
    message: "请输入启票金额",
    trigger: ["blur", "change"],
    type: "number",
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入启票金额");
      }
      if (typeof value !== "number") {
        return new Error("启票金额必须是数字");
      }
      if (value <= 0) {
        return new Error("启票金额必须大于0");
      }
      return true;
    },
  },
  invoiceOrgName: {
    required: true,
    message: "请选择启票单位",
    trigger: ["blur", "input"],
  },
  vehicleBrand: {
    required: true,
    message: "请选择车辆品牌",
    trigger: ["blur", "change"],
  },
  vehicleSeries: {
    required: true,
    message: "请选择车型系列",
    trigger: ["blur", "change"],
  },
  vehicleConfig: {
    required: true,
    message: "请选择车型配置",
    trigger: ["blur", "change"],
  },
  colorCode: {
    required: true,
    message: "请选择颜色代码",
    trigger: ["blur", "change"],
  },
  fundType: {
    required: true,
    message: "请选择资金类型",
    trigger: ["blur", "change"],
  },
};

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false;
};

// 处理启票单位选择
const handleInvoiceOrgSelect = (selectedOrgs) => {
  if (selectedOrgs && selectedOrgs.length > 0) {
    const org = selectedOrgs[0]; // 单选模式，取第一个
    form.invoiceOrgName = org.orgName;
    form.invoiceOrgId = org.id;
  }
};

const handleInvoiceOrgCancel = () => {
  showInvoiceOrgSelector.value = false;
};

// 获取车型选项
const fetchVehicleModels = async (brand) => {
  if (!brand) {
    vehicleModelOptions.value = [];
    return;
  }

  try {
    loadingModels.value = true;
    const response = await skuApi.getSkuOptions("series", { brand });

    if (response.code === 200) {
      // 去重并转换为选项格式
      const uniqueSeries = [
        ...new Set(response.data.map((item) => item.series).filter(Boolean)),
      ];
      vehicleModelOptions.value = uniqueSeries.map((series) => ({
        label: series,
        value: series,
      }));
    } else {
      messages.error(response.message || "获取车型失败");
      vehicleModelOptions.value = [];
    }
  } catch (error) {
    console.error("获取车型失败:", error);
    messages.error("获取车型失败");
    vehicleModelOptions.value = [];
  } finally {
    loadingModels.value = false;
  }
};

// 获取配置选项
const fetchVehicleConfigs = async (brand, series) => {
  if (!brand || !series) {
    vehicleConfigOptions.value = [];
    return;
  }

  try {
    loadingConfigs.value = true;
    const response = await skuApi.getSkuOptions("configName", {
      brand,
      series,
    });

    if (response.code === 200) {
      // 去重并转换为选项格式
      const uniqueConfigs = [
        ...new Set(
          response.data.map((item) => item.configName).filter(Boolean)
        ),
      ];
      vehicleConfigOptions.value = uniqueConfigs.map((configName) => ({
        label: configName,
        value: configName,
      }));
    } else {
      messages.error(response.message || "获取配置失败");
      vehicleConfigOptions.value = [];
    }
  } catch (error) {
    console.error("获取配置失败:", error);
    messages.error("获取配置失败");
    vehicleConfigOptions.value = [];
  } finally {
    loadingConfigs.value = false;
  }
};

// 获取颜色代码选项
const fetchColorCodes = async (brand, series, configName) => {
  if (!brand || !series || !configName) {
    colorCodeOptions.value = [];
    colorCodeSkuMap.value.clear();
    return;
  }

  try {
    loadingColors.value = true;
    const response = await skuApi.getSkuOptions("colorCode", {
      brand,
      series,
      configName,
    });

    if (response.code === 200) {
      // 清空之前的映射
      colorCodeSkuMap.value.clear();

      // 去重并转换为选项格式，同时建立颜色代码与SKU数据的映射
      const uniqueColors = [
        ...new Set(response.data.map((item) => item.colorCode).filter(Boolean)),
      ];

      colorCodeOptions.value = uniqueColors.map((colorCode) => {
        // 找到该颜色代码对应的SKU数据
        const skuData = response.data.find(
          (item) => item.colorCode === colorCode
        );
        if (skuData) {
          colorCodeSkuMap.value.set(colorCode, skuData);
        }

        return {
          label: colorCode,
          value: colorCode,
        };
      });
    } else {
      messages.error(response.message || "获取颜色代码失败");
      colorCodeOptions.value = [];
      colorCodeSkuMap.value.clear();
    }
  } catch (error) {
    console.error("获取颜色代码失败:", error);
    messages.error("获取颜色代码失败");
    colorCodeOptions.value = [];
    colorCodeSkuMap.value.clear();
  } finally {
    loadingColors.value = false;
  }
};

// 处理品牌变化
const handleBrandChange = (value) => {
  form.vehicleSeries = null;
  form.vehicleConfig = null;
  form.colorCode = null;
  form.skuId = null; // 清空SKU ID
  vehicleConfigOptions.value = [];
  colorCodeOptions.value = [];
  colorCodeSkuMap.value.clear(); // 清空颜色代码映射

  // 获取车型选项
  if (value) {
    fetchVehicleModels(value);
  } else {
    vehicleModelOptions.value = [];
  }
};

// 处理车型变化
const handleModelChange = (value) => {
  form.vehicleConfig = null;
  form.colorCode = null;
  form.skuId = null; // 清空SKU ID
  colorCodeOptions.value = [];
  colorCodeSkuMap.value.clear(); // 清空颜色代码映射

  // 获取配置选项
  if (value && form.vehicleBrand) {
    fetchVehicleConfigs(form.vehicleBrand, value);
  } else {
    vehicleConfigOptions.value = [];
  }
};

// 处理配置变化
const handleConfigChange = (value) => {
  form.colorCode = null;
  form.skuId = null; // 清空SKU ID

  // 获取颜色代码选项
  if (value && form.vehicleBrand && form.vehicleSeries) {
    fetchColorCodes(form.vehicleBrand, form.vehicleSeries, value);
  } else {
    colorCodeOptions.value = [];
    colorCodeSkuMap.value.clear(); // 清空颜色代码映射
  }
};

// 处理颜色代码变化
const handleColorChange = (value) => {
  console.log("颜色代码变化:", value);

  if (value && colorCodeSkuMap.value.has(value)) {
    // 如果选择的颜色在映射中存在，说明是从列表中选择的，设置对应的SKU ID
    const skuData = colorCodeSkuMap.value.get(value);
    form.skuId = skuData.id;
    console.log("设置SKU ID:", skuData.id, "对应颜色:", value);
  } else {
    // 如果颜色不在映射中，说明是手动输入的，清空SKU ID
    form.skuId = null;
    console.log("清空SKU ID，颜色为手动输入:", value);
  }
};

// 处理日期变化
const handleDateChange = (date) => {
  console.log("日期变化:", date, typeof date);
  if (!date) {
    form.erpOrderDate = "";
    return;
  }

  try {
    let formattedDate = "";

    // 检查日期类型并相应处理
    if (typeof date === "string") {
      // 如果已经是字符串格式，检查是否符合 yyyy-MM-dd 格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        formattedDate = date;
      } else {
        // 尝试解析其他格式的日期字符串
        const parsedDate = new Date(date);
        if (!isNaN(parsedDate.getTime())) {
          const year = parsedDate.getFullYear();
          const month = String(parsedDate.getMonth() + 1).padStart(2, "0");
          const day = String(parsedDate.getDate()).padStart(2, "0");
          formattedDate = `${year}-${month}-${day}`;
        }
      }
    } else if (date instanceof Date) {
      // 如果是Date对象
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      formattedDate = `${year}-${month}-${day}`;
    } else if (typeof date === "number") {
      // 如果是时间戳
      const dateObj = new Date(date);
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, "0");
      const day = String(dateObj.getDate()).padStart(2, "0");
      formattedDate = `${year}-${month}-${day}`;
    } else if (Array.isArray(date) && date.length >= 3) {
      // 如果是数组 [year, month, day]
      const [year, month, day] = date;
      formattedDate = `${year}-${String(month).padStart(2, "0")}-${String(
        day
      ).padStart(2, "0")}`;
    }

    // 更新表单值
    form.erpOrderDate = formattedDate;
    console.log("格式化后的日期:", formattedDate);
  } catch (error) {
    console.error("日期处理错误:", error);
    form.erpOrderDate = "";
  }
};

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return;

    try {
      loading.value = true;

      // 准备请求数据
      const data = {
        erpOrderDate: form.erpOrderDate || "", // 使用空字符串而不是null
        invoiceOrgName: form.invoiceOrgName,
        invoiceOrgId: form.invoiceOrgId,
        vehicleCategory: form.vehicleBrand, // 品牌 → vehicleCategory
        vehicleSeries: form.vehicleSeries, // 车型 → vehicleSeries
        vehicleModelName: form.vehicleConfig, // 配置 → vehicleModelName
        materialName: form.colorCode, // 颜色代码 → materialName
        vin: form.vin,
        startBillPrice: form.startBillPrice
          ? Number(form.startBillPrice)
          : null, // 确保金额是数字类型
        fundType: form.fundType || "",
        billRemark: form.billRemark
          ? form.billRemark.replace(/[\r\n]+/g, " ")
          : "",
      };

      // 如果颜色代码是从列表中选择的（有对应的SKU ID），则添加skuId字段
      if (
        form.skuId &&
        form.colorCode &&
        colorCodeSkuMap.value.has(form.colorCode)
      ) {
        data.skuId = form.skuId;
        console.log(
          "提交SKU ID:",
          form.skuId,
          "对应materialName:",
          form.colorCode
        );
      } else {
        console.log(
          "不提交SKU ID，materialName为手动输入或未选择:",
          form.colorCode
        );
      }

      console.log("提交的数据:", data);

      // 如果是编辑模式，添加ID
      if (props.isEdit) {
        data.id = form.id;
      }

      // 调用保存API
      const response = props.isEdit
        ? await startBillApi.updateStartBill(data)
        : await startBillApi.addStartBill(data);

      if (response.code === 200) {
        messages.success(props.isEdit ? "更新成功" : "添加成功");
        modalVisible.value = false;
        // 通知父组件保存成功
        emit("success");
      } else {
        messages.error(
          response.message || (props.isEdit ? "更新失败" : "添加失败")
        );
      }
    } catch (error) {
      console.error("保存失败:", error);
    } finally {
      loading.value = false;
    }
  });
};

// 初始化表单数据
const initFormData = () => {
  // 重置表单
  Object.assign(form, {
    id: null,
    erpOrderDate: "",
    invoiceOrgName: "",
    invoiceOrgId: "",
    vehicleBrand: null,
    vehicleSeries: null,
    vehicleConfig: null,
    colorCode: null,
    skuId: null, // 重置SKU ID
    vin: "",
    startBillPrice: null,
    fundType: null,
    billRemark: "",
  });

  // 重置联动下拉框选项
  vehicleModelOptions.value = [];
  vehicleConfigOptions.value = [];
  colorCodeOptions.value = [];
  colorCodeSkuMap.value.clear(); // 清空颜色代码映射

  // 重置日期选择器的值
  dateValue.value = null;

  // 如果是编辑模式，填充表单数据
  if (props.isEdit && props.editData) {
    // 使用解构赋值来避免直接引用可能包含循环引用的对象
    const {
      id,
      erpOrderDate,
      invoiceOrgName,
      vehicleCategory, // 后端返回的品牌字段
      vehicleSeries,
      vehicleModelName, // 后端返回的配置字段
      materialName, // 后端返回的颜色代码字段
      vin,
      startBillPrice,
      fundType,
      billRemark,
      invoiceOrgId,
      skuId, // 编辑时可能已有SKU ID
    } = props.editData;

    Object.assign(form, {
      id,
      erpOrderDate: erpOrderDate || "", // 使用空字符串而不是null
      invoiceOrgName: invoiceOrgName || "",
      invoiceOrgId: invoiceOrgId || "",
      vehicleBrand: vehicleCategory || null, // 映射 vehicleCategory → vehicleBrand
      vehicleSeries: vehicleSeries || null,
      vehicleConfig: vehicleModelName || null, // 映射 vehicleModelName → vehicleConfig
      colorCode: materialName || null, // 映射 materialName → colorCode
      skuId: skuId || null, // 设置SKU ID
      vin: vin || "",
      startBillPrice,
      fundType: fundType || null,
      billRemark: billRemark || "",
    });

    // 如果有品牌数据，需要加载对应的车型、配置和颜色选项
    if (vehicleCategory) {
      fetchVehicleModels(vehicleCategory).then(() => {
        if (vehicleSeries) {
          fetchVehicleConfigs(vehicleCategory, vehicleSeries).then(() => {
            if (vehicleModelName) {
              fetchColorCodes(vehicleCategory, vehicleSeries, vehicleModelName);
            }
          });
        }
      });
    }

    // 如果有日期，设置日期选择器的值
    if (erpOrderDate) {
      try {
        // 尝试将日期字符串转换为时间戳
        const [year, month, day] = erpOrderDate.split("-").map(Number);
        dateValue.value = new Date(year, month - 1, day).getTime();
      } catch (error) {
        console.error("日期转换错误:", error);
        dateValue.value = null;
      }
    } else {
      dateValue.value = null;
    }
  }
};

// 监听弹窗显示状态变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      initFormData();
    }
  }
);

// 监听编辑数据变化
watch(
  () => props.isEdit,
  (newVal) => {
    if (props.visible && newVal) {
      initFormData();
    }
  }
);
</script>

<style scoped>
/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.department-selector) {
    width: 100% !important;
  }
}
</style>
