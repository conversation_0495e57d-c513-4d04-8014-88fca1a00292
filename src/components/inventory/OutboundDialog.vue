<template>
  <n-modal
    v-model:show="modelVisible"
    :mask-closable="false"
    preset="card"
    title="车辆出库"
    :style="{
      width: '600px',
      maxHeight: '85vh',
    }"
    :segmented="{ content: true, footer: true }"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="VIN" path="vin">
        <n-input
          v-model:value="form.vin"
          placeholder="车架号"
          readonly
          style="background-color: #f5f5f5"
        />
      </n-form-item>

      <n-form-item label="客户选择" path="customerId">
        <div style="display: flex; gap: 8px; align-items: center; width: 100%">
          <n-input
            :value="customerDisplayText"
            placeholder="请选择客户"
            readonly
            style="flex: 1; cursor: pointer"
            @click="showCustomerSelector"
          />
          <n-button type="primary" @click="showCustomerSelector">
            选择客户
          </n-button>
          <n-button
            v-if="form.customerInfo"
            type="error"
            ghost
            @click="clearCustomer"
          >
            清空
          </n-button>
        </div>
      </n-form-item>

      <!-- 客户信息回显 -->
      <div class="customer-info">
        <n-grid :cols="2" :x-gap="16" :y-gap="16">
          <n-form-item-gi label="客户名称">
            <n-input
              :value="form.customerInfo?.customerName || ''"
              placeholder="请先选择客户"
              readonly
              style="background-color: #f5f5f5"
            />
          </n-form-item-gi>

          <n-form-item-gi label="客户类型">
            <n-input
              :value="
                form.customerInfo?.customerType === 'individual'
                  ? '个人客户'
                  : form.customerInfo?.customerType === 'institutional'
                  ? '法人客户'
                  : ''
              "
              placeholder="请先选择客户"
              readonly
              style="background-color: #f5f5f5"
            />
          </n-form-item-gi>

          <n-form-item-gi label="手机号码">
            <n-input
              :value="form.customerInfo?.mobile || ''"
              placeholder="请先选择客户"
              readonly
              style="background-color: #f5f5f5"
            />
          </n-form-item-gi>

          <n-form-item-gi label="所属单位">
            <n-input
              :value="form.customerInfo?.ownerOrgName || ''"
              placeholder="请先选择客户"
              readonly
              style="background-color: #f5f5f5"
            />
          </n-form-item-gi>
        </n-grid>
      </div>

      <n-form-item label="出库日期" path="outboundDate">
        <n-date-picker
          v-model:value="form.outboundDate"
          type="date"
          placeholder="请选择出库日期"
          style="width: 100%"
        />
      </n-form-item>

      <n-grid :cols="2" :x-gap="16">
        <n-form-item-gi label="出库金额(元)" path="outboundAmount">
          <n-input-number
            :min="amountInputConfig.min"
            :max="amountInputConfig.max"
            :step="amountInputConfig.step"
            v-model:value="form.outboundAmount"
            button-placement="both"
            placeholder="请输入出库金额"
            :precision="2"
            style="width: 100%"
          />
        </n-form-item-gi>

        <n-form-item-gi label="分佣比例(%)" path="commissionRatio">
          <n-input-number
            :min="0"
            :step="amountInputConfig.step"
            v-model:value="form.commissionRatio"
            button-placement="both"
            placeholder="请输入分佣比例"
            :max="100"
            :precision="2"
            style="width: 100%"
          />
        </n-form-item-gi>
      </n-grid>

      <n-form-item label="备注" path="remark">
        <n-input
          v-model:value="form.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="3"
        />
      </n-form-item>
    </n-form>

    <template #footer>
      <div style="display: flex; justify-content: flex-end; gap: 12px">
        <n-button @click="handleCancel">取消出库</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving">
          确认出库
        </n-button>
      </div>
    </template>

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      :initial-customer="form.customerInfo"
      @select="handleCustomerSelect"
      @cancel="handleCustomerSelectCancel"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NDatePicker,
  NButton,
} from "naive-ui";
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import usedVehicleInventoryApi from "@/api/usedVehicleInventory";
import messages from "@/utils/messages";
import { getNumberInputConfig } from "@/config/inputConfig";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  inventoryData: {
    type: Object,
    default: null,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "success"]);

// 组件状态
const formRef = ref(null);
const saving = ref(false);
const customerSelectorVisible = ref(false);

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 表单数据
const form = reactive({
  id: null,
  vin: "",
  customerId: null,
  customerInfo: null,
  outboundDate: null,
  outboundAmount: null,
  commissionRatio: null,
  remark: "",
});

// 表单验证规则
const rules = {
  customerId: {
    required: true,
    message: "请选择客户",
    trigger: ["blur", "change"],
    validator: (rule, value) => {
      if (!form.customerId || !form.customerInfo) {
        return new Error("请选择客户");
      }
      return true;
    },
  },
  outboundDate: {
    required: true,
    message: "请选择出库日期",
    trigger: ["blur", "change"],
    validator: (rule, value) => {
      if (!value) {
        return new Error("请选择出库日期");
      }
      return true;
    },
  },
  outboundAmount: {
    required: true,
    message: "请输入出库金额",
    trigger: ["blur", "change"],
    validator: (rule, value) => {
      if (!value || value <= 0) {
        return new Error("请输入有效的出库金额");
      }
      return true;
    },
  },
  commissionRatio: {
    required: true,
    message: "请输入分佣比例",
    trigger: ["blur", "change"],
    validator: (rule, value) => {
      if (value === null || value === undefined) {
        return new Error("请输入分佣比例");
      }
      if (value < 0 || value > 100) {
        return new Error("分佣比例必须在0-100之间");
      }
      return true;
    },
  },
};

// 计算属性
const modelVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const customerDisplayText = computed(() => {
  if (form.customerInfo) {
    return `${form.customerInfo.customerName} (${
      form.customerInfo.mobile || "无手机号"
    })`;
  }
  return "";
});

// 监听弹窗显示状态，弹窗打开时重置表单并设置库存数据
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm();
      // 如果有库存数据，设置ID和VIN
      if (props.inventoryData) {
        form.id = props.inventoryData.id;
        form.vin = props.inventoryData.vin;
      }
    }
  }
);

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    vin: "",
    customerId: null,
    customerInfo: null,
    outboundDate: Date.now(), // 默认设置为当前日期
    outboundAmount: null,
    commissionRatio: null,
    remark: "",
  });
};

// 显示客户选择器
const showCustomerSelector = () => {
  customerSelectorVisible.value = true;
};

// 处理客户选择
const handleCustomerSelect = (customer) => {
  form.customerId = customer.id;
  form.customerInfo = customer;
  customerSelectorVisible.value = false;
};

// 处理客户选择取消
const handleCustomerSelectCancel = () => {
  customerSelectorVisible.value = false;
};

// 清空客户
const clearCustomer = () => {
  form.customerId = null;
  form.customerInfo = null;
};

// 处理取消
const handleCancel = () => {
  modelVisible.value = false;
};

// 处理保存
const handleSave = async () => {
  try {
    await formRef.value?.validate();

    saving.value = true;

    // 构建更新数据，包含出库信息
    const updateData = {
      // 出库相关字段
      outboundAmount: Math.round(form.outboundAmount * 100), // 转换为分
      outboundDate: new Date(form.outboundDate).toISOString().split("T")[0],
      outboundCustomerId: form.customerId,
      commissionRatio: form.commissionRatio, // 分佣比例
      outboundPaymentMethod: "cash", // 默认现金
      outboundOrgId: null, // 可以根据需要设置
      outboundAgentId: null, // 可以根据需要设置
      outboundMethod: "sale", // 默认销售
      stockStatus: "sold", // 更新库存状态为已出库
      remark: form.remark ? form.remark.replace(/[\r\n]+/g, " ") : "",
    };

    // 使用更新库存接口
    const response = await usedVehicleInventoryApi.updateInventory(
      form.id,
      updateData
    );

    if (response.code === 200) {
      messages.success("车辆出库成功");
      modelVisible.value = false;
      emit("success");
    } else {
      messages.error(response.message || "出库失败");
    }
  } catch (error) {
    console.error("出库失败:", error);
    if (error.message) {
      messages.error(error.message);
    } else {
      messages.error("出库失败，请稍后重试");
    }
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.customer-info {
  margin: 16px 0;
}
</style>
