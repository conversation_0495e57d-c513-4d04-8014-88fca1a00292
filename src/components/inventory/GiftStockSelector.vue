<template>
  <n-modal
    :show="visible"
    @update:show="handleVisibleChange"
    preset="card"
    title="赠品库存选择"
    :style="{ width: '90%', height: '90%', maxHeight: '90vh' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <div class="selector-content">
      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-wrapper">
          <n-input
            v-model:value="searchKeyword"
            placeholder="请输入赠品类型、名称、规格或单位进行搜索"
            clearable
            style="width: 300px"
            @keydown.enter="handleSearch"
          >
            <template #suffix>
              <n-button text @click="handleSearch">
                <template #icon>
                  <n-icon>
                    <SearchOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
          </n-input>
          <n-switch
            v-model:value="sortByStock"
            @update:value="handleSortByStockChange"
            style="margin-left: 16px"
          >
            <template #checked> 库存优先排序 </template>
            <template #unchecked> 默认排序 </template>
          </n-switch>
          <n-text depth="3" class="search-tip">
            <n-icon size="14" style="margin-right: 4px">
              <InformationCircleOutline />
            </n-icon>
            双击数据列表可以选中该数据
          </n-text>
        </div>
      </div>

      <!-- 数据列表 -->
      <div class="data-list">
        <n-spin :show="loading">
          <n-data-table
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :row-key="(row) => row.id"
            :row-class-name="rowClassName"
            :row-props="rowProps"
            :checked-row-keys="selectedRowKeys"
            :max-height="350"
            :scroll-x="800"
            virtual-scroll
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
            @update:checked-row-keys="handleCheckedRowKeysChange"
            row-selectable
            :on-row-click="handleRowClick"
          />
        </n-spin>
      </div>

      <!-- 已选择赠品标签区域 -->
      <div v-if="selectedRows.length > 0" class="selected-tags-area">
        <div class="selected-tags-header">
          <n-text>已选择 {{ selectedRows.length }} 项</n-text>
        </div>
        <div class="selected-tags-content">
          <n-space wrap>
            <n-tag
              v-for="item in selectedRows"
              :key="item.id"
              closable
              :type="item.type === '服务' ? 'info' : 'success'"
              size="medium"
              round
              @close="handleRemoveTag(item)"
            >
              {{ item.name }}
            </n-tag>
          </n-space>
        </div>
      </div>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">取消</n-button>
        <n-button
          type="primary"
          :disabled="selectedRowKeys.length === 0"
          @click="handleConfirm"
          >确定</n-button
        >
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, watch, defineComponent, h, onMounted } from "vue";
import {
  NModal,
  NButton,
  NIcon,
  NSpin,
  NDataTable,
  NInput,
  NSpace,
  NTag,
  NText,
  NSwitch,
} from "naive-ui";
import {
  ContractOutline,
  ExpandOutline,
  SearchOutline,
  InformationCircleOutline,
} from "@vicons/ionicons5";
import messages from "@/utils/messages";
import { giftStockApi } from "@/api/giftStock";
import { getDictOptions } from "@/api/dict";

// 显式声明组件，消除IDE警告
defineComponent({
  components: {
    NModal,
    NButton,
    NIcon,
    NSpin,
    NDataTable,
    NInput,
    NSpace,
    NTag,
    NText,
    NSwitch,
    ContractOutline,
    ExpandOutline,
    SearchOutline,
    InformationCircleOutline,
  },
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: true,
  },
  // 可选参数，用于初始化筛选条件
  params: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "select"]);

// 状态变量
const loading = ref(false);
const searchKeyword = ref("");
const sortByStock = ref(false); // 是否按库存优先排序
const tableRef = ref(null); // 表格引用
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 字典数据
const categoryOptions = ref([]);
const categoryMap = ref({});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  itemCount: 0,
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
});

// 表格数据
const tableData = ref([]);

// 表格列配置
const columns = [
  {
    type: "selection",
    width: 50,
    multiple: props.multiple, // 根据props.multiple决定是否支持多选
    disabled: (row) => row.quantity === 0, // 库存为0的行不可选中
  },
  {
    title: "赠品类型",
    key: "category",
    width: 100,
    render: (row) => {
      const category = row.category;
      // 从字典中获取标签，如果没有则使用默认逻辑
      const type =
        categoryMap.value[category] ||
        (category === "GOODS"
          ? "商品"
          : category === "SERVICES"
          ? "服务"
          : category);

      // 根据类型设置颜色
      const color = category === "GOODS" ? "success" : "info";

      return h(
        NTag,
        {
          type: "primary",
          size: "small",
          color: {
            color: color === "success" ? "#18a058" : "#2080f0",
            textColor: "white",
          },
        },
        { default: () => type }
      );
    },
  },
  {
    title: "赠品名称",
    key: "name",
    width: 150,
  },
  {
    title: "规格型号",
    key: "spec",
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "仓储单位",
    key: "unit",
    width: 100,
  },
  {
    title: "库存数量",
    key: "quantity",
    width: 100,
    render: (row) => {
      const quantity = row.quantity || 0;
      return h(
        "span",
        {
          style: {
            color: quantity === 0 ? "#d03050" : "#18a058",
            fontWeight: quantity === 0 ? "bold" : "normal",
          },
        },
        quantity === 0 ? "缺货" : quantity.toString()
      );
    },
  },
  {
    title: "库存单价(元)",
    key: "price",
    width: 120,
    render: (row) => {
      // 添加￥前缀并使用千分位展示，从分转为元
      const formattedPrice = row.price
        ? `￥${(row.price / 100).toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}`
        : "0.00";
      return h(
        "span",
        {
          style: {
            color: "#f0a020",
            fontWeight: "bold",
          },
        },
        formattedPrice
      );
    },
  },
];

// 处理弹窗显示状态变化
const handleVisibleChange = (value) => {
  emit("update:visible", value);
  if (value) {
    // 打开弹窗时重置状态
    resetState();
  } else {
    // 关闭弹窗时也重置状态
    resetState();
  }
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
};

// 确认选择
const handleConfirm = () => {
  if (selectedRows.value.length > 0) {
    // 返回选中的赠品数据
    emit("select", props.multiple ? selectedRows.value : selectedRows.value[0]);
    handleClose();
  } else {
    messages.warning("请至少选择一条记录");
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchData();
};

// 数据排序函数
const sortTableData = () => {
  if (sortByStock.value) {
    // 按库存数量降序排序，库存为0的排在最后
    tableData.value.sort((a, b) => {
      const aQuantity = a.quantity || 0;
      const bQuantity = b.quantity || 0;

      // 如果一个是0，一个不是0，不是0的排在前面
      if (aQuantity === 0 && bQuantity !== 0) return 1;
      if (aQuantity !== 0 && bQuantity === 0) return -1;

      // 都不是0或都是0，按数量降序排序
      return bQuantity - aQuantity;
    });
  } else {
    // 恢复原始顺序，重新获取数据
    fetchData();
  }
};

// 处理库存排序开关变化
const handleSortByStockChange = () => {
  // 对当前数据进行重新排序，无需重新请求接口
  sortTableData();
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  fetchData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchData();
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  // 更新选中状态
  selectedRowKeys.value = keys;

  // 更新选中行数据
  selectedRows.value = tableData.value.filter((item) => keys.includes(item.id));

  // 转换数据格式，确保前端展示一致性
  selectedRows.value = selectedRows.value.map((item) => {
    return {
      ...item,
      // 确保前端展示字段一致，使用字典数据
      type:
        categoryMap.value[item.category] ||
        (item.category === "GOODS"
          ? "商品"
          : item.category === "SERVICES"
          ? "服务"
          : item.category),
      stock: item.quantity, // 保存库存数量
      stockQuantity: item.quantity, // 额外添加一个明确的库存数量字段
      costPrice: item.price,
    };
  });

  // 强制更新视图
  tableData.value = [...tableData.value];
};

// 处理移除标签
const handleRemoveTag = (item) => {
  // 从选中行中移除
  const newKeys = [...selectedRowKeys.value];
  const index = newKeys.indexOf(item.id);
  if (index > -1) {
    newKeys.splice(index, 1);
    // 使用统一的更新机制
    handleCheckedRowKeysChange(newKeys);
  }
};

// 行样式
const rowClassName = (row) => {
  let className = "";

  // 检查行是否被选中
  if (selectedRowKeys.value.includes(row.id)) {
    className += "selected-row ";
  }

  // 检查库存是否为0
  if (row.quantity === 0) {
    className += "zero-stock-row ";
  }

  return className.trim();
};

// 处理行点击事件
const handleRowClick = (row) => {
  // 如果库存为0，不允许选中
  if (row.quantity === 0) {
    messages.warning("该赠品库存为0，无法选择");
    return;
  }

  // 根据多选/单选模式处理点击
  if (props.multiple) {
    // 多选模式下，切换选中状态
    const newKeys = [...selectedRowKeys.value];
    const index = newKeys.indexOf(row.id);
    if (index > -1) {
      // 已选中，则取消选中
      newKeys.splice(index, 1);
    } else {
      // 未选中，则添加到选中项
      newKeys.push(row.id);
    }
    handleCheckedRowKeysChange(newKeys);
  } else {
    // 单选模式下，直接选中
    handleCheckedRowKeysChange([row.id]);
  }
};

// 行属性函数 - 添加双击事件和选中样式
const rowProps = (row) => {
  return {
    // 不再在这里处理点击事件，让表格的内置选择机制处理
    onDblclick: () => {
      // 如果库存为0，不允许选中
      if (row.quantity === 0) {
        messages.warning("该赠品库存为0，无法选择");
        return;
      }

      // 双击选中并确认
      if (!selectedRowKeys.value.includes(row.id)) {
        // 如果未选中，则先选中
        const newKeys = props.multiple
          ? [...selectedRowKeys.value, row.id]
          : [row.id];
        handleCheckedRowKeysChange(newKeys);
      }
      // 确认选择
      handleConfirm();
    },
    // 确保行可以被选中
    disabled: row.quantity === 0,
  };
};

// 获取数据 - 使用API
const fetchData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
    };

    // 添加关键词搜索
    if (searchKeyword.value) {
      params.keywords = searchKeyword.value;
    }

    // 添加props中的其他参数
    if (props.params) {
      Object.assign(params, props.params);
    }

    // 调用API获取数据
    const response = await giftStockApi.getGiftStockList(params);

    if (response && response.code === 200 && response.data) {
      // 更新表格数据
      tableData.value = response.data.list || [];

      // 如果开启了库存排序，对数据进行排序
      if (sortByStock.value) {
        tableData.value.sort((a, b) => {
          const aQuantity = a.quantity || 0;
          const bQuantity = b.quantity || 0;

          // 如果一个是0，一个不是0，不是0的排在前面
          if (aQuantity === 0 && bQuantity !== 0) return 1;
          if (aQuantity !== 0 && bQuantity === 0) return -1;

          // 都不是0或都是0，按数量降序排序
          return bQuantity - aQuantity;
        });
      }

      // 更新分页信息
      pagination.itemCount = response.data.total || 0;

      // 检查选中的行是否仍然存在于新数据中
      if (selectedRowKeys.value.length > 0) {
        const existingIds = tableData.value.map((item) => item.id);
        const validKeys = selectedRowKeys.value.filter((key) =>
          existingIds.includes(key)
        );

        if (validKeys.length !== selectedRowKeys.value.length) {
          // 更新选中行
          handleCheckedRowKeysChange(validKeys);
        }
      }
    } else {
      messages.warning("获取数据失败: " + (response?.message || "未知错误"));
      tableData.value = [];
      pagination.itemCount = 0;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    messages.error("获取数据失败，请稍后重试");
    tableData.value = [];
    pagination.itemCount = 0;
  } finally {
    loading.value = false;
  }
};

// 重置状态
const resetState = () => {
  searchKeyword.value = "";
  sortByStock.value = false;
  selectedRowKeys.value = [];
  selectedRows.value = [];
  pagination.page = 1;
};

// 获取字典数据
const fetchDictOptions = async () => {
  try {
    const res = await getDictOptions("gift_category");
    if (res && res.code === 200 && res.data) {
      categoryOptions.value = res.data;

      // 构建映射表，方便快速查找
      const map = {};
      res.data.forEach((item) => {
        map[item.optionValue] = item.optionLabel;
      });
      categoryMap.value = map;
    }
  } catch (error) {
    console.error("获取字典数据失败:", error);
    messages.error("获取字典数据失败");
  }
};

// 组件挂载时获取字典数据
onMounted(() => {
  fetchDictOptions();
});

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 打开弹窗时先重置状态，再获取数据
      resetState();
      fetchData();

      // 确保字典数据已加载
      if (categoryOptions.value.length === 0) {
        fetchDictOptions();
      }
    }
  }
);
</script>

<style scoped>
.selector-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
}

.search-area {
  margin-bottom: 16px;
  width: 100%;
}

.search-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.search-tip {
  margin-left: 12px;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.data-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
  min-height: 400px;
  position: relative;
}

.selected-tags-area {
  margin-top: 16px;
  padding: 12px;
  border-radius: 4px;
  background-color: #f9f9f9;
  border: 1px solid #eee;
}

.selected-tags-header {
  margin-bottom: 8px;
  font-weight: bold;
  color: #606266;
}

.selected-tags-content {
  max-height: 120px;
  overflow-y: auto;
  padding: 4px 0;
}

:deep(.selected-row) {
  background-color: rgba(24, 160, 88, 0.1) !important;
  border-left: 3px solid #18a058;
}

/* 零库存行样式 */
:deep(.zero-stock-row) {
  background-color: rgba(208, 48, 80, 0.05) !important;
  color: #999;
  opacity: 0.7;
}

:deep(.zero-stock-row .n-data-table-td) {
  color: #999 !important;
}

/* 零库存行的悬停样式 */
:deep(.zero-stock-row:hover) {
  background-color: rgba(208, 48, 80, 0.08) !important;
  cursor: not-allowed;
}

:deep(.n-data-table-table) {
  cursor: pointer;
}

:deep(.n-data-table-td) {
  padding: 8px 12px;
}

:deep(.n-data-table-th) {
  padding: 10px 12px;
  background-color: #f5f7fa;
}

/* 鼠标悬停样式 */
:deep(.n-data-table-tr:hover) {
  background-color: rgba(24, 160, 88, 0.05);
}

/* 选中行的悬停样式 */
:deep(.n-data-table-tr.selected-row:hover) {
  background-color: rgba(24, 160, 88, 0.15) !important;
}

/* 确保选中行在条纹表格中也能正确显示 */
:deep(.n-data-table-tr.n-data-table-tr--striped.selected-row) {
  background-color: rgba(24, 160, 88, 0.1) !important;
}

/* 表格滚动样式 */
:deep(.n-data-table-base-table-header) {
  position: sticky;
  top: 0;
  z-index: 2;
}

:deep(.n-data-table-wrapper) {
  height: 100%;
}

:deep(.n-data-table) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.n-data-table-base-table-body) {
  flex: 1;
  overflow: auto;
}

/* 调整分页器样式 */
:deep(.n-pagination) {
  margin-top: 12px;
}
</style>
