<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    :title="title"
    preset="card"
    style="width: 100%; height: 100%"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
      class="order-form"
    >
      <!-- 产品信息部分 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">产品信息</span>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <!-- 车辆信息表单 -->
        <n-grid :cols="4" :x-gap="16" :y-gap="1">
          <n-grid-item>
            <n-form-item label="销售日期" path="dealDate">
              <n-input
                v-model:value="form.dealDate"
                placeholder="销售日期"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="品牌" path="brand">
              <n-input
                v-model:value="form.brand"
                placeholder="车辆品牌"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车系" path="series">
              <n-input
                v-model:value="form.series"
                placeholder="车辆车系"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="配置" path="configName">
              <n-input
                v-model:value="form.configName"
                placeholder="车辆配置"
                disabled
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="颜色" path="color">
              <n-input
                v-model:value="form.color"
                placeholder="车辆颜色"
                disabled
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="VIN" path="vin" required>
              <n-input
                v-model:value="form.vin"
                placeholder="请选择VIN"
                readonly
              >
                <template #suffix>
                  <n-button
                    type="success"
                    size="tiny"
                    @click="showVinSelector"
                    style="margin-right: 4px"
                  >
                    选择VIN
                  </n-button>
                </template>
              </n-input>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车辆售价(元)" path="salesAmount">
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="form.salesAmount"
                readonly
                style="width: 100%"
                :precision="2"
                :show-button="false"
              />
              <!-- 销售限价验证错误提示 -->
              <div
                v-if="salePriceLimitError"
                style="color: #d03050; font-size: 12px; margin-top: 4px"
              >
                {{ salePriceLimitError }}
              </div>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="现金优惠(元)" path="discountAmount">
              <div style="display: flex; align-items: center; width: 100%">
                <n-input-number
                  :min="amountInputConfig.min"
                  :max="amountInputConfig.max"
                  :step="amountInputConfig.step"
                  v-model:value="form.discountAmount"
                  placeholder="现金优惠"
                  style="flex: 1"
                  :precision="2"
                  button-placement="both"
                  disabled
                />
                <n-checkbox
                  v-model:checked="form.discountDeductible"
                  style="margin-left: 10px; white-space: nowrap; width: 80px"
                  disabled
                  >转车款</n-checkbox
                >
              </div>
            </n-form-item>
          </n-grid-item>
          <!-- 销售限价字段隐藏，但保留在表单数据中用于验证 -->
        </n-grid>
      </div>

      <!-- 客户信息部分 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">客户信息</span>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <n-grid :cols="4" :x-gap="16" :y-gap="6">
          <n-grid-item>
            <n-form-item label="客户名称" path="customerName">
              <n-input
                v-model:value="form.customerName"
                placeholder="客户名称"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="联系电话" path="mobile">
              <n-input
                v-model:value="form.mobile"
                placeholder="联系电话"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售顾问" path="salesAgentName">
              <n-input
                v-model:value="form.salesAgentName"
                placeholder="销售顾问"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售单位" path="salesOrgName">
              <n-input
                v-model:value="form.salesOrgName"
                placeholder="销售单位"
                disabled
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="已付定金(元)" path="depositAmount">
              <div style="display: flex; align-items: center; width: 100%">
                <n-input-number
                  :min="amountInputConfig.min"
                  :max="amountInputConfig.max"
                  :step="amountInputConfig.step"
                  v-model:value="form.depositAmount"
                  placeholder="已付定金"
                  style="flex: 1"
                  :precision="2"
                  button-placement="both"
                  disabled
                />
                <n-checkbox
                  v-model:checked="form.depositDeductible"
                  style="margin-left: 10px; white-space: nowrap; width: 80px"
                  disabled
                  >转车款</n-checkbox
                >
              </div>
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 付款方式部分 -->
      <PaymentMethodSection
        :form="form"
        :loan-channel-options="loanChannelOptions"
        :loan-months-options="loanMonthsOptions"
        :config="paymentMethodSectionConfig"
        @handle-loan-amount-change="handleLoanAmountChange"
        @handle-loan-initial-amount-change="handleLoanInitialAmountChange"
        @handle-loan-fee-change="handleLoanFeeChange"
      />

      <!-- 车辆置换部分 -->
      <VehicleExchangeSection
        :form="form"
        :config="vehicleExchangeSectionConfig"
        @handle-used-vehicle-deductible-amount-change="
          handleUsedVehicleDeductibleAmountChange
        "
        @handle-used-vehicle-discount-payable-amount-change="
          handleUsedVehicleDiscountPayableAmountChange
        "
        @handle-used-vehicle-discount-payable-deductible-change="
          handleUsedVehicleDiscountPayableDeductibleChange
        "
      />

      <!-- 车辆保险部分 -->
      <InsuranceSection :form="form" @update:form="handleFormUpdate" />

      <!-- 专享折扣部分 -->
      <ExclusiveDiscountSection
        :form="form"
        :config="exclusiveDiscountSectionConfig"
        @handle-exclusive-discount-change="handleExclusiveDiscountChange"
        @handle-exclusive-discount-receivable-amount-change="
          handleExclusiveDiscountReceivableAmountChange
        "
        @handle-exclusive-discount-payable-amount-change="
          handleExclusiveDiscountPayableAmountChange
        "
        @handle-exclusive-discount-payable-deductible-change="
          handleExclusiveDiscountPayableDeductibleChange
        "
      />

      <!-- 售前衍生收入部分 -->
      <DerivativeIncomeSection
        :form="form"
        @update:derivativeIncomeTotal="handleDerivativeIncomeTotalChange"
      />

      <!-- 订单备注部分 -->
      <RemarkSection :form="form" ref="remarkSectionRef" />

      <!-- 财务结算部分 -->
      <FinancialSettlementSection
        :form="form"
        :derivativeIncomeTotal="derivativeIncomeTotal"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
        @handle-exclusive-discount-change="handleExclusiveDiscountChange"
      />
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel" :disabled="saving">取消</n-button>
        <n-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          :disabled="saving"
        >
          {{ saving ? "保存中..." : "确定" }}
        </n-button>
      </n-space>
    </template>

    <!-- 车辆选择器 -->
    <VehicleSKUSelector
      v-model:visible="vehicleSelectorVisible"
      @select="handleVehicleSelected"
    />

    <!-- VIN选择器 -->
    <VehicleStocksSelector
      v-model:visible="vinSelectorVisible"
      :multiple="false"
      :initial-keyword="form.skuId ? String(form.skuId) : ''"
      :disable-search="!!form.skuId"
      :filters="{
        stockStatus: 'stocking',
        trial: 'none',
        ...(form.outboundOrgId && { stockOrgId: form.outboundOrgId }),
      }"
      @confirm="handleVinSelected"
      @cancel="handleVinSelectorCancel"
    />
  </n-modal>
</template>

<script setup>
import { ref } from "vue";
import { useOutboundBillModal } from "./OutboundBillModal.js";

// 引入选择器组件
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";
import VehicleStocksSelector from "@/components/inventory/VehicleStocksSelector.vue";

// 引入各个部分组件
import PaymentMethodSection from "@/components/orders/sections/PaymentMethodSection.vue";
import VehicleExchangeSection from "@/components/orders/sections/VehicleExchangeSection.vue";
import InsuranceSection from "@/components/orders/sections/InsuranceSection.vue";
import ExclusiveDiscountSection from "@/components/orders/sections/ExclusiveDiscountSection.vue";
import DerivativeIncomeSection from "@/components/orders/sections/DerivativeIncomeSection.vue";

import RemarkSection from "@/components/orders/sections/RemarkSection.vue";
import FinancialSettlementSection from "@/components/orders/sections/FinancialSettlementSection.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: [String, Number],
    default: null,
  },
  outboundId: {
    type: [String, Number],
    default: null,
  },
  title: {
    type: String,
    default: "出库单详情",
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 衍生收入总金额状态
const derivativeIncomeTotal = ref(0);

// 使用组合式函数
const {
  // 响应式数据
  formRef,
  remarkSectionRef,
  vehicleSelectorVisible,
  vinSelectorVisible,
  selectedOutboundOrg,
  form,
  rules,
  loanChannelOptions,
  loanMonthsOptions,
  modelVisible,
  saving,
  salePriceLimitError: composableSalePriceLimitError,
  amountInputConfig,

  // 区域配置
  paymentMethodSectionConfig,
  vehicleExchangeSectionConfig,
  exclusiveDiscountSectionConfig,

  // 方法
  updateVisible: originalUpdateVisible,
  showVinSelector,
  handleVehicleSelected,
  handleVinSelected,
  handleVinSelectorCancel,
  handleSalePriceChange,
  handleDiscountChange,
  handleExclusiveDiscountChange,
  handleExclusiveDiscountReceivableAmountChange,
  handleExclusiveDiscountPayableAmountChange,
  handleExclusiveDiscountPayableDeductibleChange,
  handleLoanAmountChange,
  handleLoanInitialAmountChange,
  handleLoanFeeChange,
  handleUsedVehicleDeductibleAmountChange,
  handleUsedVehicleDiscountPayableAmountChange,
  handleUsedVehicleDiscountPayableDeductibleChange,
  handleDerivativeIncomeTotalChange: originalHandleDerivativeIncomeTotalChange,
  handleSave,
  handleCancel,
  resetForm,
  setFormData,
} = useOutboundBillModal(props, emit);

// 使用从组合式函数获取的销售限价验证错误信息
const salePriceLimitError = composableSalePriceLimitError;

// 重写 updateVisible 函数，在弹窗关闭时重置表单
const updateVisible = (val) => {
  if (!val) {
    // 当弹窗关闭时，重置表单数据
    resetForm();
  }
  originalUpdateVisible(val);
};

// 包装衍生收入总金额变化处理函数
const handleDerivativeIncomeTotalChange = (total) => {
  derivativeIncomeTotal.value = total;
  originalHandleDerivativeIncomeTotalChange(total);
};

// 处理表单更新
const handleFormUpdate = (updatedForm) => {
  // 通过组合式函数更新表单数据，确保响应式更新
  // 注意：form是一个ref对象，需要更新form.value
  Object.assign(form.value, updatedForm);

  // 如果更新了出库单位信息，同步更新selectedOutboundOrg
  if (updatedForm.outboundOrgId !== undefined) {
    if (updatedForm.outboundOrgId) {
      // 构造出库单位对象
      selectedOutboundOrg.value = {
        id: updatedForm.outboundOrgId,
        orgName: updatedForm.outboundOrgName,
        name: updatedForm.outboundOrgName, // 兼容性字段
      };
    } else {
      selectedOutboundOrg.value = null;
    }
  }
};

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<style lang="scss">
@use "@/components/orders/OrderEditModal.scss";
</style>
