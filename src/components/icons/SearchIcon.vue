<template>
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    xmlns:xlink="http://www.w3.org/1999/xlink" 
    viewBox="0 0 512 512"
    :width="size"
    :height="size"
    :style="{ color: color }"
  >
    <path 
      d="M221.09 64a157.09 157.09 0 1 0 157.09 157.09A157.1 157.1 0 0 0 221.09 64z" 
      fill="none" 
      stroke="currentColor" 
      stroke-miterlimit="10" 
      stroke-width="32"
    />
    <path 
      fill="none" 
      stroke="currentColor" 
      stroke-linecap="round" 
      stroke-miterlimit="10" 
      stroke-width="32" 
      d="M338.29 338.29L448 448"
    />
  </svg>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  size: {
    type: [String, Number],
    default: 20
  },
  color: {
    type: String,
    default: 'currentColor'
  }
})
</script>
