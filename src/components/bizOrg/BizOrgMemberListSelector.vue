<template>
  <div class="biz-org-member-list-selector">
    <n-card title="机构成员" class="member-list-card">
      <template #header-extra>
        <n-space>
          <n-button @click="refreshData" secondary size="small">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>

      <!-- 搜索框 -->
      <div class="search-area">
        <n-input
          v-model:value="searchKeyword"
          placeholder="请输入成员姓名搜索"
          clearable
          @keydown.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <n-icon>
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>
      </div>

      <!-- 成员列表 -->
      <div class="member-list-container">
        <n-scrollbar v-if="!loading && filteredMemberList.length > 0">
          <n-list hoverable clickable>
            <n-list-item
              v-for="member in filteredMemberList"
              :key="member.id"
              :class="{ selected: selectedMemberId === member.id }"
              @click="handleMemberSelect(member)"
            >
              <div class="member-item">
                <!-- 成员信息 -->
                <div class="member-header">
                  <div class="member-name">{{ member.agentName }}</div>
                  <div class="role-tags" v-if="member.businessRole">
                    <n-tag size="small" type="primary" :bordered="false">
                      {{ getRoleDisplayName(member.businessRole) }}
                    </n-tag>
                  </div>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </n-scrollbar>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <n-spin size="medium" />
        </div>

        <!-- 空状态 -->
        <div
          v-if="!loading && filteredMemberList.length === 0"
          class="empty-container"
        >
          <n-empty :description="orgId ? '暂无成员数据' : '请先选择机构'" />
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import {
  NCard,
  NInput,
  NButton,
  NIcon,
  NSpace,
  NList,
  NListItem,
  NTag,
  NScrollbar,
  NSpin,
  NEmpty,
} from "naive-ui";
import { SearchOutline, RefreshOutline } from "@vicons/ionicons5";
import { getBizOrgMembersList } from "@/api/bizOrgMembers";
import {
  getRoleNameById,
  cacheRoles,
  getRolesFromCache,
} from "@/utils/roleCache";
import { getRoles } from "@/api/roles";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  orgId: {
    type: [Number, String],
    default: null,
  },
});

// 定义组件事件
const emit = defineEmits(["select-member"]);

// 组件状态
const loading = ref(false);
const searchKeyword = ref("");
const memberList = ref([]);
const selectedMemberId = ref(null);

// 计算属性：过滤后的成员列表
const filteredMemberList = computed(() => {
  if (!searchKeyword.value) {
    return memberList.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return memberList.value.filter(
    (member) =>
      member.agentName.toLowerCase().includes(keyword) ||
      member.agentId.toLowerCase().includes(keyword)
  );
});

// 获取角色显示名称
const getRoleDisplayName = (roleId) => {
  return getRoleNameById(roleId) || roleId || "未设置";
};

// 获取成员列表
const fetchMemberList = async () => {
  if (!props.orgId) {
    memberList.value = [];
    selectedMemberId.value = null;
    return;
  }

  loading.value = true;
  try {
    const response = await getBizOrgMembersList(props.orgId, {});

    if (response.code === 200) {
      memberList.value = response.data || [];

      // 如果有数据且没有选中的成员，默认选中第一个
      if (memberList.value.length > 0 && !selectedMemberId.value) {
        const firstMember = memberList.value[0];
        handleMemberSelect(firstMember);
      }
    } else {
      messages.error(response.message || "获取成员列表失败");
    }
  } catch (error) {
    console.error("获取成员列表失败:", error);
    messages.error("获取成员列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理成员选择
const handleMemberSelect = (member) => {
  selectedMemberId.value = member.id;
  emit("select-member", member.id, member);
};

// 处理搜索
const handleSearch = () => {
  // 搜索是通过计算属性实现的，这里不需要额外操作
};

// 刷新数据
const refreshData = () => {
  fetchMemberList();
};

// 监听 orgId 变化
watch(
  () => props.orgId,
  (newOrgId) => {
    if (newOrgId) {
      fetchMemberList();
    } else {
      memberList.value = [];
      selectedMemberId.value = null;
    }
  },
  { immediate: true }
);

// 初始化角色缓存
const initializeRoleCache = async () => {
  try {
    // 先尝试从缓存获取角色数据
    const cachedRoles = getRolesFromCache();
    if (cachedRoles && cachedRoles.length > 0) {
      console.log("角色缓存已存在:", cachedRoles.length, "个角色");
      return;
    }

    // 如果缓存为空，从API获取角色数据
    console.log("角色缓存为空，从API获取角色数据...");
    const response = await getRoles();
    if (response.code === 200 && response.data) {
      // 将API返回的数据格式转换为缓存需要的格式
      const formattedRoles = response.data.map((role) => ({
        label: role.roleName,
        value: role.id,
      }));

      // 缓存角色数据到localStorage
      cacheRoles(formattedRoles);
      console.log("角色数据已缓存:", formattedRoles.length, "个角色");
    } else {
      console.error("获取角色列表失败:", response.message);
    }
  } catch (error) {
    console.error("初始化角色缓存失败:", error);
  }
};

// 组件挂载时初始化角色缓存
onMounted(() => {
  initializeRoleCache();
});

// 暴露方法给父组件
defineExpose({
  refreshData,
  getCurrentSelectedMemberId: () => selectedMemberId.value,
});
</script>

<style lang="scss" scoped>
.biz-org-member-list-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .member-list-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.n-card__header) {
      flex-shrink: 0;
    }

    :deep(.n-card__content) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
      overflow: hidden;
      min-height: 0; // 关键：允许内容收缩
    }
  }

  .search-area {
    flex-shrink: 0; // 搜索区域不收缩
    margin-bottom: 16px;
  }

  .member-list-container {
    flex: 1;
    min-height: 0; // 关键：允许容器收缩
    overflow: hidden;

    :deep(.n-scrollbar) {
      height: 100%;
      max-height: 100%;
    }
  }

  .member-item {
    width: 100%;

    .member-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 8px;

      .member-name {
        font-weight: 500;
        color: var(--n-text-color);
        font-size: 14px;
        flex: 1;
      }

      .role-tags {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        flex-shrink: 0;
      }
    }
  }

  :deep(.n-list-item) {
    &.selected {
      background-color: rgba(24, 160, 88, 0.1);
      border-left: 3px solid #18a058;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .loading-container,
  .empty-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
}
</style>
