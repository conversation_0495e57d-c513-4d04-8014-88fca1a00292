<template>
  <n-modal
    :show="modelVisible"
    @update:show="updateVisible"
    :title="title || '选择业务机构成员'"
    preset="card"
    :style="{
      width: '1200px',
      maxWidth: '95%',
      height: 'min(80vh, 800px)',
      maxHeight: 'min(80vh, 800px)',
    }"
    :mask-closable="false"
    :auto-focus="false"
    class="biz-org-member-selector-modal"
  >
    <div class="biz-org-member-selector-content">
      <!-- 左右布局容器 -->
      <div class="layout-container">
        <!-- 左侧：机构列表 -->
        <div class="left-panel">
          <biz-org-list-selector
            ref="orgSelectorRef"
            @select-org="handleOrgSelect"
          />
        </div>

        <!-- 右侧：成员列表 -->
        <div class="right-panel">
          <!-- 已选择成员显示 -->
          <div
            v-if="mode === 'multiple' && selectedMembers.length > 0"
            class="selected-members-area"
          >
            <n-card size="small" class="selected-members-card">
              <n-text strong
                >已选择 {{ selectedMembers.length }} 个成员：</n-text
              >
              <div class="selected-tags">
                <n-tag
                  v-for="member in selectedMembers"
                  :key="member.id"
                  type="success"
                  closable
                  @close="removeMember(member)"
                >
                  {{ member.agentName }} ({{ member.agentId }})
                </n-tag>
              </div>
            </n-card>
          </div>

          <!-- 成员列表选择器组件 -->
          <div class="member-selector-container">
            <biz-org-member-list-selector
              ref="memberSelectorRef"
              :org-id="currentOrgId"
              @select-member="handleMemberSelectorSelect"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="selectedMembers.length === 0"
          @click="handleConfirm"
        >
          确定
          {{ selectedMembers.length > 0 ? `(${selectedMembers.length})` : "" }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { NModal, NCard, NButton, NSpace, NTag, NText } from "naive-ui";
import BizOrgListSelector from "./BizOrgListSelector.vue";
import BizOrgMemberListSelector from "./BizOrgMemberListSelector.vue";
import { getRoles } from "@/api/roles";
import { cacheRoles, getRolesFromCache } from "@/utils/roleCache";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: "single", // 'single' | 'multiple'
    validator: (value) => ["single", "multiple"].includes(value),
  },
  initialOrgId: {
    type: [Number, String],
    default: null,
  },
  title: {
    type: String,
    default: "选择业务机构成员",
  },
});

// 定义组件事件
const emit = defineEmits(["select", "cancel", "update:visible"]);

// 组件状态
const orgSelectorRef = ref(null);
const memberSelectorRef = ref(null);
const selectedMembers = ref([]);
const currentOrgId = ref(null);
const currentOrgName = ref("");

// 计算属性：控制弹窗显示
const modelVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 更新弹窗显示状态
const updateVisible = (value) => {
  emit("update:visible", value);
};

// 处理机构选择
const handleOrgSelect = (orgId, orgInfo) => {
  currentOrgId.value = orgId;
  currentOrgName.value = orgInfo?.orgName || "";

  // 清空之前的选择
  selectedMembers.value = [];
};

// 处理成员选择器的成员选择
const handleMemberSelectorSelect = (memberId, memberInfo) => {
  if (props.mode === "single") {
    selectedMembers.value = [formatMemberData(memberInfo)];
  } else {
    // 多选模式：检查是否已选中
    const isAlreadySelected = selectedMembers.value.some(
      (selected) => selected.id === memberInfo.id
    );

    if (!isAlreadySelected) {
      selectedMembers.value.push(formatMemberData(memberInfo));
    }
  }
};

// 移除成员
const removeMember = (member) => {
  selectedMembers.value = selectedMembers.value.filter(
    (selected) => selected.id !== member.id
  );
};

// 格式化成员数据
const formatMemberData = (member) => {
  return {
    id: member.id,
    name: member.agentName,
    agentId: member.agentId,
    agentName: member.agentName,
    biz_org_id: member.orgId || currentOrgId.value,
    orgName: currentOrgName.value, // 添加机构名称
    businessRole: member.businessRole,
    dataPermissions: member.dataPermissions,
    dataRange: member.dataRange,
    dataRangeNames: member.dataRangeNames,
  };
};

// 处理确认
const handleConfirm = () => {
  const result =
    props.mode === "single"
      ? selectedMembers.value[0] || null
      : selectedMembers.value;

  emit("select", result);
  emit("update:visible", false);
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  emit("update:visible", false);
};

// 初始化角色缓存
const initializeRoleCache = async () => {
  try {
    // 先尝试从缓存获取角色数据
    const cachedRoles = getRolesFromCache();
    if (cachedRoles && cachedRoles.length > 0) {
      console.log("角色缓存已存在:", cachedRoles.length, "个角色");
      return;
    }

    // 如果缓存为空，从API获取角色数据
    console.log("角色缓存为空，从API获取角色数据...");
    const response = await getRoles();
    if (response.code === 200 && response.data) {
      // 将API返回的数据格式转换为缓存需要的格式
      const formattedRoles = response.data.map((role) => ({
        label: role.roleName,
        value: role.id,
      }));

      // 缓存角色数据到localStorage
      cacheRoles(formattedRoles);
      console.log("角色数据已缓存:", formattedRoles.length, "个角色");
    } else {
      console.error("获取角色列表失败:", response.message);
    }
  } catch (error) {
    console.error("初始化角色缓存失败:", error);
  }
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 重置状态
      selectedMembers.value = [];

      // 初始化角色缓存
      initializeRoleCache();

      // 如果有初始机构ID，则选中该机构
      if (props.initialOrgId) {
        // 等待组件渲染完成后设置初始机构
        setTimeout(() => {
          handleOrgSelect(props.initialOrgId, { orgName: "指定机构" });
        }, 100);
      }
    }
  }
);

// 组件挂载时初始化角色缓存
onMounted(() => {
  initializeRoleCache();
});
</script>

<style lang="scss" scoped>
.biz-org-member-selector-modal {
  :deep(.n-modal) {
    display: flex;
    flex-direction: column;
    height: min(80vh, 800px);
    max-height: min(80vh, 800px);
  }

  :deep(.n-modal__content) {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  :deep(.n-card) {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  :deep(.n-card__header) {
    flex-shrink: 0;
    padding: 16px 16px 0 16px;
  }

  :deep(.n-card__content) {
    padding: 16px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0; // 关键：允许内容收缩
  }

  :deep(.n-card__footer) {
    flex-shrink: 0;
    padding: 16px;
    border-top: 1px solid var(--n-border-color);
    background-color: var(--n-card-color);
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

.biz-org-member-selector-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: calc(min(80vh, 800px) - 120px); // 减去header和footer的高度
}

.layout-container {
  display: flex;
  flex: 1;
  gap: 16px;
  overflow: hidden; // 防止容器溢出
  min-height: 0; // 允许flex子项收缩
}

.left-panel {
  width: 400px;
  flex-shrink: 0;
  border-right: 1px solid var(--n-border-color);
  padding-right: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; // 关键：允许面板收缩

  // 确保BizOrgListSelector组件内部滚动
  :deep(.biz-org-list-selector) {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;

    .org-list-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .n-card__header {
        flex-shrink: 0;
      }

      .n-card__content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 16px;
        min-height: 0; // 关键：允许内容收缩
      }
    }

    .search-area {
      flex-shrink: 0; // 搜索区域不收缩
      margin-bottom: 16px;
    }

    .org-list-container {
      flex: 1;
      min-height: 0; // 关键：允许容器收缩
      overflow: hidden;

      .n-scrollbar {
        height: 100%;
        max-height: 100%;
      }
    }

    .loading-container,
    .empty-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.right-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; // 关键：允许面板收缩
  gap: 12px;
}

.selected-members-area {
  flex-shrink: 0; // 已选成员区域不收缩
  max-height: 120px; // 限制已选成员区域的最大高度
  overflow-y: auto; // 如果已选成员太多，允许滚动

  .selected-members-card {
    :deep(.n-card__content) {
      padding: 12px;
    }
  }

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
  }
}

.member-selector-container {
  flex: 1;
  min-height: 0; // 关键：允许容器收缩
  overflow: hidden;

  // 确保BizOrgMemberListSelector组件内部滚动
  :deep(.biz-org-member-list-selector) {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;

    .member-list-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .n-card__header {
        flex-shrink: 0;
      }

      .n-card__content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 16px;
        min-height: 0; // 关键：允许内容收缩
      }
    }

    .search-area {
      flex-shrink: 0; // 搜索区域不收缩
      margin-bottom: 16px;
    }

    .member-list-container {
      flex: 1;
      min-height: 0; // 关键：允许容器收缩
      overflow: hidden;

      .n-scrollbar {
        height: 100%;
        max-height: 100%;
      }
    }

    .loading-container,
    .empty-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 大屏幕优化
@media (min-width: 1920px) {
  .biz-org-member-selector-modal {
    :deep(.n-modal) {
      height: min(75vh, 750px); // 大屏幕下稍微减小高度
      max-height: min(75vh, 750px);
    }
  }

  .biz-org-member-selector-content {
    max-height: calc(min(75vh, 750px) - 120px);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .biz-org-member-selector-modal {
    :deep(.n-modal) {
      height: min(90vh, 700px);
      max-height: min(90vh, 700px);
    }
  }

  .layout-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    width: 100%;
    height: 250px; // 固定高度，避免计算问题
    max-height: 250px;
    border-right: none;
    border-bottom: 1px solid var(--n-border-color);
    padding-right: 0;
    padding-bottom: 16px;
    margin-bottom: 16px;
  }

  .right-panel {
    width: 100%;
    flex: 1;
    min-height: 300px; // 最小高度保证可用性
  }
}
</style>
