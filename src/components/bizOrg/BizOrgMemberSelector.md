# BizOrgMemberSelector 业务机构成员选择器

## 组件描述

`BizOrgMemberSelector` 是一个用于查询和选择业务机构成员的组件，采用左右结构的弹窗设计：
- **左侧**：业务机构列表（使用 `BizOrgListSelector` 组件）
- **右侧**：选中机构的成员列表，支持搜索和选择

## 功能特性

- ✅ 左右布局的弹窗设计
- ✅ 支持单选/多选模式
- ✅ 可指定初始机构ID
- ✅ 成员列表支持搜索（按姓名或员工ID）
- ✅ 支持双击快速选择（单选模式）
- ✅ 多选模式下显示已选择成员标签
- ✅ 响应式设计，适配移动端
- ✅ 完整的加载和空状态处理

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `visible` | `Boolean` | `false` | 控制弹窗显示/隐藏 |
| `mode` | `String` | `'single'` | 选择模式：`'single'` 单选 / `'multiple'` 多选 |
| `initialOrgId` | `Number/String` | `null` | 初始指定的机构ID |
| `title` | `String` | `'选择业务机构成员'` | 弹窗标题 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `select` | `memberData` | 选择成员时触发，返回选中的成员信息 |
| `cancel` | - | 取消选择时触发 |
| `update:visible` | `Boolean` | 更新弹窗显示状态 |

## 返回数据格式

### 单选模式 (mode="single")
```javascript
{
  id: 1,                    // 成员ID
  name: "张三",             // 成员姓名
  agentId: "EMP001",        // 员工ID
  agentName: "张三",        // 员工姓名
  biz_org_id: 1,           // 所属机构ID
  orgName: "长安汽车济南总店", // 所属机构名称
  businessRole: "sales_manager",     // 业务角色
  dataPermissions: "store_level",    // 数据权限
  dataRange: "1,2",                  // 数据范围
  dataRangeNames: "长安汽车济南总店, 深蓝德州分店"  // 数据范围名称
}
```

### 多选模式 (mode="multiple")
```javascript
[
  {
    id: 1,
    name: "张三",
    agentId: "EMP001",
    // ... 其他字段同单选模式
  },
  {
    id: 2,
    name: "李四",
    agentId: "EMP002",
    // ... 其他字段同单选模式
  }
]
```

## 基本用法

### 1. 单选模式
```vue
<template>
  <div>
    <n-button @click="showMemberSelector">选择成员</n-button>
    
    <biz-org-member-selector
      v-model:visible="memberSelectorVisible"
      mode="single"
      @select="handleMemberSelected"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BizOrgMemberSelector from '@/components/bizOrg/BizOrgMemberSelector.vue'

const memberSelectorVisible = ref(false)

const showMemberSelector = () => {
  memberSelectorVisible.value = true
}

const handleMemberSelected = (member) => {
  console.log('选中的成员:', member)
  // member 是单个成员对象
}

const handleCancel = () => {
  console.log('取消选择')
}
</script>
```

### 2. 多选模式
```vue
<template>
  <biz-org-member-selector
    v-model:visible="memberSelectorVisible"
    mode="multiple"
    title="选择多个成员"
    @select="handleMembersSelected"
  />
</template>

<script setup>
const handleMembersSelected = (members) => {
  console.log('选中的成员列表:', members)
  // members 是成员对象数组
}
</script>
```

### 3. 指定初始机构
```vue
<template>
  <biz-org-member-selector
    v-model:visible="memberSelectorVisible"
    mode="multiple"
    :initial-org-id="1"
    title="选择指定机构的成员"
    @select="handleMembersSelected"
  />
</template>
```

## 交互说明

### 左侧机构列表
- 点击机构名称选中机构
- 支持搜索机构名称
- 选中机构后右侧显示该机构的成员列表

### 右侧成员列表
- **单选模式**：
  - 点击成员行选中
  - 双击成员行直接确认选择
  - 选中的成员显示绿色背景和勾选图标
- **多选模式**：
  - 点击成员行或复选框进行多选
  - 顶部显示已选择成员的标签
  - 可点击标签的关闭按钮移除选择

### 搜索功能
- 支持按成员姓名或员工ID搜索
- 实时过滤显示结果

## 样式定制

组件使用了 SCSS 变量，可以通过 CSS 变量进行主题定制：

```scss
.biz-org-member-selector-modal {
  // 自定义弹窗样式
}

.member-item {
  // 自定义成员项样式
}
```

## 注意事项

1. 组件依赖 `BizOrgListSelector` 组件和相关API
2. 需要确保 `getBizOrgMembersList` API 接口可用
3. 角色显示依赖 `getRoleNameById` 工具函数
4. 组件会自动处理加载状态和错误提示
5. 在移动端会自动切换为垂直布局

## 示例页面

可以查看 `src/views/examples/BizOrgMemberSelectorExample.vue` 获取完整的使用示例。
