import { ref, computed, h, markRaw } from "vue";
import {
  NIcon,
  NTag,
  NInput,
  NSelect,
  NButton,
  NForm,
  NFormItem,
  NSpace,
  useDialog,
} from "naive-ui";
import {
  SearchOutline,
  CreateOutline,
  TrashOutline,
  CopyOutline,
  CheckmarkOutline,
  CloseOutline,
} from "@vicons/ionicons5";
import messages from "@/utils/messages";
import bizOrgMembersApi from "@/api/bizOrgMembers";
import { getRoles } from "@/api/roles";
import MemberSelector from "@/components/users/MemberSelector.vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { createColumns } from "./BizOrgMembersModalColumns.js";
import { cacheRoles, getRolesFromCache } from "@/utils/roleCache";

// 使用 markRaw 包装图标组件
const SearchOutlineIcon = markRaw(SearchOutline);
const CreateOutlineIcon = markRaw(CreateOutline);
const TrashOutlineIcon = markRaw(TrashOutline);
const CopyOutlineIcon = markRaw(CopyOutline);
const CheckmarkOutlineIcon = markRaw(CheckmarkOutline);
const CloseOutlineIcon = markRaw(CloseOutline);

export function useBizOrgMembersModal() {
  // 使用 Naive UI 的对话框
  const dialog = useDialog();

  // 状态变量
  const visible = ref(false);
  const loading = ref(false);
  const currentOrgId = ref(null);
  const currentOrgName = ref("");
  const searchKeywords = ref("");
  const tableRef = ref(null);
  const selectedMembers = ref([]);
  const editingRowId = ref(null); // 当前编辑的行ID

  // 成员数据
  const membersData = ref([]);

  // 复选框相关状态
  const checkedRowKeys = ref([]);

  // 批量授权相关状态
  const showBatchAuthModal = ref(false);
  const batchAuthLoading = ref(false);
  const batchAuthFormRef = ref(null);
  const batchAuthForm = ref({
    businessRole: null,
    dataRange: null,
  });

  // 数据范围选择器相关状态
  const showDataRangeSelector = ref(false);
  const selectedDataRangeOrgs = ref([]);
  const dataRangeOrgNames = ref("");

  // 业务角色选项 - 从API获取
  const businessRoleOptions = ref([]);

  // 计算属性
  const modalTitle = computed(() => {
    return currentOrgName.value ? `${currentOrgName.value}-成员管理` : "成员管理";
  });

  // 获取业务角色选项
  async function fetchBusinessRoles() {
    try {
      // 先尝试从缓存获取角色数据
      const cachedRoles = getRolesFromCache();
      if (cachedRoles && cachedRoles.length > 0) {
        businessRoleOptions.value = cachedRoles;
        console.log("从缓存加载角色数据:", cachedRoles.length, "个角色");
      }

      // 无论是否有缓存，都获取最新数据以保持数据同步
      const response = await getRoles();
      if (response.code === 200 && response.data) {
        // 将API返回的数据格式转换为组件需要的格式
        const formattedRoles = response.data.map(role => ({
          label: role.roleName,
          value: role.id
        }));

        // 更新组件状态
        businessRoleOptions.value = formattedRoles;

        // 缓存最新的角色数据到localStorage
        cacheRoles(formattedRoles);

        console.log("从API获取并缓存角色数据:", formattedRoles.length, "个角色");
      } else {
        console.error("获取角色列表失败:", response.message);
        // 如果API失败但有缓存数据，则继续使用缓存数据
        if (!cachedRoles || cachedRoles.length === 0) {
          messages.error("获取角色列表失败");
        }
      }
    } catch (error) {
      console.error("获取角色列表失败:", error);
      // 如果API失败但有缓存数据，则继续使用缓存数据
      const cachedRoles = getRolesFromCache();
      if (!cachedRoles || cachedRoles.length === 0) {
        messages.error("获取角色列表失败，请稍后重试");
      }
    }
  }

  // 复制文本到剪贴板
  function copyToClipboard(text) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success("已复制到剪贴板");
      })
      .catch((err) => {
        console.error("复制失败:", err);
        messages.error("复制失败");
      });
  }

  // 打开弹窗
  function openModal(orgId, orgName) {
    currentOrgId.value = orgId;
    currentOrgName.value = orgName;

    // 重置搜索框状态
    searchKeywords.value = "";

    // 重置其他状态
    checkedRowKeys.value = [];
    editingRowId.value = null;
    showBatchAuthModal.value = false;
    batchAuthForm.value = {
      businessRole: null,
      dataRange: null,
    };

    // 重置数据范围选择器状态
    showDataRangeSelector.value = false;
    selectedDataRangeOrgs.value = [];
    dataRangeOrgNames.value = "";

    visible.value = true;

    // 获取业务角色选项
    fetchBusinessRoles();

    refreshData();
  }

  // 关闭弹窗
  function handleClose() {
    visible.value = false;
    searchKeywords.value = "";
    membersData.value = [];
    selectedMembers.value = [];
    checkedRowKeys.value = [];
    showBatchAuthModal.value = false;
    batchAuthForm.value = {
      businessRole: null,
      dataRange: null,
    };

    // 重置数据范围选择器状态
    showDataRangeSelector.value = false;
    selectedDataRangeOrgs.value = [];
    dataRangeOrgNames.value = "";
  }

  // 刷新数据
  async function refreshData() {
    if (!currentOrgId.value) return;

    loading.value = true;
    try {
      const params = {};

      if (searchKeywords.value) {
        params.keywords = searchKeywords.value;
      }

      const response = await bizOrgMembersApi.getBizOrgMembersList(
        currentOrgId.value,
        params
      );

      if (response.code === 200) {
        membersData.value = response.data || [];
      } else {
        messages.error(response.message || "数据加载失败");
      }
    } catch (error) {
      console.error("加载成员数据失败:", error);
      messages.error("加载数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  }

  // 处理搜索
  function handleSearch() {
    refreshData();
  }

  // 处理成员选择器选择变化
  async function handleMembersSelected(members) {
    if (!members || members.length === 0) {
      return;
    }

    try {
      loading.value = true;

      // 过滤掉已存在的成员
      const newMembers = members.filter((member) => {
        // 确保 membersData.value 是数组
        if (!Array.isArray(membersData.value)) {
          return true;
        }
        const existingMember = membersData.value.find(
          (m) => m.agentId === member.id
        );
        return !existingMember;
      });

      if (newMembers.length === 0) {
        messages.warning("所选成员已存在于当前列表中");
        selectedMembers.value = [];
        loading.value = false;
        return;
      }

      // 准备批量创建的成员数据
      const membersToCreate = newMembers.map((member) => ({
        orgId: currentOrgId.value,
        agentId: member.id,
        agentName: member.name,
        position: member.position, // 添加职务信息
        businessRole: null, // 默认为空，需要后续设置
        dataRange: currentOrgId.value.toString(), // 默认设置为当前机构ID
        dataRangeNames: currentOrgName.value, // 默认设置为当前机构名称
        status: "active",
      }));

      // 批量创建成员记录
      const response = await bizOrgMembersApi.createBizOrgMembersBatch(
        membersToCreate
      );
      if (response.code === 200) {
        // 批量创建成功，刷新数据获取最新列表
        messages.success(`成功添加 ${membersToCreate.length} 名员工`);
        await refreshData();
      } else {
        messages.error(response.message || "添加员工失败");
      }

      // 清空选择器
      selectedMembers.value = [];
    } catch (error) {
      console.error("添加员工失败:", error);
      messages.error("添加员工失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  }

  // 编辑行
  function handleEditRow(row) {
    // 保存原始数据用于取消时恢复
    row._originalData = {
      businessRole: row.businessRole,
      dataRange: row.dataRange,
      dataRangeNames: row.dataRangeNames,
    };

    // 初始化编辑行的选中机构数据
    if (row.dataRange && row.dataRangeNames) {
      const orgIds = row.dataRange.split(",");
      const orgNames = row.dataRangeNames.split(",");
      row._selectedDataRangeOrgs = orgIds.map((id, index) => ({
        id: id.trim(),
        orgName: orgNames[index] ? orgNames[index].trim() : `机构${id}`,
      }));
    } else {
      row._selectedDataRangeOrgs = [];
    }

    editingRowId.value = row.id;
  }

  // 保存编辑
  async function handleSaveEdit(row) {
    try {
      const updateData = {
        id: row.id,
        agentId: row.agentId,
        businessRole: row.businessRole,
        dataRange: row.dataRange,
        dataRangeNames: row.dataRangeNames,
      };

      const response = await bizOrgMembersApi.updateBizOrgMember(updateData);

      if (response.code === 200) {
        messages.success("更新成功");
        editingRowId.value = null;
        delete row._originalData;
        await refreshData();
      } else {
        messages.error(response.message || "更新失败");
      }
    } catch (error) {
      console.error("更新成员失败:", error);
      messages.error("更新失败，请稍后重试");
    }
  }

  // 取消编辑
  function handleCancelEdit(row) {
    if (row._originalData) {
      // 恢复原始数据
      row.businessRole = row._originalData.businessRole;
      row.dataRange = row._originalData.dataRange;
      row.dataRangeNames = row._originalData.dataRangeNames;
      delete row._originalData;

      // 重新初始化选中机构数据
      if (row.dataRange && row.dataRangeNames) {
        const orgIds = row.dataRange.split(",");
        const orgNames = row.dataRangeNames.split(",");
        row._selectedDataRangeOrgs = orgIds.map((id, index) => ({
          id: id.trim(),
          orgName: orgNames[index] ? orgNames[index].trim() : `机构${id}`,
        }));
      } else {
        row._selectedDataRangeOrgs = [];
      }
    }
    editingRowId.value = null;
  }

  // 删除成员
  async function handleDeleteMember(memberId) {
    // 获取成员信息用于确认对话框
    const member = membersData.value.find(m => m.id === memberId);
    const memberName = member ? member.agentName : '该成员';

    // 显示确认对话框
    const confirmed = await new Promise((resolve) => {
      dialog.warning({
        title: "确认删除",
        content: `确定要删除成员 "${memberName}" 吗？此操作不可撤销。`,
        positiveText: "确认删除",
        negativeText: "取消",
        onPositiveClick: () => {
          resolve(true);
        },
        onNegativeClick: () => {
          resolve(false);
        },
      });
    });

    if (!confirmed) {
      return;
    }

    try {
      const response = await bizOrgMembersApi.deleteBizOrgMember(memberId);
      if (response.code === 200) {
        messages.success("删除成功");
        await refreshData();
      } else {
        messages.error(response.message || "删除失败");
      }
    } catch (error) {
      console.error("删除成员失败:", error);
      messages.error("删除失败，请稍后重试");
    }
  }

  // 批量删除成员
  async function handleBatchDelete() {
    if (checkedRowKeys.value.length === 0) {
      messages.warning("请先选择要删除的成员");
      return;
    }

    // 显示确认对话框
    const confirmed = await new Promise((resolve) => {
      dialog.warning({
        title: "确认删除",
        content: `确定要删除选中的 ${checkedRowKeys.value.length} 名成员吗？此操作不可撤销。`,
        positiveText: "确认删除",
        negativeText: "取消",
        onPositiveClick: () => {
          resolve(true);
        },
        onNegativeClick: () => {
          resolve(false);
        },
      });
    });

    if (!confirmed) {
      return;
    }

    try {
      // 将选中的ID数组转换为逗号分割的字符串
      const idsString = checkedRowKeys.value.join(",");

      const response = await bizOrgMembersApi.deleteBizOrgMembersBatch(idsString);
      if (response.code === 200) {
        messages.success(`成功删除 ${checkedRowKeys.value.length} 名成员`);

        // 清空选中状态
        checkedRowKeys.value = [];

        // 刷新数据
        await refreshData();
      } else {
        messages.error(response.message || "批量删除失败");
      }
    } catch (error) {
      console.error("批量删除成员失败:", error);
      messages.error("批量删除失败，请稍后重试");
    }
  }

  // 处理复选框变化
  function handleCheckedRowKeysChange(keys) {
    checkedRowKeys.value = keys;
  }

  // 批量授权
  async function handleBatchAuth() {
    if (checkedRowKeys.value.length === 0) {
      messages.warning("请先选择要授权的成员");
      return;
    }

    if (!batchAuthForm.value.businessRole && !batchAuthForm.value.dataRange) {
      messages.warning("请至少选择一项要修改的权限");
      return;
    }

    try {
      batchAuthLoading.value = true;

      // 准备批量更新的成员数据
      const membersToUpdate = checkedRowKeys.value.map((memberId) => {
        // 找到对应的成员数据以获取agentId
        const memberData = membersData.value.find(m => m.id === memberId);

        const updateData = {
          id: memberId,
          agentId: memberData ? memberData.agentId : null,
        };

        // 只更新用户选择的字段
        if (batchAuthForm.value.businessRole) {
          updateData.businessRole = batchAuthForm.value.businessRole;
        }

        if (batchAuthForm.value.dataRange) {
          updateData.dataRange = batchAuthForm.value.dataRange;
          updateData.dataRangeNames = dataRangeOrgNames.value;
        }

        return updateData;
      });

      // 批量更新成员记录
      const response = await bizOrgMembersApi.updateBizOrgMembersBatch(
        membersToUpdate
      );
      if (response.code !== 200) {
        messages.error(response.message || "批量授权失败");
        return;
      }

      messages.success(`成功为 ${checkedRowKeys.value.length} 名成员授权`);

      // 关闭弹窗并重置表单
      showBatchAuthModal.value = false;
      batchAuthForm.value = {
        businessRole: null,
        dataRange: null,
      };

      // 重置数据范围选择器状态
      selectedDataRangeOrgs.value = [];
      dataRangeOrgNames.value = "";

      // 清空选中状态
      checkedRowKeys.value = [];

      // 刷新数据
      await refreshData();
    } catch (error) {
      console.error("批量授权失败:", error);
      messages.error("批量授权失败，请稍后重试");
    } finally {
      batchAuthLoading.value = false;
    }
  }

  // 统一的机构选择器处理函数
  let currentEditingRow = null;

  function handleOrgSelectorSelect(orgs) {
    if (currentEditingRow) {
      // 单行编辑模式
      handleSingleRowDataRangeSelect(orgs);
    } else {
      // 批量授权模式
      handleDataRangeOrgSelect(orgs);
    }
  }

  // 数据范围选择器相关函数
  function handleDataRangeOrgSelect(orgs) {
    if (orgs && orgs.length > 0) {
      // 直接替换选中的机构列表
      selectedDataRangeOrgs.value = [...orgs];

      // 更新显示的机构名称和数据范围字符串
      updateDataRangeDisplay();
    }

    // 关闭选择器
    showDataRangeSelector.value = false;
  }

  function handleDataRangeSelectorCancel() {
    showDataRangeSelector.value = false;
  }

  function clearDataRangeSelection() {
    selectedDataRangeOrgs.value = [];
    dataRangeOrgNames.value = "";
    batchAuthForm.value.dataRange = null;
  }

  function updateDataRangeDisplay() {
    if (selectedDataRangeOrgs.value.length === 0) {
      dataRangeOrgNames.value = "";
      batchAuthForm.value.dataRange = null;
      return;
    }

    // 生成机构名称字符串
    const orgNames = selectedDataRangeOrgs.value
      .map((org) => org.orgName)
      .join(", ");
    dataRangeOrgNames.value = orgNames;

    // 生成机构ID字符串（逗号分隔）
    const orgIds = selectedDataRangeOrgs.value.map((org) => org.id).join(",");
    batchAuthForm.value.dataRange = orgIds;
  }

  // 为单行编辑打开数据范围选择器
  function openDataRangeSelector(row) {
    currentEditingRow = row;

    // 如果行已有数据范围，预设选中的机构
    if (row.dataRange) {
      // 这里需要根据dataRange字符串解析出机构信息
      // 由于我们只有ID，需要从已有数据中查找机构信息
      // 简化处理：清空当前选择，让用户重新选择
      selectedDataRangeOrgs.value = [];
      dataRangeOrgNames.value = "";
    }

    showDataRangeSelector.value = true;
  }

  function handleSingleRowDataRangeSelect(orgs) {
    if (currentEditingRow && orgs && orgs.length > 0) {
      // 为单行设置数据范围 - 支持多选
      const orgIds = orgs.map((org) => org.id).join(",");
      const orgNames = orgs.map((org) => org.orgName).join(", ");

      currentEditingRow.dataRange = orgIds;
      currentEditingRow.dataRangeNames = orgNames;

      // 更新编辑行的选中机构数据
      currentEditingRow._selectedDataRangeOrgs = [...orgs];
    }

    showDataRangeSelector.value = false;
    currentEditingRow = null;
  }

  // 移除单行编辑中选中的机构
  function removeSelectedOrg(row, orgId) {
    if (!row._selectedDataRangeOrgs) return;

    row._selectedDataRangeOrgs = row._selectedDataRangeOrgs.filter(org => org.id !== orgId);

    // 更新数据范围字符串
    const orgIds = row._selectedDataRangeOrgs.map(org => org.id).join(",");
    const orgNames = row._selectedDataRangeOrgs.map(org => org.orgName).join(", ");

    row.dataRange = orgIds;
    row.dataRangeNames = orgNames;
  }

  // 清空单行编辑的数据范围
  function clearRowDataRange(row) {
    row._selectedDataRangeOrgs = [];
    row.dataRange = "";
    row.dataRangeNames = "";
  }

  // 移除批量授权中选中的机构
  function removeSelectedDataRangeOrg(orgId) {
    selectedDataRangeOrgs.value = selectedDataRangeOrgs.value.filter(org => org.id !== orgId);
    updateDataRangeDisplay();
  }

  // 创建表格列配置
  const columns = createColumns({
    editingRowId,
    businessRoleOptions,
    copyToClipboard,
    handleSaveEdit,
    handleCancelEdit,
    handleEditRow,
    handleDeleteMember,
    openDataRangeSelector,
    removeSelectedOrg,
    clearRowDataRange,
  });

  return {
    // 图标组件
    SearchOutlineIcon,
    CreateOutlineIcon,
    TrashOutlineIcon,
    CopyOutlineIcon,
    CheckmarkOutlineIcon,
    CloseOutlineIcon,

    // 状态变量
    visible,
    loading,
    currentOrgId,
    currentOrgName,
    searchKeywords,
    tableRef,
    selectedMembers,
    editingRowId,
    membersData,
    checkedRowKeys,
    showBatchAuthModal,
    batchAuthLoading,
    batchAuthFormRef,
    batchAuthForm,
    showDataRangeSelector,
    selectedDataRangeOrgs,
    dataRangeOrgNames,
    businessRoleOptions,
    modalTitle,

    // 数据
    columns,

    // 方法
    copyToClipboard,
    openModal,
    handleClose,
    refreshData,
    handleSearch,
    handleMembersSelected,
    handleEditRow,
    handleSaveEdit,
    handleCancelEdit,
    handleDeleteMember,
    handleBatchDelete,
    handleCheckedRowKeysChange,
    handleBatchAuth,
    handleOrgSelectorSelect,
    handleDataRangeOrgSelect,
    handleDataRangeSelectorCancel,
    clearDataRangeSelection,
    updateDataRangeDisplay,
    openDataRangeSelector,
    handleSingleRowDataRangeSelect,
    removeSelectedOrg,
    clearRowDataRange,
    removeSelectedDataRangeOrg,

    // 组件
    MemberSelector,
    BizOrgSelector,
  };
}
