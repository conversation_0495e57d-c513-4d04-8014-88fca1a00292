<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">时间信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="创建时间">
          <div class="detail-item">
            {{ formatDateTime(orderDetail.createTime) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="更新时间">
          <div class="detail-item">
            {{ formatDateTime(orderDetail.updateTime) }}
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider } from "naive-ui";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "未设置";
  if (typeof dateStr === "number") {
    return new Date(dateStr).toLocaleString("zh-CN");
  }
  return dateStr;
};
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}
</style>
