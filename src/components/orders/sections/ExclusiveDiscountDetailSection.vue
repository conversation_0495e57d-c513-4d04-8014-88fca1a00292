<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">专项优惠</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="专项优惠类型">
          <div class="detail-item">
            {{
              getExclusiveDiscountTypeText(orderDetail.exclusiveDiscountType)
            }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="应收-厂家-专项优惠">
          <div class="detail-item money">
            {{
              renderReceivableAmount(
                orderDetail.exclusiveDiscountReceivableAmount
              )
            }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="应付-客户-专项优惠">
          <div class="detail-item money">
            {{
              renderReceivableAmount(orderDetail.exclusiveDiscountPayableAmount)
            }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="专项优惠转车款">
          <div class="detail-item">
            <n-tag
              :type="
                orderDetail.exclusiveDiscountPayableDeductible
                  ? 'success'
                  : 'default'
              "
              size="small"
            >
              {{ orderDetail.exclusiveDiscountPayableDeductible ? "是" : "否" }}
            </n-tag>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="专项优惠备注">
          <div class="detail-item">
            {{ orderDetail.exclusiveDiscountRemark || "无" }}
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider, NTag } from "naive-ui";
import { renderReceivableAmount } from "@/utils/money.js";
import { getDictLabel } from "@/utils/dictUtils";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 获取专项优惠类型文本
const getExclusiveDiscountTypeText = (type) => {
  if (!type) return "无";
  return getDictLabel("exclusive_discount_type", type, type);
};
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

.detail-item.money {
  color: #2080f0;
  font-weight: 500;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
