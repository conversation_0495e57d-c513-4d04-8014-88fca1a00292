<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">衍生收入</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="公证费">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.notaryFee) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="畅行无忧收入">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.carefreeIncome) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="延保收入">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.extendedWarrantyIncome) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="VPS收入">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.vpsIncome) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="前置利息">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.preInterest) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="挂牌费">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.licensePlateFee) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="临牌费">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.tempPlateFee) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="外卖装具">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.deliveryEquipment) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="其他收入">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.otherIncome) }}
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider } from "naive-ui";
import { renderReceivableAmount } from "@/utils/money.js";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

.detail-item.money {
  color: #2080f0;
  font-weight: 500;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}
</style>
