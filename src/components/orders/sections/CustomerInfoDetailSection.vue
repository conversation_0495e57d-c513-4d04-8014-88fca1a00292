<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">客户信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="客户名称">
          <div class="detail-item">{{ orderDetail.customerName }}</div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="客户类型">
          <div class="detail-item">
            {{ getCustomerTypeText(orderDetail.customerType) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="联系电话">
          <div class="detail-item">
            {{ orderDetail.mobile || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="客户识别码">
          <div class="detail-item">
            {{ orderDetail.customerIdCode || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="成交状态">
          <div class="detail-item">
            <n-tag
              :type="getDealStatusType(orderDetail.dealStatus)"
              size="small"
            >
              {{ getDealStatusText(orderDetail.dealStatus) }}
            </n-tag>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="客户归属">
          <div class="detail-item">
            {{ orderDetail.ownerOrgName || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider, NTag } from "naive-ui";
import { getDictLabel, getDictType } from "@/utils/dictUtils";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 获取客户类型文本
const getCustomerTypeText = (type) => {
  if (!type) return "个人客户";
  return getDictLabel("customer_type", type, type);
};

// 获取成交状态文本
const getDealStatusText = (status) => {
  if (!status) return "未成交";
  return getDictLabel("deal_status", status, status);
};

// 获取成交状态类型
const getDealStatusType = (status) => {
  if (!status) return "warning";
  return getDictType("deal_status", status) || "warning";
};
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
