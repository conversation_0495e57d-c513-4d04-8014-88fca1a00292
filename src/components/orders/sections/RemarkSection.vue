<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">订单备注</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <n-grid :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item v-if="isFieldVisible('remark')" span="2">
        <n-form-item label="其他未尽事宜，请备注" path="remark">
          <n-input
            v-model:value="currentRemark"
            type="textarea"
            placeholder="备注信息将传递给后续节点处理（不超过500字符）"
            :autosize="{ minRows: 3, maxRows: 6 }"
            :disabled="!isFieldEditable('remark')"
            maxlength="500"
            show-count
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item
        v-if="isFieldVisible('remark') && historyRemarks.length > 0"
        span="2"
      >
        <n-form-item label="历史备注">
          <div class="history-remarks">
            <div class="remarks-list">
              <div
                v-for="(item, index) in historyRemarks"
                :key="index"
                class="remark-item"
              >
                <div class="remark-row">
                  <div class="remark-time">{{ item.time }}</div>
                  <div class="remark-agent">{{ item.agentName }}</div>
                  <div class="remark-content">{{ item.remark }}</div>
                </div>
              </div>
            </div>
          </div>
        </n-form-item>
      </n-grid-item>

      <!-- 当没有历史备注时的占位区域，保持输入框宽度不变 -->
      <n-grid-item
        v-if="isFieldVisible('remark') && historyRemarks.length === 0"
        span="2"
      >
        <!-- 空白占位，保持布局一致性 -->
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { NGrid, NGridItem, NFormItem, NInput, NDivider } from "naive-ui";
import { parseRemarkHistory, mergeRemarkHistory } from "@/utils/remarkUtils";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 定义事件
const emit = defineEmits(["update:form"]);

// 当前输入的备注
const currentRemark = ref("");

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      fields: {},
    }
  );
});

// 计算属性：解析历史备注
const historyRemarks = computed(() => {
  const remark = props.form.remark;
  if (!remark) return [];

  const parsed = parseRemarkHistory(remark);
  // 转换字段名以匹配模板
  return parsed.map((item) => ({
    time: item.time,
    agentName: item.author,
    remark: item.content,
  }));
});

// 暴露方法供父组件调用，用于提交时合并备注
const getUpdatedRemarkData = () => {
  // 获取现有备注内容
  const existingRemark = props.form.remark || "";

  // 如果有当前输入，使用工具函数合并备注
  if (currentRemark.value && currentRemark.value.trim()) {
    return mergeRemarkHistory(currentRemark.value, existingRemark);
  }

  // 如果没有新输入，返回原有备注
  return existingRemark;
};

// 备注数据将在提交时通过getUpdatedRemarkData方法获取，无需实时监听

// 暴露方法给父组件
defineExpose({
  getUpdatedRemarkData,
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};
</script>

<style scoped>
.history-remarks {
  max-height: 240px; /* 足够显示5条历史备注的高度 */
  overflow-y: auto;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 60px; /* 最小高度，确保"暂无历史备注"有足够空间显示 */
}

.remarks-list {
  padding: 8px;
}

.remark-item {
  background-color: #fff;
  border: 1px solid #e8e8f0;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 6px;
  transition: all 0.2s ease;
}

.remark-item:last-child {
  margin-bottom: 0;
}

.remark-item:hover {
  border-color: #18a058;
  box-shadow: 0 2px 4px rgba(24, 160, 88, 0.1);
}

.remark-row {
  display: grid;
  grid-template-columns: 2fr 2fr 8fr;
  gap: 12px;
  align-items: center;
}

.remark-time {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remark-agent {
  font-size: 12px;
  color: #18a058;
  font-weight: 600;
  background-color: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #e0f2fe;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remark-content {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  margin: 0;
}

.no-history {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  color: #999;
  font-size: 14px;
  background-color: #fafafa;
}
</style>