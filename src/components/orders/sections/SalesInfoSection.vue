<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">销售信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="销售单位">
          <div class="detail-item">{{ orderDetail.salesOrgName }}</div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售地类型">
          <div class="detail-item">
            <n-tag
              v-if="orderDetail.salesStoreType"
              :type="getSalesStoreTypeConfig(orderDetail.salesStoreType).type"
              :bordered="false"
              style="padding: 2px 8px; font-weight: bold"
            >
              {{ getSalesStoreTypeConfig(orderDetail.salesStoreType).label }}
            </n-tag>
            <span v-else>-</span>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售顾问">
          <div class="detail-item">{{ orderDetail.salesAgentName }}</div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="交付单位">
          <div class="detail-item">{{ orderDetail.deliveryOrgName }}</div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider, NTag } from "naive-ui";
import { getDictLabel, getDictType, getDictColor } from "@/utils/dictUtils";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 获取销售地类型配置
const getSalesStoreTypeConfig = (value) => {
  return {
    label: getDictLabel("sales_store_type", value),
    type: getDictType("sales_store_type", value),
    color: getDictColor("sales_store_type", value),
  };
};
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
