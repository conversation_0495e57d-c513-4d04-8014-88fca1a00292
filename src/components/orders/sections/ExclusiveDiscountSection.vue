<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">专项优惠</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">客户是否享受专项优惠补贴？</span>
      <n-radio-group
        v-model:value="form.hasExclusiveDiscount"
        :disabled="!isFieldEditable('hasExclusiveDiscount')"
      >
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span
        class="option-tip"
        v-if="form.hasExclusiveDiscount === 'YES'"
        v-tip-position
        style="color: #18a058 !important"
      >
        客户已选择享受专项优惠补贴，请填写相关信息
      </span>
    </div>

    <!-- 当选择"是"时显示专享优惠类型和金额 -->
    <n-grid
      v-if="form.hasExclusiveDiscount === 'YES'"
      :cols="4"
      :x-gap="16"
      :y-gap="1"
    >
      <n-grid-item>
        <n-form-item label="专项优惠类型" path="exclusiveDiscountType" required>
          <n-select
            v-model:value="form.exclusiveDiscountType"
            :options="exclusiveDiscountTypeOptions"
            placeholder="请选择专项优惠类型"
            clearable
            :disabled="!isFieldEditable('exclusiveDiscountType')"
            @update:value="handleExclusiveDiscountTypeChange"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('exclusiveDiscountReceivableAmount')">
        <n-form-item
          label="应收-厂家-专项优惠(元)"
          path="exclusiveDiscountReceivableAmount"
        >
          <n-input-number
            :min="amountInputConfig.min"
            :max="amountInputConfig.max"
            :step="amountInputConfig.step"
            v-model:value="form.exclusiveDiscountReceivableAmount"
            placeholder="应收厂家专项优惠"
            style="width: 100%"
            :precision="2"
            button-placement="both"
            :disabled="!isFieldEditable('exclusiveDiscountReceivableAmount')"
            @update:value="handleExclusiveDiscountReceivableAmountChange"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item
          label="应付-客户-专项优惠(元)"
          path="exclusiveDiscountPayableAmount"
        >
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
              v-model:value="form.exclusiveDiscountPayableAmount"
              placeholder="应付客户专项优惠"
              style="flex: 1"
              :precision="2"
              button-placement="both"
              :disabled="!isFieldEditable('exclusiveDiscountPayableAmount')"
              @update:value="handleExclusiveDiscountPayableAmountChange"
            />
            <n-checkbox
              v-model:checked="form.exclusiveDiscountPayableDeductible"
              style="margin-left: 10px; white-space: nowrap; width: 80px"
              :disabled="!isFieldEditable('exclusiveDiscountPayableDeductible')"
              @update:checked="handleExclusiveDiscountPayableDeductibleChange"
              >转车款</n-checkbox
            >
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="专项优惠备注" path="exclusiveDiscountRemark">
          <n-input
            v-model:value="form.exclusiveDiscountRemark"
            placeholder="请输入专项优惠备注"
            type="text"
            :disabled="!isFieldEditable('exclusiveDiscountRemark')"
            @update:value="handleExclusiveDiscountRemarkChange"
          />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import {
  NDivider,
  NRadioGroup,
  NRadioButton,
  NGrid,
  NGridItem,
  NFormItem,
  NSelect,
  NInputNumber,
  NCheckbox,
  NInput,
} from "naive-ui";
import { useDictOptions } from "@/utils/dictUtils";
import { getNumberInputConfig } from "@/config/inputConfig";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      fields: {},
    }
  );
});

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};

// 定义组件事件
const emit = defineEmits([
  "handle-exclusive-discount-change",
  "handle-exclusive-discount-receivable-amount-change",
  "handle-exclusive-discount-payable-amount-change",
  "handle-exclusive-discount-payable-deductible-change",
]);

// 专享优惠类型选项 - 使用响应式字典数据
const { options: exclusiveDiscountTypeOptions } = useDictOptions(
  "exclusive_discount_type",
  false
);

// 组件挂载时设置默认值
onMounted(() => {
  // 如果hasExclusiveDiscount字段未设置或为空字符串，默认设置为"否"
  // 注意：接口返回的"YES"或"NO"值应该被保留
  if (
    props.form.hasExclusiveDiscount === undefined ||
    props.form.hasExclusiveDiscount === null ||
    props.form.hasExclusiveDiscount === ""
  ) {
    props.form.hasExclusiveDiscount = "NO";
  }
});

// 监听专享优惠选择变化，重置相关字段
watch(
  () => props.form.hasExclusiveDiscount,
  (newValue) => {
    if (newValue !== "YES") {
      // 如果选择"否"，清空相关字段
      props.form.exclusiveDiscountType = null;
      props.form.exclusiveDiscountAmount = 0;
      props.form.exclusiveDiscountPayableDeductible = false;
      props.form.exclusiveDiscountReceivableAmount = 0;
      props.form.exclusiveDiscountPayableAmount = 0;
      props.form.exclusiveDiscountPayableDeductible = true;
      props.form.exclusiveDiscountRemark = "";
      emit("handle-exclusive-discount-change");
    }
  }
);

// 处理专享优惠类型变化
const handleExclusiveDiscountTypeChange = () => {
  if (
    !props.form.exclusiveDiscountType ||
    props.form.exclusiveDiscountType === ""
  ) {
    props.form.exclusiveDiscountAmount = 0;
    props.form.exclusiveDiscountPayableDeductible = false;
    props.form.exclusiveDiscountReceivableAmount = 0;
    props.form.exclusiveDiscountPayableAmount = 0;
    props.form.exclusiveDiscountPayableDeductible = true;
  }
  emit("handle-exclusive-discount-change");
};

// 处理应收厂家专项优惠金额变化
const handleExclusiveDiscountReceivableAmountChange = (value) => {
  emit("handle-exclusive-discount-receivable-amount-change", value);
  emit("handle-exclusive-discount-change");
};

// 处理应付客户专项优惠金额变化
const handleExclusiveDiscountPayableAmountChange = (value) => {
  emit("handle-exclusive-discount-payable-amount-change", value);
  emit("handle-exclusive-discount-change");
};

// 处理应付客户专项优惠转车款选项变化
const handleExclusiveDiscountPayableDeductibleChange = (value) => {
  emit("handle-exclusive-discount-payable-deductible-change", value);
  emit("handle-exclusive-discount-change");
};

// 处理专项优惠备注变化
const handleExclusiveDiscountRemarkChange = () => {
  emit("handle-exclusive-discount-change");
};
</script>

<style lang="scss" scoped>
// 样式继承自全局样式文件，无需额外定义
</style>
