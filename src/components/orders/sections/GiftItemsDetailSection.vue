<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">赠品明细</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="是否有赠品">
          <div class="detail-item">
            <n-tag
              :type="orderDetail.hasGiftItems === 'YES' ? 'success' : 'default'"
              size="small"
            >
              {{ orderDetail.hasGiftItems === "YES" ? "是" : "否" }}
            </n-tag>
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>

    <!-- 赠品明细表格 -->
    <template v-if="orderDetail.hasGiftItems === 'YES' && parsedGiftItems.length > 0">
      <div style="margin-top: 16px; width: 50%">
        <n-data-table
          :columns="giftItemsColumns"
          :data="parsedGiftItems"
          :pagination="false"
          size="small"
          striped
        />
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { NGrid, NGridItem, NFormItem, NDivider, NTag, NDataTable } from "naive-ui";
import { renderReceivableAmount } from "@/utils/money.js";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 计算属性：解析赠品明细数据
const parsedGiftItems = computed(() => {
  if (!props.orderDetail?.giftItems) return [];

  try {
    // 如果是字符串，尝试解析 JSON
    if (typeof props.orderDetail.giftItems === "string") {
      const parsed = JSON.parse(props.orderDetail.giftItems);
      if (Array.isArray(parsed)) {
        return parsed;
      }
    }
    // 如果已经是数组，直接返回
    else if (Array.isArray(props.orderDetail.giftItems)) {
      return props.orderDetail.giftItems;
    }
  } catch (error) {
    console.warn("解析赠品明细数据失败:", error);
  }

  return [];
});

// 赠品明细表格列定义
const giftItemsColumns = [
  {
    title: "序号",
    key: "index",
    width: 60,
    render: (row, index) => index + 1,
  },
  {
    title: "赠品类型",
    key: "type",
    width: 100,
  },
  {
    title: "赠品名称",
    key: "name",
    width: 150,
  },
  {
    title: "规格型号",
    key: "spec",
    width: 120,
  },
  {
    title: "单位",
    key: "unit",
    width: 60,
  },
  {
    title: "数量",
    key: "quantity",
    width: 80,
    render: (row) => `${row.quantity || 0}`,
  },
  {
    title: "单价(元)",
    key: "unitPrice",
    width: 100,
    render: (row) => {
      return renderReceivableAmount(row.unitPrice || 0);
    },
  },
  {
    title: "总价(元)",
    key: "totalPrice",
    width: 100,
    render: (row) => {
      const totalInCents = (row.totalPrice || 0) * 100;
      return renderReceivableAmount(totalInCents);
    },
  },
];
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
