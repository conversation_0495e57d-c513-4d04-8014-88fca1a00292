<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">产品信息</span>
      <n-button
        v-if="sectionConfig['show-vehicle-selector']"
        type="primary"
        size="small"
        @click="handleShowVehicleSelector"
        class="title-button"
      >
        <template #icon>
          <n-icon>
            <component :is="CarOutlineIcon" />
          </n-icon>
        </template>
        选择车辆
      </n-button>
      <!-- 错误提示 -->
      <span
        v-if="vehicleSelectError"
        style="color: #d03050; font-size: 12px; margin-left: 8px"
      >
        请选择车型信息
      </span>
      <div style="flex: 1"></div>
      <!-- 添加一个占位元素，将剩余空间推到右侧 -->
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <!-- 隐藏的 skuId 字段用于验证 -->
    <n-form-item path="skuId" style="display: none">
      <n-input-number
        :min="amountInputConfig.min"
        :max="amountInputConfig.max"
        :step="amountInputConfig.step"
        v-model:value="form.skuId"
      />
    </n-form-item>

    <!-- 车辆信息表单 -->
    <n-grid :cols="4" :x-gap="16" :y-gap="16">
      <!-- 第一行：销售日期和车辆基本信息 -->
      <n-grid-item v-if="isFieldVisible('dealDate')">
        <n-form-item label="订单日期" path="dealDate" required>
          <n-date-picker
            v-model:value="form.dealDate"
            type="date"
            clearable
            style="width: 100%"
            value-format="timestamp"
            @update:value="handleDateChange('dealDate', $event)"
            :disabled="!isFieldEditable('dealDate')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('vehicleBrand')">
        <n-form-item label="品牌" path="vehicleBrand">
          <n-input
            v-model:value="form.vehicleBrand"
            placeholder="车辆品牌"
            readonly
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('vehicleSeries')">
        <n-form-item label="车型" path="vehicleSeries" required>
          <n-input
            v-model:value="form.vehicleSeries"
            placeholder="车辆车型"
            readonly
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('vehicleConfig')">
        <n-form-item label="配置" path="vehicleConfig">
          <n-input
            v-model:value="form.vehicleConfig"
            placeholder="车辆配置"
            readonly
          />
        </n-form-item>
      </n-grid-item>

      <!-- 第二行：价格信息 -->
      <n-grid-item v-if="isFieldVisible('sbAmount')">
        <n-form-item label="启票价格(元)" path="sbAmount">
          <n-input-number
            :min="amountInputConfig.min"
            :max="amountInputConfig.max"
            :step="amountInputConfig.step"
            v-model:value="form.sbAmount"
            readonly
            style="width: 100%"
            :precision="2"
            :show-button="false"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('invoicePrice')">
        <n-form-item label="开票价格(元)" path="invoicePrice" required>
          <n-input-number
            :min="amountInputConfig.min"
            :max="amountInputConfig.max"
            :step="amountInputConfig.step"
            v-model:value="form.invoicePrice"
            :placeholder="priceInputConfig.placeholder"
            style="width: 100%"
            :precision="priceInputConfig.precision"
            button-placement="both"
            @update:value="handleInvoicePriceChange"
            :disabled="!isFieldEditable('invoicePrice')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('salesAmount')">
        <n-form-item label="车辆售价(元)" path="salesAmount" required>
          <n-input-number
            :min="amountInputConfig.min"
            :max="amountInputConfig.max"
            :step="amountInputConfig.step"
            v-model:value="form.salesAmount"
            :placeholder="priceInputConfig.placeholder"
            style="width: 100%"
            :precision="priceInputConfig.precision"
            button-placement="both"
            @update:value="handleSalePriceChange"
            :disabled="!isFieldEditable('salesAmount')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('discountAmount')">
        <n-form-item label="现金优惠(元)" path="discountAmount">
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
              v-model:value="form.discountAmount"
              :placeholder="priceInputConfig.placeholder"
              style="flex: 1"
              :precision="priceInputConfig.precision"
              button-placement="both"
              @update:value="handleDiscountChange"
              :disabled="!isFieldEditable('discountAmount')"
            />
            <n-checkbox
              v-if="isFieldVisible('discountDeductible')"
              v-model:checked="form.discountDeductible"
              style="margin-left: 10px; white-space: nowrap; width: 80px"
              @update:checked="handlediscountDeductibleChange"
              :disabled="!isFieldEditable('discountDeductible')"
              >转车款</n-checkbox
            >
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { computed, markRaw } from "vue";
import { CarOutline } from "@vicons/ionicons5";
import { calculateSalesPrice } from "@/utils/businessRules";
import { getNumberInputConfig } from "@/config/inputConfig";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const CarOutlineIcon = markRaw(CarOutline);

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  vehicleSelectError: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      "show-vehicle-selector": true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      "show-vehicle-selector": true,
      fields: {},
    }
  );
});

// 计算属性：获取价格输入框配置
const priceInputConfig = computed(() => {
  return getNumberInputConfig("price");
});

// 计算属性：获取金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};

// 定义组件事件
const emit = defineEmits([
  "show-vehicle-selector",
  "update:form",
  "validate",
  "vehicle-selected", // 添加车辆选择事件
  "handle-invoice-price-change",
  "handle-sale-price-change",
  "handle-discount-change",
  "handle-discount-deductible-change",
]);

// 计算属性：表单数据的引用
const form = computed(() => props.form);

// 处理日期变化
const handleDateChange = (field, value) => {
  const updatedForm = {
    ...props.form,
    [field]: value,
  };
  emit("update:form", updatedForm);
};

// 处理车辆选择
const handleVehicleSelected = (vehicle) => {
  if (!vehicle) {
    return;
  }

  // 使用业务规则计算销售价格
  const salesPrice = calculateSalesPrice(vehicle.sbPrice, vehicle.brand);

  // 更新表单数据
  const updatedForm = {
    ...props.form, // 保留其他字段的值
    skuId: vehicle.id,
    vehicleBrand: vehicle.brand || "",
    vehicleSeries: vehicle.series || "",
    vehicleConfig: vehicle.configName || "",
    sbAmount: vehicle.sbPrice || 0,
    invoicePrice: salesPrice, // 开票价格默认与车辆售价保持一致
    salesAmount: salesPrice, // 使用计算出的销售价格
  };

  emit("update:form", updatedForm);
  emit("vehicle-selected", vehicle);
};

// 处理显示车辆选择器
const handleShowVehicleSelector = () => {
  if (sectionConfig.value["show-vehicle-selector"]) {
    emit("show-vehicle-selector");
  }
};

// 处理开票价格变化
const handleInvoicePriceChange = () => {
  emit("handle-invoice-price-change");
};

// 处理车辆售价变化
const handleSalePriceChange = () => {
  emit("handle-sale-price-change");
};

// 处理优惠金额变化
const handleDiscountChange = () => {
  emit("handle-discount-change");
};

// 处理优惠金额转车款选项变化
const handlediscountDeductibleChange = () => {
  emit("handle-discount-deductible-change");
};
</script>

<style lang="scss" scoped>
// 产品信息组件样式
</style>
