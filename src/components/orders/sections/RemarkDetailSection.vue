<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">备注信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>

    <!-- 备注历史列表 -->
    <div v-if="remarkHistory.length > 0" class="remark-history">
      <div class="remark-history-title">历史备注</div>
      <div class="remark-list">
        <div
          v-for="(item, index) in remarkHistory"
          :key="index"
          class="remark-item"
        >
          <span class="remark-time">{{ item.time }}</span>
          <span class="remark-author">{{ item.author }}</span>
          <span class="remark-content">{{ item.content }}</span>
        </div>
      </div>
    </div>

    <!-- 无备注时的显示 -->
    <div v-else class="no-remark">
      <n-empty description="暂无备注信息" size="small" />
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { NEmpty, NDivider } from "naive-ui";
import { parseRemarkHistory } from "@/utils/remarkUtils";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 解析备注历史
const remarkHistory = computed(() => {
  const remark = props.orderDetail?.remark;
  if (!remark) return [];

  const parsed = parseRemarkHistory(remark);
  // 转换字段名以匹配模板
  return parsed.map((item) => ({
    time: item.time,
    author: item.author,
    content: item.content,
  }));
});
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.remark-history {
  width: 50%;
}

.remark-history-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 12px;
}

.remark-list {
  max-height: 200px;
  overflow-y: auto;
}

.remark-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  margin-bottom: 6px;
  background-color: #fafafa;
  font-size: 14px;
  gap: 16px;
}

.remark-item:last-child {
  margin-bottom: 0;
}

.remark-time {
  flex-shrink: 0;
  width: 120px;
  font-weight: 500;
  color: #666;
  font-size: 12px;
}

.remark-author {
  flex-shrink: 0;
  width: 80px;
  color: #18a058;
  font-weight: 500;
  font-size: 12px;
}

.remark-content {
  flex: 1;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-remark {
  padding: 20px 0;
  text-align: center;
}
</style>
