<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">产品信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="品牌">
          <div class="detail-item">{{ orderDetail.brand }}</div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="车系">
          <div class="detail-item">
            {{ orderDetail.series || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="车型名称">
          <div class="detail-item">
            {{ orderDetail.modelName || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="配置名称">
          <div class="detail-item">{{ orderDetail.configName }}</div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="颜色代码">
          <div class="detail-item">
            {{ orderDetail.colorCode || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="物料代码(SKU)">
          <div class="detail-item">
            {{ orderDetail.skuId || "未设置" }}
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider } from "naive-ui";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}
</style>
