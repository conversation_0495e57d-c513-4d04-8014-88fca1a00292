<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">价格信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="启票价格">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.sbAmount) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="开票价格">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.invoiceAmount) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="车辆售价">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.salesAmount) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="成交价格">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.dealAmount) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="成交价格(大写)">
          <div class="detail-item">{{ orderDetail.dealAmountCn }}</div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="优惠金额">
          <div class="detail-item money">
            {{ renderReceivableAmount(orderDetail.discountAmount) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="优惠转车款">
          <div class="detail-item">
            <n-tag
              :type="orderDetail.discountDeductible ? 'success' : 'default'"
              size="small"
            >
              {{ orderDetail.discountDeductible ? "是" : "否" }}
            </n-tag>
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { NGrid, NGridItem, NFormItem, NDivider, NTag } from "naive-ui";
import { renderReceivableAmount } from "@/utils/money.js";

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

.detail-item.money {
  color: #2080f0;
  font-weight: 500;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
