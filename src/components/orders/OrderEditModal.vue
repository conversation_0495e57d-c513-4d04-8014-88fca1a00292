<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    :title="title"
    preset="card"
    style="width: 100%; height: 100%"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
      class="order-form"
    >
      <!-- 产品信息部分 -->
      <ProductInfoSection
        :form="form"
        @show-vehicle-selector="showVehicleSelector"
        @handle-invoice-price-change="handleInvoicePriceChange"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
        @handle-discount-deductible-change="handleDiscountChange"
        @handle-sales-expense-change="handleSalesExpenseChange"
        @validate="handleValidate"
        @vehicle-selected="handleVehicleSelected"
        @update:form="handleFormUpdate"
      />
      <!-- 客户信息部分 -->
      <customer-info-section
        :form="form"
        :config="{
          visible: true,
          editable: true,
          'show-customer-selector': false,
          'search-deposits': true,
          fields: {},
        }"
        @show-customer-selector="showCustomerSelector"
      />

      <!-- 出库信息部分 -->
      <OutboundInfoSection
        :form="form"
        :selected-outbound-org="selectedOutboundOrg"
        @handle-outbound-org-change="handleOutboundOrgChange"
        @update:form="handleFormUpdate"
      />

      <!-- 付款方式部分 -->
      <PaymentMethodSection
        :form="form"
        :loan-channel-options="loanChannelOptions"
        :loan-months-options="loanMonthsOptions"
        :show-loan-rebate-receivable="false"
        @handle-loan-amount-change="handleLoanAmountChange"
        @handle-loan-initial-amount-change="handleLoanInitialAmountChange"
        @handle-loan-fee-change="handleLoanFeeChange"
        @handle-loan-rebate-payable-amount-change="
          handleLoanRebatePayableAmountChange
        "
        @handle-loan-rebate-payable-deductible-change="
          handleLoanRebatePayableDeductibleChange
        "
      />

      <!-- 车辆置换部分 -->
      <VehicleExchangeSection
        :form="form"
        @handle-used-vehicle-deductible-amount-change="
          handleUsedVehicleDeductibleAmountChange
        "
        @handle-used-vehicle-discount-payable-amount-change="
          handleUsedVehicleDiscountPayableAmountChange
        "
        @handle-used-vehicle-discount-payable-deductible-change="
          handleUsedVehicleDiscountPayableDeductibleChange
        "
      />

      <!-- 专项优惠部分 -->
      <ExclusiveDiscountSection
        :form="form"
        :config="exclusiveDiscountConfig"
        @handle-exclusive-discount-change="handleExclusiveDiscountChange"
        @handle-exclusive-discount-receivable-amount-change="
          handleExclusiveDiscountReceivableAmountChange
        "
        @handle-exclusive-discount-payable-amount-change="
          handleExclusiveDiscountPayableAmountChange
        "
        @handle-exclusive-discount-payable-deductible-change="
          handleExclusiveDiscountPayableDeductibleChange
        "
      />

      <!-- 赠品明细部分 -->
      <GiftItemsSection :form="form" ref="giftItemsSectionRef" />

      <!-- 订单备注部分 -->
      <RemarkSection
        :form="form"
        ref="remarkSectionRef"
        @update:form="handleFormUpdate"
      />

      <!-- 财务结算部分 -->
      <FinancialSettlementSection
        :form="form"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
      />
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave">确定</n-button>
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelected"
    />

    <!-- 车辆选择器 -->
    <VehicleSKUSelector
      v-model:visible="vehicleSelectorVisible"
      @select="handleVehicleSelected"
    />
  </n-modal>
</template>

<script setup>
import { useOrderEditModal } from "./OrderEditModal.js";

// 引入选择器组件
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";

// 引入各个部分组件
import CustomerInfoSection from "@/components/orders/sections/CustomerInfoSection.vue";
import ProductInfoSection from "@/components/orders/sections/ProductInfoSection.vue";
import OutboundInfoSection from "@/components/orders/sections/OutboundInfoSection.vue";
import PaymentMethodSection from "@/components/orders/sections/PaymentMethodSection.vue";
import VehicleExchangeSection from "@/components/orders/sections/VehicleExchangeSection.vue";

import ExclusiveDiscountSection from "@/components/orders/sections/ExclusiveDiscountSection.vue";
import GiftItemsSection from "@/components/orders/sections/GiftItemsSection.vue";
import RemarkSection from "@/components/orders/sections/RemarkSection.vue";
import FinancialSettlementSection from "@/components/orders/sections/FinancialSettlementSection.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增订单",
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
  vehicleCategoryOptions: {
    type: Array,
    default: () => [],
  },
  orderStatusOptions: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 使用组合式函数
const {
  // 响应式数据
  formRef,
  customerSelectorVisible,
  vehicleSelectorVisible,
  giftItemsSectionRef,
  remarkSectionRef,
  selectedOutboundOrg,
  form,
  rules,
  loanChannelOptions,
  loanMonthsOptions,
  modelVisible,
  exclusiveDiscountConfig,

  // 方法
  updateVisible: originalUpdateVisible,
  showCustomerSelector,
  showVehicleSelector,
  handleVehicleSelected,
  handleInvoicePriceChange,
  handleSalePriceChange,
  handleDiscountChange,
  handleSalesExpenseChange,
  handleExclusiveDiscountChange,
  handleExclusiveDiscountReceivableAmountChange,
  handleExclusiveDiscountPayableAmountChange,
  handleExclusiveDiscountPayableDeductibleChange,
  handleLoanAmountChange,
  handleLoanInitialAmountChange,
  handleLoanFeeChange,
  handleLoanRebatePayableAmountChange,
  handleLoanRebatePayableDeductibleChange,
  handleUsedVehicleDeductibleAmountChange,
  handleUsedVehicleDiscountPayableAmountChange,
  handleUsedVehicleDiscountPayableDeductibleChange,

  handleOutboundOrgChange,
  handleCustomerSelected,
  handleSave,
  handleCancel,
  resetForm,
  setFormData,
} = useOrderEditModal(props, emit);

// 重写 updateVisible 函数，在弹窗关闭时重置表单
const updateVisible = (val) => {
  if (!val) {
    // 当弹窗关闭时，重置表单数据
    resetForm();
  }
  originalUpdateVisible(val);
};

// 处理表单验证
const handleValidate = (callback) => {
  // 验证表单
  if (!formRef.value) {
    callback?.(null);
    return;
  }

  // 验证整个表单
  formRef.value.validate((errors) => {
    callback?.(errors);
  });
};

// 处理表单更新
const handleFormUpdate = (updatedForm) => {
  // 通过组合式函数更新表单数据，确保响应式更新
  Object.assign(form, updatedForm);

  // 如果更新了出库单位信息，同步更新selectedOutboundOrg
  if (updatedForm.outboundOrgId !== undefined) {
    if (updatedForm.outboundOrgId) {
      // 构造出库单位对象
      selectedOutboundOrg.value = {
        id: updatedForm.outboundOrgId,
        orgName: updatedForm.outboundOrgName,
        name: updatedForm.outboundOrgName, // 兼容性字段
      };
    } else {
      selectedOutboundOrg.value = null;
    }
  }
};

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<style lang="scss">
@use "./OrderEditModal.scss";
</style>
