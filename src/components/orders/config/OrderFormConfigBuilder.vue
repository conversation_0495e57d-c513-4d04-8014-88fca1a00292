<template>
  <div class="config-builder">
    <n-card title="订单表单配置构建器">
      <n-space vertical size="large">
        <!-- 基础配置 -->
        <n-card title="基础配置" size="small">
          <n-form-item label="表单模式">
            <n-radio-group v-model:value="config.mode">
              <n-radio-button value="create">新增</n-radio-button>
              <n-radio-button value="edit">编辑</n-radio-button>
            </n-radio-group>
          </n-form-item>
        </n-card>

        <!-- 区域配置 -->
        <n-card title="区域配置" size="small">
          <n-space vertical>
            <div
              v-for="(section, sectionKey) in config.sections"
              :key="sectionKey"
            >
              <n-card :title="getSectionTitle(sectionKey)" size="small">
                <n-space vertical>
                  <!-- 区域级别配置 -->
                  <n-space>
                    <n-checkbox
                      v-model:checked="section.visible"
                      @update:checked="
                        updateSectionConfig(sectionKey, 'visible', $event)
                      "
                    >
                      显示区域
                    </n-checkbox>
                    <n-checkbox
                      v-model:checked="section.editable"
                      @update:checked="
                        updateSectionConfig(sectionKey, 'editable', $event)
                      "
                      :disabled="!section.visible"
                    >
                      允许编辑
                    </n-checkbox>

                    <!-- 特殊配置项 -->
                    <n-checkbox
                      v-if="section['show-customer-selector'] !== undefined"
                      v-model:checked="section['show-customer-selector']"
                      @update:checked="
                        updateSectionConfig(
                          sectionKey,
                          'show-customer-selector',
                          $event
                        )
                      "
                      :disabled="!section.visible"
                    >
                      显示客户选择器
                    </n-checkbox>
                    <n-checkbox
                      v-if="section['show-vehicle-selector'] !== undefined"
                      v-model:checked="section['show-vehicle-selector']"
                      @update:checked="
                        updateSectionConfig(
                          sectionKey,
                          'show-vehicle-selector',
                          $event
                        )
                      "
                      :disabled="!section.visible"
                    >
                      显示车辆选择器
                    </n-checkbox>
                  </n-space>

                  <!-- 字段级别配置 -->
                  <n-collapse v-if="section.visible && section.fields">
                    <n-collapse-item title="字段配置" name="fields">
                      <n-grid :cols="3" :x-gap="12" :y-gap="8">
                        <n-grid-item
                          v-for="(field, fieldKey) in section.fields"
                          :key="fieldKey"
                        >
                          <n-card size="small">
                            <template #header>
                              <span style="font-size: 12px">{{
                                getFieldTitle(fieldKey)
                              }}</span>
                            </template>
                            <n-space vertical size="small">
                              <n-checkbox
                                v-model:checked="field.visible"
                                @update:checked="
                                  updateFieldConfig(
                                    sectionKey,
                                    fieldKey,
                                    'visible',
                                    $event
                                  )
                                "
                                size="small"
                              >
                                显示
                              </n-checkbox>
                              <n-checkbox
                                v-model:checked="field.editable"
                                @update:checked="
                                  updateFieldConfig(
                                    sectionKey,
                                    fieldKey,
                                    'editable',
                                    $event
                                  )
                                "
                                :disabled="!field.visible || !section.editable"
                                size="small"
                              >
                                可编辑
                              </n-checkbox>
                            </n-space>
                          </n-card>
                        </n-grid-item>
                      </n-grid>
                    </n-collapse-item>
                  </n-collapse>
                </n-space>
              </n-card>
            </div>
          </n-space>
        </n-card>

        <!-- 预览和导出 -->
        <n-card title="配置预览" size="small">
          <n-space vertical>
            <n-space>
              <n-button @click="previewForm">预览表单</n-button>
              <n-button @click="exportConfig">导出配置</n-button>
              <n-button @click="importConfig">导入配置</n-button>
              <n-button @click="resetConfig">重置配置</n-button>
            </n-space>

            <n-collapse>
              <n-collapse-item title="JSON配置" name="json">
                <n-code
                  :code="JSON.stringify(config, null, 2)"
                  language="json"
                  style="max-height: 300px; overflow-y: auto"
                />
              </n-collapse-item>
            </n-collapse>
          </n-space>
        </n-card>
      </n-space>
    </n-card>

    <!-- 预览表单 -->
    <configurable-order-form
      v-model:visible="previewVisible"
      :config="config"
      title="配置预览"
      @save="handlePreviewSave"
      @cancel="handlePreviewCancel"
    />

    <!-- 导入配置对话框 -->
    <n-modal v-model:show="importVisible" title="导入配置">
      <n-card style="width: 600px">
        <n-form-item label="JSON配置">
          <n-input
            v-model:value="importJson"
            type="textarea"
            placeholder="请粘贴JSON配置"
            :autosize="{ minRows: 10, maxRows: 20 }"
          />
        </n-form-item>
        <template #footer>
          <n-space justify="end">
            <n-button @click="importVisible = false">取消</n-button>
            <n-button type="primary" @click="doImportConfig">确定</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  NCard,
  NSpace,
  NFormItem,
  NRadioGroup,
  NRadioButton,
  NCheckbox,
  NCollapse,
  NCollapseItem,
  NGrid,
  NGridItem,
  NButton,
  NCode,
  NModal,
  NInput,
} from "naive-ui";
import ConfigurableOrderForm from "../ConfigurableOrderForm.vue";
import { getDefaultOrderFormConfig } from "./OrderFormConfigFactory.js";
import messages from "@/utils/messages";

// 响应式数据
const config = reactive(getDefaultOrderFormConfig("create"));
const previewVisible = ref(false);
const importVisible = ref(false);
const importJson = ref("");

// 区域标题映射
const sectionTitles = {
  "customer-info-section": "客户信息",
  "product-info-section": "产品信息",
  "outbound-info-section": "出库信息",
  "payment-method-section": "付款方式",
  "vehicle-exchange-section": "车辆置换",
  "insurance-section": "车辆保险",
  "exclusive-discount-section": "专享优惠",
  "gift-items-section": "赠品明细",
  "remark-section": "订单备注",
  "financial-settlement-section": "财务结算",
};

// 字段标题映射
const fieldTitles = {
  customerName: "客户名称",
  customerType: "客户类型",
  customerPhone: "联系电话",
  salesStoreType: "销售地类型",
  salesAgentName: "销售顾问",
  salesOrgName: "销售单位",
  depositAmount: "已付定金",
  depositDeductible: "定金转车款",
  depositType: "定金类型",
  dealDate: "订单日期",
  vehicleBrand: "品牌",
  vehicleSeries: "车型",
  vehicleConfig: "配置",
  vehicleSbPrice: "启票价格",
  invoicePrice: "开票价格",
  salesAmount: "车辆售价",
  discountAmount: "现金优惠",
  discountDeductible: "优惠转车款",
  expectedOutboundDate: "预计出库日期",
  outboundOrgName: "出库单位",
  paymentMethod: "付款方式",
  loanChannel: "分期金融机构",
  loanAmount: "分期金额",
  loanInitialAmount: "首付金额",
  loanMonths: "分期期限",
  loanRebatePayableAmount: "应付客户分期返利",
  loanRebatePayableDeductible: "分期返利转车款",
  loanFee: "分期服务费",
  hasUsedVehicle: "是否置换",
  usedVehicleId: "置换车牌号",
  usedVehicleVin: "置换车VIN",
  usedVehicleBrand: "置换车品牌",
  usedVehicleModel: "置换车型",
  usedVehicleColor: "置换车颜色",
  usedVehicleAmount: "置换金额",
  usedVehicleDiscountPayableDeductibleAmount: "置换转车款",
  usedVehicleDiscountPayableAmount: "应付客户置换补贴",
  usedVehicleDiscountPayableDeductible: "置换补贴转车款",
  hasInsurance: "是否购买保险",
  insuranceOrgName: "保险经办机构",
  exclusiveDiscountType: "专享优惠类型",
  exclusiveDiscountReceivableAmount: "应收厂家专项优惠",
  exclusiveDiscountPayableAmount: "应付客户专项优惠",
  exclusiveDiscountPayableDeductible: "专项优惠转车款",
  exclusiveDiscountRemark: "专项优惠备注",
  hasGiftItems: "是否有赠品",
  giftItems: "赠品明细",
  remark: "付款备注",
  dealAmount: "成交价格",
  dealAmountCn: "成交价格大写",
};

// 获取区域标题
const getSectionTitle = (sectionKey) => {
  return sectionTitles[sectionKey] || sectionKey;
};

// 获取字段标题
const getFieldTitle = (fieldKey) => {
  return fieldTitles[fieldKey] || fieldKey;
};

// 更新区域配置
const updateSectionConfig = (sectionKey, configKey, value) => {
  config.sections[sectionKey][configKey] = value;
};

// 更新字段配置
const updateFieldConfig = (sectionKey, fieldKey, configKey, value) => {
  config.sections[sectionKey].fields[fieldKey][configKey] = value;
};

// 预览表单
const previewForm = () => {
  previewVisible.value = true;
};

// 导出配置
const exportConfig = () => {
  const configJson = JSON.stringify(config, null, 2);
  const blob = new Blob([configJson], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "order-form-config.json";
  a.click();
  URL.revokeObjectURL(url);
  messages.success("配置已导出");
};

// 导入配置
const importConfig = () => {
  importVisible.value = true;
};

// 执行导入配置
const doImportConfig = () => {
  try {
    const importedConfig = JSON.parse(importJson.value);
    Object.assign(config, importedConfig);
    importVisible.value = false;
    importJson.value = "";
    messages.success("配置导入成功");
  } catch (error) {
    messages.error("配置格式错误：" + error.message);
  }
};

// 重置配置
const resetConfig = () => {
  const defaultConfig = getDefaultOrderFormConfig("create");
  Object.assign(config, defaultConfig);
  messages.success("配置已重置");
};

// 处理预览保存
const handlePreviewSave = (formData) => {
  console.log("预览表单数据:", formData);
  previewVisible.value = false;
  messages.success("预览保存成功");
};

// 处理预览取消
const handlePreviewCancel = () => {
  previewVisible.value = false;
};
</script>

<style lang="scss" scoped>
.config-builder {
  padding: 20px;

  .n-card {
    margin-bottom: 16px;
  }
}
</style>
