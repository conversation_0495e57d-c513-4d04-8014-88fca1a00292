/**
 * 订单表单配置工厂测试
 */

import {
  getDefaultOrderFormConfig,
  getSimpleOrderFormConfig,
  getReadOnlyOrderFormConfig,
  getQuickOrderFormConfig,
  getOutboundOrderFormConfig,
  getPermissionBasedOrderFormConfig,
  mergeOrderFormConfig,
  validateOrderFormConfig,
  getEditableFields,
  getVisibleFields,
} from '../OrderFormConfigFactory.js'

describe('OrderFormConfigFactory', () => {
  describe('getDefaultOrderFormConfig', () => {
    test('应该返回默认的完整配置', () => {
      const config = getDefaultOrderFormConfig('create')

      expect(config.mode).toBe('create')
      expect(config.sections).toBeDefined()
      expect(config.sections['customer-info-section']).toBeDefined()
      expect(config.sections['customer-info-section'].visible).toBe(true)
      expect(config.sections['customer-info-section'].editable).toBe(true)
      expect(config.sections['customer-info-section']['show-customer-selector']).toBe(true)
    })

    test('应该包含所有必要的区域', () => {
      const config = getDefaultOrderFormConfig('create')
      const expectedSections = [
        'customer-info-section',
        'product-info-section',
        'outbound-info-section',
        'payment-method-section',
        'vehicle-exchange-section',
        'insurance-section',
        'exclusive-discount-section',
        'gift-items-section',
        'remark-section',
        'financial-settlement-section',
      ]

      expectedSections.forEach(sectionKey => {
        expect(config.sections[sectionKey]).toBeDefined()
      })
    })
  })

  describe('getSimpleOrderFormConfig', () => {
    test('应该隐藏非核心区域', () => {
      const config = getSimpleOrderFormConfig('create')

      expect(config.sections['vehicle-exchange-section'].visible).toBe(false)
      expect(config.sections['insurance-section'].visible).toBe(false)
      expect(config.sections['exclusive-discount-section'].visible).toBe(false)
      expect(config.sections['gift-items-section'].visible).toBe(false)
    })

    test('应该隐藏非核心字段', () => {
      const config = getSimpleOrderFormConfig('create')

      expect(config.sections['customer-info-section'].fields.depositAmount.visible).toBe(false)
      expect(config.sections['customer-info-section'].fields.depositDeductible.visible).toBe(false)
      expect(config.sections['customer-info-section'].fields.depositType.visible).toBe(false)
    })
  })

  describe('getReadOnlyOrderFormConfig', () => {
    test('应该将所有区域设置为不可编辑', () => {
      const config = getReadOnlyOrderFormConfig()

      Object.keys(config.sections).forEach(sectionKey => {
        expect(config.sections[sectionKey].editable).toBe(false)
      })
    })

    test('应该隐藏所有选择器', () => {
      const config = getReadOnlyOrderFormConfig()

      expect(config.sections['customer-info-section']['show-customer-selector']).toBe(false)
      expect(config.sections['product-info-section']['show-vehicle-selector']).toBe(false)
    })

    test('应该将所有字段设置为不可编辑', () => {
      const config = getReadOnlyOrderFormConfig()

      Object.keys(config.sections).forEach(sectionKey => {
        const section = config.sections[sectionKey]
        if (section.fields) {
          Object.keys(section.fields).forEach(fieldKey => {
            expect(section.fields[fieldKey].editable).toBe(false)
          })
        }
      })
    })
  })

  describe('getQuickOrderFormConfig', () => {
    test('应该隐藏非核心区域', () => {
      const config = getQuickOrderFormConfig('create')

      expect(config.sections['vehicle-exchange-section'].visible).toBe(false)
      expect(config.sections['insurance-section'].visible).toBe(false)
      expect(config.sections['exclusive-discount-section'].visible).toBe(false)
      expect(config.sections['gift-items-section'].visible).toBe(false)
      expect(config.sections['remark-section'].visible).toBe(false)
    })
  })

  describe('getPermissionBasedOrderFormConfig', () => {
    test('应该根据权限隐藏相应功能', () => {
      const permissions = {
        customerManagement: false,
        vehicleManagement: false,
        priceEdit: false,
        financial: false,
      }

      const config = getPermissionBasedOrderFormConfig('create', permissions)

      expect(config.sections['customer-info-section']['show-customer-selector']).toBe(false)
      expect(config.sections['product-info-section']['show-vehicle-selector']).toBe(false)
      expect(config.sections['product-info-section'].fields.salesAmount.editable).toBe(false)
      expect(config.sections['product-info-section'].fields.discountAmount.editable).toBe(false)
      expect(config.sections['exclusive-discount-section'].visible).toBe(false)
      expect(config.sections['financial-settlement-section'].visible).toBe(false)
    })
  })

  describe('mergeOrderFormConfig', () => {
    test('应该正确合并配置', () => {
      const baseConfig = getDefaultOrderFormConfig('create')
      const overrideConfig = {
        mode: 'edit',
        sections: {
          'customer-info-section': {
            'show-customer-selector': false,
            fields: {
              customerPhone: {
                visible: false,
              },
            },
          },
        },
      }

      const merged = mergeOrderFormConfig(baseConfig, overrideConfig)

      expect(merged.mode).toBe('edit')
      expect(merged.sections['customer-info-section']['show-customer-selector']).toBe(false)
      expect(merged.sections['customer-info-section'].fields.customerPhone.visible).toBe(false)
      // 其他配置应该保持不变
      expect(merged.sections['customer-info-section'].visible).toBe(true)
      expect(merged.sections['customer-info-section'].fields.customerName.visible).toBe(true)
    })
  })

  describe('validateOrderFormConfig', () => {
    test('应该验证有效配置', () => {
      const config = getDefaultOrderFormConfig('create')
      const result = validateOrderFormConfig(config)

      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('应该检测无效的mode', () => {
      const config = {
        mode: 'invalid',
        sections: {},
      }

      const result = validateOrderFormConfig(config)

      expect(result.valid).toBe(false)
      expect(result.errors).toContain('mode 必须是 "create" 或 "edit"')
    })

    test('应该检测缺失的sections', () => {
      const config = {
        mode: 'create',
      }

      const result = validateOrderFormConfig(config)

      expect(result.valid).toBe(false)
      expect(result.errors).toContain('sections 必须是一个对象')
    })
  })

  describe('getEditableFields', () => {
    test('应该返回可编辑字段列表', () => {
      const config = getDefaultOrderFormConfig('create')
      const editableFields = getEditableFields(config)

      expect(editableFields).toContain('customerName')
      expect(editableFields).toContain('salesAmount')
      expect(editableFields).not.toContain('finalPrice') // 财务结算字段不可编辑
    })

    test('应该排除不可见或不可编辑的字段', () => {
      const config = getReadOnlyOrderFormConfig()
      const editableFields = getEditableFields(config)

      expect(editableFields).toHaveLength(0) // 只读模式下没有可编辑字段
    })
  })

  describe('getVisibleFields', () => {
    test('应该返回可见字段列表', () => {
      const config = getDefaultOrderFormConfig('create')
      const visibleFields = getVisibleFields(config)

      expect(visibleFields).toContain('customerName')
      expect(visibleFields).toContain('salesAmount')
      expect(visibleFields).toContain('finalPrice')
    })

    test('应该排除不可见的字段', () => {
      const config = getSimpleOrderFormConfig('create')
      const visibleFields = getVisibleFields(config)

      expect(visibleFields).not.toContain('depositAmount') // 简化模式下隐藏
    })
  })
})
