# 订单表单配置系统

## 概述

订单表单配置系统允许通过配置项来快速组合和定制订单新增/编辑组件，大大降低了组件的复杂度，提高了可维护性和灵活性。

## 核心概念

### 配置结构

```javascript
{
  "mode": "create|edit", // 创建或修改模式
  "sections": {
    "section-key": {
      "visible": true,     // 显示该区域
      "editable": true,    // 允许编辑该区域
      "show-xxx-selector": true, // 特殊配置项（如选择器按钮）
      "fields": {
        "field-name": {
          "visible": true,   // 显示该字段
          "editable": true   // 允许编辑该字段
        }
      }
    }
  }
}
```

### 优先级规则

1. **区域级别优先级高于字段级别**：如果区域设置为不可编辑，则该区域下的所有字段都不可编辑
2. **字段级别可以进一步限制**：即使区域可编辑，字段仍可以设置为不可编辑
3. **可见性独立控制**：字段的可见性和可编辑性是独立的

## 使用方法

### 1. 基础使用

```vue
<template>
  <configurable-order-form
    v-model:visible="formVisible"
    :config="formConfig"
    title="新增订单"
    @save="handleSave"
    @cancel="handleCancel"
  />
</template>

<script setup>
import { ref } from 'vue'
import ConfigurableOrderForm from '@/components/orders/ConfigurableOrderForm.vue'
import { getDefaultOrderFormConfig } from '@/components/orders/config/OrderFormConfigFactory.js'

const formVisible = ref(false)
const formConfig = ref(getDefaultOrderFormConfig('create'))

const handleSave = (formData) => {
  console.log('保存数据:', formData)
}

const handleCancel = () => {
  formVisible.value = false
}
</script>
```

### 2. 使用预定义配置

```javascript
import {
  getDefaultOrderFormConfig,    // 默认完整配置
  getSimpleOrderFormConfig,     // 简化配置
  getReadOnlyOrderFormConfig,   // 只读配置
  getQuickOrderFormConfig,      // 快速录单配置
  getOutboundOrderFormConfig,   // 出库单配置
  getPermissionBasedOrderFormConfig, // 基于权限的配置
  getMobileOrderFormConfig,     // 移动端配置
} from '@/components/orders/config/OrderFormConfigFactory.js'

// 使用简化配置
const config = getSimpleOrderFormConfig('create')

// 使用权限配置
const permissions = {
  customerManagement: true,
  vehicleManagement: false,
  priceEdit: true,
  financial: false,
}
const config = getPermissionBasedOrderFormConfig('create', permissions)
```

### 3. 自定义配置

```javascript
import { 
  getDefaultOrderFormConfig, 
  mergeOrderFormConfig 
} from '@/components/orders/config/OrderFormConfigFactory.js'

// 基于默认配置进行自定义
const baseConfig = getDefaultOrderFormConfig('create')

const customConfig = {
  sections: {
    'customer-info-section': {
      'show-customer-selector': false, // 隐藏客户选择器
      fields: {
        customerPhone: {
          visible: false, // 隐藏客户电话
        },
      },
    },
    'payment-method-section': {
      visible: false, // 隐藏付款方式区域
    },
  },
}

// 合并配置
const finalConfig = mergeOrderFormConfig(baseConfig, customConfig)
```

## 配置工厂方法

### getDefaultOrderFormConfig(mode)
获取默认的完整订单表单配置，包含所有区域和字段。

**参数：**
- `mode`: 'create' | 'edit' - 表单模式

### getSimpleOrderFormConfig(mode)
获取简化的订单表单配置，隐藏非核心区域。

### getReadOnlyOrderFormConfig()
获取只读模式的订单表单配置，所有字段都不可编辑。

### getQuickOrderFormConfig(mode)
获取快速录单配置，只显示最核心的字段。

### getOutboundOrderFormConfig(mode)
获取出库单专用配置，客户和产品信息只读，重点关注出库信息。

### getPermissionBasedOrderFormConfig(mode, permissions)
根据用户权限获取配置。

**权限对象：**
```javascript
{
  customerManagement: boolean, // 客户管理权限
  vehicleManagement: boolean,  // 车辆管理权限
  priceEdit: boolean,          // 价格编辑权限
  financial: boolean,          // 财务权限
}
```

### mergeOrderFormConfig(baseConfig, overrideConfig)
合并两个配置对象，用于自定义配置。

### validateOrderFormConfig(config)
验证配置对象的有效性。

## 支持的区域和字段

### 客户信息区域 (customer-info-section)
- **特殊配置：** `show-customer-selector` - 显示客户选择器
- **字段：** customerName, customerType, customerPhone, salesStoreType, salesAgentName, salesOrgName, depositAmount, depositDeductible, depositType

### 产品信息区域 (product-info-section)
- **特殊配置：** `show-vehicle-selector` - 显示车辆选择器
- **字段：** dealDate, vehicleBrand, vehicleSeries, vehicleConfig, vehicleSbPrice, invoicePrice, salesAmount, discountAmount, discountDeductible

### 出库信息区域 (outbound-info-section)
- **字段：** expectedOutboundDate, outboundOrgName

### 付款方式区域 (payment-method-section)
- **字段：** paymentMethod, loanChannel, loanAmount, loanInitialAmount, loanMonths, loanRebatePayableAmount, loanRebatePayableDeductible, loanFee

### 车辆置换区域 (vehicle-exchange-section)
- **字段：** hasUsedVehicle, usedVehicleId, usedVehicleVin, usedVehicleBrand, usedVehicleModel, usedVehicleColor, usedVehicleAmount, usedVehicleDiscountPayableDeductibleAmount, usedVehicleDiscountPayableAmount, usedVehicleDiscountPayableDeductibleChange

### 车辆保险区域 (insurance-section)
- **字段：** hasInsurance, insuranceOrgName

### 专享优惠区域 (exclusive-discount-section)
- **字段：** exclusiveDiscountType, exclusiveDiscountReceivableAmount, exclusiveDiscountPayableAmount, exclusiveDiscountPayableDeductible, exclusiveDiscountRemark

### 赠品明细区域 (gift-items-section)
- **字段：** hasGiftItems, giftItems

### 订单备注区域 (remark-section)
- **字段：** paymentRemark

### 财务结算区域 (financial-settlement-section)
- **字段：** finalPrice, finalPriceChinese

## 工具和示例

### 配置构建器
使用 `OrderFormConfigBuilder.vue` 可视化地构建和预览配置：

```vue
<template>
  <order-form-config-builder />
</template>

<script setup>
import OrderFormConfigBuilder from '@/components/orders/config/OrderFormConfigBuilder.vue'
</script>
```

### 使用示例
查看 `OrderFormUsageExample.vue` 了解各种配置的使用方法。

## 最佳实践

1. **优先使用预定义配置**：大多数场景可以使用预定义的配置工厂方法
2. **合理使用合并配置**：对于需要微调的场景，使用 `mergeOrderFormConfig` 进行配置合并
3. **验证配置有效性**：在生产环境中使用 `validateOrderFormConfig` 验证配置
4. **保持配置简洁**：只配置需要改变的部分，其他保持默认值
5. **文档化自定义配置**：对于复杂的自定义配置，添加注释说明用途

## 扩展指南

### 添加新字段
1. 在对应的 section 组件中添加字段渲染逻辑
2. 在配置工厂中添加字段的默认配置
3. 更新字段标题映射（如果使用配置构建器）

### 添加新区域
1. 创建新的 section 组件
2. 在 `ConfigurableOrderForm.vue` 中添加渲染逻辑
3. 在配置工厂中添加区域的默认配置
4. 更新区域标题映射（如果使用配置构建器）

### 添加特殊配置项
1. 在区域级别添加特殊配置项（如选择器开关）
2. 在对应的 section 组件中处理特殊配置项
3. 在配置工厂中设置默认值
