/**
 * 订单表单配置工厂
 * 用于生成订单新增/编辑组件的配置项
 */

/**
 * 获取默认的完整订单表单配置
 * @param {string} mode - 模式：'create' | 'edit'
 * @returns {Object} 完整的订单表单配置
 */
export function getDefaultOrderFormConfig(mode = 'create') {
  return {
    mode, // 创建或修改：创建时需要清空所有字段，修改时需要查询订单详情
    sections: {
      // 客户信息区域
      'customer-info-section': {
        visible: true, // 显示客户信息区域
        editable: true, // 区域editable的优先级比字段级的editable要高
        'show-customer-selector': true, // 显示客户选择器组件
        'search-deposits': false, // 默认关闭自动查询定金功能，需要时自行开启
        fields: {
          customerName: {
            visible: true, // 显示该字段
            editable: true, // 允许编辑，并且会提交到接口
          },
          customerType: {
            visible: true,
            editable: true,
          },
          customerPhone: {
            visible: true,
            editable: true,
          },
          salesStoreType: {
            visible: true,
            editable: true,
          },
          salesAgentName: {
            visible: true,
            editable: true,
          },
          salesOrgName: {
            visible: true,
            editable: false, // 只读，由销售顾问选择器自动填充
          },
          depositAmount: {
            visible: true,
            editable: true,
          },
          depositDeductible: {
            visible: true,
            editable: true,
          },
          depositType: {
            visible: true,
            editable: true,
          },
        },
      },

      // 产品信息区域
      'product-info-section': {
        visible: true,
        editable: true,
        'show-vehicle-selector': true, // 显示车辆选择按钮
        fields: {
          dealDate: {
            visible: true,
            editable: true,
          },
          vehicleBrand: {
            visible: true,
            editable: false, // 车辆选择器填充，只读
          },
          vehicleSeries: {
            visible: true,
            editable: false, // 车辆选择器填充，只读
          },
          vehicleConfig: {
            visible: true,
            editable: false, // 车辆选择器填充，只读
          },
          sbAmount: {
            visible: true,
            editable: false, // 车辆选择器填充，只读
          },
          invoicePrice: {
            visible: true,
            editable: true,
          },
          salesAmount: {
            visible: true,
            editable: true,
          },
          discountAmount: {
            visible: true,
            editable: true,
          },
          discountDeductible: {
            visible: true,
            editable: true,
          },
        },
      },

      // 出库信息区域
      'outbound-info-section': {
        visible: true,
        editable: true,
        fields: {
          expectedOutboundDate: {
            visible: true,
            editable: true,
          },
          outboundOrgName: {
            visible: true,
            editable: true, // 通过选择器编辑
          },
        },
      },

      // 付款方式区域
      'payment-method-section': {
        visible: true,
        editable: true,
        fields: {
          paymentMethod: {
            visible: true,
            editable: true,
          },
          loanChannel: {
            visible: true,
            editable: true,
          },
          loanAmount: {
            visible: true,
            editable: true,
          },
          loanInitialAmount: {
            visible: true,
            editable: true,
          },
          loanMonths: {
            visible: true,
            editable: true,
          },
          loanRebatePayableAmount: {
            visible: true,
            editable: true,
          },
          loanRebatePayableDeductible: {
            visible: true,
            editable: true,
          },
          loanFee: {
            visible: true,
            editable: true,
          },
        },
      },

      // 车辆置换区域
      'vehicle-exchange-section': {
        visible: true,
        editable: true,
        fields: {
          hasUsedVehicle: {
            visible: true,
            editable: true,
          },
          usedVehicleId: {
            visible: true,
            editable: true,
          },
          usedVehicleVin: {
            visible: true,
            editable: true,
          },
          usedVehicleBrand: {
            visible: true,
            editable: true,
          },
          usedVehicleModel: {
            visible: true,
            editable: true,
          },
          usedVehicleColor: {
            visible: true,
            editable: true,
          },
          usedVehicleAmount: {
            visible: true,
            editable: true,
          },
          usedVehicleDiscountPayableDeductibleAmount: {
            visible: true,
            editable: true,
          },
          usedVehicleDiscountPayableAmount: {
            visible: true,
            editable: true,
          },
          usedVehicleDiscountPayableDeductible: {
            visible: true,
            editable: true,
          },
        },
      },

      // 车辆保险区域
      'insurance-section': {
        visible: true,
        editable: true,
        fields: {
          hasInsurance: {
            visible: true,
            editable: true,
          },
          insuranceOrgName: {
            visible: true,
            editable: true, // 通过选择器编辑
          },
        },
      },

      // 专享优惠区域
      'exclusive-discount-section': {
        visible: true,
        editable: true,
        fields: {
          exclusiveDiscountType: {
            visible: true,
            editable: true,
          },
          exclusiveDiscountReceivableAmount: {
            visible: true,
            editable: true,
          },
          exclusiveDiscountPayableAmount: {
            visible: true,
            editable: true,
          },
          exclusiveDiscountPayableDeductible: {
            visible: true,
            editable: true,
          },
          exclusiveDiscountRemark: {
            visible: true,
            editable: true,
          },
        },
      },

      // 赠品明细区域
      'gift-items-section': {
        visible: true,
        editable: true,
        fields: {
          hasGiftItems: {
            visible: true,
            editable: true,
          },
          giftItems: {
            visible: true,
            editable: true,
          },
        },
      },

      // 订单备注区域
      'remark-section': {
        visible: true,
        editable: true,
        fields: {
          remark: {
            visible: true,
            editable: true,
          },
        },
      },

      // 财务结算区域
      'financial-settlement-section': {
        visible: true,
        editable: false, // 财务结算通常是计算结果，不允许直接编辑
        fields: {
          dealAmount: {
            visible: true,
            editable: false, // 计算字段，只读
          },
          dealAmountCn: {
            visible: true,
            editable: false, // 计算字段，只读
          },
        },
      },
    },
  }
}

/**
 * 获取简化的订单表单配置（仅显示核心字段）
 * @param {string} mode - 模式：'create' | 'edit'
 * @returns {Object} 简化的订单表单配置
 */
export function getSimpleOrderFormConfig(mode = 'create') {
  const config = getDefaultOrderFormConfig(mode)

  // 隐藏非核心区域
  config.sections['vehicle-exchange-section'].visible = false
  config.sections['insurance-section'].visible = false
  config.sections['exclusive-discount-section'].visible = false
  config.sections['gift-items-section'].visible = false

  // 简化客户信息字段
  config.sections['customer-info-section'].fields.depositAmount.visible = false
  config.sections['customer-info-section'].fields.depositDeductible.visible = false
  config.sections['customer-info-section'].fields.depositType.visible = false

  return config
}

/**
 * 获取只读模式的订单表单配置
 * @returns {Object} 只读模式的订单表单配置
 */
export function getReadOnlyOrderFormConfig() {
  const config = getDefaultOrderFormConfig('edit')

  // 将所有区域设置为不可编辑
  Object.keys(config.sections).forEach(sectionKey => {
    const section = config.sections[sectionKey]
    section.editable = false

    // 隐藏选择器按钮
    if (section['show-customer-selector']) {
      section['show-customer-selector'] = false
    }
    if (section['show-vehicle-selector']) {
      section['show-vehicle-selector'] = false
    }

    // 将所有字段设置为不可编辑
    Object.keys(section.fields).forEach(fieldKey => {
      section.fields[fieldKey].editable = false
    })
  })

  return config
}

/**
 * 获取出库单专用的订单表单配置
 * @param {string} mode - 模式：'create' | 'edit'
 * @returns {Object} 出库单专用的订单表单配置
 */
export function getOutboundOrderFormConfig(mode = 'create') {
  const config = getDefaultOrderFormConfig(mode)

  // 出库单场景下，客户信息通常已确定，设为只读
  config.sections['customer-info-section'].editable = false
  config.sections['customer-info-section']['show-customer-selector'] = false

  // 产品信息也通常已确定
  config.sections['product-info-section'].editable = false
  config.sections['product-info-section']['show-vehicle-selector'] = false

  // 重点关注出库信息
  config.sections['outbound-info-section'].editable = true

  return config
}

/**
 * 获取快速录单的订单表单配置（最小化字段）
 * @param {string} mode - 模式：'create' | 'edit'
 * @returns {Object} 快速录单的订单表单配置
 */
export function getQuickOrderFormConfig(mode = 'create') {
  const config = getDefaultOrderFormConfig(mode)

  // 只保留最核心的字段
  const coreFields = {
    'customer-info-section': ['customerName', 'customerPhone', 'salesAgentName'],
    'product-info-section': ['dealDate', 'vehicleSeries', 'salesAmount'],
    'outbound-info-section': ['expectedOutboundDate', 'outboundOrgName'],
    'payment-method-section': ['paymentMethod'],
  }

  // 隐藏非核心区域
  config.sections['vehicle-exchange-section'].visible = false
  config.sections['insurance-section'].visible = false
  config.sections['exclusive-discount-section'].visible = false
  config.sections['gift-items-section'].visible = false
  config.sections['remark-section'].visible = false

  // 只显示核心字段
  Object.keys(coreFields).forEach(sectionKey => {
    const section = config.sections[sectionKey]
    if (section) {
      Object.keys(section.fields).forEach(fieldKey => {
        if (!coreFields[sectionKey].includes(fieldKey)) {
          section.fields[fieldKey].visible = false
        }
      })
    }
  })

  return config
}

/**
 * 根据用户权限获取订单表单配置
 * @param {string} mode - 模式：'create' | 'edit'
 * @param {Object} permissions - 用户权限对象
 * @returns {Object} 基于权限的订单表单配置
 */
export function getPermissionBasedOrderFormConfig(mode = 'create', permissions = {}) {
  const config = getDefaultOrderFormConfig(mode)

  // 如果没有客户管理权限，隐藏客户选择器
  if (!permissions.customerManagement) {
    config.sections['customer-info-section']['show-customer-selector'] = false
  }

  // 如果没有车辆管理权限，隐藏车辆选择器
  if (!permissions.vehicleManagement) {
    config.sections['product-info-section']['show-vehicle-selector'] = false
  }

  // 如果没有价格编辑权限，价格相关字段设为只读
  if (!permissions.priceEdit) {
    config.sections['product-info-section'].fields.salesAmount.editable = false
    config.sections['product-info-section'].fields.discountAmount.editable = false
  }

  // 如果没有财务权限，隐藏财务相关区域
  if (!permissions.financial) {
    config.sections['exclusive-discount-section'].visible = false
    config.sections['financial-settlement-section'].visible = false
  }

  return config
}

/**
 * 获取移动端优化的订单表单配置
 * @param {string} mode - 模式：'create' | 'edit'
 * @returns {Object} 移动端优化的订单表单配置
 */
export function getMobileOrderFormConfig(mode = 'create') {
  const config = getSimpleOrderFormConfig(mode)

  // 移动端通常分步骤填写，可以根据需要进一步简化
  // 这里可以添加移动端特有的配置逻辑

  return config
}

/**
 * 合并配置对象
 * @param {Object} baseConfig - 基础配置
 * @param {Object} overrideConfig - 覆盖配置
 * @returns {Object} 合并后的配置
 */
export function mergeOrderFormConfig(baseConfig, overrideConfig) {
  const merged = JSON.parse(JSON.stringify(baseConfig)) // 深拷贝

  if (overrideConfig.mode) {
    merged.mode = overrideConfig.mode
  }

  if (overrideConfig.sections) {
    Object.keys(overrideConfig.sections).forEach(sectionKey => {
      if (!merged.sections[sectionKey]) {
        merged.sections[sectionKey] = overrideConfig.sections[sectionKey]
      } else {
        // 合并section级别的配置
        Object.assign(merged.sections[sectionKey], overrideConfig.sections[sectionKey])

        // 合并字段级别的配置
        if (overrideConfig.sections[sectionKey].fields) {
          Object.keys(overrideConfig.sections[sectionKey].fields).forEach(fieldKey => {
            if (!merged.sections[sectionKey].fields[fieldKey]) {
              merged.sections[sectionKey].fields[fieldKey] = overrideConfig.sections[sectionKey].fields[fieldKey]
            } else {
              Object.assign(
                merged.sections[sectionKey].fields[fieldKey],
                overrideConfig.sections[sectionKey].fields[fieldKey]
              )
            }
          })
        }
      }
    })
  }

  return merged
}

/**
 * 验证配置对象的有效性
 * @param {Object} config - 配置对象
 * @returns {Object} 验证结果 { valid: boolean, errors: string[] }
 */
export function validateOrderFormConfig(config) {
  const errors = []

  if (!config) {
    errors.push('配置对象不能为空')
    return { valid: false, errors }
  }

  if (!config.mode || !['create', 'edit'].includes(config.mode)) {
    errors.push('mode 必须是 "create" 或 "edit"')
  }

  if (!config.sections || typeof config.sections !== 'object') {
    errors.push('sections 必须是一个对象')
    return { valid: false, errors }
  }

  // 验证每个section的配置
  Object.keys(config.sections).forEach(sectionKey => {
    const section = config.sections[sectionKey]

    if (typeof section.visible !== 'boolean') {
      errors.push(`${sectionKey}.visible 必须是布尔值`)
    }

    if (typeof section.editable !== 'boolean') {
      errors.push(`${sectionKey}.editable 必须是布尔值`)
    }

    if (section.fields && typeof section.fields !== 'object') {
      errors.push(`${sectionKey}.fields 必须是一个对象`)
    }

    // 验证字段配置
    if (section.fields) {
      Object.keys(section.fields).forEach(fieldKey => {
        const field = section.fields[fieldKey]

        if (typeof field.visible !== 'boolean') {
          errors.push(`${sectionKey}.fields.${fieldKey}.visible 必须是布尔值`)
        }

        if (typeof field.editable !== 'boolean') {
          errors.push(`${sectionKey}.fields.${fieldKey}.editable 必须是布尔值`)
        }
      })
    }
  })

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 获取配置中所有可见且可编辑的字段列表
 * @param {Object} config - 配置对象
 * @returns {string[]} 字段名称数组
 */
export function getEditableFields(config) {
  const editableFields = []

  if (!config || !config.sections) {
    return editableFields
  }

  Object.keys(config.sections).forEach(sectionKey => {
    const section = config.sections[sectionKey]

    // 只有当区域可见且可编辑时，才检查字段
    if (section.visible && section.editable && section.fields) {
      Object.keys(section.fields).forEach(fieldKey => {
        const field = section.fields[fieldKey]

        // 字段必须可见且可编辑
        if (field.visible && field.editable) {
          editableFields.push(fieldKey)
        }
      })
    }
  })

  return editableFields
}

/**
 * 获取配置中所有可见字段列表
 * @param {Object} config - 配置对象
 * @returns {string[]} 字段名称数组
 */
export function getVisibleFields(config) {
  const visibleFields = []

  if (!config || !config.sections) {
    return visibleFields
  }

  Object.keys(config.sections).forEach(sectionKey => {
    const section = config.sections[sectionKey]

    // 只有当区域可见时，才检查字段
    if (section.visible && section.fields) {
      Object.keys(section.fields).forEach(fieldKey => {
        const field = section.fields[fieldKey]

        // 字段必须可见
        if (field.visible) {
          visibleFields.push(fieldKey)
        }
      })
    }
  })

  return visibleFields
}
