<template>
  <div class="vehicle-table-container">
    <n-data-table
      :columns="columns"
      :data="vehicleList"
      :bordered="true"
      :single-line="false"
      :row-key="(row) => row.id"
      style="margin-bottom: 20px"
    >
      <template #empty>
        <n-empty description="暂无车辆数据，请点击➕新增" />
      </template>
    </n-data-table>
  </div>

  <!-- 车辆SKU查询器 -->
  <VehicleSKUSelector
    v-model:visible="vehicleSelectorVisible"
    @select="handleVehicleSelected"
  />
</template>

<script setup>
import { ref, reactive, computed, markRaw, h, watch } from "vue";
import { NDataTable, NInputNumber, NIcon, NEmpty } from "naive-ui";
import {
  AddCircleOutline as AddOutline,
  TrashOutline,
  Server as ArchiveOutline,
} from "@vicons/ionicons5";
import messages from "@/utils/messages";
// 引入车辆SKU选择器组件
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";
import { getNumberInputConfig } from "@/config/inputConfig";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const AddOutlineIcon = markRaw(AddOutline);
const TrashOutlineIcon = markRaw(TrashOutline);
const ArchiveOutlineIcon = markRaw(ArchiveOutline);

// 定义组件属性
const props = defineProps({
  // 车辆列表数据
  modelValue: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits([
  "update:modelValue",
  "calculate-total-sb-price",
  "calculate-total-sale-price",
  "calculate-final-price",
  "calculate-profit-rate",
]);

// 车辆列表数据
const vehicleList = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 车辆选择器相关
const vehicleSelectorVisible = ref(false);
const currentEditingRowIndex = ref(-1);

// 创建表格列配置
const createColumns = () => {
  return [
    {
      title: () => {
        return h(
          "div",
          {
            style: {
              display: "flex",
              justifyContent: "center",
            },
          },
          [
            h(NIcon, {
              component: TrashOutlineIcon,
              size: 22,
              color: "#d03050",
              style: {
                cursor: "pointer",
                marginRight: "10px",
              },
              onClick: () => confirmClearAll(),
            }),
            h(NIcon, {
              component: AddOutlineIcon,
              size: 24,
              color: "#18a058",
              style: {
                cursor: "pointer",
              },
              onClick: () => addVehicleRow(),
            }),
          ]
        );
      },
      key: "actions",
      width: 80,
      align: "center",
      render: (_, index) => {
        return h(
          "div",
          {
            style: {
              display: "flex",
              justifyContent: "center",
            },
          },
          [
            h(NIcon, {
              component: TrashOutlineIcon,
              size: 20,
              color: "#d03050",
              style: {
                cursor: "pointer",
                marginRight: "10px",
              },
              onClick: () => removeVehicleRow(index),
            }),
            h(NIcon, {
              component: ArchiveOutlineIcon,
              size: 20,
              color: "#2080f0",
              style: {
                cursor: "pointer",
              },
              onClick: () => showVehicleSelectorForRow(index),
            }),
          ]
        );
      },
    },
    {
      title: "品牌",
      key: "brand",
      width: 120,
    },
    {
      title: "车型",
      key: "series",
      width: 150,
    },
    {
      title: "配置",
      key: "configName",
      width: 180,
    },
    {
      title: "颜色代码",
      key: "colorCode",
      width: 100,
    },
    {
      title: "启票单价(元)",
      key: "sbPrice",
      width: 120,
    },
    {
      title: "数量(台)",
      key: "quantity",
      width: 120,
      render: (row, index) => {
        return h(NInputNumber, {
          value: row.quantity,
          buttonPlacement: "both",
          min: 1,
          max: 999,
          style: { width: "100%" },
          onUpdateValue: (value) => {
            vehicleList.value[index].quantity = value;
            // 更新金额计算
            updateCalculations();
          },
        });
      },
    },
    {
      title: "销售单价(元)",
      key: "salePrice",
      width: 150,
      render: (row, index) => {
        return h(NInputNumber, {
          value: row.salePrice,
          buttonPlacement: "both",
          min: amountInputConfig.value.min,
          max: amountInputConfig.value.max,
          precision: amountInputConfig.value.precision,
          step: amountInputConfig.value.step,
          style: { width: "100%" },
          onUpdateValue: (value) => {
            vehicleList.value[index].salePrice = value;
            // 更新金额计算
            updateCalculations();
          },
        });
      },
    },
  ];
};

// 表格列配置
const columns = createColumns();

// 使用dialog API
const dialog = window.$dialog;

// 添加车辆行
const addVehicleRow = (index) => {
  const newRow = {
    id: Date.now(),
    brand: "",
    series: "",
    configName: "",
    colorCode: "",
    sbPrice: 0,
    quantity: 1,
    salePrice: 0,
  };

  const newList = [...vehicleList.value];
  if (index !== undefined) {
    // 在指定位置插入
    newList.splice(index, 0, newRow);
  } else {
    // 添加到末尾
    newList.push(newRow);
  }

  vehicleList.value = newList;
};

// 删除车辆行
const removeVehicleRow = (index) => {
  const newList = [...vehicleList.value];
  newList.splice(index, 1);
  vehicleList.value = newList;

  // 更新金额计算
  updateCalculations();
};

// 确认清除所有车辆
const confirmClearAll = () => {
  if (vehicleList.value.length === 0) return;

  dialog.warning({
    title: "确认清除",
    content: `是否清除全部数据？共 ${vehicleList.value.length} 条记录将被删除。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      vehicleList.value = [];
      // 更新金额计算
      updateCalculations();
    },
  });
};

// 为特定行显示车辆选择器
const showVehicleSelectorForRow = (index) => {
  currentEditingRowIndex.value = index;
  vehicleSelectorVisible.value = true;
};

// 处理车辆选择
const handleVehicleSelected = (vehicle) => {
  if (!vehicle) return;

  // 创建新的车辆数据
  const sbPrice = vehicle.sbPrice || 0;
  const vehicleData = {
    id: Date.now(),
    brand: vehicle.brand || "",
    series: vehicle.series || "",
    configName: vehicle.configName || "",
    colorCode: vehicle.colorCode || "",
    // sbPrice是启票价格，单位为元
    sbPrice: sbPrice,
    quantity: 1,
    salePrice: parseFloat((sbPrice * 1.35).toFixed(2)), // 默认销售价为启票价的1.35倍
    skuId: vehicle.id || null, // 保存SKU ID，用于API请求
  };

  const newList = [...vehicleList.value];
  if (currentEditingRowIndex.value >= 0) {
    // 更新特定行
    newList[currentEditingRowIndex.value] = {
      ...newList[currentEditingRowIndex.value],
      ...vehicleData,
    };
    messages.success(`已更新第 ${currentEditingRowIndex.value + 1} 行车型数据`);
  } else {
    // 添加到车辆列表末尾
    newList.push(vehicleData);
    messages.success(`已添加车型: ${vehicle.brand} ${vehicle.series}`);
  }

  vehicleList.value = newList;

  // 重置当前编辑行索引
  currentEditingRowIndex.value = -1;

  // 更新金额计算
  updateCalculations();
};

// 更新所有金额计算
const updateCalculations = () => {
  emit("calculate-total-sb-price");
  emit("calculate-total-sale-price");
  emit("calculate-final-price");
  emit("calculate-profit-rate");
};

// 监听车辆列表变化，自动计算金额
watch(
  vehicleList,
  () => {
    updateCalculations();
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  addVehicleRow,
  removeVehicleRow,
  confirmClearAll,
  showVehicleSelectorForRow,
});
</script>

<style scoped>
.vehicle-table-container {
  margin-bottom: 20px;
}

:deep(.n-data-table .n-data-table-td) {
  padding: 8px 12px;
}

:deep(.n-data-table-tr:hover) {
  background-color: rgba(24, 160, 88, 0.05);
}
</style>
