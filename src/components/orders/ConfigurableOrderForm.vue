<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    :title="title"
    preset="card"
    style="width: 100%; height: 100%"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
      class="order-form"
    >
      <!-- 动态渲染各个section组件 -->
      <template
        v-for="(sectionConfig, sectionKey) in visibleSections"
        :key="sectionKey"
      >
        <!-- 客户信息部分 -->
        <customer-info-section
          v-if="sectionKey === 'customer-info-section'"
          :form="form"
          :config="sectionConfig"
          @show-customer-selector="showCustomerSelector"
        />

        <!-- 产品信息部分 -->
        <product-info-section
          v-else-if="sectionKey === 'product-info-section'"
          :form="form"
          :config="sectionConfig"
          @show-vehicle-selector="showVehicleSelector"
          @handle-invoice-price-change="handleInvoicePriceChange"
          @handle-sale-price-change="handleSalePriceChange"
          @handle-discount-change="handleDiscountChange"
          @handle-discount-deductible-change="handleDiscountChange"
          @handle-sales-expense-change="handleSalesExpenseChange"
          @validate="handleValidate"
          @vehicle-selected="handleVehicleSelected"
          @update:form="handleFormUpdate"
        />

        <!-- 出库信息部分 -->
        <outbound-info-section
          v-else-if="sectionKey === 'outbound-info-section'"
          :form="form"
          :config="sectionConfig"
          :selected-outbound-org="selectedOutboundOrg"
          @handle-outbound-org-change="handleOutboundOrgChange"
          @update:form="handleFormUpdate"
        />

        <!-- 付款方式部分 -->
        <payment-method-section
          v-else-if="sectionKey === 'payment-method-section'"
          :form="form"
          :config="sectionConfig"
          :loan-channel-options="loanChannelOptions"
          :loan-months-options="loanMonthsOptions"
          :show-loan-rebate-receivable="false"
          @handle-loan-amount-change="handleLoanAmountChange"
          @handle-loan-initial-amount-change="handleLoanInitialAmountChange"
          @handle-loan-fee-change="handleLoanFeeChange"
          @handle-loan-rebate-payable-amount-change="
            handleLoanRebatePayableAmountChange
          "
          @handle-loan-rebate-payable-deductible-change="
            handleLoanRebatePayableDeductibleChange
          "
        />

        <!-- 车辆置换部分 -->
        <vehicle-exchange-section
          v-else-if="sectionKey === 'vehicle-exchange-section'"
          :form="form"
          :config="sectionConfig"
          @handle-used-vehicle-to-vehicle-amount-change="
            handleusedVehicleDiscountPayableDeductibleAmountChange
          "
          @handle-used-vehicle-discount-payable-amount-change="
            handleUsedVehicleDiscountPayableAmountChange
          "
          @handle-used-vehicle-discount-payable-deductible-change="
            handleUsedVehicleDiscountPayableDeductibleChange
          "
        />

        <!-- 车辆保险部分 -->
        <insurance-section
          v-else-if="sectionKey === 'insurance-section'"
          :form="form"
          :config="sectionConfig"
          @update:form="handleFormUpdate"
        />

        <!-- 专享优惠部分 -->
        <exclusive-discount-section
          v-else-if="sectionKey === 'exclusive-discount-section'"
          :form="form"
          :config="sectionConfig"
          @handle-exclusive-discount-change="handleExclusiveDiscountChange"
          @handle-exclusive-discount-receivable-amount-change="
            handleExclusiveDiscountReceivableAmountChange
          "
          @handle-exclusive-discount-payable-amount-change="
            handleExclusiveDiscountPayableAmountChange
          "
          @handle-exclusive-discount-payable-deductible-change="
            handleExclusiveDiscountPayableDeductibleChange
          "
        />

        <!-- 赠品明细部分 -->
        <gift-items-section
          v-else-if="sectionKey === 'gift-items-section'"
          :form="form"
          :config="sectionConfig"
          ref="giftItemsSectionRef"
        />

        <!-- 订单备注部分 -->
        <remark-section
          v-else-if="sectionKey === 'remark-section'"
          :form="form"
          :config="sectionConfig"
          ref="remarkSectionRef"
        />

        <!-- 财务结算部分 -->
        <financial-settlement-section
          v-else-if="sectionKey === 'financial-settlement-section'"
          :form="form"
          :config="sectionConfig"
          @handle-sale-price-change="handleSalePriceChange"
          @handle-discount-change="handleDiscountChange"
        />
      </template>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave">确定</n-button>
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <customer-selector
      v-if="shouldShowCustomerSelector"
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelected"
    />

    <!-- 车辆选择器 -->
    <vehicle-sku-selector
      v-if="shouldShowVehicleSelector"
      v-model:visible="vehicleSelectorVisible"
      @select="handleVehicleSelected"
    />
  </n-modal>
</template>

<script setup>
import { computed, ref } from "vue";
import { useOrderEditModal } from "./OrderEditModal.js";
import { validateOrderFormConfig } from "./config/OrderFormConfigFactory.js";

// 引入选择器组件
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";

// 引入各个部分组件
import CustomerInfoSection from "@/components/orders/sections/CustomerInfoSection.vue";
import ProductInfoSection from "@/components/orders/sections/ProductInfoSection.vue";
import OutboundInfoSection from "@/components/orders/sections/OutboundInfoSection.vue";
import PaymentMethodSection from "@/components/orders/sections/PaymentMethodSection.vue";
import VehicleExchangeSection from "@/components/orders/sections/VehicleExchangeSection.vue";
import InsuranceSection from "@/components/orders/sections/InsuranceSection.vue";
import ExclusiveDiscountSection from "@/components/orders/sections/ExclusiveDiscountSection.vue";
import GiftItemsSection from "@/components/orders/sections/GiftItemsSection.vue";
import RemarkSection from "@/components/orders/sections/RemarkSection.vue";
import FinancialSettlementSection from "@/components/orders/sections/FinancialSettlementSection.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object,
    required: true,
    validator: (config) => {
      const validation = validateOrderFormConfig(config);
      if (!validation.valid) {
        console.error("订单表单配置验证失败:", validation.errors);
        return false;
      }
      return true;
    },
  },
  title: {
    type: String,
    default: "订单表单",
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
  vehicleCategoryOptions: {
    type: Array,
    default: () => [],
  },
  orderStatusOptions: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 计算可见的section
const visibleSections = computed(() => {
  const sections = {};
  if (props.config && props.config.sections) {
    Object.keys(props.config.sections).forEach((sectionKey) => {
      const section = props.config.sections[sectionKey];
      if (section.visible) {
        sections[sectionKey] = section;
      }
    });
  }
  return sections;
});

// 计算是否应该显示选择器
const shouldShowCustomerSelector = computed(() => {
  const customerSection = props.config?.sections?.["customer-info-section"];
  return (
    customerSection?.visible && customerSection?.["show-customer-selector"]
  );
});

const shouldShowVehicleSelector = computed(() => {
  const productSection = props.config?.sections?.["product-info-section"];
  return productSection?.visible && productSection?.["show-vehicle-selector"];
});

// 使用原有的组合式函数，但传入配置化的props
const configuredProps = computed(() => ({
  ...props,
  isEdit: props.config?.mode === "edit",
}));

// 使用组合式函数
const {
  // 响应式数据
  formRef,
  customerSelectorVisible,
  vehicleSelectorVisible,
  giftItemsSectionRef,
  remarkSectionRef,
  selectedOutboundOrg,
  form,
  rules,
  loanChannelOptions,
  loanMonthsOptions,
  modelVisible,

  // 方法
  updateVisible: originalUpdateVisible,
  showCustomerSelector,
  showVehicleSelector,
  handleVehicleSelected,
  handleInvoicePriceChange,
  handleSalePriceChange,
  handleDiscountChange,
  handleSalesExpenseChange,
  handleExclusiveDiscountChange,
  handleExclusiveDiscountReceivableAmountChange,
  handleExclusiveDiscountPayableAmountChange,
  handleExclusiveDiscountPayableDeductibleChange,
  handleLoanAmountChange,
  handleLoanInitialAmountChange,
  handleLoanFeeChange,
  handleLoanRebatePayableAmountChange,
  handleLoanRebatePayableDeductibleChange,
  handleusedVehicleDiscountPayableDeductibleAmountChange,
  handleUsedVehicleDiscountPayableAmountChange,
  handleUsedVehicleDiscountPayableDeductibleChange,
  handleOutboundOrgChange,
  handleCustomerSelected,
  handleSave,
  handleCancel,
  resetForm,
  setFormData,
} = useOrderEditModal(configuredProps, emit);

// 重写 updateVisible 函数，在弹窗关闭时重置表单
const updateVisible = (val) => {
  if (!val) {
    // 当弹窗关闭时，重置表单数据
    resetForm();
  }
  originalUpdateVisible(val);
};

// 处理表单验证
const handleValidate = (callback) => {
  // 验证表单
  if (!formRef.value) {
    callback?.(null);
    return;
  }

  // 验证整个表单
  formRef.value.validate((errors) => {
    callback?.(errors);
  });
};

// 处理表单更新
const handleFormUpdate = (updatedForm) => {
  // 通过组合式函数更新表单数据，确保响应式更新
  Object.assign(form, updatedForm);

  // 如果更新了出库单位信息，同步更新selectedOutboundOrg
  if (updatedForm.outboundOrgId !== undefined) {
    if (updatedForm.outboundOrgId) {
      // 构造出库单位对象
      selectedOutboundOrg.value = {
        id: updatedForm.outboundOrgId,
        orgName: updatedForm.outboundOrgName,
        name: updatedForm.outboundOrgName, // 兼容性字段
      };
    } else {
      selectedOutboundOrg.value = null;
    }
  }
};

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<style lang="scss">
@use "../OrderEditModal.scss";
</style>
