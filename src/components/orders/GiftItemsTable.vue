<template>
  <div class="gift-items-table">
    <n-data-table
      :columns="columns"
      :data="tableData"
      :bordered="true"
      :single-line="false"
      size="small"
      :pagination="false"
      :max-height="400"
      :striped="true"
    />

    <!-- 合计区域 -->
    <div v-if="tableData.length > 0" class="gift-total-area">
      <div class="gift-total-content">
        <span class="gift-total-label">赠品总价：</span>
        <span class="gift-total-value">{{ formatMoney(totalAmount, 2) }}</span>
      </div>
    </div>
  </div>

  <!-- 赠品选择器 -->
  <GiftStockSelector
    v-model:visible="giftSelectorVisible"
    :multiple="true"
    :params="giftSelectorParams"
    @select="handleGiftSelected"
  />
</template>

<script setup>
import { ref, markRaw, watch, h, computed } from "vue";
import {
  NButton,
  NIcon,
  NDataTable,
  NPopconfirm,
  NTag,
  NInputNumber,
} from "naive-ui";
import { Add, TrashOutline } from "@vicons/ionicons5";
import GiftStockSelector from "@/components/inventory/GiftStockSelector.vue";
import { formatMoney } from "@/utils/money.js";
import messages from "@/utils/messages";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const AddIcon = markRaw(Add);
const DeleteIcon = markRaw(TrashOutline);

// 赠品选择器状态
const giftSelectorVisible = ref(false);

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件事件
const emit = defineEmits(["update:modelValue"]);

// 表格数据
const tableData = ref([]);

// 初始化表格数据
if (props.modelValue && props.modelValue.length > 0) {
  tableData.value = [...props.modelValue];
} else {
  // 如果没有初始数据，添加一个空行
  tableData.value = [];
}

// 监听表格数据变化，更新modelValue
watch(
  tableData,
  (newValue) => {
    emit("update:modelValue", newValue);
  },
  { deep: true }
);

// 监听modelValue变化，更新表格数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (
      newValue &&
      JSON.stringify(newValue) !== JSON.stringify(tableData.value)
    ) {
      tableData.value = [...newValue];
    }
  },
  { deep: true }
);

// 添加行
const addRow = () => {
  tableData.value.push({
    id: Date.now(), // 使用时间戳作为临时ID
    type: "实物", // 赠品类型
    name: "", // 赠品名称
    spec: "", // 规格型号
    unit: "个", // 仓储单位
    quantity: 1, // 数量
    unitPrice: 0, // 单价
    totalPrice: 0, // 总价
    giftId: null, // 赠品ID
  });
};

// 打开赠品选择器
const openGiftSelector = () => {
  // 检查是否存在销售机构ID
  if (!props.form?.salesOrgId) {
    messages.warning("请先选择销售机构");
    return;
  }

  giftSelectorVisible.value = true;
};

// 处理赠品选择
const handleGiftSelected = (selectedGifts) => {
  // 如果是数组，表示多选模式
  if (Array.isArray(selectedGifts)) {
    // 为每个选中的赠品添加一行，但需要去重
    selectedGifts.forEach((gift) => {
      // 检查是否已存在相同的赠品（根据赠品ID判断）
      const existingIndex = tableData.value.findIndex(
        (item) => item.giftId === gift.id
      );

      // 如果不存在，则添加新行
      if (existingIndex === -1) {
        tableData.value.push({
          id: Date.now() + Math.random(), // 使用时间戳+随机数作为临时ID
          type: gift.type, // 赠品类型，从选择器返回的数据中获取
          name: gift.name, // 赠品名称
          spec: gift.spec, // 赠品规格
          unit: gift.unit, // 仓储单位
          quantity: 1, // 默认数量为1
          stockQuantity: gift.stockQuantity || gift.stock || 0, // 库存数量
          unitPrice: gift.costPrice / 100, // 单价，从分转为元
          totalPrice: gift.costPrice / 100, // 总价 = 单价 * 数量
          giftId: gift.id, // 保存赠品ID以便后续使用
          giftInfo: gift, // 保存完整的赠品信息
        });
      }
      // 如果已存在，可以选择更新数量或其他操作，这里我们不做任何操作
    });
  }
  // 单个对象，表示单选模式
  else if (selectedGifts && typeof selectedGifts === "object") {
    const gift = selectedGifts;

    // 检查是否已存在相同的赠品
    const existingIndex = tableData.value.findIndex(
      (item) => item.giftId === gift.id
    );

    // 如果不存在，则添加新行
    if (existingIndex === -1) {
      tableData.value.push({
        id: Date.now(), // 使用时间戳作为临时ID
        type: gift.type, // 赠品类型，从选择器返回的数据中获取
        name: gift.name, // 赠品名称
        spec: gift.spec, // 赠品规格
        unit: gift.unit, // 仓储单位
        quantity: 1, // 默认数量为1
        stockQuantity: gift.stockQuantity || gift.stock || 0, // 库存数量
        unitPrice: gift.costPrice / 100, // 单价，从分转为元
        totalPrice: gift.costPrice / 100, // 总价 = 单价 * 数量
        giftId: gift.id, // 保存赠品ID以便后续使用
        giftInfo: gift, // 保存完整的赠品信息
      });
    }
    // 如果已存在，可以选择更新数量或其他操作，这里我们不做任何操作
  }
};

// 处理数量变化
const handleQuantityChange = (value, row) => {
  // 确保数量不超过库存数量
  const maxQuantity = row.stockQuantity || 9999;
  const validValue = Math.min(value, maxQuantity);

  // 更新数量
  row.quantity = validValue;

  // 更新总价
  row.totalPrice = row.unitPrice * validValue;

  // 强制更新视图
  tableData.value = [...tableData.value];
};

// 删除行
const handleDelete = (row) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1);
  }
};

// 表格列定义
const columns = [
  {
    title: () =>
      h(
        "div",
        {
          style:
            "display: flex; justify-content: center; align-items: center; height: 100%;",
        },
        h(
          NButton,
          {
            size: "tiny",
            type: "primary",
            circle: true,
            onClick: openGiftSelector,
            disabled: props.disabled,
            style:
              "display: flex; justify-content: center; align-items: center;",
          },
          {
            default: () =>
              h(NIcon, { size: 16 }, { default: () => h(AddIcon) }),
          }
        )
      ),
    key: "index",
    width: 50,
    align: "center",
    render: (_, index) =>
      h("span", { style: "color: #666; font-size: 13px;" }, index + 1),
  },
  {
    title: "赠品类型",
    key: "type",
    width: 100,
    render: (row) => {
      const type = row.type;
      const color = type === "服务" ? "info" : "success";
      return h(
        NTag,
        {
          type: "primary",
          size: "small",
          color: {
            color: color === "success" ? "#18a058" : "#2080f0",
            textColor: "white",
          },
        },
        { default: () => type }
      );
    },
  },
  {
    title: "赠品名称",
    key: "name",
    width: 150,
  },
  {
    title: "规格型号",
    key: "spec",
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "仓储单位",
    key: "unit",
    width: 80,
  },
  {
    title: "数量",
    key: "quantity",
    width: 100,
    align: "center",
    render: (row) => {
      return h(NInputNumber, {
        value: row.quantity,
        min: 1,
        max: row.stockQuantity || 9999, // 使用库存数量作为最大值
        size: "small",
        buttonPlacement: "both",
        disabled: props.disabled,
        onUpdateValue: (value) => handleQuantityChange(value, row),
      });
    },
  },
  {
    title: "单价(元)",
    key: "unitPrice",
    width: 100,
    align: "right",
    render: (row) => {
      // 使用formatMoney格式化单价，添加千分位
      return h("span", {}, formatMoney(row.unitPrice, 2, ""));
    },
  },
  {
    title: "金额(元)",
    key: "totalPrice",
    width: 100,
    align: "right",
    render: (row) => {
      // 使用formatMoney格式化总价，添加千分位
      return h(
        "span",
        {
          style: "color: #f0a020; font-weight: bold;",
        },
        formatMoney(row.totalPrice, 2, "")
      );
    },
  },
  {
    title: () =>
      h(
        "div",
        {
          class: "clear-all-button",
          style:
            "display: flex; justify-content: center; align-items: center; height: 100%; cursor: pointer;",
        },
        props.disabled
          ? h(
              "div",
              {
                class: "delete-icon-wrapper disabled",
                style:
                  "display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; opacity: 0.3;",
              },
              h(NIcon, { size: 18 }, { default: () => h(DeleteIcon) })
            )
          : h(
              NPopconfirm,
              {
                onPositiveClick: () => {
                  tableData.value = [];
                },
              },
              {
                trigger: () =>
                  h(
                    "div",
                    {
                      class: "delete-icon-wrapper",
                      style:
                        "display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;",
                    },
                    h(NIcon, { size: 18 }, { default: () => h(DeleteIcon) })
                  ),
                default: () => "确定清空所有赠品吗？",
              }
            )
      ),
    key: "actions",
    width: 60,
    align: "center",
    render: (row) => {
      return h(
        NPopconfirm,
        {
          onPositiveClick: () => handleDelete(row),
        },
        {
          trigger: () =>
            h(
              NButton,
              {
                size: "small",
                type: "error",
                quaternary: true,
                disabled: props.disabled,
              },
              {
                icon: () => h(NIcon, null, { default: () => h(DeleteIcon) }),
              }
            ),
          default: () => "确定删除此赠品吗？",
        }
      );
    },
  },
];

// 计算属性：赠品选择器参数
const giftSelectorParams = computed(() => {
  const params = {};

  // 如果存在销售机构ID，添加到查询参数中
  if (props.form?.salesOrgId) {
    params.stockOrgId = props.form.salesOrgId;
  }

  return params;
});

// 计算赠品总价
const totalAmount = computed(() => {
  if (!tableData.value || tableData.value.length === 0) {
    return 0;
  }

  return tableData.value.reduce((sum, item) => {
    return sum + (item.totalPrice || 0);
  }, 0);
});

// 暴露方法和计算属性给父组件
defineExpose({
  addRow,
  clearRows: () => {
    tableData.value = [];
  },
  totalAmount,
});
</script>

<style scoped>
.gift-items-table {
  margin-bottom: 15px;
}

:deep(.n-data-table-th) {
  background-color: #f5f7fa;
  padding: 10px 12px;
  font-weight: bold;
  color: #333;
}

:deep(.n-data-table-td) {
  padding: 8px 12px;
}

/* 表格行样式 */
:deep(.n-data-table-tr:nth-child(even)) {
  background-color: rgba(0, 0, 0, 0.02);
}

:deep(.n-data-table-tr:hover) {
  background-color: rgba(24, 160, 88, 0.05);
}

/* 加号按钮样式 */
:deep(.n-button.n-button--primary-type.n-button--circle) {
  width: 22px;
  height: 22px;
  min-width: 22px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-button.n-button--primary-type.n-button--circle .n-icon) {
  margin: 0;
}

/* 删除图标样式 */
.delete-icon-wrapper {
  opacity: 0.5;
  transition: all 0.2s ease;
  color: #d03050;
}

.delete-icon-wrapper:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 隐藏表头文字 */
:deep(.clear-all-button .n-popover-trigger-content) {
  width: 100%;
  height: 100%;
}

/* 删除按钮样式 */
:deep(.n-button.n-button--error-type.n-button--quaternary) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  height: 24px;
  min-width: 24px;
}

:deep(.n-button.n-button--error-type.n-button--quaternary:hover) {
  background-color: rgba(208, 48, 80, 0.1);
}

:deep(.n-data-table-td[align="center"]) {
  text-align: center;
}

/* 合计区域样式 */
.gift-total-area {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-top: none;
  border-radius: 0 0 4px 4px;
}

.gift-total-content {
  display: flex;
  align-items: center;
}

.gift-total-label {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-right: 8px;
}

.gift-total-value {
  font-size: 16px;
  font-weight: bold;
  color: #f0a020;
}
</style>
