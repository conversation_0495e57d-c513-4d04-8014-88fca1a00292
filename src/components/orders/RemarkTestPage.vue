<template>
  <div class="remark-test-page">
    <n-card title="备注功能测试页面">
      <n-space vertical>
        <n-alert type="info">
          这个页面用于测试 ConfigurableOrderForm 的备注功能是否正常工作。
          测试场景：新备注应该追加到历史备注前面，而不是覆盖历史备注。
        </n-alert>

        <n-space>
          <n-button type="primary" @click="showFormWithHistory">
            测试有历史备注的表单
          </n-button>
          <n-button @click="showFormWithoutHistory">
            测试无历史备注的表单
          </n-button>
        </n-space>

        <!-- 测试数据展示 -->
        <n-card title="当前测试数据" size="small" v-if="testFormData.remark">
          <n-code :code="testFormData.remark" language="text" />
        </n-card>

        <!-- 保存结果展示 -->
        <n-card title="保存结果" size="small" v-if="saveResult">
          <n-code :code="saveResult" language="json" />
        </n-card>

        <!-- ConfigurableOrderForm -->
        <configurable-order-form
          v-model:visible="formVisible"
          :config="formConfig"
          :title="formTitle"
          :initial-data="testFormData"
          @save="handleSave"
          @cancel="handleCancel"
        />
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NCard, NSpace, NButton, NAlert, NCode } from 'naive-ui'
import ConfigurableOrderForm from './ConfigurableOrderForm.vue'
import { getDefaultOrderFormConfig } from './config/OrderFormConfigFactory.js'

// 响应式数据
const formVisible = ref(false)
const formTitle = ref('备注功能测试')
const saveResult = ref('')

// 表单配置 - 只显示备注相关的部分
const formConfig = ref({
  mode: 'create',
  sections: {
    'remark-section': {
      visible: true,
      editable: true,
      fields: {
        remark: {
          visible: true,
          editable: true
        }
      }
    }
  }
})

// 测试表单数据
const testFormData = ref({})

// 显示有历史备注的表单
const showFormWithHistory = () => {
  testFormData.value = {
    remark: '2024/1/15 10:30:00|张三|这是第一条历史备注\n2024/1/14 15:20:00|李四|这是第二条历史备注\n2024/1/13 09:10:00|王五|这是第三条历史备注'
  }
  formTitle.value = '测试有历史备注的表单'
  formVisible.value = true
}

// 显示无历史备注的表单
const showFormWithoutHistory = () => {
  testFormData.value = {
    remark: ''
  }
  formTitle.value = '测试无历史备注的表单'
  formVisible.value = true
}

// 处理保存
const handleSave = (formData) => {
  console.log('保存的表单数据:', formData)
  saveResult.value = JSON.stringify(formData, null, 2)
  formVisible.value = false
  
  // 验证备注是否正确合并
  if (formData.remark) {
    const remarkLines = formData.remark.split('\n')
    console.log('备注行数:', remarkLines.length)
    console.log('备注内容:', remarkLines)
    
    // 检查是否有新备注添加
    const hasNewRemark = remarkLines.some(line => 
      line.includes('当前用户') && 
      line.includes(new Date().toLocaleDateString())
    )
    
    if (hasNewRemark) {
      console.log('✅ 备注功能正常：新备注已添加')
    } else {
      console.log('ℹ️ 没有添加新备注（可能是因为备注输入框为空）')
    }
  }
}

// 处理取消
const handleCancel = () => {
  formVisible.value = false
}
</script>

<style scoped>
.remark-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>
