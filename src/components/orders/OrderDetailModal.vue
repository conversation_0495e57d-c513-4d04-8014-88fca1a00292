<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    title="订单详情"
    preset="card"
    style="width: 100%; height: 100%"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-spin :show="loading">
      <div v-if="orderDetail" class="order-detail-content">
        <!-- 基本信息 -->
        <BasicInfoSection :order-detail="orderDetail" />

        <!-- 客户信息 -->
        <CustomerInfoDetailSection :order-detail="orderDetail" />

        <!-- 销售信息 -->
        <SalesInfoSection :order-detail="orderDetail" />

        <!-- 产品信息 -->
        <ProductInfoDetailSection :order-detail="orderDetail" />

        <!-- 金额信息 -->
        <PriceInfoSection :order-detail="orderDetail" />

        <!-- 定金信息 -->
        <DepositInfoSection :order-detail="orderDetail" />

        <!-- 贷款信息 -->
        <LoanInfoSection
          v-if="orderDetail.paymentMethod === 'LOAN'"
          :order-detail="orderDetail"
        />

        <!-- 二手车置换信息 -->
        <UsedVehicleInfoSection
          v-if="orderDetail.usedVehicleId"
          :order-detail="orderDetail"
        />

        <!-- 专项优惠信息 -->
        <ExclusiveDiscountDetailSection
          v-if="orderDetail.hasExclusiveDiscount === 'YES'"
          :order-detail="orderDetail"
        />

        <!-- 其他衍生收入信息 -->
        <DerivativeIncomeDetailSection
          v-if="orderDetail.hasDerivativeIncome === 'YES'"
          :order-detail="orderDetail"
        />

        <!-- 赠品明细信息 -->
        <GiftItemsDetailSection :order-detail="orderDetail" />

        <!-- 备注信息 -->
        <RemarkDetailSection :order-detail="orderDetail" />

        <!-- 时间信息 -->
        <TimeInfoSection :order-detail="orderDetail" />
      </div>
      <div v-else class="empty-data">
        <n-empty description="暂无订单数据" />
      </div>
    </n-spin>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { NModal, NSpin, NButton, NSpace, NEmpty } from "naive-ui";
import vehicleOrderApi from "@/api/vehicleOrder";
import messages from "@/utils/messages";

// 导入所有section组件
import BasicInfoSection from "./sections/BasicInfoSection.vue";
import CustomerInfoDetailSection from "./sections/CustomerInfoDetailSection.vue";
import SalesInfoSection from "./sections/SalesInfoSection.vue";
import ProductInfoDetailSection from "./sections/ProductInfoDetailSection.vue";
import PriceInfoSection from "./sections/PriceInfoSection.vue";
import DepositInfoSection from "./sections/DepositInfoSection.vue";
import LoanInfoSection from "./sections/LoanInfoSection.vue";
import UsedVehicleInfoSection from "./sections/UsedVehicleInfoSection.vue";
import ExclusiveDiscountDetailSection from "./sections/ExclusiveDiscountDetailSection.vue";
import DerivativeIncomeDetailSection from "./sections/DerivativeIncomeDetailSection.vue";
import GiftItemsDetailSection from "./sections/GiftItemsDetailSection.vue";
import RemarkDetailSection from "./sections/RemarkDetailSection.vue";
import TimeInfoSection from "./sections/TimeInfoSection.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: [Number, String, null],
    default: null,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible"]);

// 组件状态
const loading = ref(false);
const orderDetail = ref(null);

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 更新可见性
const updateVisible = (val) => {
  emit("update:visible", val);
};

// 监听visible和id属性变化
watch([() => props.visible, () => props.id], ([newVisible, newId]) => {
  if (newVisible && newId) {
    fetchOrderDetail(newId);
  }
});

// 获取订单详情
const fetchOrderDetail = async (id) => {
  if (!id) return;

  loading.value = true;
  try {
    const response = await vehicleOrderApi.getOrderDetail(id);
    if (response.code === 200) {
      // 直接使用API返回的数据
      orderDetail.value = response.data;
    } else {
      messages.error(response.message || "获取订单详情失败");
    }
  } catch (error) {
    console.error("获取订单详情失败:", error);
    messages.error("获取订单详情失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理关闭
const handleClose = () => {
  modelVisible.value = false;
};
</script>

<style scoped>
.order-detail-content {
  padding: 0 10px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

:deep(.n-divider) {
  margin-top: 8px;
  margin-bottom: 16px;
  background-image: linear-gradient(
    to right,
    var(--primary-color) 0%,
    rgba(0, 0, 0, 0.06) 100%
  );
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px; /* 14px * 1.2 = 16.8px */
}

/* 金额相关字段突出显示 */
.detail-item.money {
  color: #2080f0;
  font-weight: 500;
}

/* 利润相关字段突出显示 */
.detail-item.profit {
  color: #18a058;
  font-weight: 500;
}

/* 负利润显示红色 */
.detail-item.negative-profit {
  color: #d03050;
  font-weight: 500;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

/* 订单编号特殊样式 */
.order-sn {
  font-family: monospace;
  letter-spacing: 1px;
  font-weight: 600;
}

/* 状态标签增强样式 */
:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
