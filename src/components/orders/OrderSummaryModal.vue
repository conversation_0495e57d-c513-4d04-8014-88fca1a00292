<template>
  <n-modal
    v-model:show="modalVisible"
    title="订单摘要"
    preset="card"
    style="width: 600px; max-width: 90%"
    :mask-closable="true"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleSize">
        <template #icon>
          <n-icon>
            <component :is="isMaximized ? ContractOutline : ExpandOutline" />
          </n-icon>
        </template>
      </n-button>
    </template>

    <div v-if="loading" class="loading-container">
      <n-spin size="large" />
    </div>

    <div v-else-if="error" class="error-container">
      <n-result status="error" :title="error" />
    </div>

    <div v-else-if="orderSummary" class="order-summary-content">
      <!-- 基本信息 -->
      <n-descriptions bordered :column="2" label-placement="left">
        <n-descriptions-item label="订单编号">
          <div style="display: flex; align-items: center;">
            <span style="font-family: Monaco, Consolas, 'Courier New', monospace; font-weight: 500;">
              {{ orderSummary.orderSn }}
            </span>
            <n-button 
              v-if="orderSummary.orderSn" 
              quaternary 
              circle 
              size="small" 
              @click="copyToClipboard(orderSummary.orderSn)" 
              style="margin-left: 8px;" 
              title="复制订单号"
            >
              <template #icon>
                <n-icon color="#18a058">
                  <CopyOutline />
                </n-icon>
              </template>
            </n-button>
          </div>
        </n-descriptions-item>
        <n-descriptions-item label="订单状态">
          <n-tag
            :type="getOrderStatusType(orderSummary.orderStatus)"
            size="small"
          >
            {{ getOrderStatusText(orderSummary.orderStatus) }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="客户名称">
          {{ orderSummary.customerName || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="联系电话">
          {{ orderSummary.mobile || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="车辆品牌">
          {{ orderSummary.brand || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="车辆配置">
          {{ orderSummary.configName || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="成交金额">
          <span style="color: var(--success-color); font-weight: 500;">
            ¥{{ formatMoney(orderSummary.dealAmount) }}
          </span>
        </n-descriptions-item>
        <n-descriptions-item label="订单日期">
          {{ formatDate(orderSummary.dealDate) }}
        </n-descriptions-item>
        <n-descriptions-item label="销售单位">
          {{ orderSummary.salesOrgName || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="销售顾问">
          {{ orderSummary.salesAgentName || '-' }}
        </n-descriptions-item>
      </n-descriptions>
    </div>

    <div v-else class="empty-data">
      <n-empty description="暂无订单数据" />
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="modalVisible = false">关闭</n-button>
        <n-button type="primary" @click="viewFullDetail">查看完整详情</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ContractOutline, ExpandOutline, CopyOutline } from '@vicons/ionicons5'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'

const props = defineProps({
  orderSn: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'view-detail'])

// 状态变量
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
const isMaximized = ref(false)
const loading = ref(false)
const error = ref(null)
const orderSummary = ref(null)

// 切换窗口大小
const toggleSize = () => {
  isMaximized.value = !isMaximized.value
}

// 复制到剪贴板
const copyToClipboard = (text) => {
  if (!text) {
    messages.warning('内容为空，无法复制')
    return
  }

  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text).then(() => {
      messages.success(`${text} 已复制到剪贴板`)
    }).catch(() => {
      fallbackCopyTextToClipboard(text)
    })
  } else {
    fallbackCopyTextToClipboard(text)
  }
}

// 备用复制方法
const fallbackCopyTextToClipboard = (text) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.position = 'fixed'
  textArea.style.opacity = '0'

  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    if (successful) {
      messages.success(`${text} 已复制到剪贴板`)
    } else {
      messages.error('复制失败，请手动复制')
    }
  } catch (err) {
    console.error('复制失败:', err)
    messages.error('复制失败，请手动复制')
  }

  document.body.removeChild(textArea)
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  if (typeof dateStr === 'number') {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  }
  return dateStr.split(' ')[0]
}

// 格式化金额（分转元）
const formatMoney = (amount) => {
  if (amount === undefined || amount === null) return '0.00'
  const yuan = amount / 100
  return yuan.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  const typeMap = {
    'PENDING': 'warning',
    'CONFIRMED': 'info',
    'DELIVERED': 'success',
    'CANCELLED': 'error',
    'COMPLETED': 'success'
  }
  return typeMap[status] || 'default'
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  const textMap = {
    'PENDING': '待确认',
    'CONFIRMED': '已确认',
    'DELIVERED': '已交付',
    'CANCELLED': '已取消',
    'COMPLETED': '已完成'
  }
  return textMap[status] || status || '-'
}

// 查看完整详情
const viewFullDetail = () => {
  if (orderSummary.value && orderSummary.value.id) {
    emit('view-detail', orderSummary.value.id)
  }
}

// 加载订单摘要数据
const loadOrderSummary = async () => {
  if (!props.orderSn) return

  loading.value = true
  error.value = null

  try {
    // 通过订单号查询订单
    const response = await vehicleOrderApi.getOrderList({
      orderNo: props.orderSn,
      page: 1,
      size: 1
    })

    if (response.code === 200 && response.data && response.data.list && response.data.list.length > 0) {
      orderSummary.value = response.data.list[0]
    } else {
      error.value = `未找到订单号为 ${props.orderSn} 的订单信息`
    }
  } catch (err) {
    console.error('获取订单摘要失败:', err)
    error.value = '获取订单摘要失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 监听 visible 和 orderSn 变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.orderSn) {
    loadOrderSummary()
  }
})

watch(() => props.orderSn, (newVal) => {
  if (modalVisible.value && newVal) {
    loadOrderSummary()
  }
})
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.order-summary-content {
  padding: 16px 0;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
