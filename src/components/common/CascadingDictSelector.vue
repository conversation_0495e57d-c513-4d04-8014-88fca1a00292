<template>
  <div class="cascading-dict-selector">
    <n-space :size="16" :vertical="vertical">
      <!-- 父级选择器 -->
      <div class="parent-selector">
        <n-select
          v-model:value="parentValue"
          :options="parentOptions"
          :loading="parentLoading"
          :placeholder="parentPlaceholder"
          :clearable="clearable"
          :disabled="disabled"
          :size="size"
          @update:value="handleParentChange"
        />
      </div>
      
      <!-- 子级选择器 -->
      <div class="child-selector">
        <n-select
          v-model:value="childValue"
          :options="childOptions"
          :loading="childLoading"
          :placeholder="childPlaceholder"
          :clearable="clearable"
          :disabled="disabled || !parentValue"
          :size="size"
          @update:value="handleChildChange"
        />
      </div>
    </n-space>
  </div>
</template>

<script>
import { computed, watch, ref } from 'vue'
import { NSelect, NSpace } from 'naive-ui'
import { useCascadingDict } from '@/composables/useAdvancedDict'
import { DICT_CODES } from '@/constants/dictConstants'

export default {
  name: 'CascadingDictSelector',
  components: {
    NSelect,
    NSpace,
  },
  props: {
    // 父级字典编码
    parentDictCode: {
      type: String,
      required: true,
      validator: (value) => Object.values(DICT_CODES).includes(value)
    },
    
    // 子级字典编码
    childDictCode: {
      type: String,
      required: true,
      validator: (value) => Object.values(DICT_CODES).includes(value)
    },
    
    // 父级当前值
    parentModelValue: {
      type: [String, Number],
      default: null
    },
    
    // 子级当前值
    childModelValue: {
      type: [String, Number],
      default: null
    },
    
    // 是否包含"不限"选项
    includeAll: {
      type: Boolean,
      default: true
    },
    
    // "不限"选项的标签
    allLabel: {
      type: String,
      default: '不限'
    },
    
    // "不限"选项的值
    allValue: {
      type: [String, Number],
      default: null
    },
    
    // 父级占位符
    parentPlaceholder: {
      type: String,
      default: '请选择'
    },
    
    // 子级占位符
    childPlaceholder: {
      type: String,
      default: '请先选择上级'
    },
    
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 尺寸
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    
    // 是否垂直布局
    vertical: {
      type: Boolean,
      default: false
    },
    
    // 是否自动重置子级
    autoResetChild: {
      type: Boolean,
      default: true
    }
  },
  
  emits: [
    'update:parentModelValue',
    'update:childModelValue', 
    'parentChange',
    'childChange',
    'change'
  ],
  
  setup(props, { emit }) {
    // 使用级联字典
    const {
      parentOptions,
      parentLoading,
      childOptions,
      childLoading,
      selectedParentValue,
      setParentValue,
      resetChildValue,
      loading
    } = useCascadingDict(props.parentDictCode, props.childDictCode, {
      includeAll: props.includeAll,
      allLabel: props.allLabel,
      allValue: props.allValue,
    })
    
    // 内部值
    const parentValue = ref(props.parentModelValue)
    const childValue = ref(props.childModelValue)
    
    // 监听外部值变化
    watch(() => props.parentModelValue, (newValue) => {
      parentValue.value = newValue
      setParentValue(newValue)
    }, { immediate: true })
    
    watch(() => props.childModelValue, (newValue) => {
      childValue.value = newValue
    }, { immediate: true })
    
    // 处理父级变化
    const handleParentChange = (value) => {
      parentValue.value = value
      setParentValue(value)
      
      // 自动重置子级
      if (props.autoResetChild) {
        const resetValue = resetChildValue()
        childValue.value = resetValue
        emit('update:childModelValue', resetValue)
        emit('childChange', resetValue)
      }
      
      emit('update:parentModelValue', value)
      emit('parentChange', value)
      emit('change', { parent: value, child: childValue.value })
    }
    
    // 处理子级变化
    const handleChildChange = (value) => {
      childValue.value = value
      emit('update:childModelValue', value)
      emit('childChange', value)
      emit('change', { parent: parentValue.value, child: value })
    }
    
    // 获取选中的选项信息
    const getSelectedInfo = computed(() => {
      const parentOption = parentOptions.value.find(opt => opt.value === parentValue.value)
      const childOption = childOptions.value.find(opt => opt.value === childValue.value)
      
      return {
        parent: parentOption,
        child: childOption,
        hasParent: !!parentOption && parentOption.value !== props.allValue,
        hasChild: !!childOption && childOption.value !== props.allValue,
        isComplete: !!parentOption && !!childOption && 
                   parentOption.value !== props.allValue && 
                   childOption.value !== props.allValue
      }
    })
    
    return {
      parentValue,
      childValue,
      parentOptions,
      parentLoading,
      childOptions,
      childLoading,
      loading,
      handleParentChange,
      handleChildChange,
      getSelectedInfo,
    }
  }
}
</script>

<style scoped>
.cascading-dict-selector {
  width: 100%;
}

.parent-selector,
.child-selector {
  width: 100%;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .cascading-dict-selector :deep(.n-space) {
    flex-direction: column;
  }
}
</style>
