<template>
  <n-select
    v-model:value="selectedValue"
    :options="computedOptions"
    :loading="loading"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    :filterable="filterable"
    :disabled="disabled"
    :size="size"
    @update:value="handleValueChange"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <template #empty>
      <div class="dict-selector-empty">
        <n-empty description="暂无数据" />
      </div>
    </template>

    <template #action v-if="showRefreshAction">
      <div class="dict-selector-action">
        <n-button text @click="handleRefresh">
          <template #icon>
            <n-icon><refresh-outline /></n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </template>
  </n-select>
</template>

<script>
import { computed, watch, ref } from "vue";
import { NSelect, NEmpty, NButton, NIcon } from "naive-ui";
import { RefreshOutline } from "@vicons/ionicons5";
import { useAdvancedDictOptions } from "@/composables/useAdvancedDict";
import { DICT_CODES, COMMON_DICT_CONFIGS } from "@/constants/dictConstants";

export default {
  name: "DictSelector",
  components: {
    NSelect,
    NEmpty,
    NButton,
    NIcon,
    RefreshOutline,
  },
  props: {
    // 字典编码
    dictCode: {
      type: String,
      required: true,
      validator: (value) => Object.values(DICT_CODES).includes(value),
    },

    // 当前值
    modelValue: {
      type: [String, Number, Array],
      default: null,
    },

    // 是否包含"不限"选项
    includeAll: {
      type: Boolean,
      default: true,
    },

    // "不限"选项的标签
    allLabel: {
      type: String,
      default: "不限",
    },

    // "不限"选项的值
    allValue: {
      type: [String, Number],
      default: null,
    },

    // 占位符
    placeholder: {
      type: String,
      default: "请选择",
    },

    // 是否可清空
    clearable: {
      type: Boolean,
      default: true,
    },

    // 是否多选
    multiple: {
      type: Boolean,
      default: false,
    },

    // 是否可搜索
    filterable: {
      type: Boolean,
      default: false,
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },

    // 尺寸
    size: {
      type: String,
      default: "medium",
      validator: (value) => ["small", "medium", "large"].includes(value),
    },

    // 过滤函数
    filter: {
      type: Function,
      default: null,
    },

    // 转换函数
    transform: {
      type: Function,
      default: null,
    },

    // 依赖项（用于过滤）
    dependencies: {
      type: Array,
      default: () => [],
    },

    // 是否显示刷新按钮
    showRefreshAction: {
      type: Boolean,
      default: false,
    },

    // 预设配置
    preset: {
      type: String,
      default: null,
      validator: (value) =>
        !value || Object.keys(COMMON_DICT_CONFIGS).includes(value),
    },
  },

  emits: ["update:modelValue", "change", "focus", "blur", "refresh"],

  setup(props, { emit }) {
    // 应用预设配置
    const config = computed(() => {
      const baseConfig = {
        includeAll: props.includeAll,
        allLabel: props.allLabel,
        allValue: props.allValue,
        filter: props.filter,
        transform: props.transform,
        dependencies: props.dependencies,
      };

      if (props.preset && COMMON_DICT_CONFIGS[props.preset]) {
        return { ...baseConfig, ...COMMON_DICT_CONFIGS[props.preset] };
      }

      return baseConfig;
    });

    // 使用高级字典选项
    const { options, loading, refresh, getLabel, getItem, isEmpty } =
      useAdvancedDictOptions(props.dictCode, config.value);

    // 内部值
    const selectedValue = ref(props.modelValue);

    // 计算选项（添加渲染优化）
    const computedOptions = computed(() => {
      return options.value.map((option) => ({
        ...option,
        // 确保 label 和 value 字段存在
        label: option.label || option.optionLabel || String(option.value),
        value: option.value !== undefined ? option.value : option.optionValue,
        // 添加样式信息
        style: option.color ? { color: option.color } : undefined,
        class: option.type ? `dict-option-${option.type}` : undefined,
      }));
    });

    // 监听外部值变化
    watch(
      () => props.modelValue,
      (newValue) => {
        selectedValue.value = newValue;
      },
      { immediate: true }
    );

    // 监听内部值变化
    watch(selectedValue, (newValue) => {
      emit("update:modelValue", newValue);
      emit("change", newValue, getItem(newValue));
    });

    // 事件处理
    const handleValueChange = (value) => {
      selectedValue.value = value;
    };

    const handleFocus = (e) => {
      emit("focus", e);
    };

    const handleBlur = (e) => {
      emit("blur", e);
    };

    const handleRefresh = () => {
      refresh();
      emit("refresh");
    };

    return {
      selectedValue,
      computedOptions,
      loading,
      isEmpty,
      handleValueChange,
      handleFocus,
      handleBlur,
      handleRefresh,
      getLabel,
      getItem,
    };
  },
};
</script>

<style scoped>
.dict-selector-empty {
  padding: 12px;
  text-align: center;
}

.dict-selector-action {
  padding: 8px 12px;
  border-top: 1px solid var(--n-border-color);
}

/* 字典选项样式 */
:deep(.dict-option-success) {
  color: #18a058;
}

:deep(.dict-option-warning) {
  color: #f0a020;
}

:deep(.dict-option-error) {
  color: #d03050;
}

:deep(.dict-option-info) {
  color: #2080f0;
}

:deep(.dict-option-default) {
  color: #909399;
}
</style>
