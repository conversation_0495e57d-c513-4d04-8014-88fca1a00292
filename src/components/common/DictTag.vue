<template>
  <n-tag
    v-if="showTag"
    :type="computedType"
    :color="computedColor"
    :size="size"
    :round="round"
    :bordered="bordered"
    :closable="closable"
    :disabled="disabled"
    @close="handleClose"
  >
    <template #icon v-if="showIcon">
      <n-icon>
        <component :is="iconComponent" />
      </n-icon>
    </template>

    {{ displayLabel }}
  </n-tag>

  <span v-else-if="showFallback" :class="fallbackClass">
    {{ fallbackText }}
  </span>
</template>

<script>
import { computed } from "vue";
import { NTag, NIcon } from "naive-ui";
import {
  CheckmarkCircleOutline,
  WarningOutline,
  CloseCircleOutline,
  InformationCircleOutline,
  EllipseOutline,
} from "@vicons/ionicons5";
import {
  getDictItem,
  getDictLabel,
  getDictColor,
  getDictType,
} from "@/utils/dictUtils";
import {
  DICT_CODES,
  DICT_DISPLAY_TYPES,
  DICT_COLORS,
} from "@/constants/dictConstants";

export default {
  name: "DictTag",
  components: {
    NTag,
    NIcon,
    CheckmarkCircleOutline,
    WarningOutline,
    CloseCircleOutline,
    InformationCircleOutline,
    EllipseOutline,
  },
  props: {
    // 字典编码
    dictCode: {
      type: String,
      required: true,
      validator: (value) => Object.values(DICT_CODES).includes(value),
    },

    // 字典值
    value: {
      type: [String, Number],
      required: true,
    },

    // 默认标签（当字典项不存在时显示）
    defaultLabel: {
      type: String,
      default: "未知",
    },

    // 默认类型
    defaultType: {
      type: String,
      default: "default",
      validator: (value) => Object.values(DICT_DISPLAY_TYPES).includes(value),
    },

    // 默认颜色
    defaultColor: {
      type: String,
      default: DICT_COLORS.DEFAULT,
    },

    // 尺寸
    size: {
      type: String,
      default: "medium",
      validator: (value) => ["small", "medium", "large"].includes(value),
    },

    // 是否圆角
    round: {
      type: Boolean,
      default: false,
    },

    // 是否有边框
    bordered: {
      type: Boolean,
      default: true,
    },

    // 是否可关闭
    closable: {
      type: Boolean,
      default: false,
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },

    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: false,
    },

    // 是否显示回退文本（当字典项不存在时）
    showFallback: {
      type: Boolean,
      default: true,
    },

    // 回退文本
    fallbackText: {
      type: String,
      default: null,
    },

    // 回退样式类
    fallbackClass: {
      type: String,
      default: "dict-tag-fallback",
    },

    // 强制类型（覆盖字典配置）
    forceType: {
      type: String,
      default: null,
      validator: (value) =>
        !value || Object.values(DICT_DISPLAY_TYPES).includes(value),
    },

    // 强制颜色（覆盖字典配置）
    forceColor: {
      type: String,
      default: null,
    },
  },

  emits: ["close"],

  setup(props, { emit }) {
    // 获取字典项
    const dictItem = computed(() => getDictItem(props.dictCode, props.value));

    // 是否显示标签
    const showTag = computed(() => !!dictItem.value);

    // 显示标签
    const displayLabel = computed(() => {
      if (dictItem.value) {
        return (
          dictItem.value.optionLabel ||
          dictItem.value.label ||
          String(props.value)
        );
      }
      return props.defaultLabel;
    });

    // 计算类型
    const computedType = computed(() => {
      if (props.forceType) return props.forceType;
      if (dictItem.value) {
        return getDictType(props.dictCode, props.value, props.defaultType);
      }
      return props.defaultType;
    });

    // 计算颜色
    const computedColor = computed(() => {
      if (props.forceColor) return { color: props.forceColor };
      if (dictItem.value) {
        const color = getDictColor(
          props.dictCode,
          props.value,
          props.defaultColor
        );
        return color !== DICT_COLORS.DEFAULT ? { color } : undefined;
      }
      return props.defaultColor !== DICT_COLORS.DEFAULT
        ? { color: props.defaultColor }
        : undefined;
    });

    // 图标组件
    const iconComponent = computed(() => {
      switch (computedType.value) {
        case DICT_DISPLAY_TYPES.SUCCESS:
          return CheckmarkCircleOutline;
        case DICT_DISPLAY_TYPES.WARNING:
          return WarningOutline;
        case DICT_DISPLAY_TYPES.ERROR:
          return CloseCircleOutline;
        case DICT_DISPLAY_TYPES.INFO:
          return InformationCircleOutline;
        default:
          return EllipseOutline;
      }
    });

    // 回退文本
    const computedFallbackText = computed(() => {
      return props.fallbackText || displayLabel.value;
    });

    // 关闭事件
    const handleClose = () => {
      emit("close", props.value);
    };

    return {
      showTag,
      displayLabel,
      computedType,
      computedColor,
      iconComponent,
      fallbackText: computedFallbackText,
      handleClose,
    };
  },
};
</script>

<style scoped>
.dict-tag-fallback {
  color: #909399;
  font-size: 12px;
  padding: 2px 6px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: inline-block;
}
</style>
