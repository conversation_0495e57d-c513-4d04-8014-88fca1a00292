<template>
  <n-card class="org-tree-card">
    <template #header>
      <div class="org-tree-header">
        <span>{{ title }}</span>
        <div class="search-container">
          <n-input v-model:value="deptSearchKeyword" :placeholder="searchPlaceholder" clearable size="small"
            @keydown.enter="searchDepartment" @clear="clearDeptSearch" style="width: 220px">
            <template #suffix>
              <n-icon style="cursor: pointer" @click="searchDepartment">
                <SearchOutline />
              </n-icon>
            </template>
          </n-input>
        </div>
      </div>
    </template>
    <div class="org-tree-container">
      <n-tree block-line :data="filteredDepartmentTree" :expanded-keys="expandedKeys"
        :selected-keys="selectedDepartmentKeys" :render-suffix="renderSuffix" :show-switcher="showSwitcher"
        :override-default-node-click-behavior="overrideNodeClickBehavior" selectable
        @update:expanded-keys="handleTreeExpand" />
    </div>
  </n-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { NCard, NInput, NIcon, NTree, NDropdown } from 'naive-ui'
import { SearchOutline, AddCircleOutline } from '@vicons/ionicons5'
import { MoreVertical16Filled } from '@vicons/fluent'
import { getDepartments } from '@/api/users'
import messages from '@/utils/messages'

// 定义组件属性
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '组织机构'
  },
  // 搜索框占位符
  searchPlaceholder: {
    type: String,
    default: '快速搜索'
  },
  // 是否显示切换器
  showSwitcher: {
    type: Boolean,
    default: false
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 自定义操作菜单
  actionMenuOptions: {
    type: Array,
    default: () => [
      {
        label: '新增',
        key: 'add',
        icon: AddCircleOutline
      }
    ]
  },
  // 是否默认展开所有节点
  defaultExpanded: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits([
  'select', // 选择节点
  'action', // 节点操作
  'update:expanded-keys', // 更新展开状态
  'update:selected-keys' // 更新选中状态
])

// 状态变量
const departmentTree = ref([])
const selectedDepartmentKeys = ref([])
const expandedKeys = ref([])
const deptSearchKeyword = ref('') // 搜索关键字
const originalExpandedKeys = ref([]) // 保存原始展开状态

// 过滤后的部门树
const filteredDepartmentTree = computed(() => {
  if (!deptSearchKeyword.value.trim()) {
    return departmentTree.value
  }

  // 搜索关键字
  const keyword = deptSearchKeyword.value.toLowerCase().trim()

  // 递归搜索函数
  const searchNodes = (nodes) => {
    if (!nodes || nodes.length === 0) return []

    return nodes.reduce((result, node) => {
      // 检查当前节点是否匹配
      const isMatch = node.label.toLowerCase().includes(keyword)

      // 递归搜索子节点
      const matchedChildren = searchNodes(node.children)

      // 如果当前节点匹配或者有匹配的子节点，则保留该节点
      if (isMatch || matchedChildren.length > 0) {
        // 创建节点的副本，避免修改原始数据
        const newNode = { ...node }

        // 如果有匹配的子节点，则替换子节点
        if (matchedChildren.length > 0) {
          newNode.children = matchedChildren
        }

        result.push(newNode)
      }

      return result
    }, [])
  }

  return searchNodes(departmentTree.value)
})

// 构建树形数据
function buildTreeData(departments) {
  // 如果没有部门数据，返回空数组
  if (!departments || departments.length === 0) {
    return []
  }

  const options = []
  const map = {}

  // 第一步：创建所有节点的映射
  departments.forEach(dept => {
    map[dept.id] = {
      key: dept.id.toString(),
      label: dept.name,
      children: [],
      isLeaf: true, // 初始假设所有节点都是叶子节点
      rawData: dept // 保存原始数据，方便后续使用
    }
  })

  // 第二步：构建树形结构
  departments.forEach(dept => {
    if (dept.parentId === 0 || !dept.parentId) {
      // 根节点直接添加到结果中
      options.push(map[dept.id])
    } else {
      // 子节点添加到其父节点下
      const parent = map[dept.parentId]
      if (parent) {
        parent.children.push(map[dept.id])
        parent.isLeaf = false // 如果有子节点，则不是叶子节点
      } else {
        // 如果找不到父节点，将其作为根节点处理
        console.warn(`Parent node with ID ${dept.parentId} not found for department ${dept.name} (ID: ${dept.id}). Treating as root node.`)
        options.push(map[dept.id])
      }
    }
  })

  return options
}

// 获取部门数据
async function fetchDepartments() {
  try {
    const response = await getDepartments()
    // 确保有部门数据
    if (!response.data || response.data.length === 0) {
      messages.warning('没有可用的组织机构数据')
      departmentTree.value = []
      return
    }

    departmentTree.value = buildTreeData(response.data)

    if (departmentTree.value.length > 0) {
      selectedDepartmentKeys.value = [departmentTree.value[0].key]

      // 根据 defaultExpanded 属性决定是否展开所有节点
      if (props.defaultExpanded) {
        // 初始化 expandedKeys 为所有节点的 key，使树默认全部展开
        expandedKeys.value = getAllKeys(departmentTree.value)
      } else {
        // 只展开根节点
        expandedKeys.value = departmentTree.value.map(node => node.key)
      }

      // 触发选择事件
      emit('select', selectedDepartmentKeys.value[0])
      // 触发更新选中状态事件
      emit('update:selected-keys', selectedDepartmentKeys.value)
      // 触发更新展开状态事件
      emit('update:expanded-keys', expandedKeys.value)
    }
  } catch (error) {
    console.error('Failed to fetch departments:', error)
    messages.error('获取组织机构数据失败')
  }
}

// 树节点点击行为
const overrideNodeClickBehavior = ({ option }) => {
  // 更新选中状态
  selectedDepartmentKeys.value = [option.key]

  // 触发选择事件
  emit('select', option.key)
  // 触发更新选中状态事件
  emit('update:selected-keys', selectedDepartmentKeys.value)

  // 返回 'prevent-default' 来阻止默认行为
  return 'prevent-default'
}

// 树节点展开处理
function handleTreeExpand(keys) {
  expandedKeys.value = keys
  // 触发更新展开状态事件
  emit('update:expanded-keys', expandedKeys.value)
}

// 搜索部门
function searchDepartment() {
  if (!deptSearchKeyword.value.trim()) {
    clearDeptSearch()
    return
  }

  // 保存当前展开状态
  if (originalExpandedKeys.value.length === 0) {
    originalExpandedKeys.value = [...expandedKeys.value]
  }

  // 展开所有节点以显示搜索结果
  const allKeys = getAllKeys(departmentTree.value)
  expandedKeys.value = allKeys
  // 触发更新展开状态事件
  emit('update:expanded-keys', expandedKeys.value)
}

// 清除搜索
function clearDeptSearch() {
  deptSearchKeyword.value = ''

  // 恢复原始展开状态
  if (originalExpandedKeys.value.length > 0) {
    expandedKeys.value = [...originalExpandedKeys.value]
    originalExpandedKeys.value = []
    // 触发更新展开状态事件
    emit('update:expanded-keys', expandedKeys.value)
  }
}

// 获取所有节点的key
function getAllKeys(nodes) {
  if (!nodes || nodes.length === 0) return []

  return nodes.reduce((keys, node) => {
    keys.push(node.key)
    if (node.children && node.children.length > 0) {
      keys.push(...getAllKeys(node.children))
    }
    return keys
  }, [])
}

// 渲染树节点后缀
function renderSuffix({ option }) {
  if (!props.showActions) return null

  return () => h(
    'div',
    { class: 'tree-node-action' },
    h(
      NDropdown,
      {
        trigger: 'click',
        options: props.actionMenuOptions,
        onSelect: (key) => {
          // 触发节点操作事件
          emit('action', { key, nodeKey: option.key, node: option })
        }
      },
      {
        default: () => h(NIcon, { component: MoreVertical16Filled })
      }
    )
  )
}

// 暴露方法和属性
defineExpose({
  fetchDepartments,
  departmentTree,
  selectedDepartmentKeys,
  expandedKeys,
  // 添加一个方法来获取当前选中的机构ID
  getCurrentSelectedOrgId: () => selectedDepartmentKeys.value[0],
  // 添加一个方法来获取部门树数据
  getDepartmentTree: () => departmentTree.value,
  // 添加一个方法来查找节点
  findNodeByKey: (key) => {
    const findNode = (tree, key) => {
      if (!tree || tree.length === 0) return null

      for (const node of tree) {
        if (node.key === key) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = findNode(node.children, key)
          if (found) return found
        }
      }
      return null
    }

    return findNode(departmentTree.value, key)
  }
})

// 组件挂载时获取部门数据
onMounted(fetchDepartments)
</script>

<style scoped>
.org-tree-card {
  height: calc(95vh - 32px);
  /* 固定高度，使用calc正确计算 */
  display: flex;
  flex-direction: column;
  border-bottom: none;
  /* 移除下边框 */
  min-height: 500px;
  /* 设置最小高度，确保有足够的空间 */
}

/* 覆盖NaiveUI卡片组件的边框样式 */
:deep(.n-card) {
  border: none !important;
  box-shadow: none !important;
}

:deep(.n-card-footer) {
  border-top: none !important;
}

:deep(.n-card__content) {
  padding-bottom: 16px !important;
  height: calc(95vh - 120px) !important;
  overflow: hidden !important;
}

:deep(.n-card__action) {
  border-top: none !important;
}

.org-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.org-tree-header span {
  font-size: 16px;
  font-weight: 500;
}

.search-container {
  display: flex;
  align-items: center;
}

.org-tree-container {
  flex: 1;
  /* 使用flex布局自动填充剩余空间 */
  overflow-y: auto;
  /* 垂直方向自动滚动，只在内容超出时显示滚动条 */
  overflow-x: hidden;
  /* 水平方向隐藏 */
  max-height: calc(95vh - 80px);
  /* 最大高度限制，减去标题和其他元素的高度 */
  border-bottom: 1px solid transparent;
  /* 设置下边框为透明 */
  padding-bottom: 16px;
  /* 添加底部内边距，确保最后一项完全显示 */
}

/* 自定义滚动条样式 */
.org-tree-container::-webkit-scrollbar {
  width: 6px;
  background-color: transparent; /* 透明背景 */
}

.org-tree-container::-webkit-scrollbar-track {
  background: transparent; /* 透明轨道 */
  border-radius: 3px;
}

.org-tree-container::-webkit-scrollbar-thumb {
  background: rgba(204, 204, 204, 0.5); /* 半透明滑块 */
  border-radius: 3px;
}

.org-tree-container::-webkit-scrollbar-thumb:hover {
  background: rgba(170, 170, 170, 0.8); /* 悬停时更明显 */
}

/* 默认隐藏滚动条，悬停时显示 */
.org-tree-container {
  scrollbar-width: thin; /* Firefox */
}

.org-tree-container:hover::-webkit-scrollbar-thumb {
  background: rgba(204, 204, 204, 0.8); /* 悬停时显示滚动条 */
}

/* 修改节点操作按钮的样式 */
.n-tree .n-tree-node-content {
  position: relative;
}

/* 确保树组件能够正确显示所有节点 */
:deep(.n-tree) {
  padding-bottom: 24px !important;
  max-height: none !important;
}

.tree-node-action {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.n-tree-node:hover .tree-node-action,
.n-tree-node--selected .tree-node-action {
  opacity: 1;
}

.tree-node-action .n-icon {
  font-size: 16px;
}

/* 确保下拉菜单在树节点之上 */
.n-dropdown-menu {
  z-index: 1000;
}
</style>
