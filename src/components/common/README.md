# 通用组件说明

## VehicleBrandSelector 车辆品牌选择器

### 功能特性

1. **权限过滤**：自动根据用户登录时返回的 brands 字段进行权限过滤
2. **智能显示**：当用户只有一个可选品牌时，不显示"不限"选项
3. **缓存机制**：基于 localStorage 的品牌权限缓存
4. **统一样式**：与 OrdersPage 风格一致的单选按钮组样式

### 使用方法

```vue
<template>
  <div class="filter-row">
    <div class="filter-label">车辆品牌</div>
    <div class="filter-options">
      <vehicle-brand-selector
        v-model="filterForm.vehicleBrand"
        @update:model-value="handleSearch"
        :include-all="true"
      />
    </div>
  </div>
</template>

<script setup>
import VehicleBrandSelector from "@/components/common/VehicleBrandSelector.vue";

const filterForm = reactive({
  vehicleBrand: null
});

const handleSearch = () => {
  // 处理搜索逻辑
};
</script>
```

### 属性说明

- `modelValue`: 当前选中的品牌值
- `includeAll`: 是否包含"不限"选项（默认: true）
- `autoSelectFirst`: 是否自动选中第一个选项（默认: true）

### 事件说明

- `update:modelValue`: 选中值变化时触发

### 权限逻辑

1. **管理员用户**：无 brands 限制，显示所有品牌选项
2. **单品牌用户**：只显示授权的品牌，不显示"不限"选项
3. **多品牌用户**：显示授权的品牌 + "不限"选项

### 已更新的页面

以下页面已经更新为使用新的 VehicleBrandSelector 组件或已实现默认选中第一个选项：

1. `src/views/operation/SalesPriceLimit.vue` - 销售限价规则页面 ✅
2. `src/views/inventory/OrdersPage.vue` - 订单管理页面 ✅
3. `src/views/inventory/DepositOrderPage.vue` - 定金订单页面 ✅
4. `src/views/inventory/StartBillPage.vue` - 启票管理页面 ✅
5. `src/views/inventory/ProductPage.vue` - 产品管理页面 ✅
6. `src/views/inventory/StockDetailsPage.vue` - 库存详情页面 ✅
7. `src/views/inventory/StockOutboundBillPage.vue` - 出库单页面 ✅
8. `src/views/inventory/BizOrgPage.js` - 业务机构页面 ✅
9. `src/views/inventory/DashboardPage.vue` - 仪表板页面 ✅

### 功能说明

所有使用 `VehicleBrandSelector` 组件的页面现在都会：
- 自动选中字典返回的第一个选项（当没有选中值时）
- 根据用户权限过滤可选品牌
- 当用户只有一个可选品牌时，不显示"不限"选项

### 技术实现

VehicleBrandSelector 组件通过以下方式实现默认选中功能：

1. **自动选中逻辑**：组件内部使用 `watch` 监听品牌选项变化
2. **条件判断**：只有当 `autoSelectFirst` 为 true 且当前没有选中值时才自动选中
3. **异步处理**：使用 `nextTick` 确保在下一个 DOM 更新周期中触发选中事件
4. **权限过滤**：自动选中的是经过权限过滤后的第一个选项

```javascript
// 监听品牌选项变化，自动设置默认选中第一个选项
watch(
  brandOptions,
  (newOptions) => {
    if (
      props.autoSelectFirst &&
      newOptions.length > 0 &&
      props.modelValue === null
    ) {
      // 如果当前没有选中值，自动选中第一个选项
      nextTick(() => {
        emit("update:modelValue", newOptions[0].value);
      });
    }
  },
  { immediate: true }
);
```
