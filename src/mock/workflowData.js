/**
 * 工作流程模拟数据
 */

// 应用列表数据
export const appList = [
  {
    id: 1,
    name: '新车销售',
    icon: 'CarSportOutline',
    color: '#2080f0',
    path: '/app/index/3?jump_to=/inventory/sell/orders'
  },
  {
    id: 2,
    name: '车辆启票',
    icon: 'BagHandle',
    color: '#18a058',
    path: '/app/index/3?jump_to=/inventory/buy/start'
  },
  {
    id: 3,
    name: '库存明细',
    icon: 'Archive',
    color: '#d03050',
    path: '/app/index/3?jump_to=/inventory/stock/details'
  },
  {
    id: 4,
    name: '员工管理',
    icon: 'People',
    color: '#f0a020',
    path: '/app/index/4'
  },
  {
    id: 5,
    name: '财务中心',
    icon: 'Cash',
    color: '#8a2be2',
    path: '/app/index/138'
  },
  {
    id: 6,
    name: '客户管理',
    icon: 'Person',
    color: '#0096c7',
    path: '/app/index/195?jump_to=/crm/customers'
  }
];

// 工作流程统计数据
export const workflowStats = {
  myTasks: 12,
  myInitiated: 24,
  myProcessed: 36,
  myCopied: 8
};
