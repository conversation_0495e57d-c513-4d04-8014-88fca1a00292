/**
 * 模拟菜单数据，用于测试动态路由加载
 */
export const mockMenuData = [
  {
    id: 1,
    parentId: null,
    menuLabel: '系统菜单',
    menuPath: '',
    viewPath: '',
    menuIcon: '',
    menuOrder: 1,
    visible: true
  },
  {
    id: 2,
    parentId: 1,
    menuLabel: '仪表盘',
    menuPath: '/dashboard',
    viewPath: 'system/DashboardPage',
    menuIcon: 'Odometer',
    menuOrder: 1,
    visible: true
  },
  {
    id: 3,
    parentId: 1,
    menuLabel: '系统管理',
    menuPath: '/system',
    viewPath: 'system/IndexPage',
    menuIcon: 'SettingsSharp',
    menuOrder: 2,
    visible: true,
    subMenus: [
      {
        id: 4,
        parentId: 3,
        menuLabel: '用户管理',
        menuPath: '/system/users',
        viewPath: 'system/UsersPage',
        menuIcon: 'UserFilled',
        menuOrder: 1,
        visible: true
      },
      {
        id: 5,
        parentId: 3,
        menuLabel: '角色管理',
        menuPath: '/system/roles',
        viewPath: 'system/RolesPage',
        menuIcon: 'UserFilled',
        menuOrder: 2,
        visible: true
      },
      {
        id: 6,
        parentId: 3,
        menuLabel: '菜单管理',
        menuPath: '/system/menus',
        viewPath: 'system/MenusPage',
        menuIcon: 'Menu',
        menuOrder: 3,
        visible: true
      },
      {
        id: 9,
        parentId: 3,
        menuLabel: '业务字典',
        menuPath: '/system/dict',
        viewPath: 'system/DictPage',
        menuIcon: 'BookOutline',
        menuOrder: 4,
        visible: true
      }
    ]
  },
  {
    id: 7,
    parentId: 1,
    menuLabel: '示例页面',
    menuPath: '/example',
    viewPath: 'example/ExamplePage',
    menuIcon: 'HelpFilled',
    menuOrder: 3,
    visible: true
  },
  {
    id: 8,
    parentId: 1,
    menuLabel: '库存仪表盘',
    menuPath: '/inventory',
    viewPath: 'inventory/InventoryDashboard',
    menuIcon: 'CarSportOutline',
    menuOrder: 4,
    visible: true
  }
]

export default mockMenuData
