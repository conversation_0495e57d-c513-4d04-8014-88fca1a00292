import csv
import json
import os

# 定义中英文字段映射
FIELD_MAPPING = {
    '业务类别': 'business_type',
    '车辆网络类别': 'vehicle_network_type',
    '车架号': 'vin',
    '物料编号': 'material_number',
    '车型': 'vehicle_model',
    '配置': 'configuration',
    '启票方式': 'invoice_method',
    '存放地点': 'storage_location',
    '库龄天数': 'storage_days',
    '启票日期': 'invoice_start_date',
    '启票公司': 'invoice_company',
    '启票价': 'invoice_price',
    '实销公司': 'actual_sales_company',
    '销售地类型': 'sales_location_type',
    '直营店销售地名称': 'direct_store_location_name',
    '备注（销售地）': 'sales_location_remarks',
    '数量': 'quantity',
    '订单日期': 'order_date',
    '锁单日期': 'lock_date',
    '销售日期': 'sales_date',
    '销售限价': 'sales_price_limit',
    '销售价格': 'sales_price',
    '是否分期': 'is_installment',
    '分期类型': 'installment_type',
    '金融公司': 'finance_company',
    '销售首付金额': 'down_payment',
    '分期金额': 'installment_amount',
    '银行返利总金额': 'bank_rebate_total',
    '分期返利成本（含后期预返客户部分）总金额': 'installment_rebate_cost_total',
    '分期服务费收入': 'installment_service_fee_income',
    '分期服务费成本（含提成）': 'installment_service_fee_cost',
    '公证费收入': 'notary_fee_income',
    '公证费成本（含提成）': 'notary_fee_cost',
    '畅行无忧收入': 'carefree_travel_income',
    '畅行无忧成本': 'carefree_travel_cost',
    '延保收入': 'extended_warranty_income',
    '延保成本': 'extended_warranty_cost',
    '售前其他衍生收入（VPS/前置利息/挂牌/临牌/外卖装具等）': 'other_presale_income',
    '售前其他衍生成本（含提成）': 'other_presale_cost',
    '赠送装具（含赠送保养）明细': 'gift_accessories_details',
    '赠送装具（含保养）成本': 'gift_accessories_cost',
    '销售（网销）顾问姓名': 'sales_consultant_name',
    '客户姓名': 'customer_name',
    '手机/固定电话': 'phone_number',
    '身份证号': 'id_number',
    '家庭住址': 'home_address',
    '备注（其他）': 'other_remarks',
    '已折现明细说明（价格在车款已体现）': 'discounted_details',
    '后期返现总金额': 'future_cashback_total',
    '后期返现明细说明': 'future_cashback_details',
    '后返明细2': 'future_cashback_details2',
    '开票日期': 'invoice_date',
    '开票地点': 'invoice_location',
    '发票号': 'invoice_number',
    '开票价格': 'invoice_amount',
    '清票日期': 'invoice_clearing_date',
    '物料编码带颜色': 'material_code_with_color'
}

def csv_to_json(csv_file_path, json_file_path):
    # 读取CSV文件
    data = []
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        # 使用csv.DictReader自动将第一行作为字段名
        csvreader = csv.DictReader(csvfile)
        # 将每一行数据转换为字典并添加到列表中
        for row in csvreader:
            # 创建新的行字典，使用英文字段名
            english_row = {}
            for cn_key, value in row.items():
                if cn_key in FIELD_MAPPING:
                    english_row[FIELD_MAPPING[cn_key]] = value
                else:
                    english_row[cn_key] = value  # 如果没有映射，保持原字段名
            data.append(english_row)
    
    # 将数据写入JSON文件
    with open(json_file_path, 'w', encoding='utf-8') as jsonfile:
        # ensure_ascii=False 允许输出中文字符
        # indent=2 美化输出格式
        json.dump(data, jsonfile, ensure_ascii=False, indent=2)

def main():
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 设置输入输出文件路径
    csv_file = os.path.join(current_dir, 'mock.csv')
    json_file = os.path.join(current_dir, 'mock.json')
    
    try:
        # 执行转换
        csv_to_json(csv_file, json_file)
        print(f'转换完成！JSON文件已保存到：{json_file}')
    except Exception as e:
        print(f'转换过程中出现错误：{str(e)}')

if __name__ == '__main__':
    main()