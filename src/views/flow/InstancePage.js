import { ref, markRaw, shallowRef, computed } from "vue";
import { useMessage } from "naive-ui";
import {
  CarOutline,
  DocumentTextOutline,
  CashOutline,
  PeopleOutline,
  SettingsOutline,
  TrendingUpOutline,
} from "@vicons/ionicons5";

export function useInstancePage() {
  const message = useMessage();

  // 当前视图状态：'list' 表示流程列表，'component' 表示流程组件
  const currentView = ref('list');

  // 当前显示的流程组件
  const currentFlowComponent = shallowRef(null);

  // 组件加载状态
  const isLoading = ref(false);

  // 组件缓存
  const componentCache = new Map();

  // 流程组件动态导入映射
  const flowComponents = {
    'new-car-sales': () => import('./NewCarSalesFlow.vue'),
    'finance-approval': () => import('./FinanceApprovalFlow.vue'),
    'customer-service': () => import('./CustomerServiceFlow.vue'),
    'inventory-management': () => import('./InventoryManagementFlow.vue'),
    'performance-review': () => import('./PerformanceReviewFlow.vue'),
  };

  // 所有流程定义
  const allFlows = [
    {
      id: "new-car-sales",
      title: "新车销售流程",
      description: "处理新车销售订单、合同签署、交车等完整流程",
      icon: markRaw(CarOutline),
      iconColor: "#18a058",
      status: "active",
      permissions: ["sales", "admin"], // 需要的权限
    },
    {
      id: "finance-approval",
      title: "财务审批流程",
      description: "处理各类财务审批、付款申请、费用报销等",
      icon: markRaw(CashOutline),
      iconColor: "#2080f0",
      status: "active",
      permissions: ["finance", "admin"],
    },
    {
      id: "customer-service",
      title: "客户服务流程",
      description: "客户投诉处理、售后服务、客户关怀等流程",
      icon: markRaw(PeopleOutline),
      iconColor: "#f0a020",
      status: "active",
      permissions: ["service", "admin"],
    },
    {
      id: "inventory-management",
      title: "库存管理流程",
      description: "库存调拨、盘点、出入库等库存相关流程",
      icon: markRaw(DocumentTextOutline),
      iconColor: "#d03050",
      status: "active",
      permissions: ["inventory", "admin"],
    },
    {
      id: "performance-review",
      title: "绩效考核流程",
      description: "员工绩效考核、目标设定、评估反馈等",
      icon: markRaw(TrendingUpOutline),
      iconColor: "#722ed1",
      status: "active",
      permissions: ["hr", "admin"],
    },
    {
      id: "system-config",
      title: "系统配置流程",
      description: "系统参数配置、权限设置、数据维护等",
      icon: markRaw(SettingsOutline),
      iconColor: "#666666",
      status: "maintenance",
      permissions: ["admin"],
    },
  ];

  // 模拟用户权限（实际项目中应该从用户状态或API获取）
  const userPermissions = ref(["sales", "finance", "admin"]); // 示例权限

  // 根据权限过滤流程列表
  const flowList = computed(() => {
    return allFlows.filter(flow =>
      flow.permissions.some(permission =>
        userPermissions.value.includes(permission)
      )
    );
  });

  // 处理流程卡片点击
  const handleFlowClick = async (flow) => {
    if (flow.status === "maintenance") {
      message.warning("该流程正在维护中，暂时无法使用");
      return;
    }

    // 防止重复点击
    if (isLoading.value) {
      return;
    }

    // 检查是否有对应的流程组件
    const componentLoader = flowComponents[flow.id];
    if (componentLoader) {
      try {
        isLoading.value = true;

        // 检查缓存
        let component = componentCache.get(flow.id);

        if (!component) {
          message.info(`正在加载${flow.title}...`);

          // 动态导入组件
          const componentModule = await componentLoader();
          component = componentModule.default;

          // 缓存组件
          componentCache.set(flow.id, component);

          message.success(`${flow.title}加载完成`);
        }

        currentFlowComponent.value = component;
        currentView.value = 'component';

      } catch (error) {
        console.error(`加载${flow.title}组件失败:`, error);
        message.error(`加载${flow.title}失败，请稍后重试`);
      } finally {
        isLoading.value = false;
      }
    } else {
      // 没有对应组件时显示提示
      message.info(`${flow.title}组件开发中...`);
    }

    console.log("点击了流程:", flow);
  };

  // 返回流程列表
  const backToList = () => {
    currentView.value = 'list';
    currentFlowComponent.value = null;
  };

  // 更新用户权限（供外部调用）
  const updateUserPermissions = (permissions) => {
    userPermissions.value = permissions;
  };

  return {
    flowList,
    handleFlowClick,
    currentView,
    currentFlowComponent,
    backToList,
    isLoading,
    updateUserPermissions,
    userPermissions,
  };
}
