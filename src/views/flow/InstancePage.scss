/* 发起流程页面样式 */

.flow-start-page {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
}

.main-card {
  height: calc(100vh - 32px);
}

.main-card :deep(.n-card__content) {
  padding: 24px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.flow-list-container {
  width: 100%;
}

.flow-card {
  height: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e6;
  position: relative;
}

.flow-card :deep(.n-card__content) {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.flow-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #18a058;
}

.flow-card.loading {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}

.flow-card.loading:hover {
  transform: none;
  box-shadow: none;
  border-color: #e0e0e6;
}

.flow-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
  position: relative;
}

.flow-status-tag {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
}

.flow-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.flow-info {
  flex: 1;
  text-align: center;
}

.flow-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.flow-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .flow-list-container :deep(.n-grid) {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .flow-list-container :deep(.n-grid) {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .flow-card {
    height: 180px;
  }

  .flow-title {
    font-size: 16px;
  }

  .flow-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .flow-list-container :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }

  .main-card :deep(.n-card__content) {
    padding: 16px;
  }
}