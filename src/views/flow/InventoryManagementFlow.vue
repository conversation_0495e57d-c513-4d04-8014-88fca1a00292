<template>
  <div class="inventory-management-flow">
    <n-card title="库存管理流程" class="main-card">
      <template #header-extra>
        <n-button @click="goBack" quaternary>
          <template #icon>
            <n-icon>
              <ArrowBackOutline />
            </n-icon>
          </template>
          返回
        </n-button>
      </template>
      
      <div class="flow-content">
        <n-result 
          status="info" 
          title="库存管理流程" 
          description="此流程组件正在开发中，敬请期待..."
        >
          <template #footer>
            <n-space>
              <n-button @click="goBack">返回流程列表</n-button>
            </n-space>
          </template>
        </n-result>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { NCard, NButton, NIcon, NResult, NSpace } from 'naive-ui'
import { ArrowBackOutline } from '@vicons/ionicons5'

// 定义事件
const emit = defineEmits(['back-to-list'])

const goBack = () => {
  emit('back-to-list')
}
</script>

<style scoped>
.inventory-management-flow {
  width: 100%;
  height: 100vh;
  padding: 16px;
  background-color: #f5f5f5;
}

.main-card {
  height: calc(100vh - 32px);
}

.main-card :deep(.n-card__content) {
  padding: 24px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.flow-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
