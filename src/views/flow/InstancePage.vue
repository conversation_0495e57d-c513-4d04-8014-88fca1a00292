<template>
  <div class="flow-start-page">
    <!-- 流程列表视图 -->
    <div v-if="currentView === 'list'" class="flow-list-view">
      <n-card title="发起流程" class="main-card">
        <div class="flow-list-container">
          <n-grid :cols="4" :x-gap="24" :y-gap="24">
            <n-grid-item v-for="flow in flowList" :key="flow.id">
              <n-card
                class="flow-card"
                hoverable
                :class="{ loading: isLoading }"
                @click="handleFlowClick(flow)"
              >
                <div class="flow-card-content">
                  <div class="flow-status-tag">
                    <n-tag
                      :type="flow.status === 'active' ? 'success' : 'default'"
                      size="small"
                    >
                      {{ flow.status === "active" ? "可用" : "维护中" }}
                    </n-tag>
                  </div>
                  <div class="flow-icon">
                    <n-icon size="48" :color="flow.iconColor">
                      <component :is="flow.icon" />
                    </n-icon>
                  </div>
                  <div class="flow-info">
                    <h3 class="flow-title">{{ flow.title }}</h3>
                    <p class="flow-description">{{ flow.description }}</p>
                  </div>
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </div>
      </n-card>
    </div>

    <!-- 流程组件视图 -->
    <div v-else-if="currentView === 'component'" class="flow-component-view">
      <component :is="currentFlowComponent" @back-to-list="backToList" />
    </div>
  </div>
</template>

<script setup>
import { NCard, NGrid, NGridItem, NIcon, NTag } from "naive-ui";
import { useInstancePage } from "./InstancePage.js";

// 使用组合式函数获取所有逻辑
const {
  flowList,
  handleFlowClick,
  currentView,
  currentFlowComponent,
  backToList,
  isLoading,
} = useInstancePage();
</script>

<style scoped lang="scss">
@use "./InstancePage.scss";
</style>