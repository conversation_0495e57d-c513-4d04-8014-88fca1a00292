<template>
  <div class="dict-example-page">
    <n-card title="字典抽象架构示例" class="example-card">
      <n-space direction="vertical" :size="24">
        
        <!-- 基础字典选择器示例 -->
        <n-card title="基础字典选择器" size="small">
          <n-space direction="vertical" :size="16">
            <div>
              <n-text strong>车辆品牌选择：</n-text>
              <dict-selector
                v-model="selectedBrand"
                :dict-code="DICT_CODES.VEHICLE_BRAND"
                preset="BRAND_SELECTOR"
                placeholder="请选择车辆品牌"
                @change="handleBrandChange"
              />
            </div>
            
            <div>
              <n-text strong>订单状态选择：</n-text>
              <dict-selector
                v-model="selectedOrderStatus"
                :dict-code="DICT_CODES.ORDER_STATUS"
                :include-all="true"
                all-label="全部状态"
                @change="handleOrderStatusChange"
              />
            </div>
            
            <div>
              <n-text strong>客户类型选择：</n-text>
              <dict-selector
                v-model="selectedCustomerType"
                :dict-code="DICT_CODES.CUSTOMER_TYPE"
                :include-all="false"
                @change="handleCustomerTypeChange"
              />
            </div>
          </n-space>
        </n-card>
        
        <!-- 级联字典选择器示例 -->
        <n-card title="级联字典选择器" size="small">
          <div>
            <n-text strong>省市级联选择：</n-text>
            <cascading-dict-selector
              v-model:parent-model-value="selectedProvince"
              v-model:child-model-value="selectedCity"
              :parent-dict-code="DICT_CODES.PROVINCE_CITY"
              :child-dict-code="DICT_CODES.CITY_DISTRICT"
              parent-placeholder="请选择省份"
              child-placeholder="请选择城市"
              @change="handleLocationChange"
            />
          </div>
        </n-card>
        
        <!-- 字典标签显示示例 -->
        <n-card title="字典标签显示" size="small">
          <n-space :size="12">
            <dict-tag
              :dict-code="DICT_CODES.ORDER_STATUS"
              value="pending"
              show-icon
            />
            <dict-tag
              :dict-code="DICT_CODES.VEHICLE_STATUS"
              value="1"
              show-icon
              round
            />
            <dict-tag
              :dict-code="DICT_CODES.DEAL_STATUS"
              value="completed"
              show-icon
              size="large"
            />
          </n-space>
        </n-card>
        
        <!-- 高级字典功能示例 -->
        <n-card title="高级字典功能" size="small">
          <n-space direction="vertical" :size="16">
            
            <!-- 字典搜索 -->
            <div>
              <n-text strong>字典搜索：</n-text>
              <n-input
                v-model:value="searchQuery"
                placeholder="搜索车辆品牌..."
                @input="handleSearch"
              />
              <n-space class="search-results" :size="8">
                <dict-tag
                  v-for="result in searchResults"
                  :key="result.value"
                  :dict-code="DICT_CODES.VEHICLE_BRAND"
                  :value="result.value"
                  size="small"
                />
              </n-space>
            </div>
            
            <!-- 多字典管理 -->
            <div>
              <n-text strong>多字典状态：</n-text>
              <n-space direction="vertical" :size="8">
                <div v-for="(dict, code) in multiDictData" :key="code">
                  <n-text>{{ code }}: </n-text>
                  <n-tag :type="dict.loading.value ? 'warning' : 'success'">
                    {{ dict.loading.value ? '加载中' : `已加载 ${dict.options.value.length} 项` }}
                  </n-tag>
                </div>
              </n-space>
            </div>
            
          </n-space>
        </n-card>
        
        <!-- 选择结果显示 -->
        <n-card title="选择结果" size="small">
          <n-code :code="JSON.stringify(selectionResults, null, 2)" language="json" />
        </n-card>
        
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { 
  NCard, 
  NSpace, 
  NText, 
  NInput, 
  NTag, 
  NCode 
} from 'naive-ui'

// 导入新的字典组件和工具
import DictSelector from '@/components/common/DictSelector.vue'
import CascadingDictSelector from '@/components/common/CascadingDictSelector.vue'
import DictTag from '@/components/common/DictTag.vue'

// 导入字典常量和组合式函数
import { DICT_CODES } from '@/constants/dictConstants'
import { 
  useAdvancedDictOptions, 
  useMultipleDict, 
  useDictSearch 
} from '@/composables/useAdvancedDict'

// 基础选择器状态
const selectedBrand = ref(null)
const selectedOrderStatus = ref(null)
const selectedCustomerType = ref(null)

// 级联选择器状态
const selectedProvince = ref(null)
const selectedCity = ref(null)

// 字典搜索功能
const searchQuery = ref('')
const { searchResults, search } = useDictSearch(DICT_CODES.VEHICLE_BRAND, {
  includeAll: false,
  searchFields: ['label', 'value']
})

// 多字典管理
const multiDictData = useMultipleDict([
  DICT_CODES.VEHICLE_BRAND,
  DICT_CODES.ORDER_STATUS,
  DICT_CODES.CUSTOMER_TYPE,
  DICT_CODES.LOAN_CHANNEL
], { includeAll: false })

// 选择结果汇总
const selectionResults = computed(() => ({
  brand: selectedBrand.value,
  orderStatus: selectedOrderStatus.value,
  customerType: selectedCustomerType.value,
  location: {
    province: selectedProvince.value,
    city: selectedCity.value
  },
  searchQuery: searchQuery.value,
  searchResultCount: searchResults.value.length
}))

// 事件处理
const handleBrandChange = (value, item) => {
  console.log('品牌变化:', value, item)
}

const handleOrderStatusChange = (value, item) => {
  console.log('订单状态变化:', value, item)
}

const handleCustomerTypeChange = (value, item) => {
  console.log('客户类型变化:', value, item)
}

const handleLocationChange = ({ parent, child }) => {
  console.log('地理位置变化:', { province: parent, city: child })
}

const handleSearch = (query) => {
  search(query)
}
</script>

<style scoped>
.dict-example-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-card {
  margin-bottom: 24px;
}

.search-results {
  margin-top: 12px;
  min-height: 32px;
}

:deep(.n-card__content) {
  padding: 16px;
}

:deep(.n-code) {
  max-height: 200px;
  overflow-y: auto;
}
</style>
