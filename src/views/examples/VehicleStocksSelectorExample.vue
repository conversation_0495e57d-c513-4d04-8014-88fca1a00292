<template>
  <div class="example-page">
    <h1>车辆库存查询器示例</h1>

    <n-space vertical>
      <n-card title="组件演示">
        <n-space>
          <n-button type="primary" @click="showSingleSelector">
            单选模式
          </n-button>

          <n-button type="info" @click="showMultipleSelector">
            多选模式
          </n-button>

          <n-button type="success" @click="showFilteredSelector">
            带过滤条件
          </n-button>

          <n-button type="warning" @click="showOutboundSelector">
            出库模式(排除试驾车)
          </n-button>
        </n-space>
      </n-card>

      <n-card
        title="选择结果"
        v-if="selectedVehicle || selectedVehicles.length"
      >
        <div v-if="selectedVehicle">
          <h3>单选结果</h3>
          <n-descriptions bordered>
            <n-descriptions-item label="VIN">{{
              selectedVehicle.vin
            }}</n-descriptions-item>
            <n-descriptions-item label="库存状态">{{
              getStatusText(selectedVehicle.stockStatus)
            }}</n-descriptions-item>
            <n-descriptions-item label="所在仓库">{{
              selectedVehicle.stockOrgName
            }}</n-descriptions-item>
            <n-descriptions-item label="所属单位">{{
              selectedVehicle.ownerOrgName
            }}</n-descriptions-item>
          </n-descriptions>
        </div>

        <div v-if="selectedVehicles.length">
          <h3>多选结果</h3>
          <n-data-table
            :columns="resultColumns"
            :data="selectedVehicles"
            :bordered="false"
            :pagination="false"
          />
        </div>
      </n-card>
    </n-space>

    <!-- 车辆库存查询器组件 -->
    <vehicle-stocks-selector
      v-model:visible="selectorVisible"
      :multiple="selectorMultiple"
      :filters="selectorFilters"
      :default-selected="defaultSelected"
      @cancel="handleSelectorCancel"
      @confirm="handleSelectorConfirm"
    />
  </div>
</template>

<script setup>
import { ref, h } from "vue";
import { NTag } from "naive-ui";
import VehicleStocksSelector from "@/components/inventory/VehicleStocksSelector.vue";

// 状态变量
const selectorVisible = ref(false);
const selectorMultiple = ref(false);
const selectorFilters = ref({});
const defaultSelected = ref([]);

const selectedVehicle = ref(null);
const selectedVehicles = ref([]);

// 结果表格列配置
const resultColumns = [
  {
    title: "VIN",
    key: "vin",
    width: 180,
  },
  {
    title: "库存状态",
    key: "stockStatus",
    width: 100,
    render(row) {
      const statusMap = {
        stocking: { text: "在库", type: "success" },
        transiting: { text: "在途", type: "info" },
        sold: { text: "已售", type: "warning" },
        returned: { text: "已退", type: "error" },
      };
      const status = statusMap[row.stockStatus] || {
        text: row.stockStatus || "未知",
        type: "default",
      };
      return h(
        NTag,
        {
          type: status.type,
          size: "small",
          round: true,
          style: {
            padding: "0 10px",
            fontWeight: "bold",
          },
        },
        { default: () => status.text }
      );
    },
  },
  {
    title: "所在仓库",
    key: "stockOrgName",
    width: 150,
  },
  {
    title: "所属单位",
    key: "ownerOrgName",
    width: 150,
  },
];

// 显示单选选择器
const showSingleSelector = () => {
  selectorMultiple.value = false;
  selectorFilters.value = {};
  defaultSelected.value = [];
  selectorVisible.value = true;
};

// 显示多选选择器
const showMultipleSelector = () => {
  selectorMultiple.value = true;
  selectorFilters.value = {};
  defaultSelected.value = [];
  selectorVisible.value = true;
};

// 显示带过滤条件的选择器
const showFilteredSelector = () => {
  selectorMultiple.value = true;
  selectorFilters.value = {
    stockStatus: "stocking", // 只显示"在库"状态的车辆
  };
  defaultSelected.value = [];
  selectorVisible.value = true;
};

// 显示出库模式选择器(排除试驾车)
const showOutboundSelector = () => {
  selectorMultiple.value = false;
  selectorFilters.value = {
    stockStatus: "stocking", // 只显示"在库"状态的车辆
    trial: "none", // 排除试驾车
  };
  defaultSelected.value = [];
  selectorVisible.value = true;
};

// 处理选择器取消
const handleSelectorCancel = () => {
  console.log("选择器已取消");
};

// 处理选择器确认
const handleSelectorConfirm = (result) => {
  console.log("选择器已确认", result);

  if (selectorMultiple.value) {
    // 多选模式
    selectedVehicles.value = result;
    selectedVehicle.value = null;
  } else {
    // 单选模式
    selectedVehicle.value = result;
    selectedVehicles.value = [];
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    stocking: "在库",
    transiting: "在途",
    sold: "已售",
    returned: "已退",
  };
  return statusMap[status] || status || "未知";
};
</script>

<style scoped>
.example-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 20px;
}

.n-card {
  margin-bottom: 20px;
}
</style>
