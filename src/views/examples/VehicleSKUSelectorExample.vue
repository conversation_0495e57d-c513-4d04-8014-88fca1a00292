<template>
  <div class="example-page">
    <h1>车辆SKU选择器示例</h1>
    
    <n-space vertical>
      <n-card title="品牌权限测试">
        <n-space>
          <n-button type="primary" @click="showSelector">
            打开SKU选择器
          </n-button>
          
          <n-button type="info" @click="testBrandCache">
            测试品牌缓存
          </n-button>
          
          <n-button type="warning" @click="clearBrandCache">
            清除品牌缓存
          </n-button>
          
          <n-button type="success" @click="setBrandCache">
            设置测试品牌缓存
          </n-button>
        </n-space>
        
        <n-divider />
        
        <div>
          <h3>当前品牌缓存：</h3>
          <n-tag v-if="!cachedBrands || cachedBrands.length === 0" type="warning">
            无限制（显示所有品牌）
          </n-tag>
          <n-tag 
            v-for="brand in cachedBrands" 
            :key="brand" 
            type="success" 
            style="margin-right: 8px"
          >
            {{ brand }}
          </n-tag>
        </div>
      </n-card>
      
      <n-card title="选择结果" v-if="selectedSku">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="品牌">{{ selectedSku.brand }}</n-descriptions-item>
          <n-descriptions-item label="车系">{{ selectedSku.series }}</n-descriptions-item>
          <n-descriptions-item label="配置">{{ selectedSku.configName }}</n-descriptions-item>
          <n-descriptions-item label="颜色代码">{{ selectedSku.colorCode }}</n-descriptions-item>
          <n-descriptions-item label="启票价">
            ￥{{ Number(selectedSku.sbPrice || 0).toLocaleString('zh-CN') }}
          </n-descriptions-item>
          <n-descriptions-item label="库存数量">{{ selectedSku.stockingQuantity }}</n-descriptions-item>
        </n-descriptions>
      </n-card>
    </n-space>
    
    <!-- 车辆SKU选择器组件 -->
    <vehicle-s-k-u-selector
      v-model:visible="selectorVisible"
      @select="handleSkuSelected"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { NCard, NSpace, NButton, NTag, NDivider, NDescriptions, NDescriptionsItem } from 'naive-ui'
import VehicleSKUSelector from '@/components/inventory/VehicleSKUSelector.vue'
import { vehicleBrandUtils } from '@/utils/dictUtils'
import messages from '@/utils/messages'

// 状态变量
const selectorVisible = ref(false)
const selectedSku = ref(null)
const cachedBrands = ref([])

// 显示选择器
const showSelector = () => {
  selectorVisible.value = true
}

// 处理SKU选择
const handleSkuSelected = (sku) => {
  selectedSku.value = sku
  messages.success(`已选择车辆：${sku.brand} ${sku.series} ${sku.configName}`)
}

// 测试品牌缓存
const testBrandCache = () => {
  const brands = vehicleBrandUtils.getCachedBrands()
  cachedBrands.value = brands
  
  if (brands && brands.length > 0) {
    messages.info(`当前缓存的品牌：${brands.join(', ')}`)
  } else {
    messages.info('当前没有品牌限制，将显示所有品牌')
  }
}

// 清除品牌缓存
const clearBrandCache = () => {
  vehicleBrandUtils.clearCachedBrands()
  cachedBrands.value = []
  messages.success('已清除品牌缓存')
}

// 设置测试品牌缓存
const setBrandCache = () => {
  const testBrands = ['深蓝', '阿维塔']
  vehicleBrandUtils.setCachedBrands(testBrands)
  cachedBrands.value = testBrands
  messages.success(`已设置测试品牌缓存：${testBrands.join(', ')}`)
}

// 页面加载时获取当前缓存
onMounted(() => {
  testBrandCache()
})
</script>

<style scoped>
.example-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 24px;
}

h3 {
  margin: 16px 0 8px 0;
  color: #666;
}
</style>
