<template>
  <div class="receivable-dialog-example">
    <n-card title="应收账款对话框示例">
      <n-space vertical>
        <n-space>
          <n-button type="primary" @click="showAddDialog"
            >新增应收账款</n-button
          >
          <n-button @click="showEditDialog">编辑应收账款</n-button>
        </n-space>

        <!-- 表单数据展示 -->
        <n-card title="当前表单数据" size="small">
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </n-card>

        <!-- 应收账款对话框 -->
        <receivable-dialog
          :show="dialogVisible"
          @update:show="dialogVisible = $event"
          :form-data="formData"
          :amount-in-chinese="amountInChinese"
          :saving="saving"
          :mode="dialogMode"
          @save="handleSave"
          @org-change="handleOrgChange"
          @subject-change="handleSubjectChange"
          @target-change="handleTargetChange"
          @amount-change="handleAmountChange"
        />
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import ReceivableDialog from "@/components/financial/ReceivableDialog.vue";

// 响应式数据
const dialogVisible = ref(false);
const dialogMode = ref("add");
const saving = ref(false);

// 表单数据
const formData = ref({
  // 表单字段
  receivableOrgId: null,
  receivableSubject: null,
  receivableTarget: null,
  receivableAmount: null,
  receivableSummary: "",

  // 接口提交字段（自动填充）
  feeId: null, // 费用科目ID
  receivableOrgName: "", // 应收机构名称
  feeTarget: "", // 应收对象名称
  feeAmount: null, // 应收金额（分）
});

// 计算金额大写
const amountInChinese = computed(() => {
  if (!formData.value.receivableAmount) return "";
  return convertToChinese(formData.value.receivableAmount);
});

// 显示新增对话框
function showAddDialog() {
  dialogMode.value = "add";
  formData.value = {
    // 表单字段
    receivableOrgId: null,
    receivableSubject: null,
    receivableTarget: null,
    receivableAmount: null,
    receivableSummary: "",

    // 接口提交字段（自动填充）
    feeId: null,
    receivableOrgName: "",
    feeTarget: "",
    feeAmount: null,
  };
  dialogVisible.value = true;
}

// 显示编辑对话框
function showEditDialog() {
  dialogMode.value = "edit";
  formData.value = {
    // 表单字段
    receivableOrgId: 1,
    receivableSubject: 1, // 使用科目ID而不是代码
    receivableTarget: "客户",
    receivableAmount: 150000,
    receivableSummary: "车辆销售款项",

    // 接口提交字段（提交时会自动填充）
    feeId: null,
    receivableOrgName: "",
    feeTarget: "",
    feeAmount: null,
  };
  dialogVisible.value = true;
}

// 处理保存
async function handleSave() {
  saving.value = true;
  try {
    // 模拟保存操作
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log("=== 最终提交的数据 ===");
    console.log("完整表单数据:", JSON.stringify(formData.value, null, 2));

    // 提取接口需要的字段
    const submitData = {
      feeId: formData.value.feeId,
      receivableOrgId: formData.value.receivableOrgId,
      receivableOrgName: formData.value.receivableOrgName,
      feeTarget: formData.value.feeTarget,
      feeAmount: formData.value.feeAmount,
      receivableSummary: formData.value.receivableSummary,
    };

    console.log("接口提交数据:", JSON.stringify(submitData, null, 2));
    console.log(
      "金额检查: 页面输入",
      formData.value.receivableAmount,
      "元 -> 接口提交",
      formData.value.feeAmount,
      "分"
    );

    dialogVisible.value = false;
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
    saving.value = false;
  }
}

// 处理机构变化
function handleOrgChange(org) {
  console.log("机构变化:", org);
}

// 处理科目变化
function handleSubjectChange(subject) {
  console.log("科目变化:", subject);
}

// 处理对象变化
function handleTargetChange(target) {
  console.log("对象变化:", target);
}

// 处理金额变化
function handleAmountChange(amount) {
  console.log("金额变化:", amount);
}

// 数字转中文大写
function convertToChinese(num) {
  if (!num || num === 0) return "零元整";

  const digits = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];

  const numStr = Math.floor(num * 100).toString();
  const len = numStr.length;

  if (len <= 2) {
    const jiao = Math.floor(num * 10) % 10;
    const fen = Math.floor(num * 100) % 10;
    let result = digits[Math.floor(num)] + "元";
    if (jiao > 0) result += digits[jiao] + "角";
    if (fen > 0) result += digits[fen] + "分";
    if (jiao === 0 && fen === 0) result += "整";
    return result;
  }

  // 简化版本，实际项目中建议使用专门的数字转中文库
  return `${num}元`;
}
</script>

<style lang="scss" scoped>
.receivable-dialog-example {
  padding: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
