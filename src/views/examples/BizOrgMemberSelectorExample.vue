<template>
  <div class="biz-org-member-selector-example">
    <n-card title="业务机构成员选择器示例">
      <n-space direction="vertical" size="large">
        <!-- 操作按钮区域 -->
        <n-space>
          <n-button type="primary" @click="showSingleSelector">
            单选模式
          </n-button>
          <n-button type="info" @click="showMultipleSelector">
            多选模式
          </n-button>
          <n-button type="warning" @click="showWithInitialOrg">
            指定初始机构
          </n-button>
        </n-space>

        <n-divider />

        <!-- 选择结果显示区域 -->
        <n-space direction="vertical">
          <n-text strong>选择结果：</n-text>
          
          <!-- 单选结果 -->
          <div v-if="singleSelectedMember">
            <n-text>单选结果：</n-text>
            <n-descriptions :column="2" bordered>
              <n-descriptions-item label="成员ID">
                {{ singleSelectedMember.id }}
              </n-descriptions-item>
              <n-descriptions-item label="成员姓名">
                {{ singleSelectedMember.name }}
              </n-descriptions-item>
              <n-descriptions-item label="员工ID">
                {{ singleSelectedMember.agentId }}
              </n-descriptions-item>
              <n-descriptions-item label="所属机构ID">
                {{ singleSelectedMember.biz_org_id }}
              </n-descriptions-item>
              <n-descriptions-item label="业务角色">
                {{ singleSelectedMember.businessRole || '未设置' }}
              </n-descriptions-item>
              <n-descriptions-item label="数据权限">
                {{ singleSelectedMember.dataPermissions || '未设置' }}
              </n-descriptions-item>
            </n-descriptions>
          </div>

          <!-- 多选结果 -->
          <div v-if="multipleSelectedMembers.length > 0">
            <n-text>多选结果（{{ multipleSelectedMembers.length }} 个成员）：</n-text>
            <n-data-table
              :columns="resultColumns"
              :data="multipleSelectedMembers"
              :pagination="false"
              :bordered="true"
              size="small"
              style="margin-top: 8px;"
            />
          </div>

          <!-- 无选择结果 -->
          <div v-if="!singleSelectedMember && multipleSelectedMembers.length === 0">
            <n-empty description="尚未选择成员" />
          </div>
        </n-space>
      </n-space>
    </n-card>

    <!-- 业务机构成员选择器组件 -->
    <biz-org-member-selector
      v-model:visible="memberSelectorVisible"
      :mode="currentMode"
      :initial-org-id="initialOrgId"
      :title="currentTitle"
      @select="handleMemberSelected"
      @cancel="handleMemberSelectCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  NCard, 
  NButton, 
  NSpace, 
  NDivider, 
  NText, 
  NDescriptions, 
  NDescriptionsItem, 
  NDataTable, 
  NEmpty 
} from 'naive-ui'
import BizOrgMemberSelector from '@/components/bizOrg/BizOrgMemberSelector.vue'
import messages from '@/utils/messages'

// 组件状态
const memberSelectorVisible = ref(false)
const currentMode = ref('single')
const currentTitle = ref('选择业务机构成员')
const initialOrgId = ref(null)
const singleSelectedMember = ref(null)
const multipleSelectedMembers = ref([])

// 表格列配置
const resultColumns = [
  {
    title: '成员ID',
    key: 'id',
    width: 80
  },
  {
    title: '成员姓名',
    key: 'name',
    width: 120
  },
  {
    title: '员工ID',
    key: 'agentId',
    width: 120
  },
  {
    title: '所属机构ID',
    key: 'biz_org_id',
    width: 120
  },
  {
    title: '业务角色',
    key: 'businessRole',
    width: 150,
    render: (row) => row.businessRole || '未设置'
  },
  {
    title: '数据权限',
    key: 'dataPermissions',
    width: 120,
    render: (row) => row.dataPermissions || '未设置'
  }
]

// 显示单选模式选择器
const showSingleSelector = () => {
  currentMode.value = 'single'
  currentTitle.value = '选择业务机构成员（单选）'
  initialOrgId.value = null
  memberSelectorVisible.value = true
}

// 显示多选模式选择器
const showMultipleSelector = () => {
  currentMode.value = 'multiple'
  currentTitle.value = '选择业务机构成员（多选）'
  initialOrgId.value = null
  memberSelectorVisible.value = true
}

// 显示指定初始机构的选择器
const showWithInitialOrg = () => {
  currentMode.value = 'multiple'
  currentTitle.value = '选择业务机构成员（指定初始机构）'
  initialOrgId.value = 1 // 假设机构ID为1
  memberSelectorVisible.value = true
}

// 处理成员选择
const handleMemberSelected = (selectedData) => {
  if (currentMode.value === 'single') {
    singleSelectedMember.value = selectedData
    multipleSelectedMembers.value = []
    messages.success(`已选择成员：${selectedData?.name || '未知'}`)
  } else {
    multipleSelectedMembers.value = selectedData || []
    singleSelectedMember.value = null
    messages.success(`已选择 ${selectedData?.length || 0} 个成员`)
  }
  
  console.log('选择的成员数据:', selectedData)
}

// 处理取消选择
const handleMemberSelectCancel = () => {
  messages.info('已取消选择')
}
</script>

<style lang="scss" scoped>
.biz-org-member-selector-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.n-descriptions) {
  margin-top: 8px;
}

:deep(.n-data-table) {
  .n-data-table-td {
    padding: 8px 12px;
  }
}
</style>
