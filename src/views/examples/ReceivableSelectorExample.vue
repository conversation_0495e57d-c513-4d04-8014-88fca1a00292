<template>
  <div class="receivable-selector-example">
    <n-card title="应收账款选择器示例" class="example-card">
      <n-space vertical>
        <n-button type="primary" @click="showReceivableSelector">
          打开应收账款选择器
        </n-button>

        <n-divider />

        <h3>选中的应收账款信息</h3>
        <div v-if="selectedReceivable" class="selected-receivable">
          <n-descriptions bordered>
            <n-descriptions-item label="订单编号">
              {{ selectedReceivable.orderSn }}
            </n-descriptions-item>
            <n-descriptions-item label="客户名称">
              {{ selectedReceivable.customerName }}
            </n-descriptions-item>
            <n-descriptions-item label="客户手机号">
              {{ selectedReceivable.mobile }}
            </n-descriptions-item>
            <n-descriptions-item label="应收科目">
              {{ selectedReceivable.feeName }}
            </n-descriptions-item>
            <n-descriptions-item label="应收对象">
              {{ selectedReceivable.feeTarget || '-' }}
            </n-descriptions-item>
            <n-descriptions-item label="应收金额">
              {{ formatAmount(selectedReceivable.feeAmount) }}
            </n-descriptions-item>
            <n-descriptions-item label="对账状态">
              <n-tag :type="selectedReceivable.confirmed ? 'success' : 'error'">
                {{ selectedReceivable.confirmed ? '已对账' : '未对账' }}
              </n-tag>
            </n-descriptions-item>
          </n-descriptions>
        </div>
        <div v-else class="no-receivable-selected">
          <n-empty description="尚未选择应收账款" />
        </div>
      </n-space>
    </n-card>

    <!-- 应收账款选择器组件 -->
    <receivable-selector
      v-model:visible="receivableSelectorVisible"
      :initial-receivable="selectedReceivable"
      @select="handleReceivableSelected"
      @cancel="handleReceivableSelectCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  NCard, 
  NButton, 
  NSpace, 
  NDivider, 
  NDescriptions, 
  NDescriptionsItem, 
  NEmpty,
  NTag
} from 'naive-ui'
import ReceivableSelector from '@/components/financial/ReceivableSelector.vue'
import messages from '@/utils/messages'

// 组件状态
const receivableSelectorVisible = ref(false)
const selectedReceivable = ref(null)

// 显示应收账款选择器
const showReceivableSelector = () => {
  receivableSelectorVisible.value = true
}

// 处理应收账款选择
const handleReceivableSelected = (receivable) => {
  selectedReceivable.value = receivable
  messages.success(`已选择应收账款：${receivable.feeName}`)
}

// 处理应收账款选择取消
const handleReceivableSelectCancel = () => {
  messages.info('已取消选择应收账款')
}

// 格式化金额显示
const formatAmount = (amount) => {
  if (amount === undefined || amount === null) return '￥0.00'
  return `￥${(amount / 100).toLocaleString('zh-CN', { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  })}`
}
</script>

<style scoped>
.receivable-selector-example {
  padding: 20px;
}

.example-card {
  max-width: 800px;
  margin: 0 auto;
}

.selected-receivable {
  margin-top: 16px;
}

.no-receivable-selected {
  margin-top: 16px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  text-align: center;
}
</style>
