import { ref, reactive, computed, onMounted, h, markRaw } from "vue";
import messages from "@/utils/messages";
import bizOrgApi from "@/api/bizOrg";
import { getDictOptions } from "@/api/dict";
import { NIcon, NTag } from "naive-ui";
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
  ChevronUpOutline,
  ChevronDownOutline,
  PeopleOutline,
} from "@vicons/ionicons5";

import BizOrgEditModal from "@/components/bizOrg/BizOrgEditModal.vue";
import BizOrgMembersModal from "@/components/bizOrg/BizOrgMembersModal.vue";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchOutlineIcon = markRaw(SearchOutline);
const RefreshOutlineIcon = markRaw(RefreshOutline);
const AddOutlineIcon = markRaw(AddOutline);
const CreateOutlineIcon = markRaw(CreateOutline);
const ChevronUpOutlineIcon = markRaw(ChevronUpOutline);
const ChevronDownOutlineIcon = markRaw(ChevronDownOutline);
const PeopleOutlineIcon = markRaw(PeopleOutline);

export function useBizOrgPage() {
  // 状态变量
  const tableRef = ref(null);
  const bizOrgEditModalRef = ref(null);
  const membersModalRef = ref(null);
  const loading = ref(false);
  const dialogVisible = ref(false);
  const dialogTitle = ref("新增机构");
  const isEdit = ref(false);
  const isFilterExpanded = ref(false); // 筛选区域展开状态

  // 字典选项数据
  const provinceOptions = ref([{ label: "不限", value: null }]);
  const cityOptions = ref([{ label: "不限", value: null }]);
  const mainBrandOptions = ref([{ label: "不限", value: null }]);
  const businessPermissionOptions = ref([{ label: "不限", value: null }]);
  const orgTypeOptions = ref([{ label: "不限", value: null }]);

  // 字典映射表，用于快速查找字典标签
  const dictMaps = reactive({
    province: {},
    city: {},
    mainBrand: {},
    businessPermission: {},
    orgType: {},
  });

  // 字典渲染工具函数
  const getDictLabel = (dictType, value) => {
    if (!value || !dictMaps[dictType]) return value;
    return dictMaps[dictType][value] || value;
  };

  // 兼容字典API数据字段命名的工具函数
  const getOptionLabel = (item) => {
    return item.optionLabel || item.optionLabel;
  };

  const getOptionValue = (item) => {
    return item.optionValue || item.optionValue;
  };

  // 计算属性：根据选中的省份过滤城市选项
  const filteredCityOptions = computed(() => {
    if (!filterForm.province) {
      return [];
    }

    const filtered = cityOptions.value.filter(
      (city) => city.value === null || city.parent === filterForm.province
    );

    return [
      { label: "不限", value: null },
      ...filtered.filter((item) => item.value !== null),
    ];
  });

  // 筛选表单
  const filterForm = reactive({
    province: null,
    city: null,
    mainBrand: null,
    businessPermission: null,
    orgType: null,
    keywords: "",
  });

  // 数据列表
  const bizOrgData = ref([]);

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
  });

  // 表格数据
  const filteredData = computed(() => {
    return bizOrgData.value;
  });

  // 加载字典选项数据
  const loadDictOptions = async () => {
    try {
      // 加载省份选项
      const provinceRes = await getDictOptions("province_city");
      if (provinceRes.code === 200) {
        provinceOptions.value = [
          { label: "不限", value: null },
          ...provinceRes.data.map((item) => ({
            label: getOptionLabel(item),
            value: getOptionValue(item),
          })),
        ];
        // 构建省份字典映射表
        provinceRes.data.forEach((item) => {
          dictMaps.province[getOptionValue(item)] = getOptionLabel(item);
        });
      }

      // 加载城市选项
      const cityRes = await getDictOptions("city_district");
      if (cityRes.code === 200) {
        cityOptions.value = [
          { label: "不限", value: null },
          ...cityRes.data.map((item) => ({
            label: getOptionLabel(item),
            value: getOptionValue(item),
            parent: item.parent,
          })),
        ];
        // 构建城市字典映射表
        cityRes.data.forEach((item) => {
          dictMaps.city[getOptionValue(item)] = getOptionLabel(item);
        });
      }

      // 加载主营品牌选项
      const brandRes = await getDictOptions("vehicle_brand");
      if (brandRes.code === 200) {
        mainBrandOptions.value = [
          { label: "不限", value: null },
          ...brandRes.data.map((item) => ({
            label: getOptionLabel(item),
            value: getOptionLabel(item), // 主营品牌使用标签作为值
          })),
        ];

        // 自动选中第一个选项（如果当前没有选中值）
        if (filterForm.mainBrand === null && mainBrandOptions.value.length > 0) {
          filterForm.mainBrand = mainBrandOptions.value[0].value;
        }

        // 构建主营品牌字典映射表 - 支持双向映射
        brandRes.data.forEach((item) => {
          const optionValue = getOptionValue(item);
          const optionLabel = getOptionLabel(item);
          dictMaps.mainBrand[optionValue] = optionLabel;
          dictMaps.mainBrand[optionLabel] = optionLabel; // 标签映射到自身
        });
      }

      // 加载业务权限选项
      const permissionRes = await getDictOptions("business_permission");
      if (permissionRes.code === 200) {
        businessPermissionOptions.value = [
          { label: "不限", value: null },
          ...permissionRes.data.map((item) => ({
            label: getOptionLabel(item),
            value: getOptionValue(item),
          })),
        ];
        // 构建业务权限字典映射表
        permissionRes.data.forEach((item) => {
          dictMaps.businessPermission[getOptionValue(item)] = getOptionLabel(item);
        });
      }

      // 加载机构类型选项
      const orgTypeRes = await getDictOptions("org_type");
      if (orgTypeRes.code === 200) {
        orgTypeOptions.value = [
          { label: "不限", value: null },
          ...orgTypeRes.data.map((item) => ({
            label: getOptionLabel(item),
            value: getOptionValue(item),
          })),
        ];
        // 构建机构类型字典映射表
        orgTypeRes.data.forEach((item) => {
          dictMaps.orgType[getOptionValue(item)] = getOptionLabel(item);
        });
      }
    } catch (error) {
      console.error("加载字典选项失败:", error);
      messages.error("加载字典选项失败");
    }
  };

  // 刷新数据
  const refreshData = async () => {
    loading.value = true;
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize,
      };

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords;
      }

      // 处理省份筛选
      if (filterForm.province) {
        params.province = filterForm.province;
      }

      // 处理城市筛选
      if (filterForm.city) {
        params.city = filterForm.city;
      }

      // 处理主营品牌筛选
      if (filterForm.mainBrand) {
        params.mainBrand = filterForm.mainBrand;
      }

      // 处理业务权限筛选
      if (filterForm.businessPermission) {
        params.businessPermission = filterForm.businessPermission;
      }

      // 处理机构类型筛选
      if (filterForm.orgType) {
        params.orgType = filterForm.orgType;
      }

      // 调用API获取数据
      const response = await bizOrgApi.getBizOrgList(params);

      if (response.code === 200) {
        // 直接使用返回的数据列表
        bizOrgData.value = response.data.list;

        // 更新分页信息
        pagination.itemCount = response.data.total;
      } else {
        messages.error(response.message || "数据加载失败");
      }
    } catch (error) {
      messages.error("加载数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1;
    refreshData();
  };

  // 处理省份变化
  const handleProvinceChange = (value) => {
    filterForm.province = value;
    // 当省份改变时，重置城市选择
    filterForm.city = null;
    handleSearch();
  };

  // 显示新增机构对话框
  const showAddDialog = () => {
    // 重置表单
    if (bizOrgEditModalRef.value) {
      bizOrgEditModalRef.value.resetForm();
    }

    // 打开编辑弹窗
    isEdit.value = false;
    dialogTitle.value = "新增机构";
    dialogVisible.value = true;
  };

  // 处理编辑
  const handleEdit = async (id) => {
    try {
      loading.value = true;

      // 调用API获取详细数据
      const response = await bizOrgApi.getBizOrgDetail(id);

      if (response.code === 200) {
        const apiData = response.data;

        isEdit.value = true;
        dialogTitle.value = "编辑机构";

        // 设置表单数据
        if (bizOrgEditModalRef.value) {
          bizOrgEditModalRef.value.setFormData(apiData);
        }

        dialogVisible.value = true;
      } else {
        messages.error(response.message || "获取机构数据失败");
      }
    } catch (error) {
      console.error("获取机构数据失败:", error);
      messages.error("获取机构数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 处理员工管理
  const handleEmployeeManagement = (orgId) => {
    // 找到对应的机构信息
    const org = bizOrgData.value.find((item) => item.id === orgId);
    if (!org) {
      messages.error("未找到机构信息");
      return;
    }

    // 打开成员管理弹窗
    membersModalRef.value.openModal(orgId, org.orgName);
  };

  // 处理保存成功
  const handleSaveSuccess = () => {
    refreshData(); // 刷新数据列表
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page;
    refreshData();
  };

  // 处理分页大小变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    refreshData();
  };

  // 切换筛选区域展开状态
  const toggleFilterExpansion = () => {
    isFilterExpanded.value = !isFilterExpanded.value;
  };

  // 表格列配置
  const columns = [
    {
      title: "机构ID",
      key: "id",
      width: 100,
      align: "center",
      render(row) {
        return h(
          "span",
          {
            style: {
              fontFamily: "monospace",
              color: "#666",
            },
          },
          row.id
        );
      },
    },
    {
      title: "机构名称",
      key: "orgName",
      width: 200,
      render(row) {
        return h(
          "span",
          {
            style: {
              fontWeight: 600,
              color: "var(--primary-color)",
            },
          },
          row.orgName
        );
      },
    },
    {
      title: "所在地区",
      key: "location",
      width: 180,
      render(row) {
        const provinceName = getDictLabel("province", row.province);
        const cityName = getDictLabel("city", row.city);
        if (provinceName && cityName) {
          return `${provinceName}/${cityName}`;
        } else if (provinceName) {
          return provinceName;
        } else if (cityName) {
          return cityName;
        }
        return "未设置";
      },
    },
    {
      title: "主营品牌",
      key: "salesBrands",
      width: 220,
      render(row) {
        // 兼容数组和逗号分割字符串两种格式
        let brands = [];
        if (row.salesBrands) {
          if (Array.isArray(row.salesBrands)) {
            brands = row.salesBrands;
          } else if (typeof row.salesBrands === "string") {
            brands = row.salesBrands
              .split(",")
              .map((item) => item.trim())
              .filter((item) => item);
          }
        }

        if (brands.length === 0) {
          return "未设置";
        }

        // 定义品牌颜色映射
        const getBrandStyle = (brand) => {
          const brandColors = ["info", "success", "warning", "error"];
          const index = brand.charCodeAt(0) % brandColors.length;
          return { type: brandColors[index] };
        };

        const tags = brands.map((brand) => {
          const brandLabel = getDictLabel("mainBrand", brand);
          const style = getBrandStyle(brand);

          return h(
            NTag,
            {
              size: "large",
              type: style.type,
              bordered: false,
              style: {
                margin: "2px",
                fontWeight: "bold",
              },
            },
            { default: () => brandLabel }
          );
        });

        return h("div", { style: { display: "flex", flexWrap: "wrap" } }, tags);
      },
    },
    {
      title: "机构类型",
      key: "orgType",
      width: 100,
      render(row) {
        const typeLabel = getDictLabel("orgType", row.orgType);

        // 根据机构类型设置标签样式
        const getTypeStyle = (orgType) => {
          switch (orgType) {
            case "group":
              return { type: "success", color: "#18a058" };
            case "single_store":
              return { type: "info", color: "#2080f0" };
            case "secondary_network":
              return { type: "warning", color: "#f0a020" };
            default:
              return { type: "default", color: "#909399" };
          }
        };

        const style = getTypeStyle(row.orgType);

        return h(
          NTag,
          {
            type: style.type,
            bordered: false,
            style: {
              padding: "2px 8px",
              fontWeight: "bold",
            },
          },
          { default: () => typeLabel }
        );
      },
    },
    {
      title: "联系人",
      key: "contactPerson",
      width: 100,
    },
    {
      title: "联系电话",
      key: "contactPhone",
      width: 120,
      render(row) {
        // 手机号脱敏处理：隐藏中间四位，以****代替
        if (!row.contactPhone) return "";
        if (row.contactPhone.length === 11) {
          return (
            row.contactPhone.substring(0, 3) +
            "****" +
            row.contactPhone.substring(7)
          );
        }
        return row.contactPhone;
      },
    },
    {
      title: "业务权限",
      key: "bizPermissions",
      width: 240,
      render(row) {
        // 兼容数组和逗号分割字符串两种格式
        let permissions = [];
        if (row.bizPermissions) {
          if (Array.isArray(row.bizPermissions)) {
            permissions = row.bizPermissions;
          } else if (typeof row.bizPermissions === "string") {
            permissions = row.bizPermissions
              .split(",")
              .map((item) => item.trim())
              .filter((item) => item);
          }
        }

        if (permissions.length === 0) {
          return "无权限";
        }

        // 定义权限颜色映射
        const getPermissionStyle = (permission) => {
          switch (permission) {
            case "can_stock_in": // 可入库
              return { type: "success", color: "#18a058" };
            case "can_sell": // 可销售
              return { type: "info", color: "#2080f0" };
            case "can_stock_out": // 可出库
              return { type: "warning", color: "#f0a020" };
            case "can_settle": // 可结算
              return { type: "error", color: "#d03050" };
            default:
              return { type: "default", color: "#909399" };
          }
        };

        const tags = permissions.map((permission) => {
          const permissionLabel = getDictLabel("businessPermission", permission);
          const style = getPermissionStyle(permission);

          return h(
            NTag,
            {
              size: "large",
              type: style.type,
              bordered: false,
              style: {
                margin: "2px",
                fontWeight: "bold",
              },
            },
            { default: () => permissionLabel }
          );
        });

        return h("div", { style: { display: "flex", flexWrap: "wrap" } }, tags);
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 100,
      fixed: "right",
      align: "center",
      render: (row) => {
        const actions = [];

        // 编辑按钮
        actions.push(
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#18a058",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleEdit(row.id),
              title: "编辑机构",
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
          )
        );

        // 员工管理按钮 - 只有非external_partner类型的机构才显示
        if (row.orgType !== 'external_partner') {
          actions.push(
            h(
              "div",
              {
                style: {
                  cursor: "pointer",
                  color: "#2080f0",
                  fontSize: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                },
                onClick: () => handleEmployeeManagement(row.id),
                title: "员工管理",
              },
              [h(NIcon, { size: 20 }, { default: () => h(PeopleOutlineIcon) })]
            )
          );
        }

        return h(
          "div",
          {
            style: {
              display: "flex",
              justifyContent: "center",
              gap: "8px",
            },
          },
          actions
        );
      },
    },
  ];

  // 初始化
  onMounted(() => {
    loadDictOptions();
    refreshData();
  });

  return {
    // 组件引用
    tableRef,
    bizOrgEditModalRef,
    membersModalRef,

    // 图标组件
    SearchOutlineIcon,
    RefreshOutlineIcon,
    AddOutlineIcon,
    CreateOutlineIcon,
    ChevronUpOutlineIcon,
    ChevronDownOutlineIcon,
    PeopleOutlineIcon,

    // 状态变量
    loading,
    dialogVisible,
    dialogTitle,
    isEdit,
    isFilterExpanded,

    // 数据
    provinceOptions,
    cityOptions,
    mainBrandOptions,
    businessPermissionOptions,
    orgTypeOptions,
    filteredCityOptions,
    filterForm,
    bizOrgData,
    pagination,
    filteredData,
    columns,

    // 工具函数
    getDictLabel,

    // 方法
    loadDictOptions,
    refreshData,
    handleSearch,
    handleProvinceChange,
    showAddDialog,
    handleEdit,
    handleEmployeeManagement,
    handleSaveSuccess,
    handlePageChange,
    handlePageSizeChange,
    toggleFilterExpansion,

    // 组件
    BizOrgEditModal,
    BizOrgMembersModal,
  };
}
