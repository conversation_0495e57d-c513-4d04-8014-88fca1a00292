<template>
  <div class="test-page">
    <n-card title="库存详情弹窗测试">
      <n-space>
        <n-button type="primary" @click="showDetailModal">
          查看库存详情（含调拨记录）
        </n-button>
        <n-button type="success" @click="showEditModal"> 编辑库存 </n-button>
        <n-button type="info" @click="showAddModal"> 新增库存 </n-button>
      </n-space>
    </n-card>

    <!-- 库存详情弹窗 -->
    <stock-form-modal
      v-model:visible="modalVisible"
      :stock-id="currentStockId"
      :stock-data="currentStockData"
      :readonly="isReadonly"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useMessage } from "naive-ui";
import StockFormModal from "@/components/inventory/StockFormModal.vue";

const message = useMessage();

// 响应式数据
const modalVisible = ref(false);
const currentStockId = ref(null);
const currentStockData = ref(null);
const isReadonly = ref(false);

// 模拟库存数据（包含调拨记录）
const mockStockData = {
  id: 1,
  vin: "LS6C3E2S3SF017982",
  stockAmount: 30000000, // 30万元，以分为单位
  createTime: "2024-01-15 10:30:00",
  stockStatus: "stocking",
  stockType: "CASH",
  trialStatus: "trialing",
  trialingBeginDate: 1705286400000, // 时间戳
  brand: "雷克萨斯",
  series: "ES",
  configName: "300h 卓越版",
  color: "珍珠白",
  // 模拟调拨记录数据
  transfers: [
    {
      createdTime: "2024-01-20 14:30:00",
      sourceOrgName: "北京4S店",
      creatorAgentName: "张三",
      updatedTime: "2024-01-20 15:45:00",
      targetOrgName: "上海4S店",
      receptAgentName: "李四",
      transferStatus: "confirmed",
      transferType: "INTERNAL",
      remark: "库存调拨，支援上海店销售",
    },
    {
      createdTime: "2024-01-18 09:15:00",
      sourceOrgName: "广州4S店",
      creatorAgentName: "王五",
      updatedTime: null,
      targetOrgName: "深圳4S店",
      receptAgentName: null,
      transferStatus: "pending",
      transferType: "EXTERNAL",
      remark: "客户指定要求调拨",
    },
    {
      createdTime: "2024-01-15 16:20:00",
      sourceOrgName: "成都4S店",
      creatorAgentName: "赵六",
      updatedTime: "2024-01-16 10:30:00",
      targetOrgName: "重庆4S店",
      receptAgentName: "钱七",
      transferStatus: "confirmed",
      transferType: "INTERNAL",
      remark: "区域库存平衡调拨",
    },
    {
      createdTime: "2024-01-12 11:45:00",
      sourceOrgName: "杭州4S店",
      creatorAgentName: "孙八",
      updatedTime: null,
      targetOrgName: "宁波4S店",
      receptAgentName: null,
      transferStatus: "rejected",
      transferType: "EXTERNAL",
      remark: "紧急调拨申请，因库存不足被拒绝",
    },
    {
      createdTime: "2024-01-10 13:20:00",
      sourceOrgName: "南京4S店",
      creatorAgentName: "周九",
      updatedTime: "2024-01-10 14:15:00",
      targetOrgName: "苏州4S店",
      receptAgentName: "吴十",
      transferStatus: "confirmed",
      transferType: "INTERNAL",
      remark: "月度库存调拨计划",
    },
    {
      createdTime: "2024-01-08 08:30:00",
      sourceOrgName: "武汉4S店",
      creatorAgentName: "郑十一",
      updatedTime: "2024-01-08 09:45:00",
      targetOrgName: "长沙4S店",
      receptAgentName: "王十二",
      transferStatus: "confirmed",
      transferType: "EXTERNAL",
      remark: "客户需求调拨",
    },
  ],
};

// 显示详情弹窗
const showDetailModal = () => {
  currentStockId.value = 1;
  currentStockData.value = mockStockData;
  isReadonly.value = true;
  modalVisible.value = true;
};

// 显示编辑弹窗
const showEditModal = () => {
  currentStockId.value = 1;
  currentStockData.value = mockStockData;
  isReadonly.value = false;
  modalVisible.value = true;
};

// 显示新增弹窗
const showAddModal = () => {
  currentStockId.value = null;
  currentStockData.value = null;
  isReadonly.value = false;
  modalVisible.value = true;
};

// 处理成功回调
const handleSuccess = () => {
  message.success("操作成功");
};
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>
