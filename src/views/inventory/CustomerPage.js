import { ref, reactive, computed, onMounted, h, markRaw } from "vue";
import messages from "@/utils/messages";
import customerApi from "@/api/customer";
import { NIcon, NTag } from "naive-ui";
import { formatDate } from "@/utils/dateUtils";
import { getDictOptions } from "@/utils/dictUtils";
import {
  CreateOutline,
  RefreshOutline,
  AddOutline,
  SearchOutline,
} from "@vicons/ionicons5";
import SearchIcon from '@/components/icons/SearchIcon.vue';
import {
  dateRangeOptions,
  getDateRangeParams,
} from "@/utils/dateRange";
import { getBizOrgDetail } from "@/api/bizOrg";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchIconComponent = markRaw(SearchIcon);
const CreateOutlineIcon = markRaw(CreateOutline);
const RefreshOutlineIcon = markRaw(RefreshOutline);
const AddOutlineIcon = markRaw(AddOutline);
const SearchOutlineIcon = markRaw(SearchOutline);

export function useCustomerPage() {
  // 状态变量
  const tableRef = ref(null);
  const loading = ref(false);
  const dialogVisible = ref(false);
  const dialogTitle = ref("新增客户");
  const isEdit = ref(false);
  const selectedRows = ref([]);

  // 字典选项数据
  const customerCategoryOptions = ref([]);
  const dealStatusOptions = ref([]);

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    customerType: null,
    ownerOrgs: [],
    keywords: "",
    dealStatus: null,
    showOrgSelector: false,
  });

  // 表单数据
  const form = reactive({
    id: null,
    customerName: "",
    customerType: "individual",
    customerIdCode: "",
    mobile: "",
    ownerOrgId: null,
    ownerOrgName: "",
    ownerSellerId: null,
    ownerSellerName: "",
    address: "",
    remark: "",
    dealStatus: "CUSTOMER",
  });

  // 销售顾问选择器状态
  const sellerSelectorVisible = ref(false);

  // 表单验证规则
  const rules = {
    customerName: {
      required: true,
      message: "请输入客户名称",
      trigger: ["blur", "input"],
    },
    customerType: {
      required: true,
      message: "请选择客户类型",
      trigger: ["blur", "change"],
    },
    customerIdCode: {
      required: false,
      trigger: ["blur", "input"],
    },
    mobile: {
      required: true,
      message: "请输入手机号码",
      trigger: ["blur", "input"],
    },
    ownerOrgName: {
      required: true,
      message: "请选择归属单位",
      trigger: ["blur", "input"],
    },
    ownerSellerName: {
      required: true,
      message: "请选择销售顾问",
      trigger: ["blur", "input"],
    },
  };

  // 数据列表
  const customerData = ref([]);

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    onChange: (page) => {
      pagination.page = page;
      refreshData();
    },
    onUpdatePageSize: (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      refreshData();
    },
  });

  // 获取字典项显示信息的辅助函数
  const getDictItemInfo = (dictOptions, value) => {
    const item = dictOptions.find((option) => option.value === value);
    if (item) {
      return {
        text: item.label,
        type: item.type || "default",
        color: item.color || "#909399",
      };
    }
    return {
      text: "未知",
      type: "default",
      color: "#909399",
    };
  };

  // 表格列配置
  const columns = [
    { type: "selection", width: 50 },
    {
      title: "创建日期",
      key: "createTime",
      width: 120,
      render(row) {
        return formatDate(row.createTime, "YYYY-MM-DD");
      },
    },
    {
      title: "客户类型",
      key: "customerType",
      width: 100,
      render(row) {
        const status = getDictItemInfo(
          customerCategoryOptions.value,
          row.customerType
        );

        return h(
          NTag,
          {
            type: status.type,
            bordered: false,
            style: {
              padding: "2px 8px",
              fontWeight: "bold",
            },
          },
          { default: () => status.text }
        );
      },
    },
    {
      title: "客户名称",
      key: "customerName",
      width: 150,
    },
    {
      title: "联系方式",
      key: "mobile",
      width: 120,
    },
    {
      title: "归属单位",
      key: "ownerOrgName",
      width: 180,
    },
    {
      title: "销售顾问",
      key: "ownerSellerName",
      width: 100,
    },
    {
      title: "成交状态",
      key: "dealStatus",
      width: 100,
      render(row) {
        const status = getDictItemInfo(dealStatusOptions.value, row.dealStatus);

        return h(
          NTag,
          {
            type: status.type,
            bordered: false,
            style: {
              padding: "2px 8px",
              fontWeight: "bold",
            },
          },
          { default: () => status.text }
        );
      },
    },
    {
      title: "备注",
      key: "remark",
      width: 150,
    },
    {
      title: "操作",
      key: "actions",
      width: 100,
      fixed: "right",
      align: "center",
      render: (row) => {
        return h(
          "div",
          { style: { display: "flex", justifyContent: "center", gap: "12px" } },
          [
            h(
              "div",
              {
                style: {
                  cursor: "pointer",
                  color: "#18a058",
                  fontSize: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                },
                onClick: () => handleView(row.id),
              },
              [h(SearchIconComponent, { size: 20, color: '#18a058' })]
            ),
            h(
              "div",
              {
                style: {
                  cursor: "pointer",
                  color: "#18a058",
                  fontSize: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                },
                onClick: () => handleEdit(row.id),
              },
              [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
            ),
          ]
        );
      },
    },
  ];

  // 表格数据
  const filteredData = computed(() => {
    return customerData.value;
  });

  // 加载字典数据
  const loadDictData = () => {
    // 加载客户类型字典
    customerCategoryOptions.value = getDictOptions("customer_type", true);

    // 加载成交状态字典
    dealStatusOptions.value = getDictOptions("deal_status", true);
  };

  // 刷新数据
  const refreshData = async () => {
    loading.value = true;
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize,
      };

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords;
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(
          filterForm.dateRange,
          filterForm.customDateRange
        );
        if (dateRange.startDate) params.startDate = dateRange.startDate;
        if (dateRange.endDate) params.endDate = dateRange.endDate;
      }

      // 处理客户类型
      if (filterForm.customerType) {
        params.customerType = filterForm.customerType;
      }

      // 处理归属单位
      if (filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0) {
        params.owner_org_code = filterForm.ownerOrgs
          .map((org) => org.id)
          .join(",");
      }

      // 处理成交状态
      if (filterForm.dealStatus) {
        params.dealStatus = filterForm.dealStatus;
      }

      // 调用API获取数据
      const response = await customerApi.getCustomerList(params);

      if (response.code === 200) {
        customerData.value = response.data.list;
        pagination.itemCount = response.data.total;
        pagination.pageCount = response.data.pages;
      } else {
        messages.error(response.message || "数据加载失败");
      }
    } catch (error) {
      console.error("加载数据失败:", error);
      messages.error("加载数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch);
  };

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch);
  };

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1;
    refreshData();
  };

  // 显示新增对话框
  const showAddDialog = () => {
    isEdit.value = false;
    dialogTitle.value = "新增客户";

    // 重置表单
    const customerType = "individual";
    Object.assign(form, {
      id: null,
      customerName: "",
      customerType: customerType,
      customerIdCode: "",
      mobile: "",
      ownerOrgId: null,
      ownerOrgName: "",
      ownerSellerId: null,
      ownerSellerName: "",
      address: "",
      remark: "",
      dealStatus: customerType === "institutional" ? "LEADS" : "CUSTOMER",
    });

    dialogVisible.value = true;
  };

  // 处理编辑
  const handleEdit = async (id) => {
    try {
      loading.value = true;
      console.log("编辑客户:", id);

      const response = await customerApi.getCustomerDetail(id);

      if (response.code === 200) {
        const apiData = response.data;

        isEdit.value = true;
        dialogTitle.value = "编辑客户";

        Object.assign(form, {
          id: apiData.id,
          customerName: apiData.customerName || "",
          customerType: apiData.customerType || "individual",
          customerIdCode: apiData.customerIdCode || "",
          mobile: apiData.mobile || "",
          ownerOrgId: apiData.ownerOrgId || null,
          ownerOrgName: apiData.ownerOrgName || "",
          ownerSellerId: apiData.ownerSellerId || null,
          ownerSellerName: apiData.ownerSellerName || "",
          address: apiData.address || "",
          remark: apiData.remark || "",
          dealStatus: apiData.dealStatus || "CUSTOMER",
        });

        dialogVisible.value = true;
      } else {
        messages.error(response.message || "获取客户数据失败");
      }
    } catch (error) {
      console.error("获取客户数据失败:", error);
      messages.error("获取客户数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 详情弹窗
  const detailDialogVisible = ref(false);
  const currentDetailId = ref(null);

  // 处理查看
  const handleView = (id) => {
    currentDetailId.value = id;
    detailDialogVisible.value = true;
  };

  // 客户选择器
  const customerSelectorVisible = ref(false);

  // 显示客户选择器
  const showCustomerSelector = () => {
    customerSelectorVisible.value = true;
  };

  // 处理客户选择
  const handleCustomerSelected = (customer) => {
    messages.success(`已选择客户: ${customer.customerName}`);
    console.log("选择的客户:", customer);

    Object.assign(form, {
      id: customer.id,
      customerName: customer.customerName,
      customerType: customer.customerType,
      customerIdCode: customer.customerIdCode,
      mobile: customer.mobile,
      ownerOrgId: customer.ownerOrgId,
      ownerOrgName: customer.ownerOrgName,
      ownerSellerId: customer.ownerSellerId,
      ownerSellerName: customer.ownerSellerName,
      address: customer.address,
      remark: customer.remark,
      dealStatus: customer.dealStatus,
    });

    isEdit.value = true;
    dialogTitle.value = "编辑客户";
    dialogVisible.value = true;
  };

  // 处理保存
  const handleSave = async () => {
    try {
      loading.value = true;

      const data = {
        customerName: form.customerName,
        customerType: form.customerType,
        customerIdCode: form.customerIdCode,
        mobile: form.mobile,
        ownerOrgId: form.ownerOrgId,
        ownerOrgName: form.ownerOrgName,
        ownerSellerId: form.ownerSellerId,
        ownerSellerName: form.ownerSellerName,
        address: form.address || "",
        remark: form.remark || "",
        dealStatus: form.dealStatus,
      };

      if (isEdit.value) {
        data.id = form.id;
      }

      const response = isEdit.value
        ? await customerApi.updateCustomer(data)
        : await customerApi.addCustomer(data);

      if (response.code === 200) {
        messages.success(isEdit.value ? "更新成功" : "添加成功");
        dialogVisible.value = false;
        refreshData();
      } else {
        messages.error(
          response.message || (isEdit.value ? "更新失败" : "添加失败")
        );
      }
    } catch (error) {
      console.error("保存失败:", error);
      messages.error("保存失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 处理选择变化
  const handleSelectionChange = (keys) => {
    selectedRows.value = customerData.value.filter((item) =>
      keys.includes(item.id)
    );
  };

  // 计算属性：选中的机构文本
  const selectedOrgText = computed(() => {
    if (filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0) {
      if (filterForm.ownerOrgs.length === 1) {
        return filterForm.ownerOrgs[0].orgName;
      } else {
        return `已选择 ${filterForm.ownerOrgs.length} 个机构`;
      }
    }
    return "请选择归属单位";
  });

  // 处理筛选条件的机构选择
  const handleFilterOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      filterForm.ownerOrgs = [...orgs];
      handleSearch();
    }
  };

  // 处理筛选条件的机构选择取消
  const handleFilterOrgCancel = () => {
    filterForm.showOrgSelector = false;
  };

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.ownerOrgs = [];
    handleSearch();
  };

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.ownerOrgs = filterForm.ownerOrgs.filter(
      (org) => org.id !== orgToRemove.id
    );
    handleSearch();
  };

  // 显示销售顾问选择器
  const showSellerSelector = () => {
    sellerSelectorVisible.value = true;
  };

  // 处理销售顾问选择
  const handleSellerSelect = async (seller) => {
    if (seller) {
      form.ownerSellerId = seller.agentId;
      form.ownerSellerName = seller.name || seller.agentName;

      if (seller.biz_org_id) {
        form.ownerOrgId = seller.biz_org_id;

        try {
          const response = await getBizOrgDetail(seller.biz_org_id);
          if (response.code === 200 && response.data) {
            form.ownerOrgName = response.data.orgName;
          } else {
            form.ownerOrgName = `机构ID: ${seller.biz_org_id}`;
          }
        } catch (error) {
          console.error("获取机构详情失败:", error);
          form.ownerOrgName = `机构ID: ${seller.biz_org_id}`;
        }
      }

      messages.success(`已选择销售顾问：${seller.name || seller.agentName}`);
    } else {
      form.ownerSellerId = null;
      form.ownerSellerName = "";
      form.ownerOrgId = null;
      form.ownerOrgName = "";
    }
  };

  // 处理客户类型变化
  const handleCustomerTypeChange = (value) => {
    if (value === "institutional") {
      form.dealStatus = "LEADS";
    } else {
      form.dealStatus = "CUSTOMER";
    }
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page;
  };

  // 处理导入成功
  const handleImportSuccess = async (fileInfo) => {
    try {
      messages.success(`文件上传成功: ${fileInfo.fileName}`);
      console.log("文件上传路径:", fileInfo.fileKey);

      const response = await customerApi.importCustomers(fileInfo.fileKey);

      if (response.code === 200) {
        messages.success("文件解析成功");
        refreshData();
      } else {
        messages.error(response.message || "文件解析失败");
      }
    } catch (error) {
      console.error("文件解析失败:", error);
      messages.error("文件解析失败，请检查文件格式是否正确");
    }
  };

  // 处理导入错误
  const handleImportError = (errorMsg) => {
    console.error(`导入失败: ${errorMsg}`);
  };

  return {
    // 状态
    tableRef,
    loading,
    dialogVisible,
    dialogTitle,
    isEdit,
    selectedRows,
    customerCategoryOptions,
    dealStatusOptions,
    filterForm,
    form,
    sellerSelectorVisible,
    rules,
    customerData,
    pagination,
    columns,
    filteredData,
    detailDialogVisible,
    currentDetailId,
    customerSelectorVisible,
    selectedOrgText,

    // 图标
    RefreshOutlineIcon,
    AddOutlineIcon,
    SearchOutlineIcon,

    // 方法
    loadDictData,
    refreshData,
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    showAddDialog,
    handleEdit,
    handleView,
    showCustomerSelector,
    handleCustomerSelected,
    handleSave,
    handleSelectionChange,
    handleFilterOrgSelect,
    handleFilterOrgCancel,
    clearOrgSelection,
    removeOrg,
    showSellerSelector,
    handleSellerSelect,
    handleCustomerTypeChange,
    handlePageChange,
    handleImportSuccess,
    handleImportError,
    getDictItemInfo,
  };
}
