/* 车险管理页面样式 */

.vehicle-insurance-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-orgs-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 表格容器样式 - 优化虚拟滚动 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 2px 10px;
  background-color: #fafafa;
  min-height: 40px;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* 虚拟滚动表格样式优化 */
:deep(.n-data-table) {
  width: 100% !important;
  min-width: 100%;

  /* 表头样式 */
  .n-data-table-base-table-header {
    .n-data-table-th {
      font-weight: 600;
      text-align: center !important;

      .n-data-table-th__title {
        justify-content: center;
      }
    }
  }

  /* 复选框列对齐样式 */
  .n-data-table-th--selection,
  .n-data-table-td--selection {
    text-align: center !important;
    vertical-align: middle !important;

    .n-checkbox {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      width: 100% !important;
      height: 100% !important;
    }
  }

  /* 虚拟滚动容器样式 */
  .n-data-table-base-table-body {
    overflow: auto;
  }

  /* 确保虚拟滚动表格不出现额外的空白列 */
  .n-data-table-base-table {
    width: 100% !important;
    min-width: 100%;
    table-layout: auto;
    /* 改为auto以支持自适应宽度 */
    border-collapse: collapse;
  }

  /* 虚拟滚动行样式 */
  .n-data-table-tr {
    height: 48px;
    width: 100%;
  }

  /* 数据单元格样式 */
  .n-data-table-td {
    text-align: center !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 修复虚拟滚动表格列之间的空白区域问题 */
  .n-data-table-base-table-header,
  .n-data-table-base-table-body {

    .n-data-table-th,
    .n-data-table-td {
      border-right: 1px solid var(--n-border-color);
      box-sizing: border-box;

      &:last-child {
        border-right: none;
      }
    }
  }

  /* 确保固定列的边框连续性 */
  .n-data-table-th--fixed-left,
  .n-data-table-td--fixed-left {
    border-right: 1px solid var(--n-border-color) !important;
  }

  /* 修复空白列问题 */
  .n-data-table-base-table {

    .n-data-table-th,
    .n-data-table-td {
      &:not([data-col-key]) {
        display: none !important;
      }
    }
  }

  /* 确保表格内容区域填满容器 */
  .n-data-table-wrapper {
    width: 100% !important;
    min-width: 100%;
  }

  /* 修复表格右侧空白问题 */
  .n-data-table-base-table-body-wrapper {
    width: 100% !important;
    min-width: 100%;
    overflow-x: auto;
  }

  /* 表格容器自适应宽度 */
  .n-data-table-container {
    width: 100% !important;
    min-width: 100%;
  }
}

/* 编辑状态下的输入框样式 */
:deep(.n-input-number) {
  .n-input-number-input {
    text-align: center;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-options {
    flex-wrap: wrap;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-label {
    width: auto;
    line-height: 24px;
  }
}