import { ref, reactive, computed, h } from 'vue'
import messages from '@/utils/messages'
import outboundApi from '@/api/outbound'

import { NIcon, NTag } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  CreateOutline,
  ExitOutline,
  CopyOutline
} from '@vicons/ionicons5'
import { Building } from '@vicons/tabler'
import SearchIcon from '@/components/icons/SearchIcon.vue'
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate
} from '@/utils/dateRange'
import OrderEditModal from '@/components/orders/OrderEditModal.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'
import VehicleStocksSelector from '@/components/inventory/VehicleStocksSelector.vue'
import OutboundDetailModal from '@/components/inventory/OutboundDetailModal.vue'

export default function useStockOutboundBillPage() {
  // 状态变量
  const tableRef = ref(null)
  const orderEditModalRef = ref(null)
  const loading = ref(false)
  const dialogVisible = ref(false)
  const dialogTitle = ref('新增出库单')
  const isEdit = ref(false)

  // 库存选择器相关
  const vehicleSelectorVisible = ref(false)
  const currentOutboundId = ref(null)
  const currentOutboundVin = ref('')
  const currentOutboundSkuId = ref('')
  const currentOrderId = ref(null)

  // 出库单弹窗相关
  const outboundBillVisible = ref(false)

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailData = ref(null)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于虚拟滚动（无页面滚动模式）
  const tableMaxHeight = computed(() => {
    // 根据屏幕尺寸动态调整各组件高度
    const screenHeight = windowHeight.value

    // 基础组件高度
    const pagepadding = 32 // 页面上下padding
    let filterHeight, toolbarHeight, paginationHeight, margin

    // 根据屏幕高度动态调整
    if (screenHeight >= 1080) {
      // 大屏幕 (24寸等)
      filterHeight = 180
      toolbarHeight = 70
      paginationHeight = 60
      margin = 20
      return 800
    } else if (screenHeight >= 768) {
      // 中等屏幕
      filterHeight = 160
      toolbarHeight = 60
      paginationHeight = 50
      margin = 15
      return 360
    } else {
      // 小屏幕
      filterHeight = 140
      toolbarHeight = 50
      paginationHeight = 40
      margin = 10
    }

    // 计算表格容器的可用高度（不包括分页组件）
    const containerHeight =
      screenHeight -
      pagepadding -
      filterHeight -
      toolbarHeight -
      margin

    // 表格本身的高度需要减去分页组件的高度
    const tableHeight = containerHeight - paginationHeight

    // 动态最小高度和最大高度
    const minHeight = Math.min(250, screenHeight * 0.25)
    const maxHeight = screenHeight * 0.6 // 表格最大不超过屏幕高度的60%

    const finalHeight = Math.max(Math.min(tableHeight, maxHeight), minHeight)

    return 800
  })

  // 出库单状态选项
  const orderStatusOptions = [
    { label: '不限', value: null },
    { label: '待出库', value: 'PENDING' },
    { label: '已出库', value: 'COMPLETED' }
  ]

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组以支持多选
    minAmount: null,
    maxAmount: null,
    keywords: '',
    orderStatus: 'PENDING' // 默认选中"待出库"状态
  })

  // 数据列表
  const ordersData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false
  })

  // 窗口大小变化处理
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  // 表格数据
  const filteredData = computed(() => {
    return ordersData.value
  })

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`
      }
    }
    return "选择出库单位"
  })

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch((err) => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  // 表格列配置
  const columns = [
    {
      title: '订单编号',
      key: 'orderSn',
      width: 250,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              cursor: 'pointer',
              transition: 'color 0.2s'
            },
            onClick: () => copyToClipboard(row.orderSn),
            title: '点击复制订单编号',
            onMouseenter: (e) => {
              e.target.style.color = 'var(--primary-color)'
            },
            onMouseleave: (e) => {
              e.target.style.color = ''
            }
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.orderSn),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '出库状态',
      key: 'outboundStatus',
      render(row) {
        const statusMap = {
          'PENDING': { text: '待出库', type: 'warning' },
          'COMPLETED': { text: '已出库', type: 'success' }
        }
        const status = statusMap[row.outboundStatus] || {
          text: row.outboundStatus || '未知',
          type: 'default'
        }
        return h(
          NTag,
          {
            type: status.type,
            size: 'small',
            round: true,
            style: {
              padding: '0 10px',
              fontWeight: 'bold'
            }
          },
          { default: () => status.text }
        )
      }
    },
    {
      title: '经办人',
      key: 'outboundAgentName',
      render(row) {
        return row.outboundAgentName || '-'
      }
    },
    {
      title: '销售单位',
      key: 'salesOrgName'
    },
    {
      title: '销售顾问',
      key: 'salesAgentName'
    },
    {
      title: '客户名称',
      key: 'customerName',
      ellipsis: {
        tooltip: true,
      }
    },
    {
      title: '联系电话',
      key: 'mobile',
      render(row) {
        // 手机号脱敏处理：隐藏中间四位，以****代替
        if (!row.mobile) return ''
        if (row.mobile.length === 11) {
          return row.mobile.substring(0, 3) + '****' + row.mobile.substring(7)
        }
        return row.mobile
      }
    },
    {
      title: '品牌',
      key: 'brand'
    },
    {
      title: '车系',
      key: 'series',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '配置',
      key: 'configName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '颜色',
      key: 'colorCode',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: 'VIN',
      key: 'vin',
      width: 200,
      fixed: 'right',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        if (!row.vin) return '待绑定'
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              cursor: 'pointer',
              transition: 'color 0.2s'
            },
            onClick: () => copyToClipboard(row.vin),
            title: '点击复制VIN',
            onMouseenter: (e) => {
              e.target.style.color = 'var(--primary-color)'
            },
            onMouseleave: (e) => {
              e.target.style.color = ''
            }
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.vin),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 130,
      fixed: 'right',
      align: 'center',
      render: (row) => {
        // 只有待出库状态的订单才显示出库按钮
        if (row.outboundStatus === 'PENDING') {
          return h('div', { style: { display: 'flex', justifyContent: 'center' } }, [
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#f0a020',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleOutbound(row.id),
                title: '出库'
              },
              [h(NIcon, { size: 20 }, { default: () => h(ExitOutline) })]
            )
          ])
        } else if (row.outboundStatus === 'COMPLETED') {
          // 已出库状态显示详情图标
          return h('div', { style: { display: 'flex', justifyContent: 'center' } }, [
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleViewDetail(row),
                title: '查看详情'
              },
              [h(SearchIcon, { size: 20, color: '#18a058' })]
            )
          ])
        } else {
          // 其他状态不显示操作按钮
          return h('span', { style: { color: '#999' } }, '-')
        }
      }
    }
  ]

  // 表格横向滚动宽度 - 参考官方demo设置
  const scrollX = computed(() => {
    // 按照官方demo的方式，设置一个合适的滚动宽度
    // 确保能容纳所有列的内容
    return 1800
  })

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.brand = filterForm.vehicleCategory
      }

      // 处理出库单位 - 支持多选，使用机构代码的逗号分隔格式
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        // 使用机构id作为机构代码，以逗号分隔的格式传入
        params.outbound_org_code = filterForm.invoiceOrgs
          .map((org) => org.id)
          .join(",")
      }

      // 处理出库单状态
      if (filterForm.orderStatus) {
        params.outboundStatus = filterForm.orderStatus
      }

      // 调用API获取数据
      const response = await outboundApi.getOutboundList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        ordersData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages
          refreshData()
          return
        }
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 处理编辑
  const handleEdit = async (id) => {
    try {
      loading.value = true

      // 调用API获取详细数据
      const response = await outboundApi.getOutboundDetail(id)

      if (response.code === 200) {
        const apiData = response.data

        isEdit.value = true
        dialogTitle.value = '编辑出库单'

        // 设置表单数据
        if (orderEditModalRef.value) {
          orderEditModalRef.value.setFormData(apiData)
        }

        dialogVisible.value = true
      } else {
        messages.error(response.message || '获取出库单数据失败')
      }
    } catch (error) {
      console.error('获取出库单数据失败:', error)
      messages.error('获取出库单数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理查看详情
  const handleViewDetail = (row) => {
    currentDetailData.value = row
    detailDialogVisible.value = true
  }

  // 处理保存成功
  const handleSaveSuccess = () => {
    refreshData() // 刷新数据列表
  }

  // 处理选择变化
  const handleSelectionChange = () => {
    // 这里可以处理多选逻辑，当前页面暂时不需要
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理页面大小变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 处理出库操作
  const handleOutbound = async (id) => {
    try {
      loading.value = true

      // 保存当前操作的出库单ID
      currentOutboundId.value = id

      // 查找当前行数据，获取orderId
      const currentRow = ordersData.value.find(item => item.id === id)
      if (currentRow && currentRow.orderId) {
        // 如果在行数据中找到orderId，则保存它
        currentOrderId.value = currentRow.orderId
        console.log('从行数据中获取到orderId:', currentOrderId.value)
      } else {
        // 如果在行数据中没有找到orderId，则设置为null
        currentOrderId.value = null
        console.log('行数据中没有orderId字段')
      }

      // 获取出库单详情
      const response = await outboundApi.getOutboundDetail(id)

      if (response.code === 200) {
        const outboundData = response.data

        // 如果详情数据中有orderId且当前还没有orderId，则保存它
        if (outboundData.orderId && !currentOrderId.value) {
          currentOrderId.value = outboundData.orderId
          console.log('从详情数据中获取到orderId:', currentOrderId.value)
        }

        // 保存VIN和skuId，确保转换为字符串类型
        currentOutboundVin.value = outboundData.vin || ''
        currentOutboundSkuId.value = outboundData.skuId ? String(outboundData.skuId) : ''

        // 显示出库单组件
        outboundBillVisible.value = true
      } else {
        messages.error(response.message || '获取出库单详情失败')
      }
    } catch (error) {
      console.error('获取出库单详情失败:', error)
      messages.error('获取出库单详情失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理选择器取消
  const handleSelectorCancel = () => {
    // 重置状态
    currentOutboundId.value = null
    currentOutboundVin.value = ''
    currentOutboundSkuId.value = ''
    currentOrderId.value = null // 同时重置orderId
  }

  // 处理车辆选择
  const handleVehicleSelected = async (vehicle) => {
    if (!currentOutboundId.value) {
      messages.error('出库单ID无效')
      return
    }

    try {
      loading.value = true

      // 构建出库数据
      const outboundData = {
        id: currentOutboundId.value,
        vin: vehicle.vin || '',
        outboundStatus: 'COMPLETED', // 更新状态为已出库
        outboundDate: Date.now() // 设置出库日期为当前时间
      }

      // 如果有orderId，则添加到请求数据中
      if (currentOrderId.value) {
        outboundData.orderId = currentOrderId.value
        console.log('添加orderId到出库请求:', currentOrderId.value)
      } else {
        console.log('没有orderId可添加到出库请求')
      }

      // 调用API更新出库单
      const response = await outboundApi.updateOutbound(currentOutboundId.value, outboundData)

      if (response.code === 200) {
        messages.success('出库成功')
        // 刷新数据列表
        refreshData()
      } else {
        messages.error(response.message)
      }
    } catch (error) {
      console.error('出库失败:', error)
    } finally {
      loading.value = false
      // 重置状态
      currentOutboundId.value = null
      currentOrderId.value = null // 同时重置orderId
    }
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs]
      handleSearch()
    }
  }

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = []
    handleSearch()
  }

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.invoiceOrgs = filterForm.invoiceOrgs.filter(
      (org) => org.id !== orgToRemove.id
    )
    handleSearch()
  }

  // 处理出库单保存
  const handleOutboundBillSave = () => {
    outboundBillVisible.value = false
    refreshData() // 刷新数据列表
  }

  // 生命周期方法
  const initialize = async () => {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)
    // 初始化数据
    refreshData()
  }

  const cleanup = () => {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 组件引用
    OrderEditModal,
    BizOrgSelector,
    VehicleStocksSelector,
    OutboundDetailModal,
    SearchIcon,

    // 图标
    SearchOutline,
    RefreshOutline,
    CreateOutline,
    ExitOutline,
    Building,

    // 响应式数据
    tableRef,
    orderEditModalRef,
    loading,
    dialogVisible,
    dialogTitle,
    isEdit,
    vehicleSelectorVisible,
    outboundBillVisible,
    currentOutboundId,
    currentOutboundVin,
    currentOutboundSkuId,
    currentOrderId,
    orderStatusOptions,
    filterForm,
    ordersData,
    pagination,
    windowHeight,
    detailDialogVisible,
    currentDetailData,
    showOrgSelector,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    scrollX,
    selectedOrgText,

    // 日期相关
    dateRangeOptions,

    // 业务方法
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    handleEdit,
    handleViewDetail,
    handleSaveSuccess,
    handleSelectionChange,
    handlePageChange,
    handlePageSizeChange,
    handleOutbound,
    handleVehicleSelected,
    handleSelectorCancel,
    handleOutboundBillSave,
    refreshData,

    // 机构选择器相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 生命周期方法
    initialize,
    cleanup
  }
}
