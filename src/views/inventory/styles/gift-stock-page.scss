/* 赠品库存页面样式 */

.gift-stock-page-new {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;

  // 新行高亮样式
  :deep(.new-row-highlight) {
    background-color: rgba(0, 128, 0, 0.1) !important;
    /* 浅绿色背景 */
    transition: background-color 0.5s ease;
    /* 平滑过渡效果 */

    &:hover {
      background-color: rgba(0, 128, 0, 0.15) !important;
    }
  }
}

// 标签样式
:deep(.n-tag) {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;

  &.n-tag--success {
    background-color: rgba(0, 128, 0, 0.1);
    color: #18a058;
  }

  &.n-tag--info {
    background-color: rgba(0, 122, 255, 0.1);
    color: #2080f0;
  }
}

// 输入框样式
:deep(.n-input) {
  .n-input__input {
    font-size: 14px;
  }

  .n-input__count {
    font-size: 12px;
  }
}

// 数字输入框样式
:deep(.n-input-number) {
  width: 100%;

  .n-input-number-suffix {
    color: #666;
  }
}

// 选择器样式
:deep(.n-select) {
  width: 100%;
}