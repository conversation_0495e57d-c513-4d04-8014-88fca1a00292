<template>
  <div class="gift-stock-page-new">
    <biz-org-list-layout
      ref="layoutRef"
      title="赠品库存"
      search-placeholder="请输入赠品名称或规格"
      :columns="columns"
      :api-service="giftStockApi"
      org-id-field="stockOrgId"
      row-key="id"
      :default-new-row="defaultNewRow"
      @select-org="handleOrgSelect"
      @add-data="handleAddData"
      @edit-data="handleEditData"
      @save-data="handleSaveData"
      @cancel-edit="handleCancelEdit"
      @delete-data="handleDeleteData"
      @delete-new-row="handleDeleteNewRow"
    />

    <!-- 赠品出库记录弹窗 -->
    <GiftOutboundRecordsModal
      v-model:visible="outboundRecordsVisible"
      :gift-info="currentGiftInfo"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import BizOrgListLayout from "@/components/layout/BizOrgListLayout.vue";
import GiftOutboundRecordsModal from "@/components/inventory/GiftOutboundRecordsModal.vue";
import { useGiftStockPageNew } from "./GiftStockPageNew.js";

// 定义emit函数
const emit = defineEmits([
  "select-org",
  "add-data",
  "edit-data",
  "save-data",
  "cancel-edit",
  "delete-data",
  "refresh-data",
  "delete-new-row",
]);

// 布局组件引用
const layoutRef = ref(null);

// 使用组合式函数获取所有需要的数据和方法
const {
  giftStockApi,
  defaultNewRow,
  createColumns,
  handleOrgSelect,
  handleAddData,
  handleEditData,
  handleCancelEdit: handleCancelEditLogic,
  handleSaveData,
  handleDeleteData,
  // 出库记录弹窗相关
  outboundRecordsVisible,
  currentGiftInfo,
} = useGiftStockPageNew();

// 创建表格列定义，传入事件处理函数
const columns = createColumns({
  "cancel-edit": handleCancelEdit,
  "delete-new-row": handleDeleteNewRow,
  "edit-data": (row) => {
    emit("edit-data", row);
  },
  "save-data": async (row) => {
    // 通知 BizOrgListLayout 清理编辑状态并刷新数据
    if (layoutRef.value) {
      // 清理指定行的编辑状态
      layoutRef.value.clearEditingState(row.id);
      // 刷新数据列表以获取最新数据（包括新的真实ID）
      await layoutRef.value.refreshData();
    }
    emit("save-data", row);
  },
});

// 处理取消编辑事件
function handleCancelEdit(row) {
  // 创建一个简单的 emit 对象
  const emitObj = (eventName, data) => {
    if (eventName === "delete-new-row") {
      handleDeleteNewRow(data);
    }
  };

  // 调用业务逻辑处理取消编辑
  handleCancelEditLogic(row, emitObj);
  emit("cancel-edit", row);
}

// 处理删除新行事件
function handleDeleteNewRow(row) {
  if (layoutRef.value) {
    // 调用布局组件的删除方法
    layoutRef.value.removeRowFromList(row);
  }
  emit("delete-new-row", row);
}
</script>

<style lang="scss">
@use "./styles/gift-stock-page";
</style>
