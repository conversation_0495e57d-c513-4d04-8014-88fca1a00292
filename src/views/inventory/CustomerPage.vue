<template>
  <div class="customer-page">
    <!-- 筛选条件区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">创建日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">客户类型</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.customerType"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in customerCategoryOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">成交状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dealStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dealStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">归属单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="filterForm.showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon>
                  <component :is="BuildingIcon" />
                </n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <div
              v-if="filterForm.ownerOrgs && filterForm.ownerOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.ownerOrgs"
                :key="org.id"
                :bordered="false"
                type="success"
                size="small"
                closable
                @close="() => removeOrg(org)"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon>
              <component :is="RefreshOutlineIcon" />
            </n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon>
              <component :is="AddOutlineIcon" />
            </n-icon>
          </template>
          新增客户
        </n-button>

        <n-button type="info" @click="showCustomerSelector" round>
          <template #icon>
            <n-icon>
              <component :is="SearchOutlineIcon" />
            </n-icon>
          </template>
          选择客户
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/customer-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入客户名称或联系电话"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon>
              <component :is="SearchOutlineIcon" />
            </n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row) => row.id"
      @update:checked-row-keys="handleSelectionChange"
      @update:page="handlePageChange"
    />

    <!-- 新增/编辑弹窗 -->
    <customer-edit-modal
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :form-data="form"
      :customer-type-options="customerCategoryOptions"
      :deal-status-options="dealStatusOptions"
      :rules="rules"
      @save="handleSave"
      @cancel="dialogVisible = false"
      @customer-type-change="handleCustomerTypeChange"
      @show-seller-selector="showSellerSelector"
    />

    <!-- 详情弹窗 -->
    <customer-detail-modal
      :visible="detailDialogVisible"
      @update:visible="(val) => (detailDialogVisible = val)"
      :id="currentDetailId"
    />

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelected"
    />

    <!-- 筛选条件的业务机构选择器 -->
    <biz-org-selector
      v-model:visible="filterForm.showOrgSelector"
      title="选择归属单位"
      business-permission="can_sell"
      :multiple="true"
      @select="handleFilterOrgSelect"
      @cancel="handleFilterOrgCancel"
    />

    <!-- 销售顾问选择器 -->
    <biz-org-member-selector
      v-model:visible="sellerSelectorVisible"
      mode="single"
      title="选择销售顾问"
      @select="handleSellerSelect"
      @cancel="() => (sellerSelectorVisible = false)"
    />
  </div>
</template>

<script setup>
import { onMounted, markRaw } from "vue";
import { dateRangeOptions } from "@/utils/dateRange";
import { useCustomerPage } from "./CustomerPage.js";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import BizOrgMemberSelector from "@/components/bizOrg/BizOrgMemberSelector.vue";
import CustomerEditModal from "@/components/customer/CustomerEditModal.vue";
import CustomerDetailModal from "@/components/customer/CustomerDetailModal.vue";
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import FileUploadButton from "@/components/FileUploadButton.vue";
import { Building } from "@vicons/tabler";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const BuildingIcon = markRaw(Building);

// 使用组合式函数
const {
  // 状态
  tableRef,
  loading,
  dialogVisible,
  dialogTitle,
  isEdit,
  selectedRows,
  customerCategoryOptions,
  dealStatusOptions,
  filterForm,
  form,
  sellerSelectorVisible,
  rules,
  customerData,
  pagination,
  columns,
  filteredData,
  detailDialogVisible,
  currentDetailId,
  customerSelectorVisible,
  selectedOrgText,

  // 图标
  RefreshOutlineIcon,
  AddOutlineIcon,
  SearchOutlineIcon,

  // 方法
  loadDictData,
  refreshData,
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  showAddDialog,
  handleEdit,
  handleView,
  showCustomerSelector,
  handleCustomerSelected,
  handleSave,
  handleSelectionChange,
  handleFilterOrgSelect,
  handleFilterOrgCancel,
  clearOrgSelection,
  removeOrg,
  showSellerSelector,
  handleSellerSelect,
  handleCustomerTypeChange,
  handlePageChange,
  handleImportSuccess,
  handleImportError,
} = useCustomerPage();

// 初始化
onMounted(() => {
  loadDictData();
  refreshData();
});
</script>

<style src="./CustomerPage.scss" scoped></style>