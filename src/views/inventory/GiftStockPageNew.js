import { h, ref, onMounted, markRaw } from 'vue'
import { NTag, NInputNumber, NInput, NSelect, NSpace, NButton, NIcon } from 'naive-ui'
import { giftStockApi } from '@/api/giftStock'
import { formatMoney } from '@/utils/money'
import { getDictOptions } from '@/api/dict'
import messages from '@/utils/messages'
import { CheckmarkCircleOutline, CloseCircleOutline, SearchOutline } from '@vicons/ionicons5'
import { Edit } from '@vicons/carbon'

export function useGiftStockPageNew() {
  // 出库记录弹窗状态
  const outboundRecordsVisible = ref(false)
  const currentGiftInfo = ref(null)

  // 赠品类型选项 - 初始默认值，将在组件挂载时从字典中获取
  const categoryOptions = ref([
    { label: '商品', value: 'GOODS' },
    { label: '服务', value: 'SERVICES' }
  ])

  // 字典映射表
  const categoryMap = ref({
    'GOODS': '商品',
    'SERVICES': '服务'
  })

  // 字典数据加载状态
  const isDictLoaded = ref(false)

  // 单位选项
  const unitOptions = [
    { label: '个', value: '个' },
    { label: '套', value: '套' },
    { label: '份', value: '份' }
  ]

  // 默认新行数据
  const defaultNewRow = {
    name: '',
    category: 'GOODS', // 默认类型为商品
    spec: '',
    unit: '个', // 默认单位为个
    price: 0, // 单价（分）
    quantity: 0, // 库存数量
    amount: 0, // 总金额（分）
    editorName: JSON.parse(localStorage.getItem('user') || '{}').nickname || '当前用户',
    createTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
  }

  // 格式化金额显示（分转元，添加千分位分隔符）
  const formatAmount = (amount) => {
    // 将分转换为元并格式化
    return formatMoney(amount ? amount / 100 : 0)
  }

  // 比较数据是否有变化
  function hasDataChanged(originalData, currentData) {
    // 比较关键字段是否有变化
    return originalData.name !== currentData.name ||
      originalData.category !== currentData.category ||
      originalData.spec !== currentData.spec ||
      originalData.unit !== currentData.unit ||
      originalData.price !== currentData.price ||
      originalData.quantity !== currentData.quantity ||
      originalData.amount !== currentData.amount
  }

  // 事件处理函数
  function handleOrgSelect(orgId) {
    console.log('Selected organization:', orgId)
  }

  // 移除本地编辑状态控制，使用 BizOrgListLayout 的统一控制

  function handleAddData(newRow) {
    newRow.isEditing = true
    // 编辑状态现在由 BizOrgListLayout 统一管理
  }

  function handleEditData(row) {
    console.log('Editing row:', row)

    // 编辑状态检查现在由 BizOrgListLayout 统一管理

    // 保存原始数据，用于比较是否有变化
    localStorage.setItem(`original_gift_row_${row.id}`, JSON.stringify({
      name: row.name,
      category: row.category,
      spec: row.spec,
      unit: row.unit,
      price: row.price,
      quantity: row.quantity,
      amount: row.amount
    }))
    // 标记为编辑状态，用于列渲染
    row.isEditing = true
  }

  function handleCancelEdit(row, emit) {
    // 移除编辑状态标记
    row.isEditing = false
    // 编辑状态现在由 BizOrgListLayout 统一管理

    // 如果是新行，需要从列表中删除
    if (row.isNew || (typeof row.id === 'string' && row.id.startsWith('temp_'))) {
      // 通知父组件删除该行
      if (emit) {
        emit('delete-new-row', row)
      }
    } else {
      // 恢复原始数据
      const originalData = localStorage.getItem(`original_gift_row_${row.id}`)
      if (originalData) {
        const original = JSON.parse(originalData)
        Object.assign(row, original)
      }
    }

    // 清除本地存储的原始数据
    if (row.id) {
      localStorage.removeItem(`original_gift_row_${row.id}`);
    }
  }

  function handleDeleteData(row) {
    console.log('Deleted row:', row)
  }

  // 查询出入库记录
  function handleViewRecords(row) {
    console.log('查询出库记录:', row)
    // 设置当前赠品信息并打开弹窗
    currentGiftInfo.value = row
    outboundRecordsVisible.value = true
  }

  // 获取字典数据
  async function fetchDictOptions() {
    try {
      const res = await getDictOptions('gift_category')
      if (res && res.code === 200 && res.data && res.data.length > 0) {
        // 转换字典数据为选择器选项格式
        categoryOptions.value = res.data.map(item => ({
          label: item.optionLabel,
          value: item.optionValue
        }))

        // 构建映射表，方便快速查找
        const map = {}
        res.data.forEach(item => {
          map[item.optionValue] = item.optionLabel
        })
        categoryMap.value = map
        isDictLoaded.value = true

        console.log('字典数据加载成功:', {
          options: categoryOptions.value,
          map: categoryMap.value
        })
      } else {
        console.warn('字典数据为空，使用默认值')
        setDefaultCategoryOptions()
      }
    } catch (error) {
      console.error('获取字典数据失败:', error)
      setDefaultCategoryOptions()
    }
  }

  // 设置默认的赠品类型选项
  function setDefaultCategoryOptions() {
    categoryOptions.value = [
      { label: '商品', value: 'GOODS' },
      { label: '服务', value: 'SERVICES' }
    ]
    categoryMap.value = {
      'GOODS': '商品',
      'SERVICES': '服务'
    }
    isDictLoaded.value = true
  }

  async function handleSaveData(row) {
    console.log('Saving row:', row)

    // 验证必填字段
    if (!row.name) {
      messages.error('赠品名称不能为空')
      return false
    }

    // 验证字段长度
    if (row.name.length > 50) {
      messages.error('赠品名称不能超过50个字符')
      return false
    }

    if (row.spec && row.spec.length > 50) {
      messages.error('规格型号不能超过50个字符')
      return false
    }

    try {
      // 检查是否是临时ID（以temp_开头）
      const isTemporaryId = typeof row.id === 'string' && row.id.startsWith('temp_')
      let response = null

      if (row.isNew || isTemporaryId) {
        // 创建新行数据的副本，移除id字段
        const newRowData = { ...row }

        // 删除id字段，避免在新增时传入id
        if (newRowData.id) {
          delete newRowData.id
        }

        // 调用API创建赠品库存
        response = await giftStockApi.createGiftStock(newRowData)

        if (response && response.code === 200) {
          messages.success('赠品库存创建成功')
          // 数据刷新将由 BizOrgListLayout 处理
        } else {
          throw new Error(response?.message || '创建失败')
        }
      } else {
        // 检查数据是否有变化
        const originalData = JSON.parse(localStorage.getItem(`original_gift_row_${row.id}`));

        // 如果没有原始数据或数据有变化，才发送更新请求
        if (!originalData || hasDataChanged(originalData, row)) {
          // 更新现有赠品库存
          response = await giftStockApi.updateGiftStock(row)

          if (response && response.code === 200) {
            messages.success('赠品库存更新成功')
          } else {
            throw new Error(response?.message || '更新失败')
          }
        } else {

          // 数据未变化，直接返回成功
          messages.info('数据未变化')
        }

        // 清除本地存储的原始数据
        localStorage.removeItem(`original_gift_row_${row.id}`);
      }

      // 移除编辑状态标记
      row.isEditing = false
      // 编辑状态现在由 BizOrgListLayout 统一管理
      return true
    } catch (error) {
      console.error('Failed to save gift stock:', error)
      messages.error(error.message || '保存失败')
      return false
    }
  }

  // 创建表格列定义的函数
  function createColumns(eventHandlers) {
    return markRaw([
      {
        title: '赠品名称',
        key: 'name',
        width: 200,
        fixed: 'left', // 将赠品名称设为冻结首列
        ellipsis: {
          tooltip: true
        },
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInput, {
              value: row.name,
              maxlength: 50,
              showCount: true,
              onUpdateValue(v) {
                row.name = v
              }
            })
          }

          return h('span', row.name)
        }
      },
      {
        title: '赠品类型',
        key: 'category',
        width: 100,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            // 处理不同格式的category值，确保选择器能正确显示当前值
            let currentValue = row.category

            // 等待字典数据加载完成后再处理值的转换
            if (isDictLoaded.value) {
              // 如果当前值在字典选项中找不到，尝试根据显示值反向查找
              if (currentValue && !categoryOptions.value.find(opt => opt.value === currentValue)) {
                // 尝试根据显示值查找对应的value
                const foundOption = categoryOptions.value.find(opt => opt.label === currentValue)
                if (foundOption) {
                  currentValue = foundOption.value
                  row.category = currentValue // 更新行数据为正确的value
                }
              }
            }

            return h(NSelect, {
              value: currentValue,
              options: categoryOptions.value,
              placeholder: isDictLoaded.value ? '请选择赠品类型' : '加载中...',
              disabled: !isDictLoaded.value,
              onUpdateValue(v) {
                row.category = v
              }
            })
          }

          // 从字典映射表中获取显示值
          let displayValue = categoryMap.value[row.category]

          // 如果在字典中找不到，使用备用逻辑
          if (!displayValue) {
            // 检查是否已经是显示值
            if (categoryOptions.value.find(opt => opt.label === row.category)) {
              displayValue = row.category
            } else {
              // 使用默认逻辑判断
              const isGoods = row.category === 'GOODS' ||
                row.category === 'goods' ||
                row.category === '商品' ||
                (row.category && row.category.toLowerCase() === 'goods')
              displayValue = isGoods ? '商品' : '服务'
            }
          }

          // 根据类型设置标签颜色
          const isGoods = displayValue === '商品' ||
            row.category === 'GOODS' ||
            row.category.toLowerCase() === 'goods'

          return h(NTag, { type: isGoods ? 'success' : 'info' }, {
            default: () => displayValue
          })
        }
      },
      {
        title: '规格型号',
        key: 'spec',
        width: 150,
        ellipsis: {
          tooltip: true
        },
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInput, {
              value: row.spec,
              maxlength: 50,
              showCount: true,
              onUpdateValue(v) {
                row.spec = v
              }
            })
          }

          return h('span', row.spec)
        }
      },
      {
        title: '单位',
        key: 'unit',
        width: 80,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NSelect, {
              value: row.unit,
              options: unitOptions,
              onUpdateValue(v) {
                row.unit = v
              }
            })
          }

          return h('span', row.unit)
        }
      },
      {
        title: '库存数量',
        key: 'quantity',
        width: 100,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInputNumber, {
              value: row.quantity || 0,
              min: 0,
              precision: 0, // 整数
              step: 1,
              buttonPlacement: 'both',
              onUpdateValue(v) {
                row.quantity = v || 0
                // 更新总金额
                row.amount = (v || 0) * (row.price || 0)
              }
            })
          }

          return h('span', row.quantity)
        }
      },
      {
        title: '单价(元)',
        key: 'price',
        width: 120,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInputNumber, {
              value: (row.price || 0) / 100, // 分转元
              min: 0,
              precision: 2, // 保留2位小数
              step: 1,
              buttonPlacement: 'both',
              onUpdateValue(v) {
                // 元转分，并保存
                const priceInCents = Math.round((v || 0) * 100)
                row.price = priceInCents
                // 更新总金额
                row.amount = (row.quantity || 0) * priceInCents
              }
            })
          }

          // 显示为千分位格式
          return h('span', formatAmount(row.price || 0))
        }
      },
      {
        title: '总价值(元)',
        key: 'amount',
        width: 160,
        render(row) {
          // 显示为千分位格式
          return h('span', formatAmount(row.amount || 0))
        }
      },
      {
        title: '经办人',
        key: 'editorName',
        width: 100
      },
      {
        title: '操作',
        key: 'actions',
        width: 120,
        align: 'center',
        fixed: 'right',
        render: (row) => {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NSpace, { align: 'center' }, {
              default: () => [
                h(NButton, {
                  quaternary: true,
                  circle: true,
                  size: 'small',
                  type: 'success',
                  onClick: async () => {
                    const result = await handleSaveData(row);
                    if (result && eventHandlers['save-data']) {
                      eventHandlers['save-data'](row);
                    }
                  },
                  style: 'color: #18a058; font-size: 18px;'
                }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
                h(NButton, {
                  quaternary: true,
                  circle: true,
                  size: 'small',
                  type: 'error',
                  onClick: () => {
                    // 直接调用 Vue 组件的取消编辑处理函数
                    if (eventHandlers['cancel-edit']) {
                      eventHandlers['cancel-edit'](row);
                    }
                  },
                  style: 'color: #d03050; font-size: 18px;'
                }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
              ]
            })
          }

          return h(NSpace, { align: 'center' }, {
            default: () => [
              h(NButton, {
                quaternary: true,
                circle: true,
                size: 'small',
                // 编辑按钮的禁用状态现在由 BizOrgListLayout 统一管理
                onClick: () => {
                  handleEditData(row);
                  if (eventHandlers['edit-data']) {
                    eventHandlers['edit-data'](row);
                  }
                }
              }, { default: () => h(NIcon, { component: Edit }) }),
              h(NButton, {
                quaternary: true,
                circle: true,
                size: 'small',
                type: 'info',
                onClick: () => {
                  handleViewRecords(row);
                },
                style: 'color: #2080f0;'
              }, { default: () => h(NIcon, { component: SearchOutline }) })
              // 不显示删除按钮
            ]
          })
        }
      }
    ])
  }

  // 组件挂载时获取字典数据
  onMounted(async () => {
    // 立即获取字典数据，确保在用户操作前完成加载
    await fetchDictOptions()
  })

  // 页面初始化时就开始加载字典数据
  fetchDictOptions()

  return {
    categoryOptions,
    categoryMap,
    unitOptions,
    defaultNewRow,
    formatAmount,
    hasDataChanged,
    handleOrgSelect,
    handleAddData,
    handleEditData,
    handleCancelEdit,
    handleDeleteData,
    handleSaveData,
    fetchDictOptions,
    createColumns,
    giftStockApi,
    isDictLoaded,
    // 出库记录弹窗相关
    outboundRecordsVisible,
    currentGiftInfo
  }
}
