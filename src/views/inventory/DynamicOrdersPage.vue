<template>
  <div class="dynamic-orders-page">
    <n-card title="📦 动态加载的订单页面">
      <n-space vertical size="large">
        <!-- 页面信息 -->
        <n-alert type="success">
          <template #header>🎉 Hash模式动态路由加载成功！</template>
          <p>这个页面是通过Hash模式动态路由加载的</p>
          <p><strong>访问路径:</strong> <code>/#/inventory/dynamic-orders-page</code></p>
          <p><strong>组件文件:</strong> <code>src/views/inventory/DynamicOrdersPage.vue</code></p>
          <p><strong>加载方式:</strong> 动态路由 + 懒加载</p>
        </n-alert>

        <!-- 功能演示 -->
        <n-grid cols="1 s:2 m:3" responsive="screen" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-card title="📋 订单统计" size="small">
              <n-space vertical>
                <n-statistic label="总订单数" :value="orderStats.total" />
                <n-statistic label="待处理" :value="orderStats.pending" />
                <n-statistic label="已完成" :value="orderStats.completed" />
              </n-space>
            </n-card>
          </n-grid-item>

          <n-grid-item>
            <n-card title="📊 今日数据" size="small">
              <n-space vertical>
                <n-statistic label="新增订单" :value="orderStats.today" />
                <n-statistic label="处理订单" :value="orderStats.processed" />
                <n-statistic label="完成订单" :value="orderStats.finished" />
              </n-space>
            </n-card>
          </n-grid-item>

          <n-grid-item>
            <n-card title="💰 金额统计" size="small">
              <n-space vertical>
                <n-statistic 
                  label="今日金额" 
                  :value="orderStats.todayAmount" 
                  :precision="2"
                  suffix="元"
                />
                <n-statistic 
                  label="本月金额" 
                  :value="orderStats.monthAmount" 
                  :precision="2"
                  suffix="元"
                />
              </n-space>
            </n-card>
          </n-grid-item>
        </n-grid>

        <!-- 订单表格 -->
        <n-card title="📋 订单列表" size="small">
          <template #header-extra>
            <n-space>
              <n-button type="primary" size="small" @click="refreshData">
                刷新数据
              </n-button>
              <n-button type="success" size="small" @click="addOrder">
                新增订单
              </n-button>
            </n-space>
          </template>
          
          <n-data-table
            :columns="columns"
            :data="orderData"
            :pagination="pagination"
            :loading="loading"
            striped
            :row-key="(row) => row.id"
          />
        </n-card>

        <!-- 动态路由测试 -->
        <n-card title="🔗 动态路由测试" size="small">
          <n-space vertical>
            <p>测试Hash模式下的动态路由跳转：</p>
            <n-space>
              <n-button @click="navigateToPage('customer-page')" type="primary">
                客户管理页面
              </n-button>
              <n-button @click="navigateToPage('inventory/stock-page')" type="info">
                库存管理页面
              </n-button>
              <n-button @click="navigateToPage('system/users')" type="warning">
                用户管理页面
              </n-button>
            </n-space>
            <n-space>
              <n-button @click="goBack" type="default">
                返回测试页面
              </n-button>
              <n-button @click="goHome" type="default">
                返回首页
              </n-button>
            </n-space>
          </n-space>
        </n-card>

        <!-- 技术说明 -->
        <n-card title="🛠️ 技术实现" size="small">
          <n-space vertical>
            <h4>Hash模式动态路由的优势：</h4>
            <ul>
              <li>✅ 无需CDN配置，完全避免404错误</li>
              <li>✅ 支持组件懒加载，提升性能</li>
              <li>✅ 支持直接访问URL</li>
              <li>✅ 支持浏览器前进/后退</li>
              <li>✅ 支持页面刷新</li>
            </ul>
            
            <h4>实现原理：</h4>
            <ol>
              <li>使用 <code>import.meta.glob</code> 预扫描所有组件</li>
              <li>根据路径规则动态匹配组件文件</li>
              <li>使用 <code>router.addRoute</code> 动态添加路由</li>
              <li>Hash模式确保所有请求都指向 index.html</li>
            </ol>
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  NCard, NSpace, NAlert, NGrid, NGridItem, NStatistic, 
  NButton, NDataTable 
} from 'naive-ui'
import { dynamicRouteLoader } from '@/router/dynamicRouteLoader.js'

const router = useRouter()

// 订单统计数据
const orderStats = reactive({
  total: 2468,
  pending: 234,
  completed: 2234,
  today: 45,
  processed: 38,
  finished: 32,
  todayAmount: 156780.50,
  monthAmount: 4567890.25
})

// 表格配置
const loading = ref(false)
const pagination = reactive({
  page: 1,
  pageSize: 8,
  showSizePicker: true,
  pageSizes: [8, 16, 32],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`
})

const columns = [
  {
    title: '订单编号',
    key: 'orderNo',
    width: 140
  },
  {
    title: '客户名称',
    key: 'customerName',
    width: 120
  },
  {
    title: '产品名称',
    key: 'productName',
    width: 150
  },
  {
    title: '订单金额',
    key: 'amount',
    width: 100,
    render: (row) => `¥${row.amount.toLocaleString()}`
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row) => {
      const statusMap = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[row.status] || row.status
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 120
  }
]

// 订单数据
const orderData = ref([])

// 生成模拟数据
const generateMockData = () => {
  const statuses = ['pending', 'processing', 'completed', 'cancelled']
  const customers = ['华为技术有限公司', '小米科技', '比亚迪汽车', '腾讯科技', '阿里巴巴集团']
  const products = ['Model Y', 'iPhone 15', '华为Mate60', '小米14', 'MacBook Pro']
  
  const data = []
  for (let i = 1; i <= 50; i++) {
    data.push({
      id: i,
      orderNo: `DO${String(i).padStart(6, '0')}`,
      customerName: customers[Math.floor(Math.random() * customers.length)],
      productName: products[Math.floor(Math.random() * products.length)],
      amount: Math.floor(Math.random() * 500000) + 10000,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
    })
  }
  return data
}

// 刷新数据
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    orderData.value = generateMockData()
    loading.value = false
  }, 800)
}

// 新增订单
const addOrder = () => {
  alert('新增订单功能演示')
}

// 导航到其他页面
const navigateToPage = async (pagePath) => {
  try {
    await dynamicRouteLoader.addDynamicRoute(pagePath)
    router.push(`/${pagePath}`)
  } catch (error) {
    console.error('导航失败:', error)
    alert(`导航到 ${pagePath} 失败，可能组件不存在`)
  }
}

// 返回测试页面
const goBack = () => {
  router.push('/test/dynamic-route')
}

// 返回首页
const goHome = () => {
  router.push('/')
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.dynamic-orders-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

ul, ol {
  margin: 8px 0;
  padding-left: 20px;
}

li {
  margin-bottom: 4px;
}

h4 {
  margin: 16px 0 8px 0;
  color: #333;
}
</style>
