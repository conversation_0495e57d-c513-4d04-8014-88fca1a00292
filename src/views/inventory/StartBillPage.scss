/* 启票页面样式 */

.start-bill-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-separator {
  color: #999;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-orgs-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 表格容器样式 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
}

.data-table-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 15px;
  background-color: #fafafa;
  border-top: 1px solid #e0e0e6;
  min-height: 40px;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* 表格内部滚动样式 */
:deep(.data-table-wrapper .n-data-table) {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 确保flex子项可以收缩 */
}

:deep(.data-table-wrapper .n-data-table-wrapper) {
  flex: 1;
  overflow: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* 使用全局滚动条样式 */
:deep(.data-table-wrapper .n-data-table-base-table-body) {
  overflow: visible !important;
}

/* 表格主体样式 */
:deep(.data-table-wrapper .n-data-table-base-table) {
  overflow: visible;
  height: 100%;
}

/* 冻结表头样式 */
:deep(.data-table-wrapper .n-data-table-thead) {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fafafa;
}

:deep(.data-table-wrapper .n-data-table-thead .n-data-table-th) {
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e6;
  text-align: center !important;
}

:deep(.data-table-wrapper .n-data-table-thead .n-data-table-th .n-data-table-th__title) {
  justify-content: center;
}

/* 确保表格容器正确处理滚动 */

:deep(.data-table-wrapper .n-data-table-base-table-header) {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 优化表格容器高度，减少空白区域 */
:deep(.data-table-wrapper .n-data-table-base-table-body) {
  flex: 1;
  min-height: 0;
}

/* 确保表格内容区域充分利用空间 */
:deep(.data-table-wrapper .n-scrollbar) {
  height: 100% !important;
}

:deep(.data-table-wrapper .n-scrollbar > .n-scrollbar-container) {
  height: 100% !important;
}

/* VIN列等宽字体样式 */
:deep(.n-data-table .n-data-table-td) {
  font-family: inherit;
}

/* 使用全局滚动条样式 */

/* 表头样式 */
:deep(.data-table-wrapper .n-data-table-th) {
  text-align: center !important;
  font-weight: 600;
  background-color: #fafafa;
}

:deep(.data-table-wrapper .n-data-table-th .n-data-table-th__title) {
  justify-content: center;
}

/* VIN列表头居中对齐 */
:deep(.data-table-wrapper .n-data-table-th:nth-child(2) .n-data-table-th__title) {
  justify-content: center !important;
}

/* 数据单元格样式 */
:deep(.data-table-wrapper .n-data-table-td) {
  text-align: center !important;
  padding: 12px 8px !important;
  vertical-align: middle;
}

/* VIN列左对齐 */
:deep(.data-table-wrapper .n-data-table-td:nth-child(2)) {
  text-align: left !important;
}

/* 操作列居中对齐 */
:deep(.data-table-wrapper .n-data-table-td:last-child) {
  text-align: center !important;
}

/* 表格行悬停效果 */
:deep(.n-data-table .n-data-table-tr:hover .n-data-table-td) {
  background-color: rgba(24, 160, 88, 0.05);
}

/* 选中行样式 */
:deep(.n-data-table .n-data-table-tr--checked .n-data-table-td) {
  background-color: rgba(24, 160, 88, 0.1);
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* 响应式样式 */
@media (max-height: 900px) {
  .start-bill-page {
    height: 100vh;
  }
}

@media (max-height: 768px) {
  .start-bill-page {
    height: 100vh;
    padding: 12px;
    gap: 12px;
  }

  .pagination-container {
    min-height: 28px;
  }
}

@media (max-width: 1200px) {
  .pagination-container {
    min-height: 28px;
  }

  :deep(.pagination-container .n-pagination) {
    font-size: 14px;
  }
}