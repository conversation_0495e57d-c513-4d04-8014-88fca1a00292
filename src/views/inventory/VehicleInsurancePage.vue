<template>
  <div class="vehicle-insurance-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">返利状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.rebateStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in rebateStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">经办单位</div>
          <div class="filter-options">
            <n-button @click="showOrgSelector = true" style="width: 200px">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              {{ selectedOrgText || "请选择保险经办单位" }}
            </n-button>
            <div
              v-if="filterForm.salesOrgIds.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in selectedOrgs"
                :key="org.id"
                type="info"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button
          type="success"
          @click="confirmRebate"
          round
          :disabled="selectedRowKeys.length === 0"
        >
          <template #icon>
            <n-icon><CreateOutline /></n-icon>
          </template>
          确认返利 ({{ selectedRowKeys.length }})
        </n-button>
        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入VIN/保险公司进行搜索"
          style="width: 320px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :checked-row-keys="selectedRowKeys"
          @update:checked-row-keys="handleSelectionChange"
          :max-height="tableMaxHeight"
          :scroll-x="scrollX"
          virtual-scroll
          striped
          size="medium"
        />
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择销售单位"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useVehicleInsurancePage from "./VehicleInsurancePage.js";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";

// 使用组合式函数获取所有页面逻辑
const {
  // 图标
  SearchOutline,
  RefreshOutline,
  CreateOutline,

  // 响应式数据
  tableRef,
  loading,
  editableRowKeys,
  vehicleBrandOptions,
  rebateStatusOptions,
  insuranceProviderOptions,
  filterForm,
  insuranceData,
  pagination,
  windowHeight,
  showOrgSelector,
  selectedOrgs,
  selectedRowKeys,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  scrollX,
  selectedOrgText,

  // 业务方法
  handleSearch,
  refreshData,
  handlePageChange,
  handlePageSizeChange,
  editRow,
  cancelEdit,
  saveRow,
  handleOrgSelect,
  handleOrgCancel,
  removeOrg,
  handleSelectionChange,
  confirmRebate,

  // 生命周期方法
  initialize,
  cleanup,
} = useVehicleInsurancePage();

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./VehicleInsurancePage.scss";
</style>
