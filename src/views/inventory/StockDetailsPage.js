import { ref, reactive, computed, h } from 'vue'
import messages from '@/utils/messages'
import stocksApi from '@/api/stocks'
import { vehicleBrandUtils, useDictOptions } from '@/utils/dictUtils'
import { NIcon, NTag, NSelect, NInput, useDialog } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
  ContractOutline,
  ExpandOutline,
  SwapHorizontalOutline,
  CopyOutline,
  CheckmarkOutline,
  CloseOutline
} from '@vicons/ionicons5'
import { Building } from '@vicons/tabler'
import SearchIcon from '@/components/icons/SearchIcon.vue'
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate
} from '@/utils/dateRange'
import StartBillDetailModal from '@/components/inventory/StartBillDetailModal.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'

export default function useStockDetailsPage() {
  // 初始化对话框
  const dialog = useDialog()
  window.$dialog = dialog

  // 状态变量
  const tableRef = ref(null)
  const loading = ref(false)
  const dialogVisible = ref(false)
  const currentStockId = ref(null)
  const currentStockData = ref(null)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)

  // 调拨相关状态
  const transferDialogVisible = ref(false)
  const transferDialogTitle = ref('')
  const selectedRows = ref([])

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于虚拟滚动
  const tableMaxHeight = computed(() => {
    const screenHeight = windowHeight.value

    // 基础组件高度配置
    const pagepadding = 32 // 页面上下padding
    const filterHeight = 140 // 筛选区域高度（4行筛选条件）
    const toolbarHeight = 60 // 工具栏高度
    const paginationHeight = 50 // 分页区域高度
    const margin = 20 // 额外边距

    // 计算表格容器的可用高度
    const calculatedHeight = screenHeight - pagepadding - filterHeight - toolbarHeight - paginationHeight - margin
    // 根据屏幕尺寸进行优化
    let finalHeight
    if (screenHeight >= 1440) {
      // 大屏幕 (27寸+)
      finalHeight = Math.max(calculatedHeight, 700)
    } else if (screenHeight >= 1080) {
      // 24寸屏幕
      finalHeight = Math.max(calculatedHeight, 500)
    } else if (screenHeight >= 768) {
      // 中等屏幕
      finalHeight = Math.max(calculatedHeight, 400)
    } else {
      // 小屏幕 (13寸等)
      finalHeight = Math.max(calculatedHeight, 300)
    }
    return finalHeight - 185;
  })

  // 仓储状态选项 - 使用响应式字典数据
  const { options: stockStatusOptions } = useDictOptions('stock_status', true)

  // 调拨状态选项 - 使用响应式字典数据
  const { options: transferStatusOptions } = useDictOptions('transfer_status', true)

  // 库存类型选项
  const stockTypeOptions = ref([
    { label: '不限', value: null },
    { label: '现金车', value: 'CASH' }
  ])

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组以支持多选
    stockStatus: null, // 仓储状态
    transferStatus: null, // 调拨状态
    stockType: null, // 库存类型
    minAmount: null,
    maxAmount: null,
    keywords: ''
  })

  // 数据列表
  const stocksData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0, // 总记录数
    showQuickJumper: false
  })

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailId = ref(null)
  const currentDetailData = ref(null)

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch((err) => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`
      }
    }
    return "选择库存单位"
  })

  // 计算属性：状态映射表 - 从字典数据生成
  const statusMap = computed(() => {
    const map = {}
    stockStatusOptions.value.forEach(item => {
      if (item.value !== null) {
        // 根据字典数据中的type和color属性设置标签样式
        const dictItem = stockStatusOptions.value.find(opt => opt.value === item.value)
        map[item.value] = {
          text: item.label,
          type: getTagTypeFromDictType(item.value),
          color: dictItem?.color
        }
      }
    })
    return map
  })

  // 计算属性：调拨状态映射表 - 从字典数据生成
  const transferStatusMap = computed(() => {
    const map = {}
    transferStatusOptions.value.forEach(item => {
      if (item.value !== null) {
        // 根据字典数据中的type和color属性设置标签样式
        const dictItem = transferStatusOptions.value.find(opt => opt.value === item.value)
        map[item.value] = {
          text: item.label,
          type: getTransferTagTypeFromDictType(item.value),
          color: dictItem?.color
        }
      }
    })
    return map
  })

  // 根据状态值获取标签类型
  const getTagTypeFromDictType = (statusValue) => {
    switch (statusValue) {
      case 'stocking':
        return 'success'
      case 'transiting':
        return 'info'
      case 'sold':
        return 'warning'
      case 'returned':
        return 'error'
      default:
        return 'default'
    }
  }

  // 根据调拨状态值获取标签类型
  const getTransferTagTypeFromDictType = (statusValue) => {
    switch (statusValue) {
      case 'pending':
        return 'warning'
      case 'received':
        return 'success'
      case 'rejected':
        return 'error'
      default:
        return 'default'
    }
  }

  // 窗口大小变化处理
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  // 表格数据
  const filteredData = computed(() => {
    return stocksData.value
  })

  // 表格列配置 - 根据官方虚拟滚动要求重新定义
  const columns = [
    {
      type: 'selection',
      width: 50,
      fixed: 'left'
    },
    {
      title: 'VIN',
      key: 'vin',
      width: 220,
      fixed: 'left',
      align: 'center',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              cursor: 'pointer',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace'
            },
            onClick: () => copyToClipboard(row.vin),
            title: '点击复制VIN'
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.vin),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                },
                onMouseover: (e) => {
                  e.target.style.opacity = 1
                },
                onMouseout: (e) => {
                  e.target.style.opacity = 0.8
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '入库日期',
      key: 'createTime',
      align: 'center',
      render(row) {
        if (!row.createTime) return '-'
        const date = new Date(row.createTime)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }
    },
    {
      title: '库存单位',
      key: 'stockOrgName',
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '库存状态',
      key: 'stockStatus',
      align: 'center',
      render(row) {
        const status = statusMap.value[row.stockStatus] || {
          text: row.stockStatus || '未知',
          type: 'default'
        }
        return h(
          NTag,
          {
            type: status.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => status.text }
        )
      }
    },
    {
      title: '调拨动态',
      key: 'transferStatus',
      align: 'center',
      render(row) {
        // 如果没有调拨状态，显示"-"
        if (!row.transferStatus) {
          return h('span', { style: { color: '#909399' } }, '无')
        }

        const status = transferStatusMap.value[row.transferStatus] || {
          text: row.transferStatus || '未知',
          type: 'default'
        }
        return h(
          NTag,
          {
            type: status.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => status.text }
        )
      }
    },
    {
      title: '库存成本(元)',
      key: 'stockAmount',
      align: 'center',
      sorter: (a, b) => a.stockAmount - b.stockAmount,
      render(row) {
        const amountInYuan = row.stockAmount / 100
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
      }
    },
    {
      title: '库龄(天)',
      key: 'stockDays',
      align: 'center'
    },
    {
      title: '品牌',
      key: 'brand',
      align: 'center'
    },
    {
      title: '车型',
      key: 'series',
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '配置',
      key: 'configName',
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '颜色',
      key: 'colorCode',
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '试乘试驾车',
      key: 'trialStatus',
      align: 'center',
      render(row) {
        const isTrialing = row.trialStatus === 'trialing'
        return h(
          NTag,
          {
            type: isTrialing ? 'success' : 'default',
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => isTrialing ? '是' : '否' }
        )
      }
    },
    {
      title: '现金车',
      key: 'stockType',
      align: 'center',
      render(row) {
        const isCash = row.stockType === 'CASH'
        return h(
          NTag,
          {
            type: isCash ? 'success' : 'default',
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => isCash ? '是' : '否' }
        )
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 280,
      align: 'center',
      fixed: 'right',
      render: (row) => {
        const actions = [
          // 查看详情按钮 - 始终显示
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#18a058',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleView(row.id),
              title: '查看详情'
            },
            [h(SearchIcon, { size: 20, color: '#18a058' })]
          )
        ]

        // 如果调拨状态不是"待确认"，显示编辑按钮（修改状态按钮暂时隐藏）
        if (row.transferStatus !== 'pending') {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleEdit(row.id),
                title: '编辑'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CreateOutline) })]
            )
            // 修改状态按钮暂时隐藏
            // h(
            //   'div',
            //   {
            //     style: {
            //       cursor: 'pointer',
            //       color: '#f0a020',
            //       fontSize: '20px',
            //       display: 'flex',
            //       alignItems: 'center',
            //       justifyContent: 'center'
            //     },
            //     onClick: () => handleChangeStatus(row),
            //     title: '修改状态'
            //   },
            //   [h(NIcon, { size: 20 }, { default: () => h(SwapHorizontalOutline) })]
            // )
          )
        }

        // 如果调拨状态为"待确认"，添加确认和拒绝按钮
        if (row.transferStatus === 'pending') {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleConfirmTransfer(row.id),
                title: '确认接收'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CheckmarkOutline) })]
            ),
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#d03050',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleRejectTransfer(row.id),
                title: '拒绝接收'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CloseOutline) })]
            )
          )
        }

        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '12px',
            width: '100%',
            height: '100%'
          }
        }, actions)
      }
    }
  ]

  // 表格横向滚动宽度 - 参考官方demo设置
  const scrollX = computed(() => {
    // 按照官方demo的方式，设置一个合适的滚动宽度
    // 确保能容纳所有列的内容
    return 2200
  })

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别 - 传递label而不是value
      if (filterForm.vehicleCategory) {
        // 如果选择的是null（不限），直接传递null
        if (filterForm.vehicleCategory === null) {
          params.vehicleCategory = null
        } else {
          // 根据value获取对应的label传递给接口
          const brandLabel = vehicleBrandUtils.getLabel(filterForm.vehicleCategory)
          params.vehicleCategory = brandLabel !== '未知' ? brandLabel : filterForm.vehicleCategory
        }
      }

      // 处理仓储单位 - 支持多选，使用机构代码的逗号分隔格式
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        // 使用机构id作为机构代码，以逗号分隔的格式传入
        params.stockOrgId = filterForm.invoiceOrgs
          .map((org) => org.id)
          .join(",")
      }

      // 处理仓储状态
      if (filterForm.stockStatus) {
        params.stockStatus = filterForm.stockStatus
      }

      // 处理调拨状态
      if (filterForm.transferStatus) {
        params.transferStatus = filterForm.transferStatus
      }

      // 处理库存类型
      if (filterForm.stockType) {
        params.stockType = filterForm.stockType
      }

      // 调用API获取数据
      const response = await stocksApi.getStocksList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        stocksData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages
          refreshData()
          return
        }
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 处理关键字清除
  const handleKeywordsClear = () => {
    // 先清空关键字
    filterForm.keywords = ''
    // 然后重置页码并刷新数据
    pagination.page = 1
    refreshData()
  }

  // 显示新增对话框
  const showAddDialog = () => {
    currentStockId.value = null
    currentStockData.value = null
    dialogVisible.value = true
  }

  // 处理编辑
  const handleEdit = (id) => {
    currentStockId.value = id
    // 从当前数据中找到对应的库存记录
    currentStockData.value = filteredData.value.find(item => item.id === id) || null
    dialogVisible.value = true
  }

  // 处理查看
  const handleView = (id) => {
    currentDetailId.value = id
    // 从当前数据中找到对应的库存记录
    currentDetailData.value = filteredData.value.find(item => item.id === id) || null
    detailDialogVisible.value = true
  }

  // 处理表单成功回调
  const handleFormSuccess = () => {
    refreshData()
  }

  // 处理详情弹窗成功回调
  const handleDetailSuccess = () => {
    // 详情弹窗是只读的，通常不需要刷新数据
    // 但为了保持一致性，这里也可以刷新
    refreshData()
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 处理状态变更
  const handleChangeStatus = (row) => {
    // 使用字典数据中的状态选项（排除"不限"选项和"已售"状态）
    const statusOptions = stockStatusOptions.value.filter(item => item.value !== null && item.value !== 'sold')

    const dialog = window.$dialog.create({
      title: '修改库存状态',
      content: () => {
        return h('div', [
          h('p', { style: { marginBottom: '16px' } }, `当前状态: ${statusOptions.find(item => item.value === row.stockStatus)?.label || '未知'}`),
          h('p', { style: { marginBottom: '8px' } }, '选择新状态:'),
          h(
            NSelect,
            {
              options: statusOptions,
              value: row.stockStatus,
              style: { width: '100%' },
              onUpdateValue: (value) => {
                dialog.destroy()
                updateStockStatus(row.id, value)
              }
            }
          )
        ])
      },
      positiveText: '取消',
      negativeText: null,
      autoFocus: false
    })
  }

  // 处理批量状态变更
  const handleBatchChangeStatus = () => {
    if (selectedRows.value.length === 0) {
      messages.warning('请先选择要修改状态的库存')
      return
    }

    // 过滤掉待确认状态的数据
    const validRows = selectedRows.value.filter(row => row.transferStatus !== 'pending')
    const pendingRows = selectedRows.value.filter(row => row.transferStatus === 'pending')

    if (pendingRows.length > 0) {
      messages.warning(`已过滤掉${pendingRows.length}条待确认状态的库存，不允许修改状态`)
    }

    if (validRows.length === 0) {
      messages.warning('没有可修改状态的库存数据')
      return
    }

    // 使用字典数据中的状态选项（排除"不限"选项和"已售"状态）
    const statusOptions = stockStatusOptions.value.filter(item => item.value !== null && item.value !== 'sold')

    const dialog = window.$dialog.create({
      title: `批量修改库存状态 (已选择${validRows.length}条记录)`,
      content: () => {
        return h('div', [
          h('p', { style: { marginBottom: '8px' } }, '选择新状态:'),
          h(
            NSelect,
            {
              options: statusOptions,
              placeholder: '请选择状态',
              style: { width: '100%' },
              onUpdateValue: (value) => {
                dialog.destroy()
                batchUpdateStockStatus(validRows.map(row => row.id), value)
              }
            }
          )
        ])
      },
      positiveText: '取消',
      negativeText: null,
      autoFocus: false
    })
  }

  // 更新库存状态
  const updateStockStatus = async (id, status) => {
    try {
      loading.value = true
      const response = await stocksApi.updateStockStatus(id, status)

      if (response.code === 200) {
        messages.success('状态更新成功')
        refreshData()
      } else {
        messages.error(response.message || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      messages.error('状态更新失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 批量更新库存状态
  const batchUpdateStockStatus = async (ids, status) => {
    try {
      loading.value = true
      const response = await stocksApi.batchUpdateStockStatus(ids, status)

      if (response.code === 200) {
        messages.success(`批量状态更新成功，共更新${ids.length}条记录`)
        // 清空选择
        selectedRows.value = []
        refreshData()
      } else {
        messages.error(response.message || '批量状态更新失败')
      }
    } catch (error) {
      console.error('批量状态更新失败:', error)
      messages.error('批量状态更新失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs]
      handleSearch()
    }
  }

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = []
    handleSearch()
  }

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.invoiceOrgs = filterForm.invoiceOrgs.filter(
      (org) => org.id !== orgToRemove.id
    )
    handleSearch()
  }

  // 处理行选择
  const handleRowSelection = (rowKeys) => {
    selectedRows.value = filteredData.value.filter(row => rowKeys.includes(row.id))
  }

  // 显示内部调拨对话框
  const showInternalTransferDialog = () => {
    if (selectedRows.value.length === 0) {
      messages.warning('请先选择要调拨的库存')
      return
    }

    // 过滤掉待确认状态的数据
    const validRows = selectedRows.value.filter(row => row.transferStatus !== 'pending')
    const pendingRows = selectedRows.value.filter(row => row.transferStatus === 'pending')

    if (pendingRows.length > 0) {
      messages.warning(`已过滤掉${pendingRows.length}条待确认状态的库存，不允许调拨`)
    }

    if (validRows.length === 0) {
      messages.warning('没有可调拨的库存数据')
      return
    }

    // 更新选中的行为有效的行
    selectedRows.value = validRows

    transferDialogTitle.value = '内部调拨'
    transferDialogVisible.value = true
  }

  // 显示外部调拨对话框
  const showExternalTransferDialog = () => {
    if (selectedRows.value.length === 0) {
      messages.warning('请先选择要调拨的库存')
      return
    }

    // 过滤掉待确认状态的数据
    const validRows = selectedRows.value.filter(row => row.transferStatus !== 'pending')
    const pendingRows = selectedRows.value.filter(row => row.transferStatus === 'pending')

    if (pendingRows.length > 0) {
      messages.warning(`已过滤掉${pendingRows.length}条待确认状态的库存，不允许调拨`)
    }

    if (validRows.length === 0) {
      messages.warning('没有可调拨的库存数据')
      return
    }

    // 更新选中的行为有效的行
    selectedRows.value = validRows

    transferDialogTitle.value = '外部调拨'
    transferDialogVisible.value = true
  }

  // 处理调拨成功回调
  const handleTransferSuccess = () => {
    // 清空选择
    selectedRows.value = []
    // 刷新数据
    refreshData()
  }

  // 处理确认调拨
  const handleConfirmTransfer = async (stockId) => {
    try {
      loading.value = true
      // 调用确认调拨的API
      const response = await stocksApi.confirmTransfer(stockId)

      if (response.code === 200) {
        messages.success('调拨确认成功')
        refreshData()
      } else {
        messages.error(response.message || '确认失败')
      }
    } catch (error) {
      console.error('确认调拨失败:', error)
      messages.error('确认调拨失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理拒绝调拨
  const handleRejectTransfer = (stockId) => {
    let rejectReason = ''

    const dialog = window.$dialog.create({
      title: '拒绝调拨',
      content: () => {
        return h('div', [
          h('p', { style: { marginBottom: '16px' } }, '请输入拒绝原因:'),
          h(
            NInput,
            {
              type: 'textarea',
              placeholder: '请输入拒绝原因',
              rows: 3,
              maxlength: 200,
              showCount: true,
              onUpdateValue: (value) => {
                rejectReason = value
              }
            }
          )
        ])
      },
      positiveText: '拒绝',
      negativeText: '取消',
      onPositiveClick: () => {
        if (!rejectReason.trim()) {
          messages.warning('请输入拒绝原因')
          return false // 阻止对话框关闭
        }
        confirmRejectTransfer(stockId, rejectReason.trim())
      }
    })
  }

  // 确认拒绝调拨
  const confirmRejectTransfer = async (stockId, reason) => {
    try {
      loading.value = true
      // 调用拒绝调拨的API
      const response = await stocksApi.rejectTransfer(stockId, reason)

      if (response.code === 200) {
        messages.success('调拨拒绝成功')
        refreshData()
      } else {
        messages.error(response.message || '拒绝失败')
      }
    } catch (error) {
      console.error('拒绝调拨失败:', error)
      messages.error('拒绝调拨失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 初始化函数
  const initialize = async () => {
    window.addEventListener('resize', handleResize)
    // 字典数据现在通过响应式方式自动加载
    refreshData()
  }

  // 清理函数
  const cleanup = () => {
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 组件引用
    BizOrgSelector,

    // 图标
    SearchOutline,
    RefreshOutline,
    AddOutline,
    SearchIcon,
    CreateOutline,
    ContractOutline,
    ExpandOutline,
    SwapHorizontalOutline,
    CopyOutline,
    CheckmarkOutline,
    CloseOutline,
    Building,

    // 响应式数据
    tableRef,
    loading,
    dialogVisible,
    currentStockId,
    currentStockData,
    stockStatusOptions,
    transferStatusOptions,
    stockTypeOptions,
    filterForm,
    stocksData,
    pagination,
    windowHeight,
    detailDialogVisible,
    currentDetailId,
    currentDetailData,
    showOrgSelector,

    // 调拨相关状态
    transferDialogVisible,
    transferDialogTitle,
    selectedRows,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    scrollX,
    selectedOrgText,
    statusMap,
    transferStatusMap,

    // 日期相关
    dateRangeOptions,

    // 业务方法
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    handleKeywordsClear,
    showAddDialog,
    handleEdit,
    handleView,
    handleFormSuccess,
    handleDetailSuccess,
    handlePageChange,
    handlePageSizeChange,
    handleChangeStatus,
    handleBatchChangeStatus,
    refreshData,

    // 机构选择器相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 调拨相关方法
    handleRowSelection,
    showInternalTransferDialog,
    showExternalTransferDialog,
    handleTransferSuccess,
    handleConfirmTransfer,
    handleRejectTransfer,

    // 生命周期方法
    initialize,
    cleanup
  }
}
