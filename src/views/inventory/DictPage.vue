<template>
  <div class="dict-page">
    <n-grid :cols="24" :x-gap="12" class="dict-grid">
      <!-- 左侧字典列表 -->
      <n-grid-item :span="8">
        <div class="dict-list-container">
          <n-card title="业务字典" class="dict-list-card">
            <template #header-extra>
              <n-button
                type="primary"
                size="small"
                @click="showAddDictDialog"
                round
              >
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                新增字典项
              </n-button>
            </template>

            <!-- 搜索框 -->
            <div class="search-box">
              <n-input
                v-model:value="dictSearchKeyword"
                placeholder="搜索字典项"
                clearable
              >
                <template #prefix>
                  <n-icon><SearchOutline /></n-icon>
                </template>
              </n-input>
            </div>

            <!-- 字典列表 -->
            <div class="dict-list">
              <n-spin :show="dictLoading">
                <n-list hoverable clickable>
                  <n-list-item
                    v-for="dict in filteredDictList"
                    :key="dict.optionGroup"
                    :class="{
                      'active-dict':
                        currentDict &&
                        currentDict.optionGroup === dict.optionGroup,
                    }"
                    @click="selectDict(dict)"
                  >
                    <div class="dict-item-container">
                      <div class="dict-content">
                        <n-thing
                          :title="dict.optionGroupName"
                          :description="dict.optionGroup"
                        >
                          <template #description>
                            <div class="dict-code">{{ dict.optionGroup }}</div>
                            <div class="dict-remark" v-if="dict.optionComment">
                              {{ dict.optionComment }}
                            </div>
                          </template>
                        </n-thing>
                      </div>
                      <div class="dict-actions">
                        <n-button
                          circle
                          secondary
                          size="small"
                          @click.stop="showEditDictDialog(dict)"
                          class="action-btn"
                        >
                          <template #icon>
                            <n-icon><CreateOutline /></n-icon>
                          </template>
                        </n-button>
                      </div>
                    </div>
                  </n-list-item>
                  <n-empty
                    v-if="filteredDictList.length === 0"
                    description="暂无数据"
                  />
                </n-list>
              </n-spin>
            </div>
          </n-card>
        </div>
      </n-grid-item>

      <!-- 右侧字典值列表 -->
      <n-grid-item :span="16">
        <div class="dict-values-container">
          <n-card
            :title="
              currentDict ? `${currentDict.optionGroupName} - 字典值` : '字典值'
            "
            class="dict-values-card"
          >
            <template #header-extra>
              <n-button
                v-if="currentDict"
                type="primary"
                size="small"
                @click="showAddOptionDialog"
                round
              >
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                新增字典值
              </n-button>
            </template>

            <!-- 搜索框 -->
            <div class="search-box" v-if="currentDict">
              <n-input
                v-model:value="optionSearchKeyword"
                placeholder="搜索字典值"
                clearable
              >
                <template #prefix>
                  <n-icon><SearchOutline /></n-icon>
                </template>
              </n-input>
            </div>

            <!-- 字典值表格 -->
            <div class="dict-values">
              <n-spin :show="optionsLoading">
                <n-data-table
                  v-if="currentDict"
                  :columns="optionsColumns"
                  :data="filteredOptions"
                  :row-key="(row) => row.id || row._tempKey"
                  :pagination="false"
                  :scroll-x="scrollX"
                  virtual-scroll
                  size="small"
                  striped
                />
                <n-empty v-else description="请选择左侧字典" />
              </n-spin>
            </div>
          </n-card>
        </div>
      </n-grid-item>
    </n-grid>

    <!-- 新增/编辑字典对话框 -->
    <DictFormModal
      v-model:visible="dictDialogVisible"
      :is-edit="isEditDict"
      :form-data="dictForm"
      :loading="dictSaving"
      @save="saveDict"
      @cancel="dictDialogVisible = false"
    />

    <!-- 机构选择器 -->
    <BizOrgSelector
      v-model:visible="showOrgSelector"
      title="选择可用机构"
      :single="false"
      :selected-orgs="editingRowOrgIds"
      @select="handleOrgSelect"
      @cancel="showOrgSelector = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed } from "vue";
import { useDialog, useMessage } from "naive-ui";
import {
  AddOutline,
  TrashOutline,
  CreateOutline,
  SearchOutline,
  CheckmarkOutline,
  CloseOutline,
} from "@vicons/ionicons5";
import {
  NButton,
  NSpace,
  NIcon,
  NDataTable,
  NCard,
  NGrid,
  NGridItem,
  NList,
  NListItem,
  NThing,
  NSpin,
  NEmpty,
  NInput,
  NInputNumber,
  NTag,
} from "naive-ui";
import {
  getDictList,
  getDictOptions,
  updateDict,
  createDictGroup,
  createDictOption,
  updateDictOption,
  deleteDictOption,
} from "@/api/dict";
import DictFormModal from "@/components/dict/DictFormModal.vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";

// 消息和对话框
const message = useMessage();
const dialog = useDialog();

// 字典列表相关
const dictList = ref([]);
const dictLoading = ref(false);
const dictSearchKeyword = ref("");
const currentDict = ref(null);

// 字典值相关
const dictOptions = ref([]);
const optionsLoading = ref(false);
const optionSearchKeyword = ref("");

// 行内编辑相关
const editingRowKey = ref(null); // 当前编辑行的key
const editingRowData = ref({}); // 编辑行的数据
const isAddingNewRow = ref(false); // 是否正在添加新行

// 字符串哈希函数 - 参考Java HashMap的哈希算法，减少碰撞
const generateHashCode = (text) => {
  if (!text) return "";

  // Java String.hashCode() 算法：s[0]*31^(n-1) + s[1]*31^(n-2) + ... + s[n-1]
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    hash = (hash * 31 + text.charCodeAt(i)) >>> 0; // >>> 0 确保无符号32位整数
  }

  // 应用Java HashMap的扰动函数，进一步减少碰撞
  hash = hash ^ (hash >>> 16);

  // 生成8位大写十六进制
  return hash.toString(16).toUpperCase().padStart(8, "0").slice(0, 8);
};

// 字典表单相关
const dictDialogVisible = ref(false);
const isEditDict = ref(false);
const dictSaving = ref(false);
const dictForm = ref({
  dict_code: "",
  dict_name: "",
  scope: "group_available",
  org_ids: "",
  org_names: "",
  remark: "",
});

// 机构选择器相关状态
const showOrgSelector = ref(false);
const editingRowOrgIds = ref([]);

// 表格横向滚动宽度 - 参考官方demo设置
const scrollX = computed(() => {
  // 按照官方demo的方式，设置一个合适的滚动宽度
  // 确保能容纳所有列的内容
  return 800;
});

// 字典值表格列配置
const optionsColumns = computed(() => {
  const baseColumns = [
    {
      title: "序号",
      key: "optionOrder",
      align: "center",
      width: 80,
      minWidth: 80,
      render(row) {
        const isEditing = editingRowKey.value === (row._tempKey || row.id);
        if (isEditing) {
          return h(NInputNumber, {
            value: editingRowData.value.optionOrder,
            onUpdateValue: (value) => {
              editingRowData.value.optionOrder = value;
            },
            size: "small",
            buttonPlacement: "both",
            min: 1,
            style: { width: "100%" },
          });
        }
        return row.optionOrder || 0;
      },
    },
    {
      title: "选项标签",
      key: "optionLabel",
      minWidth: 120,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const isEditing = editingRowKey.value === (row._tempKey || row.id);
        if (isEditing) {
          const isEmpty =
            !editingRowData.value.optionLabel ||
            !editingRowData.value.optionLabel.trim();

          return h(NInput, {
            value: editingRowData.value.optionLabel,
            onUpdateValue: (value) => {
              editingRowData.value.optionLabel = value;
            },
            onBlur: () => {
              // 失去焦点时，如果是新增行且选项标签有值但选项值为空，自动生成选项值
              if (
                isAddingNewRow.value &&
                editingRowData.value.optionLabel &&
                editingRowData.value.optionLabel.trim() &&
                (!editingRowData.value.optionValue ||
                  !editingRowData.value.optionValue.trim())
              ) {
                const labelValue = editingRowData.value.optionLabel.trim();
                const generatedValue = generateHashCode(labelValue);

                // 检查生成的值是否重复，如果重复则添加数字后缀
                let finalValue = generatedValue;
                let counter = 1;
                while (
                  dictOptions.value.some(
                    (item) =>
                      item.optionValue === finalValue &&
                      (item.id || item._tempKey) !== editingRowKey.value
                  )
                ) {
                  finalValue = `${generatedValue}${counter}`;
                  counter++;
                }
                editingRowData.value.optionValue = finalValue;
              }
            },
            size: "small",
            placeholder: "请输入选项标签",
            status: isEmpty ? "error" : undefined,
            style: { width: "100%" },
          });
        }
        return row.optionLabel;
      },
    },
    {
      title: "选项值",
      key: "optionValue",
      minWidth: 120,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const isEditing = editingRowKey.value === (row._tempKey || row.id);
        if (isEditing) {
          // 检查是否有重复值
          const currentValue = editingRowData.value.optionValue;
          const isDuplicate =
            currentValue &&
            dictOptions.value.some(
              (item) =>
                item.optionValue === currentValue.trim() &&
                (item.id || item._tempKey) !== editingRowKey.value
            );

          return h(NInput, {
            value: editingRowData.value.optionValue,
            onUpdateValue: (value) => {
              editingRowData.value.optionValue = value;
            },
            size: "small",
            placeholder: "请输入选项值",
            disabled: !isAddingNewRow.value, // 编辑时禁用选项值修改
            status: isDuplicate ? "error" : undefined,
            style: { width: "100%" },
          });
        }
        return row.optionValue;
      },
    },
  ];

  // 如果当前字典是机构专用，添加可用机构列
  if (currentDict.value && currentDict.value.optionOrgRange !== null) {
    baseColumns.push({
      title: "可用机构",
      key: "availableOrgs",
      minWidth: 150,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const isEditing = editingRowKey.value === (row._tempKey || row.id);
        if (isEditing) {
          const orgNames = editingRowData.value.availableOrgNames || "";
          const isEmpty = !orgNames.trim();

          // 编辑模式下显示机构标签
          if (!isEmpty) {
            const orgNameArray = orgNames
              .split(",")
              .filter((name) => name.trim());
            let displayText = "";
            let tooltipText = "";

            if (orgNameArray.length === 1) {
              displayText = orgNameArray[0];
              tooltipText = orgNameArray[0];
            } else if (orgNameArray.length > 1) {
              displayText = `${orgNameArray[0]}等${orgNameArray.length}个`;
              tooltipText = orgNameArray.join("、");
            }

            return h(
              NTag,
              {
                type: "info",
                size: "small",
                style: { cursor: "pointer", maxWidth: "100%" },
                onClick: () => openOrgSelectorForRow(),
              },
              {
                default: () => displayText,
                // 使用tooltip显示完整机构列表
                ...(tooltipText && {
                  tooltip: () => tooltipText,
                }),
              }
            );
          }

          return h(
            NButton,
            {
              onClick: () => openOrgSelectorForRow(),
              size: "small",
              style: { width: "100%", textAlign: "left" },
              secondary: true,
              type: "error",
            },
            {
              default: () => "请选择机构",
            }
          );
        }

        // 非编辑模式下的显示
        const orgNames = row.availableOrgNames || row.optionOrgRangeNames;
        const orgIds = row.availableOrgIds || row.optionOrgRange;

        // 如果机构ID为空或null，说明是集团通用
        if (!orgIds || !orgIds.trim()) {
          return h(
            NTag,
            {
              type: "success",
              size: "small",
            },
            {
              default: () => "集团通用",
            }
          );
        }

        // 如果有机构ID但没有机构名称，显示暂未设置
        if (!orgNames || !orgNames.trim()) {
          return h(
            NTag,
            {
              type: "warning",
              size: "small",
            },
            {
              default: () => "暂未设置",
            }
          );
        }

        const orgNameArray = orgNames.split(",").filter((name) => name.trim());
        let displayText = "";
        let tooltipText = "";

        if (orgNameArray.length === 1) {
          displayText = orgNameArray[0];
          tooltipText = orgNameArray[0];
        } else if (orgNameArray.length > 1) {
          displayText = `${orgNameArray[0]}等${orgNameArray.length}个`;
          tooltipText = orgNameArray.join("、");
        }

        return h(
          NTag,
          {
            type: "success",
            size: "small",
            style: { cursor: "default" },
          },
          {
            default: () => displayText,
            // 使用tooltip显示完整机构列表
            ...(tooltipText && {
              tooltip: () => tooltipText,
            }),
          }
        );
      },
    });
  }

  baseColumns.push(
    {
      title: "备注",
      key: "optionComment",
      minWidth: 100,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const isEditing = editingRowKey.value === (row._tempKey || row.id);
        if (isEditing) {
          return h(NInput, {
            value: editingRowData.value.optionComment,
            onUpdateValue: (value) => {
              editingRowData.value.optionComment = value;
            },
            size: "small",
            placeholder: "请输入备注",
            style: { width: "100%" },
          });
        }
        return row.optionComment || "-";
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 100,
      fixed: "right",
      render(row) {
        const isEditing = editingRowKey.value === (row._tempKey || row.id);

        if (isEditing) {
          // 编辑状态：显示保存和取消按钮
          return h(
            NSpace,
            { size: 8 },
            {
              default: () => [
                h(
                  NButton,
                  {
                    circle: true,
                    size: "small",
                    type: "success",
                    onClick: () => saveInlineEdit(),
                    class: "action-btn",
                  },
                  {
                    icon: () =>
                      h(NIcon, null, { default: () => h(CheckmarkOutline) }),
                  }
                ),
                h(
                  NButton,
                  {
                    circle: true,
                    size: "small",
                    type: "error",
                    onClick: () => cancelInlineEdit(),
                    class: "action-btn",
                  },
                  {
                    icon: () =>
                      h(NIcon, null, { default: () => h(CloseOutline) }),
                  }
                ),
              ],
            }
          );
        } else {
          // 正常状态：显示编辑和删除按钮
          return h(
            NSpace,
            { size: 8 },
            {
              default: () => [
                h(
                  NButton,
                  {
                    circle: true,
                    secondary: true,
                    size: "small",
                    onClick: () => startInlineEdit(row),
                    class: "action-btn",
                  },
                  {
                    icon: () =>
                      h(NIcon, null, { default: () => h(CreateOutline) }),
                  }
                ),
                h(
                  NButton,
                  {
                    circle: true,
                    secondary: true,
                    size: "small",
                    type: "error",
                    onClick: () => confirmDeleteOption(row),
                    class: "action-btn",
                  },
                  {
                    icon: () =>
                      h(NIcon, null, { default: () => h(TrashOutline) }),
                  }
                ),
              ],
            }
          );
        }
      },
    }
  );

  return baseColumns;
});

// 移除分页配置，显示完整数据

// 过滤后的字典列表
const filteredDictList = computed(() => {
  if (!dictSearchKeyword.value) return dictList.value;

  const keyword = dictSearchKeyword.value.toLowerCase();
  return dictList.value.filter(
    (dict) =>
      dict.optionGroup.toLowerCase().includes(keyword) ||
      dict.optionGroupName.toLowerCase().includes(keyword) ||
      (dict.optionComment && dict.optionComment.toLowerCase().includes(keyword))
  );
});

// 过滤后的字典值列表
const filteredOptions = computed(() => {
  if (!optionSearchKeyword.value) return dictOptions.value;

  const keyword = optionSearchKeyword.value.toLowerCase();
  return dictOptions.value.filter(
    (option) =>
      option.optionValue.toLowerCase().includes(keyword) ||
      option.optionLabel.toLowerCase().includes(keyword) ||
      (option.optionComment &&
        option.optionComment.toLowerCase().includes(keyword))
  );
});

// 页面加载时获取字典列表
onMounted(() => {
  fetchDictList();
});

// 获取字典列表
const fetchDictList = async () => {
  dictLoading.value = true;
  try {
    const res = await getDictList();
    dictList.value = res.data || [];
  } catch (error) {
    console.error("获取字典列表失败:", error);
    message.error("获取字典列表失败");
  } finally {
    dictLoading.value = false;
  }
};

// 选择字典
const selectDict = async (dict) => {
  currentDict.value = dict;
  await fetchDictOptions(dict.optionGroup);
};

// 获取字典值列表
const fetchDictOptions = async (optionGroup) => {
  if (!optionGroup) return;

  optionsLoading.value = true;
  try {
    const res = await getDictOptions(optionGroup);
    dictOptions.value = res.data || [];
  } catch (error) {
    console.error("获取字典值列表失败:", error);
    message.error("获取字典值列表失败");
  } finally {
    optionsLoading.value = false;
  }
};

// 显示新增字典对话框
const showAddDictDialog = () => {
  isEditDict.value = false;
  dictForm.value = {
    dict_code: "",
    dict_name: "",
    scope: "group_available",
    org_ids: "",
    org_names: "",
    remark: "",
  };
  dictDialogVisible.value = true;
};

// 显示编辑字典对话框
const showEditDictDialog = (dict) => {
  isEditDict.value = true;

  // 根据optionOrgRange字段判断可用范围和机构信息
  const hasOrgRange = dict.optionOrgRange && dict.optionOrgRange.trim();
  const scope = hasOrgRange ? "specific_org" : "group_available";

  dictForm.value = {
    dict_code: dict.optionGroup,
    dict_name: dict.optionGroupName,
    scope: scope,
    org_ids: hasOrgRange ? dict.optionOrgRange : "",
    org_names: hasOrgRange ? dict.optionOrgRangeNames || "" : "",
    remark: dict.optionComment || "",
  };
  dictDialogVisible.value = true;
};

// 保存字典
const saveDict = async (formData) => {
  dictSaving.value = true;
  try {
    if (isEditDict.value) {
      await updateDict(formData);
      message.success("字典更新成功");

      // 更新本地列表中的数据
      const index = dictList.value.findIndex(
        (item) => item.optionGroup === formData.dict_code
      );
      if (index !== -1) {
        // 转换为新的数据结构
        const updatedDict = {
          ...dictList.value[index],
          optionGroup: formData.dict_code,
          optionGroupName: formData.dict_name,
          optionComment: formData.remark,
          optionOrgRange: formData.scope === "specific_org" ? "" : null,
        };
        dictList.value[index] = updatedDict;

        // 如果当前选中的是被编辑的字典，也需要更新
        if (
          currentDict.value &&
          currentDict.value.optionGroup === formData.dict_code
        ) {
          currentDict.value = { ...currentDict.value, ...updatedDict };
        }
      }
    } else {
      // 新增字典项 - 使用新的接口
      const dictGroupData = {
        optionGroup: formData.dict_code,
        optionGroupName: formData.dict_name,
        // 如果是机构专用，则设置optionOrgRange为空字符串（表示机构专用但未指定具体机构）
        ...(formData.scope === "specific_org" ? { optionOrgRange: "" } : {}),
      };

      await createDictGroup(dictGroupData);
      message.success("字典创建成功");

      // 转换为新的数据结构并添加到本地列表
      const newDict = {
        id: formData.dict_code,
        optionComment: formData.remark,
        optionGroup: formData.dict_code,
        optionGroupName: formData.dict_name,
        optionGroupType: "CUSTOMIZE",
        optionLabel: formData.dict_name,
        optionOrder: 0,
        optionOrgRange: formData.scope === "specific_org" ? "" : null,
        optionValue: formData.dict_name,
      };
      dictList.value.push(newDict);
    }

    dictDialogVisible.value = false;
  } catch (error) {
    console.error("保存字典失败:", error);
    message.error("保存字典失败");
  } finally {
    dictSaving.value = false;
  }
};

// 打开机构选择器
const openOrgSelectorForRow = () => {
  // 解析当前编辑行的机构ID
  const orgIds = editingRowData.value.availableOrgIds || "";
  editingRowOrgIds.value = orgIds
    ? orgIds
        .split(",")
        .map((id) => parseInt(id))
        .filter((id) => !isNaN(id) && id > 0)
    : [];
  showOrgSelector.value = true;
};

// 处理机构选择
const handleOrgSelect = (orgs) => {
  const orgIds = orgs
    .filter((org) => org.id && org.id > 0)
    .map((org) => org.id)
    .join(",");
  const orgNames = orgs
    .filter((org) => org.id && org.id > 0)
    .map((org) => org.orgName || org.name)
    .join(",");

  editingRowData.value.availableOrgIds = orgIds;
  editingRowData.value.availableOrgNames = orgNames;
  showOrgSelector.value = false;
};

// 显示新增字典值对话框 - 改为行内新增
const showAddOptionDialog = () => {
  if (!currentDict.value) {
    message.warning("请先选择字典");
    return;
  }

  // 如果已经有编辑行，先取消
  if (editingRowKey.value) {
    cancelInlineEdit();
  }

  // 计算新行的默认序号（当前最大序号+1）
  const maxSort = dictOptions.value.reduce((max, item) => {
    const sort = item.optionOrder || 0;
    return sort > max ? sort : max;
  }, 0);

  // 创建新行数据
  const newRowKey = `new_${Date.now()}`;
  const newRow = {
    id: null, // 新增行暂时没有ID
    optionValue: "", // 初始为空，等待用户输入选项标签后自动生成
    optionLabel: "",
    optionOrder: maxSort + 1,
    optionComment: "",
    optionGroup: currentDict.value.optionGroup,
    optionGroupName: currentDict.value.optionGroupName,
    optionGroupType: "CUSTOMIZE", // 默认为用户字典
    isNew: true, // 标记为新行
    _tempKey: newRowKey, // 临时key用于标识行
    // 如果是机构专用字典，初始化机构相关字段
    availableOrgIds: currentDict.value.optionOrgRange !== null ? "" : undefined,
    availableOrgNames:
      currentDict.value.optionOrgRange !== null ? "" : undefined,
  };

  // 添加到列表开头
  dictOptions.value.unshift(newRow);

  // 设置编辑状态
  editingRowKey.value = newRowKey; // 使用临时key作为编辑标识
  editingRowData.value = { ...newRow };
  isAddingNewRow.value = true;
};

// 开始行内编辑
const startInlineEdit = (row) => {
  // 如果已经有编辑行，先取消
  if (editingRowKey.value) {
    cancelInlineEdit();
  }

  editingRowKey.value = row.id;
  editingRowData.value = {
    ...row,
    // 如果是机构专用字典，从optionOrgRange字段读取机构ID，从optionOrgRangeNames读取机构名称
    availableOrgIds:
      currentDict.value?.optionOrgRange !== null
        ? row.availableOrgIds || row.optionOrgRange || ""
        : undefined,
    availableOrgNames:
      currentDict.value?.optionOrgRange !== null
        ? row.availableOrgNames || row.optionOrgRangeNames || ""
        : undefined,
  };
  isAddingNewRow.value = false;
};

// 保存行内编辑
const saveInlineEdit = async () => {
  if (!currentDict.value) {
    message.warning("请先选择字典");
    return;
  }

  // 验证必填字段
  if (
    !editingRowData.value.optionValue ||
    !editingRowData.value.optionValue.trim()
  ) {
    message.error("选项值不能为空");
    return;
  }

  if (
    !editingRowData.value.optionLabel ||
    !editingRowData.value.optionLabel.trim()
  ) {
    message.error("选项标签不能为空");
    return;
  }

  // 如果是机构专用字典，验证机构字段
  if (currentDict.value.optionOrgRange !== null) {
    if (
      !editingRowData.value.availableOrgIds ||
      !editingRowData.value.availableOrgIds.trim()
    ) {
      message.error("机构专用字典必须选择可用机构");
      return;
    }
  }

  // 检查选项值是否重复
  const trimmedOptionValue = editingRowData.value.optionValue.trim();
  const existingOption = dictOptions.value.find(
    (item) =>
      item.optionValue === trimmedOptionValue &&
      (item.id || item._tempKey) !== editingRowKey.value // 排除当前编辑的行
  );

  if (existingOption) {
    message.error(`选项值 "${trimmedOptionValue}" 已存在，请使用其他值`);
    return;
  }

  try {
    if (isAddingNewRow.value) {
      // 新增 - 包含完整的字典组信息和选项属性（使用驼峰命名）
      const newData = {
        optionGroup: currentDict.value.optionGroup,
        optionGroupName: currentDict.value.optionGroupName,
        optionValue: trimmedOptionValue,
        optionLabel: editingRowData.value.optionLabel.trim(),
        optionOrder: editingRowData.value.optionOrder || 0,
        optionComment: editingRowData.value.optionComment
          ? editingRowData.value.optionComment.trim()
          : "",
      };

      // 如果是机构专用字典，添加机构字段
      if (
        currentDict.value.optionOrgRange !== null &&
        editingRowData.value.availableOrgIds
      ) {
        newData.optionOrgRange = editingRowData.value.availableOrgIds;
      }

      await createDictOption(newData);
      message.success("字典值创建成功");
    } else {
      // 编辑 - 按ID更新选项（使用驼峰命名）
      const updateData = {
        optionOrder: editingRowData.value.optionOrder || 0,
        optionValue: editingRowData.value.optionValue,
        optionLabel: editingRowData.value.optionLabel.trim(),
        optionComment: editingRowData.value.optionComment
          ? editingRowData.value.optionComment.trim()
          : "",
      };

      // 如果是机构专用字典，添加机构字段
      if (
        currentDict.value.optionOrgRange !== null &&
        editingRowData.value.availableOrgIds !== undefined
      ) {
        updateData.optionOrgRange = editingRowData.value.availableOrgIds || "";
      }

      await updateDictOption(editingRowData.value.id, updateData);
      message.success("字典值更新成功");
    }

    // 清除编辑状态
    editingRowKey.value = null;
    editingRowData.value = {};
    isAddingNewRow.value = false;

    // 刷新字典值列表数据
    await fetchDictOptions(currentDict.value.optionGroup);
  } catch (error) {
    console.error("保存字典值失败:", error);
    message.error("保存字典值失败");
  }
};

// 取消行内编辑
const cancelInlineEdit = () => {
  if (isAddingNewRow.value) {
    // 如果是新增行，从列表中移除
    // 使用 _tempKey 或 optionValue 来匹配要删除的行
    dictOptions.value = dictOptions.value.filter((item) => {
      const itemKey = item._tempKey || item.optionValue;
      return itemKey !== editingRowKey.value;
    });
  }

  // 清除编辑状态
  editingRowKey.value = null;
  editingRowData.value = {};
  isAddingNewRow.value = false;
};

// 确认删除字典值
const confirmDeleteOption = (option) => {
  // 检查是否只有一个选项，如果是则禁止删除
  if (dictOptions.value.length <= 1) {
    message.warning("字典项必须至少保留一个选项，无法删除");
    return;
  }

  dialog.warning({
    title: "确认删除",
    content: `确定要删除选项 "${option.optionLabel}" 吗？删除后将无法恢复。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => deleteSelectedOption(option),
  });
};

// 删除字典值
const deleteSelectedOption = async (option) => {
  if (!currentDict.value) return;

  optionsLoading.value = true;
  try {
    await deleteDictOption(option.id);
    message.success("字典值删除成功");

    // 从本地列表中移除
    dictOptions.value = dictOptions.value.filter(
      (item) => item.id !== option.id
    );
  } catch (error) {
    console.error("删除字典值失败:", error);
    message.error("删除字典值失败");
  } finally {
    optionsLoading.value = false;
  }
};
</script>

<style scoped>
.dict-page {
  padding: 16px;
  height: 100vh; /* 使用90%视口高度 */
  overflow: hidden; /* 防止页面级别的滚动 */
  box-sizing: border-box; /* 确保padding包含在高度内 */
}

.dict-list-card {
  height: auto; /* 让左侧card高度自适应内容 */
  margin-bottom: 0;
}

.dict-values-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 0; /* 移除默认的底部边距 */
}

.dict-values-card :deep(.n-card__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 左右两侧的滚动容器 */
.dict-list-container {
  height: auto; /* 让左侧容器高度自适应 */
  max-height: calc(95vh - 50px); /* 设置最大高度防止过高 */
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

.dict-values-container {
  height: calc(95vh - 50px); /* 使用90%视口高度减去页面padding和其他元素 */
  overflow: hidden; /* 让内部组件处理滚动 */
  padding: 0;
}

/* 自定义容器滚动条样式 */
.dict-list-container::-webkit-scrollbar,
.dict-values-container::-webkit-scrollbar {
  width: 1px;
}

.dict-list-container::-webkit-scrollbar-track,
.dict-values-container::-webkit-scrollbar-track {
  background: transparent;
}

.dict-list-container::-webkit-scrollbar-thumb,
.dict-values-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dict-list-container::-webkit-scrollbar-thumb:hover,
.dict-values-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.search-box {
  margin-bottom: 16px;
  flex-shrink: 0; /* 防止搜索框被压缩 */
}

.dict-list {
  /* 移除内部滚动，让外层容器处理滚动 */
  flex: 1;
}

.dict-values {
  flex: 1;
  min-height: 0;
  padding: 16px 0;
}

/* 表格主体滚动区域 */
.dict-values :deep(.n-data-table-base-table-body) {
  overflow-y: auto;
  overflow-x: hidden;
}

/* 右侧字典值区域的自定义滚动条样式 */
.dict-values :deep(.n-data-table-wrapper)::-webkit-scrollbar,
.dict-values :deep(.n-data-table-base-table-body)::-webkit-scrollbar {
  width: 1px;
}

.dict-values :deep(.n-data-table-wrapper)::-webkit-scrollbar-track,
.dict-values :deep(.n-data-table-base-table-body)::-webkit-scrollbar-track {
  background: transparent;
}

.dict-values :deep(.n-data-table-wrapper)::-webkit-scrollbar-thumb,
.dict-values :deep(.n-data-table-base-table-body)::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dict-values :deep(.n-data-table-wrapper)::-webkit-scrollbar-thumb:hover,
.dict-values
  :deep(.n-data-table-base-table-body)::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.dict-code {
  color: #666;
  font-size: 12px;
}

.dict-remark {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.active-dict {
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.n-list-item) {
  padding: 12px 16px;
}

:deep(.n-thing-main__description) {
  margin-top: 8px;
}

.dict-item-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.dict-content {
  flex: 1;
  padding-right: 8px;
}

.dict-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 12px;
  padding-top: 4px;
  align-items: center;
}

.action-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}
</style>
