/* 产品页面样式 */

.stock-details-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-separator {
  color: #999;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.department-selector) {
  width: 350px !important;
  min-width: 350px !important;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 表格容器样式 - 参考StockDetailsPage */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 2px 10px;
  background-color: #fafafa;
  // border-top: 1px solid #e0e0e6;
  min-height: 40px;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* 普通表格样式优化 - 不使用虚拟滚动 */
:deep(.n-data-table) {
  width: 100% !important;

  /* 表头样式 */
  .n-data-table-base-table-header {
    .n-data-table-th {
      font-weight: 600;
      text-align: center !important;

      .n-data-table-th__title {
        justify-content: center;
      }
    }
  }

  /* 数据单元格样式 */
  .n-data-table-td {
    text-align: center !important;
  }

  /* 表格基础样式 */
  .n-data-table-base-table {
    width: 100% !important;
  }
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}