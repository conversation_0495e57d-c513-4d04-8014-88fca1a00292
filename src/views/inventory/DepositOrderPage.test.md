# 定金订单页面修复验证清单

## ✅ 已修复的问题

### 1. API调用问题
- [x] 将 `vehicleOrderApi.getOrderList()` 改为 `vehicleOrderApi.getDepositOrderList()`
- [x] 将 `vehicleOrderApi.getOrderDetail()` 改为 `vehicleOrderApi.getDepositOrderDetail()`

### 2. 表格列配置
- [x] 修正函数名：`createOrdersTableColumns` → `createDepositOrdersTableColumns`
- [x] 更新日期字段：`dealDate` → `depositDate`
- [x] 更新金额字段：`dealAmount` → `depositAmount`
- [x] 更新状态字段：`orderStatus` → `depositStatus`

### 3. 业务逻辑
- [x] 移除销售订单相关的编辑逻辑
- [x] 更新对话框标题：`新增销售订单` → `新增定金订单`
- [x] 更新编辑标题：`编辑销售订单` → `编辑定金订单`

### 4. 组件引用
- [x] 替换组件：`OrderEditModal` → `DepositOrderModal`
- [x] 更新导入和使用

### 5. UI文本和标签
- [x] 筛选条件：`销售日期` → `定金日期`
- [x] 筛选条件：`订单状态` → `定金状态`
- [x] 表格列：`销售日期` → `定金日期`
- [x] 表格列：`订单状态` → `定金状态`
- [x] 表格列：`成交金额` → `定金金额`

### 6. 字典数据
- [x] 添加 `depositStatusUtils` 工具函数
- [x] 更新状态选项使用定金状态而非订单状态
- [x] 为定金状态添加类型和颜色配置

### 7. 模拟数据
- [x] 订单编号前缀：`SO` → `DO`
- [x] 日期字段：`dealDate` → `depositDate`
- [x] 状态字段：`orderStatus` → `depositStatus`
- [x] 金额字段：`dealAmount` → `depositAmount`
- [x] 订单类型固定为 `deposit`

## 🔍 验证要点

### 功能验证
1. 页面能正常加载
2. 筛选条件正确显示定金相关字段
3. 表格列显示定金订单相关信息
4. 新增按钮显示"定金订单"
5. 编辑功能使用定金订单组件
6. 状态筛选使用定金状态选项

### 数据验证
1. API调用使用正确的定金订单接口
2. 模拟数据生成定金订单格式
3. 状态显示使用定金状态字典
4. 金额显示为定金金额而非成交金额

### UI验证
1. 所有文本标签正确显示定金相关内容
2. 状态标签使用正确的颜色和类型
3. 表格列宽度和显示正常
4. 筛选条件布局正常

## 📝 注意事项

1. 定金订单使用专门的API接口，与销售订单分离
2. 定金状态包括：已收定金、已退定金、已转车款
3. 定金金额通常小于成交金额，用于预付款
4. 定金订单可以转换为正式销售订单
