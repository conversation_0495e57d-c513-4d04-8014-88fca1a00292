<template>
  <div class="fee-subjects-page">
    <!-- 标题和工具栏 -->
    <n-space justify="space-between" align="center" class="page-header">
      <div class="title-container">
        <h2 class="section-title">费用科目设置</h2>
        <div class="divider"></div>
      </div>
      <n-space>
        <n-button type="primary" @click="handleRefresh">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button type="info" @click="handleAddSubject">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增
        </n-button>
        <n-input
          v-model:value="searchParams.keywords"
          placeholder="搜索科目名称"
          style="width: 300px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
          <template #suffix>
            <span style="color: #999">支持模糊搜索</span>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="subjectsData"
          :loading="loading"
          :pagination="false"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          :scroll-x="scrollX"
          virtual-scroll
          :bordered="false"
          :single-line="false"
          :striped="true"
          size="medium"
        >
          <template #empty>
            <div class="empty-state">
              <span>暂无数据</span>
            </div>
          </template>
        </n-data-table>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="pagination.itemCount > 0">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          :show-size-picker="pagination.showSizePicker"
          @update:page="pagination.onChange"
          @update:page-size="pagination.onUpdatePageSize"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, h, onMounted, computed, onUnmounted } from "vue";
import {
  NSpace,
  NButton,
  NInput,
  NIcon,
  NDataTable,
  NTag,
  NSwitch,
  NSelect,
  NInputNumber,
  useDialog,
  useMessage,
} from "naive-ui";
import {
  RefreshOutline,
  AddOutline,
  SearchOutline,
  CreateOutline,
  TrashOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
} from "@vicons/ionicons5";
import { feeSubjectApi } from "@/api/feeSubject";
import messages from "@/utils/messages";
import { formatMoney } from "@/utils/money";

// 状态变量
const tableRef = ref(null);
const loading = ref(false);
const subjectsData = ref([]);
const editableRowKeys = ref([]); // 当前可编辑的行的key集合
const originalRowData = ref({}); // 保存编辑前的原始数据

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight);

// 计算表格最大高度 - 适应禁用外层滚动的情况，让分页紧贴屏幕底部
const tableMaxHeight = computed(() => {
  const screenHeight = windowHeight.value;

  // 页面各部分高度 - 减少空白区域
  const pageTopPadding = 16; // 页面顶部padding
  const pageHeaderHeight = 60; // 页面头部（标题和工具栏）
  const paginationHeight = 50; // 分页区域高度（减少）
  const pageBottomPadding = 0; // 页面底部padding设为0，让分页紧贴底部
  const margin = 8; // 减少额外边距

  // 计算可用高度
  const usedHeight =
    pageTopPadding +
    pageHeaderHeight +
    paginationHeight +
    pageBottomPadding +
    margin;
  const availableHeight = screenHeight - usedHeight - 105;

  // 直接使用计算出的可用高度，不设置最大限制，让表格充分利用空间
  return Math.max(availableHeight, 400); // 最小高度400px
});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page) => {
    pagination.page = page;
    searchParams.page = page;
    fetchSubjects();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    searchParams.page = 1;
    searchParams.size = pageSize;
    fetchSubjects();
  },
});

// 搜索参数
const searchParams = reactive({
  keywords: "",
  page: 1,
  size: 20,
});

// 对话框
const dialog = useDialog();
const message = useMessage();

// 科目类别选项
const categoryOptions = feeSubjectApi.getSubjectCategoryOptions();

// 表格横向滚动宽度 - 参考官方demo设置
const scrollX = computed(() => {
  // 按照官方demo的方式，设置一个合适的滚动宽度
  // 确保能容纳所有列的内容
  return 1200;
});

// 表格列定义
const columns = [
  {
    title: "科目ID",
    key: "id",
    width: 80,
    render(row) {
      // 如果是新增行，显示"系统自动生成"
      if (row.isNew) {
        return h(
          "span",
          { style: "color: #999; font-style: italic;" },
          "系统自动生成"
        );
      }
      return h("span", row.id);
    },
  },
  {
    title: "科目名称",
    key: "subjectName",
    ellipsis: {
      tooltip: true,
    },
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing && !isSystemFee) {
        return h(NInput, {
          value: row.subjectName,
          maxlength: 20,
          showCount: true,
          onUpdateValue(v) {
            subjectsData.value[index].subjectName = v;
          },
        });
      }

      return h("span", row.subjectName);
    },
  },
  {
    title: "科目类别",
    key: "subjectCategory",
    width: 150,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing && !isSystemFee) {
        return h(NSelect, {
          value: row.subjectCategory,
          options: categoryOptions,
          onUpdateValue(v) {
            subjectsData.value[index].subjectCategory = v;
            // 更新类别名称
            const category = categoryOptions.find((item) => item.value === v);
            if (category) {
              subjectsData.value[index].subjectCategoryName = category.label;
            }
          },
        });
      }

      return h("span", row.subjectCategoryName);
    },
  },
  {
    title: "可收款",
    key: "receivable",
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing && !isSystemFee) {
        return h(NSwitch, {
          value: row.receivable,
          onUpdateValue(v) {
            subjectsData.value[index].receivable = v;
          },
        });
      }

      return h(
        NTag,
        { type: row.receivable ? "success" : "default" },
        {
          default: () => (row.receivable ? "是" : "否"),
        }
      );
    },
  },
  {
    title: "可付款",
    key: "payable",
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing && !isSystemFee) {
        return h(NSwitch, {
          value: row.payable,
          onUpdateValue(v) {
            subjectsData.value[index].payable = v;
          },
        });
      }

      return h(
        NTag,
        { type: row.payable ? "success" : "default" },
        {
          default: () => (row.payable ? "是" : "否"),
        }
      );
    },
  },
  {
    title: "科目状态",
    key: "usable",
    width: 100,
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing && !isSystemFee) {
        return h(NSwitch, {
          value: row.usable !== false, // 默认为true
          onUpdateValue(v) {
            subjectsData.value[index].usable = v;
          },
        });
      }

      return h(
        NTag,
        {
          type: row.usable !== false ? "success" : "error",
          bordered: false,
          style: {
            padding: "2px 8px",
            fontWeight: "bold",
          },
        },
        {
          default: () => (row.usable !== false ? "启用" : "停用"),
        }
      );
    },
  },
  {
    title: "默认金额",
    key: "defaultAmount",
    width: 150,
    render(row) {
      // 默认金额列不可编辑，只显示数据
      return h("span", formatMoney(row.defaultAmount / 100));
    },
  },
  {
    title: "经办人",
    key: "editorName",
    width: 120,
    render(row) {
      return h("span", row.editorName || "-");
    },
  },
  {
    title: "备注",
    key: "remark",
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render(row, index) {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing && !isSystemFee) {
        return h(NInput, {
          value: row.remark,
          maxlength: 100,
          showCount: true,
          onUpdateValue(v) {
            subjectsData.value[index].remark = v;
          },
        });
      }

      return h("span", row.remark || "-");
    },
  },
  {
    title: "操作",
    key: "actions",
    align: "center",
    width: 120,
    render: (row, index) => {
      // 判断当前行是否处于编辑状态
      const isEditing = editableRowKeys.value.includes(row.id);
      // 判断是否为系统费用（不允许编辑/删除）
      const isSystemFee = row.subjectCategory === "SYSTEM_FEE";

      if (isEditing) {
        return h(
          NSpace,
          { align: "center" },
          {
            default: () => [
              h(
                NButton,
                {
                  quaternary: true,
                  circle: true,
                  size: "small",
                  type: "success",
                  onClick: () => saveRow(row),
                  style: "color: #18a058; font-size: 18px;",
                },
                {
                  default: () =>
                    h(NIcon, { component: CheckmarkCircleOutline }),
                }
              ),
              h(
                NButton,
                {
                  quaternary: true,
                  circle: true,
                  size: "small",
                  type: "error",
                  onClick: () => cancelEdit(row),
                  style: "color: #d03050; font-size: 18px;",
                },
                { default: () => h(NIcon, { component: CloseCircleOutline }) }
              ),
            ],
          }
        );
      }

      // 如果是系统费用，显示"系统管理"文字，不显示操作按钮
      if (isSystemFee) {
        return h(
          "span",
          {
            style: "color: #999; font-size: 12px; font-style: italic;",
          },
          "禁止修改"
        );
      }

      return h(
        NSpace,
        { align: "center" },
        {
          default: () => [
            h(
              NButton,
              {
                quaternary: true,
                circle: true,
                size: "small",
                onClick: () => editRow(row),
                style: "color: #2080f0; font-size: 18px;",
              },
              { default: () => h(NIcon, { component: CreateOutline }) }
            ),
            h(
              NButton,
              {
                quaternary: true,
                circle: true,
                size: "small",
                onClick: () => deleteRow(row, index),
                style: "color: #d03050; font-size: 18px;",
              },
              { default: () => h(NIcon, { component: TrashOutline }) }
            ),
          ],
        }
      );
    },
  },
];

// 获取费用科目列表
async function fetchSubjects() {
  loading.value = true;
  try {
    const response = await feeSubjectApi.getFeeSubjectList(searchParams);

    // 检查API响应数据结构
    if (!response || !response.data || !Array.isArray(response.data.list)) {
      console.error("API返回数据格式错误:", response);
      messages.error("获取费用科目列表失败：数据格式错误");
      subjectsData.value = [];
      pagination.itemCount = 0;
      return;
    }

    // 处理API返回的数据，确保必要字段有默认值
    const processedData = response.data.list.map((item) => {
      // 根据科目类别代码获取类别名称
      const category = categoryOptions.find(
        (opt) => opt.value === item.subjectCategory
      );
      const subjectCategoryName = category
        ? category.label
        : item.subjectCategoryName || "未知类别";

      return {
        ...item,
        // 确保 usable 字段有默认值（如果API没有返回该字段）
        usable: item.usable !== undefined ? item.usable : true,
        // 确保 defaultAmount 字段有默认值
        defaultAmount: item.defaultAmount || 0,
        // 确保 remark 字段存在
        remark: item.remark || "",
        // 确保 editorName 字段存在
        editorName: item.editorName || "-",
        // 确保科目类别名称存在
        subjectCategoryName: subjectCategoryName,
      };
    });

    subjectsData.value = processedData;
    pagination.itemCount = response.data.total || 0;
  } catch (error) {
    console.error("Failed to fetch fee subjects:", error);
    messages.error("获取费用科目列表失败");
  } finally {
    loading.value = false;
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1;
  searchParams.page = 1;
  fetchSubjects();
}

// 刷新
function handleRefresh() {
  fetchSubjects();
}

// 添加新科目
function handleAddSubject() {
  // 获取当前用户信息
  const currentUser = JSON.parse(localStorage.getItem("user") || "{}");
  const nickname = currentUser.nickname || "当前用户";

  // 创建一个新的科目行
  const newSubject = {
    id: `temp_${Date.now()}`, // 临时ID，保存时会被替换
    subjectName: "",
    subjectCategory: "OTHER_FEE",
    subjectCategoryName: "其他费用",
    receivable: true,
    payable: true,
    usable: true, // 默认启用
    defaultAmount: 0, // 默认金额，单位分
    remark: "",
    creatorName: nickname,
    editorName: nickname, // 经办人默认为当前用户
    isNew: true, // 标记为新行
  };

  // 添加到数据列表
  subjectsData.value.unshift(newSubject);

  // 设置为编辑状态
  editableRowKeys.value.push(newSubject.id);
}

// 编辑行
function editRow(row) {
  // 系统费用不允许编辑
  if (row.subjectCategory === "SYSTEM_FEE") {
    messages.warning("系统费用不允许编辑");
    return;
  }

  // 保存原始数据，用于取消编辑时恢复
  originalRowData.value[row.id] = { ...row };

  editableRowKeys.value.push(row.id);
}

// 取消编辑
function cancelEdit(row) {
  // 从可编辑行集合中移除
  editableRowKeys.value = editableRowKeys.value.filter((id) => id !== row.id);

  // 如果是新添加的行，则从数据中移除
  if (row.isNew) {
    subjectsData.value = subjectsData.value.filter(
      (item) => item.id !== row.id
    );
  } else {
    // 如果是编辑现有行，恢复原始数据，不发送请求
    const originalData = originalRowData.value[row.id];
    if (originalData) {
      // 找到当前行在数组中的索引
      const index = subjectsData.value.findIndex((item) => item.id === row.id);
      if (index !== -1) {
        // 恢复原始数据
        subjectsData.value[index] = { ...originalData };
      }
      // 清除保存的原始数据
      delete originalRowData.value[row.id];
    }
  }
}

// 保存行
async function saveRow(row) {
  // 验证必填字段
  if (!row.subjectName) {
    messages.error("科目名称不能为空");
    return;
  }

  // 验证字段长度
  if (row.subjectName.length > 20) {
    messages.error("科目名称不能超过20个字符");
    return;
  }

  if (row.remark && row.remark.length > 100) {
    messages.error("备注不能超过100个字符");
    return;
  }

  try {
    // 获取当前用户信息
    const currentUser = JSON.parse(localStorage.getItem("user") || "{}");
    const nickname = currentUser.nickname || "当前用户";

    if (row.isNew) {
      // 创建新科目
      const newSubject = {
        subjectName: row.subjectName,
        subjectCategory: row.subjectCategory,
        receivable: row.receivable,
        payable: row.payable,
        usable: row.usable !== false, // 确保有值
        defaultAmount: row.defaultAmount,
        remark: row.remark ? row.remark.replace(/[\r\n]+/g, " ") : "",
        editorName: nickname, // 经办人为当前用户
      };

      // 调用API创建科目
      await feeSubjectApi.createFeeSubject(newSubject);
      messages.success("科目创建成功");
    } else {
      // 更新现有科目
      const updatedSubject = {
        id: row.id,
        subjectName: row.subjectName,
        subjectCategory: row.subjectCategory,
        receivable: row.receivable,
        payable: row.payable,
        usable: row.usable !== false, // 确保有值
        defaultAmount: row.defaultAmount,
        remark: row.remark ? row.remark.replace(/[\r\n]+/g, " ") : "",
        editorName: nickname, // 经办人更新为当前用户
      };

      // 调用API更新科目
      await feeSubjectApi.updateFeeSubject(updatedSubject);
      messages.success("科目更新成功");
    }

    // 从可编辑行集合中移除
    editableRowKeys.value = editableRowKeys.value.filter((id) => id !== row.id);

    // 清除保存的原始数据
    delete originalRowData.value[row.id];

    // 重新获取数据
    await fetchSubjects();
  } catch (error) {
    console.error("Failed to save fee subject:", error);
    messages.error("保存科目失败");
  }
}

// 删除行
function deleteRow(row, index) {
  // 系统费用不允许删除
  if (row.subjectCategory === "SYSTEM_FEE") {
    messages.warning("系统费用不允许删除");
    return;
  }

  dialog.warning({
    title: "删除确认",
    content: `确定要删除科目"${row.subjectName}"吗？此操作不可恢复。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        if (!row.isNew) {
          // 如果不是新行，则调用API删除
          await feeSubjectApi.deleteFeeSubject(row.id);
        }

        // 从数据中移除
        subjectsData.value.splice(index, 1);
        messages.success("科目删除成功");
      } catch (error) {
        console.error("Failed to delete fee subject:", error);
        messages.error("删除科目失败");
      }
    },
  });
}

// 窗口大小变化监听器
const handleResize = () => {
  windowHeight.value = window.innerHeight;
};

// 页面加载时获取数据
onMounted(() => {
  fetchSubjects();
  // 添加窗口大小变化监听器
  window.addEventListener("resize", handleResize);
});

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.fee-subjects-page {
  padding: 16px 16px 0 16px; /* 移除底部padding，让分页紧贴底部 */
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  margin-bottom: 16px;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 4px 0;
}

.divider {
  height: 3px;
  width: 100%;
  background: linear-gradient(
    to right,
    var(--primary-color) 0%,
    rgba(0, 0, 0, 0.05) 100%
  );
  border-radius: 3px;
}

/* 表格容器样式 */
.table-container {
  position: relative;
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  overflow: hidden;
  width: 100%;
  margin-bottom: 0; /* 移除底部边距 */
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 分页容器样式 */
.pagination-container {
  flex-shrink: 0;
  padding: 8px 16px; /* 减少上下padding */
  border-top: 1px solid #e0e0e6;
  background-color: #fafafa;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-height: 50px; /* 设置最小高度 */
}

/* 表头样式 */
:deep(.n-data-table-base-table-header .n-data-table-th) {
  font-weight: 600;
  text-align: center !important;
  background-color: #fafafa;
}

:deep(
    .n-data-table-base-table-header .n-data-table-th .n-data-table-th__title
  ) {
  justify-content: center;
}

/* 数据单元格样式 */
:deep(.n-data-table-td) {
  text-align: center !important;
}

/* 虚拟滚动表格样式优化 */
:deep(.n-data-table) {
  width: 100% !important;
}

/* 确保虚拟滚动表格不出现额外的空白列 */
:deep(.n-data-table .n-data-table-base-table) {
  width: 100% !important;
  table-layout: fixed;
  border-collapse: collapse;
}

/* 虚拟滚动行样式 */
:deep(.n-data-table .n-data-table-tr) {
  height: 48px;
}

/* 修复虚拟滚动表格列之间的空白区域问题 */
:deep(.n-data-table-base-table-header .n-data-table-th),
:deep(.n-data-table-base-table-body .n-data-table-td) {
  border-right: 1px solid var(--n-border-color);
  box-sizing: border-box;
}

:deep(.n-data-table-base-table-header .n-data-table-th:last-child),
:deep(.n-data-table-base-table-body .n-data-table-td:last-child) {
  border-right: none;
}

/* 确保表格内容区域填满容器 */
:deep(.n-data-table .n-data-table-wrapper) {
  width: 100% !important;
}

/* 修复表格右侧空白问题 */
:deep(.n-data-table .n-data-table-base-table-body-wrapper) {
  width: 100% !important;
  overflow-x: auto;
}
</style>