/* 试乘试驾车管理页面样式 */

.trial-vehicles-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 表格容器样式 - 优化虚拟滚动 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 2px 10px;
  background-color: #fafafa;
  min-height: 40px;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trial-vehicles-page {
    padding: 8px;
    gap: 8px;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-label {
    width: auto;
    line-height: 1.5;
  }

  .filter-options {
    width: 100%;
  }

  .toolbar {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar> :deep(.n-space) {
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.n-data-table) {
  width: 100% !important;

  /* 表头样式 */
  .n-data-table-base-table-header {
    .n-data-table-th {
      font-weight: 600;
      text-align: center !important;
      background-color: #fafafa;

      .n-data-table-th__title {
        justify-content: center;
      }
    }
  }

  /* 数据单元格样式 */
  .n-data-table-td {
    text-align: center !important;
    padding: 12px 8px;
  }

  /* 表格行悬停效果 */
  .n-data-table-tbody .n-data-table-tr:hover {
    background-color: rgba(24, 160, 88, 0.05);
  }

  /* 选中行样式 */
  .n-data-table-tbody .n-data-table-tr.n-data-table-tr--checked {
    background-color: rgba(24, 160, 88, 0.1);
  }
}

/* 状态标签样式 */
:deep(.n-tag) {
  font-weight: 500;
}

/* 操作按钮样式 */
:deep(.n-button--ghost) {
  border-width: 1px;
}

:deep(.n-button--ghost:hover) {
  border-width: 1px;
}

/* 操作列图标按钮样式 */
:deep(.action-icon-button) {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

:deep(.action-icon-button:hover) {
  transform: scale(1.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.action-icon-button.n-button--primary-type) {
  color: #18a058;
}

:deep(.action-icon-button.n-button--primary-type:hover) {
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.action-icon-button.n-button--warning-type) {
  color: #f0a020;
}

:deep(.action-icon-button.n-button--warning-type:hover) {
  background-color: rgba(240, 160, 32, 0.1);
}

:deep(.action-icon-button.n-button--error-type) {
  color: #d03050;
}

:deep(.action-icon-button.n-button--error-type:hover) {
  background-color: rgba(208, 48, 80, 0.1);
}

/* 加载状态样式 */
:deep(.n-data-table--loading) {
  min-height: 200px;
}

/* 空状态样式 */
:deep(.n-empty) {
  padding: 40px 20px;
}

/* 筛选按钮样式 */
.filter-options .n-button {
  min-width: 120px;
}

/* 标签关闭按钮样式 */
:deep(.n-tag .n-tag__close) {
  margin-left: 6px;
}

/* 工具栏按钮间距 */
.toolbar .n-space {
  flex-wrap: wrap;
}

.toolbar .n-button {
  white-space: nowrap;
}

/* 搜索框样式 */
.toolbar .n-input {
  min-width: 200px;
}

/* VIN码列样式 */
:deep(.n-data-table-td:nth-child(2)) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

/* 操作列样式 */
:deep(.n-data-table-td:last-child) {
  padding: 8px;
}

:deep(.n-data-table-td:last-child .n-space) {
  justify-content: center;
}

/* 新增弹窗样式 */
:deep(.n-modal .n-card) {
  max-height: 80vh;
  overflow-y: auto;
}

:deep(.n-form-item) {
  margin-bottom: 16px;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

/* 机构选择器样式 */
.filter-options .n-tag {
  max-width: 150px;
}

.filter-options .n-tag .n-tag__content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 已选择机构按钮样式 */
.selected-org-button {
  margin-right: 8px;
  margin-bottom: 4px;
  max-width: none;
  /* 允许按钮根据内容自适应宽度 */
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.selected-org-button .org-name {
  flex: 1;
}

.selected-org-button .close-icon {
  opacity: 0.7;
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.selected-org-button:hover .close-icon {
  opacity: 1;
}

/* 清除全部按钮特殊样式 */
.clear-all-button {
  font-weight: 500;
}

.clear-all-button:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* 批量操作按钮样式 */
.toolbar .n-button--warning {
  background-color: #f0a020;
  border-color: #f0a020;
}

.toolbar .n-button--warning:hover {
  background-color: #fcb040;
  border-color: #fcb040;
}

/* 表格滚动条样式已移至全局样式 */

:deep(.n-data-table-base-table-body::-webkit-scrollbar-thumb) {
  background-color: #d0d0d0;
  border-radius: 3px;
}

:deep(.n-data-table-base-table-body::-webkit-scrollbar-thumb:hover) {
  background-color: #a0a0a0;
}

/* 数据为空时的样式 */
:deep(.n-data-table--empty) {
  min-height: 300px;
}

:deep(.n-data-table-empty) {
  padding: 60px 20px;
}

/* 加载动画优化 */
:deep(.n-spin-container) {
  min-height: 200px;
}

/* 表格头部固定样式 */
:deep(.n-data-table-thead) {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #fafafa;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .trial-vehicles-page {
    padding: 4px;
  }

  .filter-section {
    gap: 12px;
  }

  .toolbar .n-input {
    min-width: 150px;
    width: 100%;
  }

  :deep(.n-data-table) {
    font-size: 12px;
  }

  :deep(.n-data-table-td) {
    padding: 8px 4px;
  }

  .pagination-container {
    padding: 12px 8px;
  }
}