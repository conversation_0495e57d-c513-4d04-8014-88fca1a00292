/* 引入财务表格公共样式 */
@use "./common-financial-table.scss";

.accounts-receivable-page {
  padding: 16px 16px 8px 16px;
  /* 减少底部padding，为分页提供合适间距 */
  height: 100vh;
  /* 使用全屏高度 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  /* 防止页面滚动 */
}

.toolbar {
  margin-bottom: 0;
}

.filter-card {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-orgs-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 固定列样式优化 */
:deep(.n-data-table-th--fixed-left),
:deep(.n-data-table-td--fixed-left) {
  background-color: #fff !important;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

:deep(.n-data-table-th--fixed-right),
:deep(.n-data-table-td--fixed-right) {
  background-color: #fff !important;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* 收款摘要列表头左对齐 */
:deep(.n-data-table-th[data-col-key="receivableSummary"] .n-data-table-th__title) {
  justify-content: flex-start;
}

/* 收款摘要列内容左对齐 */
:deep(.n-data-table-td[data-col-key="receivableSummary"] .n-data-table-td__content) {
  justify-content: flex-start;
}