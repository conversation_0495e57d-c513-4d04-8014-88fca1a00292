<template>
  <div class="trial-vehicles-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">所属单位</div>
          <div class="filter-options">
            <n-space :size="8" :wrap="true">
              <n-button
                @click="showOrgSelector = true"
                :type="selectedOrgs.length > 0 ? 'primary' : 'default'"
                round
                size="small"
              >
                <template #icon>
                  <n-icon><Building /></n-icon>
                </template>
                {{ selectedOrgText }}
              </n-button>
              <!-- 清除全部按钮 -->
              <n-button
                v-if="selectedOrgs.length > 0"
                ghost
                type="success"
                round
                size="small"
                @click="clearOrgSelection"
                class="selected-org-button clear-all-button"
              >
                <span class="org-name">清除全部</span>
                <n-icon
                  size="14"
                  class="close-icon"
                  style="margin-left: 6px; right: -5px"
                >
                  <CloseOutline />
                </n-icon>
              </n-button>
              <n-button
                v-for="org in selectedOrgs"
                :key="org.id"
                ghost
                type="success"
                round
                size="small"
                @click="removeOrg(org.id)"
                class="selected-org-button"
              >
                <span class="org-name">{{ org.name || org.orgName }}</span>
                <n-icon
                  size="14"
                  class="close-icon"
                  style="margin-left: 6px; right: -5px"
                >
                  <CloseOutline />
                </n-icon>
              </n-button>
            </n-space>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">库存状态</div>
          <div class="filter-options">
            <n-space :size="8" :wrap="true">
              <n-button
                v-for="option in stockStatusOptions"
                :key="option.value"
                @click="handleStockStatusChange(option.value)"
                :type="
                  filterForm.stockStatus === option.value
                    ? 'primary'
                    : 'default'
                "
                round
                size="small"
              >
                {{ option.label }}
              </n-button>
            </n-space>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showAddModal = true" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增
        </n-button>
        <n-button
          type="warning"
          @click="batchUpdateStatus"
          round
          :disabled="selectedRowKeys.length === 0"
        >
          <template #icon>
            <n-icon><CreateOutline /></n-icon>
          </template>
          更新 ({{ selectedRowKeys.length }})
        </n-button>

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入VIN或车辆信息"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
          @clear="handleKeywordsClear"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="getRowKey"
          :checked-row-keys="selectedRowKeys"
          @update:checked-row-keys="handleSelectionChange"
          :max-height="tableMaxHeight"
          :scroll-x="scrollX"
          virtual-scroll
          striped
          size="medium"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      v-model:visible="showOrgSelector"
      :selected-orgs="selectedOrgs"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />

    <!-- 新增试驾车弹窗 -->
    <AddTrialVehicleModal
      v-model:visible="showAddModal"
      :org-options="orgOptions"
      @success="handleAddSuccess"
    />

    <!-- 出库弹窗 -->
    <OutboundModal
      v-model:visible="showOutboundModal"
      :vehicle-data="currentOutboundRow"
      @success="handleOutboundSuccess"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import { useTrialVehicles } from "./TrialVehicles.js";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import AddTrialVehicleModal from "./components/AddTrialVehicleModal.vue";
import OutboundModal from "./components/OutboundModal.vue";

// 使用组合式函数获取所有页面逻辑
const {
  // 图标
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
  CloseOutline,
  Building,

  // 响应式数据
  tableRef,
  loading,
  filterForm,
  pagination,
  showOrgSelector,
  selectedOrgs,
  selectedRowKeys,
  showAddModal,
  showOutboundModal,
  currentOutboundRow,
  orgOptions,
  stockStatusOptions,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  scrollX,
  selectedOrgText,

  // 业务方法
  handleSearch,
  handleKeywordsClear,
  refreshData,
  handlePageChange,
  handlePageSizeChange,
  handleSelectionChange,
  getRowKey,
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,
  batchUpdateStatus,
  handleOutbound,
  handleOutboundSuccess,
  handleStockStatusChange,

  // 生命周期方法
  initialize,
  cleanup,
} = useTrialVehicles();

// 新增成功回调
const handleAddSuccess = () => {
  refreshData();
};

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./TrialVehicles.scss";
</style>
