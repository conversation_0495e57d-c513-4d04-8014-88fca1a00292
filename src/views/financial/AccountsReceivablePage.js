import { ref, reactive, onMounted, onUnmounted, computed, h } from "vue";
import { useDialog, NInput } from "naive-ui";
import {
  RefreshOutline,
  AddCircleOutline,
  CheckmarkCircleOutline,
  CopyOutline,
  CloseCircleOutline,
  DocumentTextOutline,
  BusinessOutline as Building
} from "@vicons/ionicons5";

import {
  dateRangeOptions,
  handleDateRangeChange as handleDateChange,
  getDateRangeParams,
} from "@/utils/dateRange";
import arbApi from "@/api/arb";
import messages from "@/utils/messages";

import { renderAmountInChinese } from "@/utils/money";
import { getRowKey } from "@/utils/tableUtils";
import { createAccountsReceivableColumns } from "./AccountsReceivableColumns";
import {
  checkBatchConfirmEnabled,
  getBatchConfirmTooltip,
  prepareBatchConfirmData
} from "@/utils/batchConfirmUtils";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import FeeSubjectSelector from "@/components/financial/FeeSubjectSelector.vue";
import OrderSummaryModal from "@/components/orders/OrderSummaryModal.vue";
import OrderDetailModal from "@/components/orders/OrderDetailModal.vue";
import VehicleTrackingModal from "@/components/inventory/VehicleTrackingModal.vue";
import ReceivableDialog from "@/components/financial/ReceivableDialog.vue";
import ConfirmReceiptDialog from "@/components/financial/ConfirmReceiptDialog.vue";

export function useAccountsReceivablePage() {
  // 状态变量
  const tableRef = ref(null);
  const loading = ref(false);
  const selectedRows = ref([]);
  const receivableData = ref([]);
  // 窗口尺寸响应式变量
  const windowHeight = ref(window.innerHeight);

  // 计算表格最大高度 - 针对不同屏幕尺寸优化，支持虚拟滚动
  const tableMaxHeight = computed(() => {
    const screenHeight = windowHeight.value;

    // 更新后的高度计算
    const pageTopPadding = 16; // 页面顶部padding
    const filterHeight = 140; // 筛选区域固定高度
    const toolbarHeight = 60; // 工具栏固定高度
    const paginationHeight = 44; // 分页区域实际高度（8+8+4+24）
    const pageBottomPadding = 8; // 页面底部padding

    // 计算表格可用高度
    const usedHeight = pageTopPadding + filterHeight + toolbarHeight + paginationHeight + pageBottomPadding;
    const availableHeight = screenHeight - usedHeight;

    // 针对不同分辨率设置合适的高度
    if (screenHeight >= 1440) {
      // 2K及以上 (1440p+)
      return Math.max(availableHeight - 50, 600);
    } else if (screenHeight >= 1080) {
      // 1080P
      return Math.max(availableHeight - 30, 500);
    } else if (screenHeight >= 1000) {
      // 中等屏幕 (1000px-1079px)
      return Math.max(availableHeight - 25, 450);
    } else if (screenHeight >= 800) {
      // 14寸高分屏等 (800px-999px) - 您的屏幕类型
      return Math.max(availableHeight - 15, 380);
    } else {
      // 小屏幕 (13寸低分屏等)
      return Math.max(availableHeight - 10, 300);
    }
  });
  // 新增应收账款弹窗相关
  const receivableDialogVisible = ref(false);
  const receivableDialogRef = ref(null); // ReceivableDialog 组件引用
  const receivableSaving = ref(false);
  const receivableForm = reactive({
    // 表单字段
    receivableOrgId: null,
    receivableSubject: null,
    receivableTarget: null,
    receivableAmount: null,
    receivableSummary: "",

    // 接口提交字段（自动填充）
    feeId: null,
    receivableOrgName: "",
    feeTarget: "",
    feeAmount: null,
  });

  // 应收金额大写
  const amountInChinese = computed(() => {
    return renderAmountInChinese(receivableForm.receivableAmount);
  });
  // 搜索参数
  const searchParams = reactive({
    customerName: "",
    salesOrgName: "",
    status: "NOT_CONFIRM", // 默认选中未对账
    page: 1,
    size: 20,
    keywords: "",
    receivableOrgs: "", // 应收机构参数，以逗号分隔的机构id
  });
  // 日期范围
  const dateRange = ref(null);
  const customDateRange = ref(null);
  // 应收状态选项
  const statusOptions = [
    { label: "不限", value: null },
    { label: "未入账", value: "NOT_CONFIRM" },
    { label: "已入账", value: "CONFIRMED" },
    { label: "已退回", value: "RETURNED" },
  ];
  // 机构选择相关状态
  const showOrgSelector = ref(false);
  const selectedOrgs = ref([]);
  // 应收科目选择相关状态
  const showSubjectSelector = ref(false);
  const selectedSubjects = ref([]);
  const selectedSubjectIds = computed(() => selectedSubjects.value.map(subject => subject.id));
  // 弹窗状态
  const showOrderSummaryModal = ref(false);
  const showOrderDetailModal = ref(false);
  const showVehicleTrackingModal = ref(false);
  const selectedOrderId = ref(null);
  const selectedVin = ref("");
  // 确认收款弹窗相关
  const showConfirmReceiptDialog = ref(false);
  const confirmReceiptSaving = ref(false);
  const selectedReceivableData = ref({});
  // 计算选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (!selectedOrgs.value || selectedOrgs.value.length === 0) {
      return "请选择应收机构";
    }
    if (selectedOrgs.value.length === 1) {
      return selectedOrgs.value[0].orgName;
    }
    return `已选择 ${selectedOrgs.value.length} 个机构`;
  });
  // 计算选中应收科目的显示文本
  const selectedSubjectText = computed(() => {
    if (!selectedSubjects.value || selectedSubjects.value.length === 0) {
      return "请选择应收科目";
    }
    if (selectedSubjects.value.length === 1) {
      return selectedSubjects.value[0].subjectName;
    }
    return `已选择 ${selectedSubjects.value.length} 个科目`;
  });
  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    onChange: (page) => {
      pagination.page = page;
      searchParams.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      searchParams.page = 1;
      searchParams.size = pageSize;
      fetchData();
    },
  });
  // 对话框
  const dialog = useDialog();
  // 方法
  async function fetchData() {
    loading.value = true;
    try {
      // 构建查询参数
      const params = { ...searchParams };

      // 处理日期范围参数
      if (dateRange.value) {
        const dateParams = getDateRangeParams(dateRange.value, customDateRange.value);
        if (dateParams.startDate) {
          params.beginDate = dateParams.startDate;
        }
        if (dateParams.endDate) {
          params.endDate = dateParams.endDate;
        }
      }

      // 如果有关键字搜索，则清空特定字段，让后端统一处理关键字搜索
      if (params.keywords) {
        // 清空特定字段，避免重复查询条件
        params.customerName = "";
        params.salesOrgName = "";
      }

      const response = await arbApi.getArbList(params);

      // 直接使用接口返回的原始数据，不再进行父子行分组处理
      if (response && response.data && response.data.list) {
        receivableData.value = response.data.list;
        // 更新分页信息
        pagination.itemCount = response.data.total || response.data.list.length;
        pagination.pageCount = Math.ceil(pagination.itemCount / searchParams.size);
      } else {
        receivableData.value = [];
        pagination.itemCount = 0;
        pagination.pageCount = 1;
      }
    } catch (error) {
      console.error("获取应收账款数据失败:", error);
      messages.error("获取应收账款数据失败");
    } finally {
      loading.value = false;
    }
  }
  function refreshData() {
    searchParams.page = 1;
    pagination.page = 1;
    fetchData();
  }
  function searchData() {
    searchParams.page = 1;
    pagination.page = 1;
    // 清空选中状态
    selectedRows.value = [];
    fetchData();
  }
  function handleDateRangeChange(value) {
    handleDateChange(value, searchParams, searchData);
  }
  function handleCheck(keys) {
    selectedRows.value = keys;
  }
  // 处理清除关键字
  function handleClearKeywords() {
    // 确保关键字已被清空
    searchParams.keywords = "";
    // 重置页码并发起查询
    searchParams.page = 1;
    pagination.page = 1;
    // 清空选中状态
    selectedRows.value = [];
    fetchData();
  }
  // 处理订单号点击 - 显示气泡提示框
  function handleOrderClick(orderSn) {
    // 使用 NaiveUI 的 message 组件显示可关闭的提示
    messages.info(`订单号: ${orderSn}`, {
      duration: 5000, // 5秒后自动关闭
      closable: true, // 可手动关闭
    });
  }
  // 处理车架号点击
  function handleVinClick(vin) {
    selectedVin.value = vin;
    showVehicleTrackingModal.value = true;
  }
  // 处理查看订单完整详情
  function handleViewOrderDetail(orderId) {
    selectedOrderId.value = orderId;
    showOrderSummaryModal.value = false; // 关闭摘要弹窗
    showOrderDetailModal.value = true; // 打开详情弹窗
  }
  // 显示新增应收账款弹窗
  function showAddDialog() {
    // 重置表单数据
    Object.assign(receivableForm, {
      // 表单字段
      receivableOrgId: null,
      receivableSubject: null,
      receivableTarget: null,
      receivableAmount: null,
      receivableSummary: "",

      // 接口提交字段（自动填充）
      feeId: null,
      receivableOrgName: "",
      feeTarget: "",
      feeAmount: null,
    });

    receivableDialogVisible.value = true;
  }
  // 保存应收账款
  function saveReceivable() {
    console.log("=== AccountsReceivablePage saveReceivable 开始 ===");
    console.log("receivableForm 数据:", JSON.stringify(receivableForm, null, 2));

    // 延迟一下，确保表单状态已更新
    setTimeout(() => {
      // 使用 ReceivableDialog 组件的表单引用进行验证
      const formRef = receivableDialogRef.value?.formRef;
      formRef?.validate(async (errors) => {
        if (errors) {
          console.error("表单验证错误:", errors);
          return;
        }

        receivableSaving.value = true;
        try {
          // 构造保存的数据 - 使用 ReceivableDialog 组件处理后的字段
          const submitData = {
            feeId: receivableForm.feeId,                    // 应收科目ID
            receivableOrgId: receivableForm.receivableOrgId, // 应收机构ID
            receivableOrgName: receivableForm.receivableOrgName, // 应收机构名称
            feeTarget: receivableForm.feeTarget,            // 应收对象名称
            feeAmount: receivableForm.feeAmount,            // 金额（分，已转换）
            receivableSummary: receivableForm.receivableSummary // 收款摘要
          };

          console.log("=== 最终提交到接口的数据 ===");
          console.log(JSON.stringify(submitData, null, 2));

          await arbApi.createArb(submitData);

          messages.success("应收账款添加成功");
          receivableDialogVisible.value = false;

          // 刷新数据
          refreshData();
        } catch (error) {
          console.error("保存应收账款失败:", error);
          messages.error("保存应收账款失败");
        } finally {
          receivableSaving.value = false;
        }
      });
    }, 0);
  }
  function confirmPayment(row) {
    // 统一构造数据格式，单条数据也使用batchData格式
    const unifiedData = {
      id: row.id,
      feeName: row.feeName,
      feeAmount: row.feeAmount,
      receivableOrgId: row.receivableOrgId,
      receivableSummary: row.receivableSummary || "",
      batchData: [row], // 单条数据也放入数组中
      isBatch: false, // 标识为单条操作（用于API调用区分）
    };

    // 设置选中的应收账款数据
    selectedReceivableData.value = unifiedData;
    // 显示确认收款弹窗
    showConfirmReceiptDialog.value = true;
  }
  // 处理确认收款（统一逻辑）
  async function handleConfirmReceipt(submitData) {
    confirmReceiptSaving.value = true;
    try {
      const batchData = selectedReceivableData.value.batchData;
      const totalActualAmount = submitData.feeAmount; // 已经是分为单位
      const totalReceivableAmount = selectedReceivableData.value.feeAmount; // 总应收金额（分）

      // 构造批量确认收款的请求数据
      const requestData = {
        accountId: submitData.accountId,
        totalActualAmount: totalActualAmount,
        receivableItems: batchData.map(row => {
          let allocatedAmount;

          if (batchData.length === 1) {
            // 单条数据：直接使用实收金额（允许与应收金额不一致，差额由后台处理）
            allocatedAmount = totalActualAmount;
          } else {
            // 批量数据：按比例分配实收金额（实收金额等于总应收金额，在UI层已限制）
            const proportion = (row.feeAmount || 0) / totalReceivableAmount;
            allocatedAmount = Math.round(totalActualAmount * proportion);
          }

          return {
            id: row.id,
            feeAmount: allocatedAmount,
          };
        })
      };

      // 如果有业务流水号，添加到请求数据中
      if (submitData.businessSerialNumber) {
        requestData.businessSerialNumber = submitData.businessSerialNumber;
      }

      // 调用批量确认收款API（保证事务一致性）
      await arbApi.batchConfirmReceivablePayment(requestData);

      // 根据数据条数和业务流水号显示不同的成功消息
      const dataCount = batchData.length;
      let successMessage;

      if (dataCount === 1) {
        successMessage = submitData.businessSerialNumber
          ? `收款确认成功（流水号：${submitData.businessSerialNumber}）`
          : "收款确认成功";
      } else {
        successMessage = submitData.businessSerialNumber
          ? `成功确认${dataCount}条应收账款收款（流水号：${submitData.businessSerialNumber}）`
          : `成功确认${dataCount}条应收账款收款`;
      }

      messages.success(successMessage);

      // 清空选中状态（如果是批量操作）
      if (dataCount > 1) {
        selectedRows.value = [];
      }

      showConfirmReceiptDialog.value = false;

      // 重新加载数据
      refreshData();
    } catch (error) {
      console.error("确认收款失败:", error);
      messages.error("确认收款失败");
    } finally {
      confirmReceiptSaving.value = false;
    }
  }
  // 检查批量确认是否可用
  const batchConfirmEnabled = computed(() => {
    return checkBatchConfirmEnabled(selectedRows.value, receivableData.value, getRowKey);
  });
  // 批量确认按钮的提示文案
  const batchConfirmTooltip = computed(() => {
    return getBatchConfirmTooltip(selectedRows.value, receivableData.value, getRowKey);
  });
  // 批量确认应收账款
  function batchConfirmReceivables() {
    const batchData = prepareBatchConfirmData(selectedRows.value, receivableData.value, getRowKey);
    if (!batchData) {
      return;
    }

    // 确保批量标识正确设置
    batchData.isBatch = true;

    // 设置选中的应收账款数据为批量数据
    selectedReceivableData.value = batchData;

    // 显示确认收款弹窗
    showConfirmReceiptDialog.value = true;
  }
  // 退回应收账款
  function returnReceivable(row) {
    // 创建响应式的退回原因变量
    const returnReason = ref('');

    dialog.warning({
      title: "退回确认",
      content: () => h('div', [
        h('p', { style: 'margin-bottom: 12px;' },
          '确定要退回该应收账款吗？退回后该记录状态将变为"已退回"。'
        ),
        h('p', {
          style: 'margin-bottom: 12px; color: #d03050; font-weight: 500; background-color: #fef2f2; padding: 8px; border-radius: 4px; border-left: 4px solid #d03050;'
        },
          '⚠️ 警告：与之相关的应收账款将全部被取消。'
        ),
        h('div', { style: 'margin-bottom: 8px; font-weight: 500;' }, '退回原因'),
        h(NInput, {
          required: true,
          value: returnReason.value,
          placeholder: '请输入退回原因（必填）',
          type: 'textarea',
          rows: 3,
          maxlength: 200,
          showCount: true,
          'onUpdate:value': (value) => {
            returnReason.value = value;
          }
        })
      ]),
      positiveText: "退回",
      negativeText: "取消",
      onPositiveClick: async () => {
        // 验证退回原因是否为空
        if (!returnReason.value || returnReason.value.trim() === '') {
          messages.error('请输入退回原因');
          return false; // 阻止对话框关闭
        }

        try {
          // 获取原有的收款摘要
          const originalSummary = row.receivableSummary || '';

          // 构造新的收款摘要：原摘要/退回原因：具体原因
          const newSummary = originalSummary
            ? `${originalSummary}/退回原因：${returnReason.value.trim()}`
            : `退回原因：${returnReason.value.trim()}`;

          // 调用API更新应收账款状态为已退回
          const updateData = {
            id: row.id,
            billStatus: 'RETURNED',
            receivableSummary: newSummary
          };
          await arbApi.updateArb(row.id, updateData);

          messages.success(`应收账款 ${row.feeName || row.feeId} 已退回`);
          // 重新加载数据
          refreshData();
        } catch (error) {
          console.error("退回应收账款失败:", error);
          messages.error("退回应收账款失败");
          return false; // 阻止对话框关闭
        }
      },
    });
  }
  // 表格列配置
  const columns = createAccountsReceivableColumns({
    onOrderClick: handleOrderClick,
    onVinClick: handleVinClick,
    confirmPayment,
    returnReceivable
  });
  // 机构选择相关方法
  const handleOrgSelect = (orgs) => {
    selectedOrgs.value = orgs;
    // 将选中的机构id以逗号分隔的形式设置到搜索参数中
    searchParams.receivableOrgs = orgs.map(org => org.id).join(',');
    // 触发搜索
    searchData();
  };
  const handleOrgCancel = () => {
    showOrgSelector.value = false;
  };
  const clearOrgSelection = () => {
    selectedOrgs.value = [];
    searchParams.receivableOrgs = "";
    searchData();
  };
  const removeOrg = (orgToRemove) => {
    selectedOrgs.value = selectedOrgs.value.filter(org => org.id !== orgToRemove.id);
    searchParams.receivableOrgs = selectedOrgs.value.map(org => org.id).join(',');
    searchData();
  };
  // 应收科目选择相关方法
  const handleSubjectSelect = (subjects) => {
    selectedSubjects.value = subjects;
    // 将选中的科目id以逗号分隔的形式设置到搜索参数中
    searchParams.feeIds = subjects.map(subject => subject.id).join(',');
    // 触发搜索
    searchData();
  };
  const clearSubjectSelection = () => {
    selectedSubjects.value = [];
    searchParams.feeIds = "";
    searchData();
  };
  const removeSubject = (subjectToRemove) => {
    selectedSubjects.value = selectedSubjects.value.filter(subject => subject.id !== subjectToRemove.id);
    searchParams.feeIds = selectedSubjects.value.map(subject => subject.id).join(',');
    searchData();
  };
  // 监听窗口大小变化
  const handleResize = () => {
    windowHeight.value = window.innerHeight;
  };
  // 生命周期钩子
  onMounted(() => {
    fetchData();

    // 初始化窗口高度
    windowHeight.value = window.innerHeight;

    window.addEventListener('resize', handleResize);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  return {
    // 状态变量
    tableRef,
    loading,
    selectedRows,
    receivableData,
    tableMaxHeight,

    // 弹窗相关
    receivableDialogVisible,
    receivableDialogRef,
    receivableSaving,
    receivableForm,

    // 选项数据
    amountInChinese,

    // 搜索相关
    searchParams,
    dateRange,
    customDateRange,
    statusOptions,
    dateRangeOptions,

    // 机构选择相关
    showOrgSelector,
    selectedOrgs,
    selectedOrgText,

    // 应收科目选择相关
    showSubjectSelector,
    selectedSubjects,
    selectedSubjectIds,
    selectedSubjectText,

    // 弹窗相关
    showOrderSummaryModal,
    showOrderDetailModal,
    showVehicleTrackingModal,
    selectedOrderId,
    selectedVin,

    // 确认收款弹窗相关
    showConfirmReceiptDialog,
    confirmReceiptSaving,
    selectedReceivableData,

    // 分页配置
    pagination,

    // 表格配置
    columns,

    // 计算属性
    batchConfirmEnabled,
    batchConfirmTooltip,

    // 方法
    refreshData,
    searchData,
    handleDateRangeChange,
    handleCheck,
    handleClearKeywords,

    showAddDialog,
    saveReceivable,
    getRowKey,
    batchConfirmReceivables,
    returnReceivable,
    handleOrderClick,
    handleVinClick,
    handleViewOrderDetail,
    handleConfirmReceipt,

    // 机构选择相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 应收科目选择相关方法
    handleSubjectSelect,
    clearSubjectSelection,
    removeSubject,

    // 图标组件
    RefreshOutline,
    AddCircleOutline,
    CheckmarkCircleOutline,
    CopyOutline,
    CloseCircleOutline,
    DocumentTextOutline,
    Building,

    // 组件
    BizOrgSelector,
    FeeSubjectSelector,
    OrderSummaryModal,
    OrderDetailModal,
    VehicleTrackingModal,
    ReceivableDialog,
    ConfirmReceiptDialog,
  };
}
