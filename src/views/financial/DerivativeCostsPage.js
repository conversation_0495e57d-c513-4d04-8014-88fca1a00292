import { ref, reactive, computed, h, nextTick } from 'vue'
import { NTag, NInputNumber, NTooltip, NIcon, useDialog } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  CreateOutline,
  InformationCircleOutline,
  CopyOutline,
  CloseOutline,
} from '@vicons/ionicons5'
import { Building } from '@vicons/tabler'
import messages from '@/utils/messages'
import { derivativeCostsApi } from '@/api/derivativeCosts'

export default function useDerivativeCostsPage() {
  // 初始化对话框
  const dialog = useDialog()
  window.$dialog = dialog

  // 状态变量
  const tableRef = ref(null)
  const loading = ref(false)
  const editableRowKeys = ref([]) // 当前可编辑的行的key集合
  const originalRowData = ref({}) // 存储编辑前的原始行数据
  const selectedRowKeys = ref([]) // 选中的行的key集合

  // 窗口尺寸响应式变量
  const windowHeight = ref(window.innerHeight)
  const windowWidth = ref(window.innerWidth)

  // 计算表格最大高度 - 根据数据条数动态计算，预留足够空间
  const tableMaxHeight = computed(() => {
    const screenHeight = windowHeight.value
    const pagepadding = 32 // 页面上下padding
    const filterHeight = 120 // 筛选区域高度
    const toolbarHeight = 60 // 工具栏高度
    const paginationHeight = 60 // 分页区域高度
    const margin = 60 // 增加额外边距

    // 计算基础可用高度
    const baseAvailableHeight = screenHeight - pagepadding - filterHeight - toolbarHeight - paginationHeight - margin

    // 根据数据计算所需高度（假设全部展开的最大情况）
    const dataCount = derivativeCostsData.value.length
    const rowHeight = 50 // 增加每行高度，考虑实际渲染可能比预期高
    const headerHeight = 50 // 增加表头高度
    const childRowsPerParent = 9 // 每个父行有9个子行

    // 计算最大可能的行数（全部展开）
    const maxPossibleRows = dataCount + (dataCount * childRowsPerParent)
    const maxCalculatedHeight = headerHeight + (maxPossibleRows * rowHeight) + 120 // 增加缓冲区到120px

    // 返回较小值，确保不超过屏幕高度，但至少保证能显示当前数据
    return Math.min(baseAvailableHeight, Math.max(maxCalculatedHeight, 500))
  })

  // 确认状态选项 - 从字典数据动态加载
  const confirmStatusOptions = ref([
    { label: '不限', value: null }
  ])

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)
  const selectedOrgs = ref([])

  // 筛选表单
  const filterForm = reactive({
    keywords: '',
    salesOrgIds: [], // 销售单位ID数组
    confirmed: null // 确认状态
  })

  // 数据列表
  const derivativeCostsData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false
  })

  // 处理展开行键变化
  const handleExpand = (keys) => {
    expandedKeys.value = keys
  }

  // 获取行的唯一标识
  const getRowKey = (row) => {
    if (row.isChild) {
      return `child-${row.parentId}-${row.itemType}`
    }
    return row.id
  }

  // 计算筛选后的数据 - 转换为树形结构
  const filteredData = computed(() => {
    return derivativeCostsData.value.map(order => {
      // 创建所有可能的子行数据
      const allChildren = [
        {
          id: `${order.id}-notary`,
          parentId: order.id,
          isChild: true,
          itemType: 'notary',
          itemName: '公证费',
          income: order.notaryFeeIncome || 0,
          cost: order.notaryFeeCost || 0
        },
        {
          id: `${order.id}-carefree`,
          parentId: order.id,
          isChild: true,
          itemType: 'carefree',
          itemName: '畅行无忧',
          income: order.carefreeIncome || 0,
          cost: order.carefreeCost || 0
        },
        {
          id: `${order.id}-warranty`,
          parentId: order.id,
          isChild: true,
          itemType: 'warranty',
          itemName: '延保收入',
          income: order.extendedWarrantyIncome || 0,
          cost: order.extendedWarrantyCost || 0
        },
        {
          id: `${order.id}-vps`,
          parentId: order.id,
          isChild: true,
          itemType: 'vps',
          itemName: 'VPS收入',
          income: order.vpsIncome || 0,
          cost: order.vpsCost || 0
        },
        {
          id: `${order.id}-interest`,
          parentId: order.id,
          isChild: true,
          itemType: 'interest',
          itemName: '预收利息',
          income: order.preInterestIncome || 0,
          cost: order.preInterestCost || 0
        },
        {
          id: `${order.id}-license`,
          parentId: order.id,
          isChild: true,
          itemType: 'license',
          itemName: '牌照费',
          income: order.licensePlateFeeIncome || 0,
          cost: order.licensePlateFeeCost || 0
        },
        {
          id: `${order.id}-temp`,
          parentId: order.id,
          isChild: true,
          itemType: 'temp',
          itemName: '临牌费',
          income: order.tempPlateFeeIncome || 0,
          cost: order.tempPlateFeeCost || 0
        },
        {
          id: `${order.id}-equipment`,
          parentId: order.id,
          isChild: true,
          itemType: 'equipment',
          itemName: '交车装备',
          income: order.deliveryEquipmentIncome || 0,
          cost: order.deliveryEquipmentCost || 0
        },
        {
          id: `${order.id}-other`,
          parentId: order.id,
          isChild: true,
          itemType: 'other',
          itemName: '其他收入',
          income: order.otherIncome || 0,
          cost: order.otherCost || 0
        }
      ]

      // 过滤掉收入为0的子行
      const children = allChildren.filter(child => (child.income || 0) > 0)

      // 计算总收入和总成本
      const totalIncome = children.reduce((sum, child) => sum + (child.income || 0), 0)
      const totalCost = children.reduce((sum, child) => sum + (child.cost || 0), 0)

      return {
        ...order,
        totalIncome,
        totalCost,
        children
      }
    })
  })

  // 计算已选择的机构文本
  const selectedOrgText = computed(() => {
    if (selectedOrgs.value.length === 0) {
      return '选择销售单位'
    }
    if (selectedOrgs.value.length === 1) {
      return selectedOrgs.value[0].name || selectedOrgs.value[0].orgName
    }
    return `已选择 ${selectedOrgs.value.length} 个单位`
  })

  // 展开的行键
  const expandedKeys = ref([])

  // 表格列配置
  const columns = [
    {
      type: 'selection',
      width: 50,
      align: 'center',
      disabled: (row) => {
        // 已确认状态的数据不可选择，或者是子行
        return row.confirmed === true || row.isChild
      }
    },
    {
      title: () => h('div', { style: { display: 'flex', alignItems: 'center', gap: '4px' } }, [
        h('span', '销售订单号'),
        h(NTooltip, {
          trigger: 'hover',
          placement: 'top'
        }, {
          trigger: () => h(NIcon, {
            size: 16,
            style: { color: '#909399', cursor: 'help' }
          }, {
            default: () => h(InformationCircleOutline)
          }),
          default: () => '点击左侧箭头可以展开该订单的衍生收入明细'
        })
      ]),
      key: 'orderSn',
      align: 'left',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        // 子行不显示订单号
        if (row.isChild) return ''

        // 复制到剪贴板的函数
        const copyToClipboard = async (text) => {
          try {
            await navigator.clipboard.writeText(text)
            messages.success('订单号已复制到剪贴板')
          } catch (err) {
            console.error('复制失败:', err)
            messages.error('复制失败')
          }
        }

        return h('div', { style: { display: 'flex', alignItems: 'center', gap: '4px' } }, [
          h('span', row.orderSn || '-'),
          row.orderSn ? h(NIcon, {
            size: 16,
            class: 'copy-icon',
            style: { color: '#18a058', cursor: 'pointer' },
            onClick: (e) => {
              e.stopPropagation()
              copyToClipboard(row.orderSn)
            }
          }, {
            default: () => h(CopyOutline)
          }) : null
        ])
      }
    },
    {
      title: 'VIN',
      key: 'vin',
      align: 'center',

      ellipsis: {
        tooltip: true
      },
      render(row) {
        // 子行不显示VIN码
        if (row.isChild) return ''
        return row.vin || '-'
      }
    },

    {
      title: '科目名称',
      key: 'subjectName',
      align: 'center',
      render(row) {
        if (row.isChild) {
          // 子行显示具体项目名称
          return row.itemName || '-'
        }
        // 父行显示小计
        return '小计'
      }
    },
    {
      title: '衍生收入(元)',
      key: 'derivativeIncome',
      width: 150,
      align: 'center',
      render(row) {
        if (row.isChild) {
          // 子行显示收入金额
          const income = (row.income || 0) / 100
          return `¥${income.toFixed(2)}`
        }
        // 父行显示总收入
        const totalIncome = (row.totalIncome || 0) / 100
        return `¥${totalIncome.toFixed(2)}`
      }
    },
    {
      title: () => h('div', { style: { display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px', height: '100%' } }, [
        h('span', '衍生成本(元)'),
        h(NTooltip, {
          trigger: 'hover',
          placement: 'top'
        }, {
          trigger: () => h(NIcon, {
            size: 16,
            style: { color: '#909399', cursor: 'help', display: 'flex', alignItems: 'center' }
          }, {
            default: () => h(InformationCircleOutline)
          }),
          default: () => '点击编辑衍生成本，失去焦点自动保存，按TAB/回车键切换到下一字段，按ESC键取消编辑'
        })
      ]),
      key: 'derivativeCost',
      align: 'center',
      render(row) {
        if (row.isChild) {
          // 检查父行是否已确认
          const parentRow = derivativeCostsData.value.find(item => item.id === row.parentId)
          const isConfirmed = parentRow && parentRow.confirmed === true

          // 子行显示可编辑的成本输入框
          const isEditing = editableRowKeys.value.includes(row.id)
          if (isEditing) {
            // 获取原始值用于比较
            const originalData = originalRowData.value[row.id]
            const originalCost = originalData ? originalData.cost : row.cost

            return h('div', {
              'data-row-id': row.id,
              style: { width: '100%' }
            }, [
              h(NInputNumber, {
                value: row.cost ? row.cost / 100 : 0,
                precision: 2,
                min: 0,
                step: 0.01,
                placeholder: '输入成本',
                size: 'small',
                buttonPlacement: 'both',
                style: { width: '100%' },
                onUpdateValue: (value) => {
                  row.cost = value ? Math.round(value * 100) : 0
                },
                onKeydown: (e) => {
                  // 监听回车键和TAB键，都执行相同的导航操作
                  if (e.key === 'Enter' || e.key === 'Tab') {
                    e.preventDefault()
                    handleTabNavigation(row, originalCost)
                  }
                  // 监听ESC键，取消编辑并恢复原值
                  if (e.key === 'Escape') {
                    e.preventDefault()
                    cancelEdit(row)
                  }
                },
                onBlur: () => {
                  // 失去焦点时自动保存（如果值发生了变化）
                  saveRowIfChanged(row, originalCost)
                }
              })
            ])
          }

          const costValue = row.cost ? (row.cost / 100).toFixed(2) : '0.00'

          // 如果已确认，显示普通文本
          if (isConfirmed) {
            return `¥${costValue}`
          }

          // 未确认状态，显示可点击的带虚线下划线的文本
          return h('span', {
            class: 'editable-cost-text',
            onClick: () => editRow(row)
          }, `¥${costValue}`)
        }
        // 父行显示总成本
        const totalCost = (row.totalCost || 0) / 100
        return `¥${totalCost.toFixed(2)}`
      }
    },

    {
      title: '确认状态',
      key: 'confirmStatus',
      width: 120,
      align: 'center',
      render(row) {
        // 子行不显示确认状态
        if (row.isChild) return ''

        const isConfirmed = row.confirmed === true
        return h(NTag, {
          type: isConfirmed ? 'success' : 'warning',
          size: 'small'
        }, {
          default: () => isConfirmed ? '已确认' : '未确认'
        })
      }
    },
    {
      title: '经办单位',
      key: 'handlingOrg',
      width: 150,
      align: 'center',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        // 子行不显示经办单位
        if (row.isChild) return ''
        return row.editorOrgName || row.handlingOrgName || row.bizOrgName || '-'
      }
    },
    {
      title: '经办人',
      key: 'handlingPerson',
      width: 120,
      align: 'center',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        // 子行不显示经办人
        if (row.isChild) return ''
        return row.editorName || row.handlingPersonName || row.agentName || '-'
      }
    }
  ]

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 添加销售单位筛选
      if (filterForm.salesOrgIds.length > 0) {
        params.editorOrgs = filterForm.salesOrgIds.join(',')
      }

      // 添加确认状态筛选
      if (filterForm.confirmed !== null) {
        params.confirmed = filterForm.confirmed
      }

      const response = await derivativeCostsApi.getDerivativeCostsList(params)

      if (response.code === 200) {
        derivativeCostsData.value = response.data.list || []
        pagination.itemCount = response.data.total || 0
      } else {
        messages.error(response.message || '获取衍生费用数据失败')
      }
    } catch (error) {
      console.error('获取衍生费用数据失败:', error)
      messages.error('获取衍生费用数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理搜索
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 编辑行
  const editRow = (row) => {
    const rowKey = row.id

    // 如果当前已有其他行在编辑状态，先清除所有编辑状态
    if (editableRowKeys.value.length > 0) {
      // 清除所有编辑状态，不保存数据
      editableRowKeys.value.forEach(existingKey => {
        // 恢复原始数据
        const originalData = originalRowData.value[existingKey]
        if (originalData) {
          // 找到对应的行数据并恢复
          const restoreRow = findRowByKey(existingKey)
          if (restoreRow) {
            Object.assign(restoreRow, originalData)
          }
          delete originalRowData.value[existingKey]
        }
      })
      // 清空编辑状态数组
      editableRowKeys.value = []
    }

    // 在编辑前保存原始数据的深拷贝，用于取消编辑时恢复
    originalRowData.value[rowKey] = JSON.parse(JSON.stringify(row))

    // 设置为编辑状态
    editableRowKeys.value.push(rowKey)
  }

  // 根据行键查找行数据的辅助函数
  const findRowByKey = (key) => {
    for (const order of filteredData.value) {
      // 检查父行
      if (order.id === key) {
        return order
      }
      // 检查子行
      if (order.children) {
        for (const child of order.children) {
          if (child.id === key) {
            return child
          }
        }
      }
    }
    return null
  }

  // 取消编辑
  const cancelEdit = (row) => {
    const rowKey = row.id
    const originalData = originalRowData.value[rowKey]

    if (originalData) {
      // 恢复原始数据
      Object.assign(row, originalData)
      delete originalRowData.value[rowKey]
    }

    // 移除编辑状态
    const index = editableRowKeys.value.indexOf(rowKey)
    if (index > -1) {
      editableRowKeys.value.splice(index, 1)
    }
  }

  // 处理TAB键导航
  const handleTabNavigation = async (currentRow, originalCost) => {
    if (!currentRow.isChild) return

    // 先保存当前行（如果有变化）
    const currentCost = currentRow.cost || 0
    const originalValue = originalCost || 0

    if (currentCost !== originalValue) {
      // 值发生了变化，保存当前行
      await saveRow(currentRow)
    } else {
      // 值没有变化，直接清除当前编辑状态
      const index = editableRowKeys.value.indexOf(currentRow.id)
      if (index > -1) {
        editableRowKeys.value.splice(index, 1)
      }
      delete originalRowData.value[currentRow.id]
    }

    // 查找同一订单下的所有可编辑子行
    const parentOrder = filteredData.value.find(order => order.id === currentRow.parentId)
    if (!parentOrder || !parentOrder.children) return

    // 获取所有可编辑的子行（未确认状态的父订单的子行）
    const editableChildren = parentOrder.children.filter(child => {
      const parentRow = derivativeCostsData.value.find(item => item.id === child.parentId)
      return parentRow && parentRow.confirmed !== true
    })

    if (editableChildren.length === 0) return

    // 找到当前行在可编辑子行中的索引
    const currentIndex = editableChildren.findIndex(child => child.id === currentRow.id)
    if (currentIndex === -1) return

    // 计算下一个要编辑的行的索引（循环）
    const nextIndex = (currentIndex + 1) % editableChildren.length
    const nextRow = editableChildren[nextIndex]

    // 设置下一行为编辑状态
    setTimeout(() => {
      editRow(nextRow)
      // 使用nextTick确保DOM更新后再聚焦
      nextTick(() => {
        // 查找对应的输入框并聚焦
        const inputElement = document.querySelector(`[data-row-id="${nextRow.id}"] input`)
        if (inputElement) {
          inputElement.focus()
          inputElement.select() // 选中所有文本，方便用户直接输入
        }
      })
    }, 100) // 给一点延迟确保保存操作完成
  }

  // 检查值是否发生变化，如果变化则保存
  const saveRowIfChanged = async (row, originalCost) => {
    if (!row.isChild) return

    // 比较当前值与原始值
    const currentCost = row.cost || 0
    const originalValue = originalCost || 0

    // 如果值没有变化，直接退出编辑状态，不发送请求
    if (currentCost === originalValue) {
      // 移除编辑状态
      const index = editableRowKeys.value.indexOf(row.id)
      if (index > -1) {
        editableRowKeys.value.splice(index, 1)
      }
      // 清除原始数据
      delete originalRowData.value[row.id]
      return
    }

    // 值发生了变化，调用保存函数
    await saveRow(row)
  }

  // 保存行
  const saveRow = async (row) => {
    if (!row.isChild) return

    try {
      loading.value = true

      // 根据子行类型确定要更新的字段
      const updateData = {}
      const parentId = row.parentId

      switch (row.itemType) {
        case 'notary':
          updateData.notaryFeeCost = row.cost || 0
          break
        case 'carefree':
          updateData.carefreeCost = row.cost || 0
          break
        case 'warranty':
          updateData.extendedWarrantyCost = row.cost || 0
          break
        case 'vps':
          updateData.vpsCost = row.cost || 0
          break
        case 'interest':
          updateData.preInterestCost = row.cost || 0
          break
        case 'license':
          updateData.licensePlateFeeCost = row.cost || 0
          break
        case 'temp':
          updateData.tempPlateFeeCost = row.cost || 0
          break
        case 'equipment':
          updateData.deliveryEquipmentCost = row.cost || 0
          break
        case 'other':
          updateData.otherCost = row.cost || 0
          break
        default:
          messages.warning('未知的项目类型')
          return
      }

      const response = await derivativeCostsApi.updateDerivativeCosts(parentId, updateData)

      if (response.code === 200) {
        messages.success('保存成功')

        // 移除编辑状态
        const index = editableRowKeys.value.indexOf(row.id)
        if (index > -1) {
          editableRowKeys.value.splice(index, 1)
        }

        // 清除原始数据
        delete originalRowData.value[row.id]

        // 刷新数据
        await refreshData()
      } else {
        messages.error(response.message || '保存失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      messages.error('保存失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    selectedOrgs.value = orgs
    filterForm.salesOrgIds = orgs.map(org => org.id)
    showOrgSelector.value = false
    handleSearch()
  }

  // 取消机构选择
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 移除机构
  const removeOrg = (orgId) => {
    selectedOrgs.value = selectedOrgs.value.filter(org => org.id !== orgId)
    filterForm.salesOrgIds = selectedOrgs.value.map(org => org.id)
    handleSearch()
  }

  // 清除所有机构选择
  const clearOrgSelection = () => {
    selectedOrgs.value = []
    filterForm.salesOrgIds = []
    handleSearch()
  }

  // 处理行选择变化
  const handleSelectionChange = (keys) => {
    selectedRowKeys.value = keys
  }

  // 确认状态
  const confirmStatus = async () => {
    if (selectedRowKeys.value.length === 0) {
      messages.warning('请选择要确认的记录')
      return
    }

    // 过滤掉已经是确认状态的记录
    const validKeys = selectedRowKeys.value.filter(key => {
      const row = derivativeCostsData.value.find(item => item.id === key)
      return row && row.confirmed !== true
    })

    if (validKeys.length === 0) {
      messages.warning('所选记录均已确认，无需重复操作')
      return
    }

    dialog.warning({
      title: '确认状态',
      content: `确定要将选中的 ${validKeys.length} 条记录标记为已确认吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          loading.value = true

          // 批量更新确认状态
          const promises = validKeys.map(id =>
            derivativeCostsApi.updateConfirmStatus(id, true)
          )

          await Promise.all(promises)

          messages.success(`成功确认 ${validKeys.length} 条记录`)
          selectedRowKeys.value = []
          await refreshData()
        } catch (error) {
          console.error('确认状态失败:', error)
          messages.error('确认状态失败，请稍后重试')
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 初始化
  const initialize = () => {
    // 监听窗口大小变化
    const handleResize = () => {
      windowHeight.value = window.innerHeight
      windowWidth.value = window.innerWidth
    }
    window.addEventListener('resize', handleResize)

    // 加载确认状态选项
    loadConfirmStatusOptions()

    // 初始加载数据
    refreshData()
  }

  // 清理
  const cleanup = () => {
    const handleResize = () => {
      windowHeight.value = window.innerHeight
      windowWidth.value = window.innerWidth
    }
    window.removeEventListener('resize', handleResize)
  }

  // 加载确认状态选项
  const loadConfirmStatusOptions = () => {
    try {
      confirmStatusOptions.value = [
        { label: '不限', value: null },
        { label: '未确认', value: false },
        { label: '已确认', value: true }
      ]
    } catch (error) {
      console.error('加载确认状态选项失败:', error)
    }
  }

  return {
    // 图标
    SearchOutline,
    RefreshOutline,
    CreateOutline,
    CloseOutline,
    Building,

    // 响应式数据
    tableRef,
    loading,
    editableRowKeys,
    confirmStatusOptions,
    filterForm,
    derivativeCostsData,
    pagination,
    windowHeight,
    windowWidth,
    showOrgSelector,
    selectedOrgs,
    selectedRowKeys,
    expandedKeys,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    selectedOrgText,

    // 业务方法
    refreshData,
    handleSearch,
    handlePageChange,
    handlePageSizeChange,
    editRow,
    cancelEdit,
    saveRow,
    saveRowIfChanged,
    handleTabNavigation,
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,
    handleSelectionChange,
    confirmStatus,
    handleExpand,
    getRowKey,
    findRowByKey,

    // 生命周期方法
    initialize,
    cleanup
  }
}
