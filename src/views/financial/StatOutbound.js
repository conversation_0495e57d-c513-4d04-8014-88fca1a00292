import { ref, reactive, computed, h } from 'vue'
import messages from '@/utils/messages'
import outboundApi from '@/api/outbound'
import { formatDate } from '@/utils/dateUtils'

import { NIcon, NTag } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  CopyOutline,
  DownloadOutline
} from '@vicons/ionicons5'
import { Building } from '@vicons/tabler'
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate
} from '@/utils/dateRange'
import OrderDetailModal from '@/components/orders/OrderDetailModal.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'

export default function useStatOutbound() {
  // 状态变量
  const tableRef = ref(null)
  const loading = ref(false)
  const exporting = ref(false)

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailId = ref(null)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于虚拟滚动（无页面滚动模式）
  const tableMaxHeight = computed(() => {
    // 根据屏幕尺寸动态调整各组件高度
    const screenHeight = windowHeight.value

    // 基础组件高度
    const pagepadding = 32 // 页面上下padding
    let filterHeight, toolbarHeight, paginationHeight, margin

    // 根据屏幕高度动态调整
    if (screenHeight >= 1080) {
      // 大屏幕 (24寸等)
      filterHeight = 180
      toolbarHeight = 70
      paginationHeight = 60
      margin = 20
      return 800
    } else if (screenHeight >= 768) {
      // 中等屏幕
      filterHeight = 160
      toolbarHeight = 60
      paginationHeight = 50
      margin = 15
      return 360
    } else {
      // 小屏幕
      filterHeight = 140
      toolbarHeight = 50
      paginationHeight = 40
      margin = 10
    }

    // 计算表格容器的可用高度（不包括分页组件）
    const containerHeight =
      screenHeight -
      pagepadding -
      filterHeight -
      toolbarHeight -
      margin

    // 表格本身的高度需要减去分页组件的高度
    const tableHeight = containerHeight - paginationHeight

    // 动态最小高度和最大高度
    const minHeight = Math.min(250, screenHeight * 0.25)
    const maxHeight = screenHeight * 0.6 // 表格最大不超过屏幕高度的60%

    const finalHeight = Math.max(Math.min(tableHeight, maxHeight), minHeight)

    return finalHeight
  })

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组以支持多选
    minAmount: null,
    maxAmount: null,
    keywords: ''
  })

  // 数据列表
  const ordersData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false
  })

  // 窗口大小变化处理
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  // 表格数据
  const filteredData = computed(() => {
    return ordersData.value
  })

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`
      }
    }
    return "选择出库单位"
  })

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch((err) => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  // 表格列配置
  const columns = [
    {
      title: 'VIN',
      key: 'vin',
      width: 250,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              cursor: 'pointer',
              transition: 'color 0.2s'
            },
            onClick: () => copyToClipboard(row.vin),
            title: '点击复制车架号',
            onMouseenter: (e) => {
              e.target.style.color = 'var(--primary-color)'
            },
            onMouseleave: (e) => {
              e.target.style.color = ''
            }
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.vin),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '出库时间',
      key: 'outboundTime',
      render(row) {
        return row.outboundTime ? formatDate(row.outboundTime, 'YYYY-MM-DD HH:mm:ss') : '-'
      }
    },
    {
      title: '订单日期',
      key: 'orderDate',
      render(row) {
        return row.orderDate ? formatDate(row.orderDate, 'YYYY-MM-DD HH:mm:ss') : '-'
      }
    },
    {
      title: '出库经办人',
      key: 'outboundAgentName',
      render(row) {
        return row.outboundAgentName || '-'
      }
    },
    {
      title: '销售单位',
      key: 'salesOrgName'
    },
    {
      title: '销售顾问',
      key: 'salesAgentName'
    },
    {
      title: '客户名称',
      key: 'customerName'
    },
    {
      title: '联系电话',
      key: 'mobile',
      width: 240,
      render(row) {
        // 手机号脱敏处理：隐藏中间四位，以****代替
        if (!row.mobile) return ''
        if (row.mobile.length === 11) {
          return row.mobile.substring(0, 3) + '****' + row.mobile.substring(7)
        }
        return row.mobile
      }
    },
    {
      title: '品牌',
      key: 'brand'
    },
    {
      title: '车系',
      key: 'series',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '配置',
      key: 'configName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '颜色',
      key: 'colorCode',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '订单号',
      key: 'orderSn',
      width: 220,
      render(row) {
        return h(
          'div',
          {
            style: {
              alignItems: 'center',
              display: 'flex',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              cursor: 'pointer',
              transition: 'color 0.2s'
            },
            onClick: () => copyToClipboard(row.orderSn),
            title: '点击复制订单号',
            onMouseenter: (e) => {
              e.target.style.color = 'var(--primary-color)'
            },
            onMouseleave: (e) => {
              e.target.style.color = ''
            }
          },
          [
            h('span', {
              style: {
                marginRight: '4px'
              }
            }, row.orderSn),
            h(
              NIcon,
              {
                size: 16,
                color: 'var(--primary-color)',
                style: {
                  opacity: 0.8,
                  transition: 'opacity 0.2s',
                  flexShrink: 0
                }
              },
              { default: () => h(CopyOutline) }
            )
          ]
        )
      }
    },
    {
      title: '折旧金额(元)',
      key: 'depreciationAmount',
      align: 'center',
      render(row) {
        if (!row.depreciationAmount && row.depreciationAmount !== 0) return '-'
        // 数据是分为单位，需要除以100转换为元
        return `${(row.depreciationAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '厂家支持(元)',
      key: 'manufacturerSupportAmount',
      align: 'center',
      render(row) {
        if (!row.manufacturerSupportAmount && row.manufacturerSupportAmount !== 0) return '-'
        // 数据是分为单位，需要除以100转换为元
        return `${(row.manufacturerSupportAmount / 100).toFixed(2)}`
      }
    }
  ]

  // 表格横向滚动宽度 - 参考官方demo设置
  const scrollX = computed(() => {
    // 按照官方demo的方式，设置一个合适的滚动宽度
    // 由于这个页面字段较多，设置较大的滚动宽度
    return 3000
  })

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.brand = filterForm.vehicleCategory
      }

      // 处理出库单位 - 支持多选，使用机构代码的逗号分隔格式
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        // 使用机构id作为机构代码，以逗号分隔的格式传入
        params.outbound_org_code = filterForm.invoiceOrgs
          .map((org) => org.id)
          .join(",")
      }

      // 始终查询已出库状态的数据
      params.outboundStatus = 'COMPLETED'

      // 调用API获取数据
      const response = await outboundApi.getOutboundList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        ordersData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages
          refreshData()
          return
        }
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 处理查看
  const handleView = (id) => {
    currentDetailId.value = id
    detailDialogVisible.value = true
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理页面大小变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs]
      handleSearch()
    }
  }

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = []
    handleSearch()
  }

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.invoiceOrgs = filterForm.invoiceOrgs.filter(
      (org) => org.id !== orgToRemove.id
    )
    handleSearch()
  }

  // 导出数据
  const handleExport = async () => {
    exporting.value = true
    try {
      // 构建查询参数，与refreshData方法保持一致
      const params = {}

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.vehicleCategory = filterForm.vehicleCategory
      }

      // 处理机构筛选
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        params.invoiceOrgIds = filterForm.invoiceOrgs.map(org => org.id)
      }

      // 始终查询已出库状态的数据
      params.outboundStatus = 'COMPLETED'

      // 调用导出API
      const response = await outboundApi.exportVehicleOutbound(params)

      if (response.code === 200 && response.data && response.data.file) {
        // 创建下载链接
        const link = document.createElement('a')
        link.href = response.data.file
        link.target = '_blank'
        link.download = `车辆出库数据_${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        messages.success('导出成功')
      } else {
        messages.error(response.message || '导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      messages.error('导出失败，请重试')
    } finally {
      exporting.value = false
    }
  }

  // 生命周期方法
  const initialize = async () => {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)
    // 初始化数据
    refreshData()
  }

  const cleanup = () => {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 组件引用
    OrderDetailModal,
    BizOrgSelector,

    // 图标
    SearchOutline,
    RefreshOutline,
    DownloadOutline,
    Building,

    // 响应式数据
    tableRef,
    loading,
    exporting,
    filterForm,
    ordersData,
    pagination,
    windowHeight,
    detailDialogVisible,
    currentDetailId,
    showOrgSelector,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    scrollX,
    selectedOrgText,

    // 日期相关
    dateRangeOptions,

    // 业务方法
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    handleView,
    handlePageChange,
    handlePageSizeChange,
    refreshData,
    handleExport,

    // 机构选择器相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 生命周期方法
    initialize,
    cleanup
  }
}
