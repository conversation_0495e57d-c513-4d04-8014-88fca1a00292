/**
 * 财务表格公共样式
 * 用于应收账款和应付账款页面的表格样式
 */

/* 表格容器样式 - PC端优化，为分页预留底部间距 */
.table-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 276px);
  /* 精确计算：16+140+60+52+8 = 276px (减少分页高度和底部间距) */
  min-height: 400px;
  flex: 1;
  /* 占用剩余空间 */
}

.data-table-wrapper {
  flex: 1;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  background: #fff;
  min-height: 0;
  /* 确保虚拟滚动容器有明确的高度 */
  display: flex;
  flex-direction: column;
  height: 100%;
  /* 确保容器占满可用高度 */
}

:deep(.n-data-table) {
  margin-top: 0;
  border-radius: 8px;
  height: 100% !important;
  /* 强制表格占满容器高度 */
}

/* 表格滚动控制 - 支持虚拟滚动 */
:deep(.n-data-table-wrapper) {
  overflow-x: auto !important;
  overflow-y: auto !important;
  /* 强制允许垂直滚动，配合虚拟滚动使用 */
  height: 100% !important;
  flex: 1;
  /* 确保包装器占满可用空间 */
}

:deep(.n-data-table-table) {
  min-width: 1350px;
  /* 设置最小宽度确保所有列都能显示 */
  table-layout: fixed;
  /* 固定表格布局，确保列宽度严格按照设置显示 */
}

/* 虚拟滚动容器优化 - 去掉内部滚动条，只保留外层滚动 */
:deep(.n-data-table-base-table-body) {
  /* 禁用内部滚动，使用外层wrapper的滚动 */
  overflow: visible !important;
}

/* 确保虚拟滚动的内容容器正确设置 */
:deep(.n-data-table-base-table-body .n-scrollbar) {
  height: 100% !important;
  overflow: visible !important;
}

:deep(.n-data-table-base-table-body .n-scrollbar-content) {
  height: 100% !important;
}

/* 确保表头始终可见 */
:deep(.n-data-table-thead) {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
}

/* 禁用内部组件的滚动条，使用全局滚动条样式 */
:deep(.n-data-table-base-table-body::-webkit-scrollbar) {
  display: none;
  /* 隐藏内部滚动条 */
}

:deep(.n-scrollbar::-webkit-scrollbar) {
  display: none;
  /* 隐藏NaiveUI内部滚动条 */
}

/* 确保只有外层wrapper显示滚动条 */
:deep(.n-data-table-base-table-body .n-scrollbar-rail) {
  display: none !important;
}

/* 分页容器样式 */
.pagination-container {
  padding: 8px 8px 4px 0;
  /* 减少padding高度，顶部、右侧、底部padding，左侧无padding */
  display: flex;
  justify-content: flex-end;
  /* 右对齐 */
  background: #fff;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
  /* 防止分页区域被压缩 */
}

/* 表格行样式 */
:deep(.n-data-table-tr) {
  transition: background-color 0.3s;
}

:deep(.n-data-table-tr:hover) {
  background-color: #f5f7fa;
}

/* 子行样式 */
:deep(.n-data-table-tr[data-row-key^="fee-"]) {
  background-color: #f9fbff;
}

:deep(.n-data-table-tr[data-row-key^="fee-"]:hover) {
  background-color: #f0f7ff;
}

/* 确保表头和内容对齐 */
:deep(.n-data-table-th .n-data-table-th__title) {
  display: flex;
  justify-content: center;
}

/* 金额列表头居中对齐 */
:deep(.n-data-table-th[data-col-key="feeAmount"] .n-data-table-th__title) {
  justify-content: center;
}

/* 确保所有单元格内容居中 */
:deep(.n-data-table-td .n-data-table-td__content) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 金额列居中对齐 */
:deep(.n-data-table-td[data-col-key="feeAmount"] .n-data-table-td__content) {
  justify-content: center;
}

/* 添加以下样式来增加图标之间的间距 */
:deep(.n-icon) {
  margin: 0 2px;
}

/* 操作列图标样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-icon) {
  font-size: 24px;
  margin: 0 4px;
  transition: transform 0.3s;
}

:deep(.n-data-table-td[data-col-key="actions"] .n-button:hover .n-icon) {
  transform: scale(1.2);
}

/* 为操作栏添加新的样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 6px !important;
  white-space: nowrap;
  /* 防止换行 */
}

/* 确保操作列有足够宽度，防止图标换行 */
:deep(.n-data-table-td[data-col-key="actions"]) {
  min-width: 140px !important;
  white-space: nowrap;
}

/* 确保标识列有足够宽度，防止文字换行 */
:deep(.n-data-table-th[data-col-key="id"]),
:deep(.n-data-table-td[data-col-key="id"]) {
  min-width: 100px !important;
  white-space: nowrap;
}

/* 表格单元格统一样式 */
:deep(.n-data-table-th) {
  background-color: #f9f9f9 !important;
  font-weight: 600;
  padding: 12px 8px;
}

:deep(.n-data-table-td) {
  padding: 10px 8px;
}

:deep(.n-button) {
  margin-right: 4px;
}

/* 修复虚拟滚动表格列之间的对齐问题 */
:deep(.n-data-table-base-table-header .n-data-table-th),
:deep(.n-data-table-base-table-body .n-data-table-td) {
  border-right: 1px solid var(--n-border-color);
  box-sizing: border-box;
}

/* 确保表格列严格按照设定宽度显示 */
:deep(.n-data-table .n-data-table-base-table) {
  width: 100% !important;
  table-layout: fixed;
  border-collapse: separate;
}

/* 优化按钮样式 */
:deep(.n-button.n-button--quaternary) {
  padding: 4px 12px;
  height: 32px;
  line-height: 24px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.n-data-table-td[data-col-key="actions"] .n-button) {
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-button.n-button--quaternary:hover) {
  background-color: rgba(24, 160, 88, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

:deep(.n-button.n-button--quaternary.n-button--primary:hover) {
  background-color: rgba(32, 128, 240, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* PC端响应式设计 - 针对1080P和2K优化，保持底部间距 */
@media (min-height: 1440px) {

  /* 2K及以上分辨率 */
  .table-container {
    height: calc(100vh - 276px);
    min-height: 600px;
  }
}

@media (min-height: 1080px) and (max-height: 1439px) {

  /* 1080P分辨率 */
  .table-container {
    height: calc(100vh - 276px);
    min-height: 450px;
  }
}

@media (max-height: 1079px) and (min-height: 1000px) {

  /* 中等分辨率 (1000px-1079px) */
  .table-container {
    height: calc(100vh - 260px);
    min-height: 400px;
  }
}

@media (max-height: 999px) and (min-height: 800px) {

  /* 14寸高分屏等中小屏幕 (800px-999px) */
  .table-container {
    height: calc(100vh - 250px);
    /* 适度减少固定区域高度 */
    min-height: 350px;
    /* 适合14寸屏幕的最小高度 */
  }
}

@media (max-height: 799px) {

  /* 小屏幕分辨率 (13寸低分屏等) */
  .table-container {
    height: calc(100vh - 240px);
    /* 进一步减少固定区域高度 */
    min-height: 300px;
    /* 降低最小高度以适应小屏幕 */
  }
}

/* 宽屏优化 */
@media (min-width: 1920px) {
  :deep(.n-data-table-table) {
    min-width: 1350px;
    /* 2K宽屏保持标准宽度 */
  }
}

@media (max-width: 1600px) {
  :deep(.n-data-table-table) {
    min-width: 1350px;
    /* 较小宽屏保持标准宽度 */
  }
}