<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="新增试驾车"
    style="width: 800px"
    :mask-closable="false"
  >
    <div class="add-trial-vehicle-form">
      <!-- 车辆信息区域 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">车辆信息</span>
          <div style="flex: 1"></div>
          <n-button
            type="primary"
            @click="handleVehicleSelectorClick"
            class="title-button"
            size="small"
          >
            <template #icon>
              <n-icon>
                <component :is="AddOutlineIcon" />
              </n-icon>
            </template>
            选择车辆
          </n-button>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <n-grid :cols="4" :x-gap="16" :y-gap="1">
          <n-grid-item>
            <n-form-item label="VIN码">
              <n-input
                v-model:value="formData.vin"
                placeholder="请选择车辆自动填充"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车辆品牌">
              <n-input
                v-model:value="formData.brand"
                placeholder="请选择车辆自动填充"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车型系列">
              <n-input
                v-model:value="formData.series"
                placeholder="请选择车辆自动填充"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="配置名称">
              <n-input
                v-model:value="formData.configName"
                placeholder="请选择车辆自动填充"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车辆颜色">
              <n-input
                v-model:value="formData.color"
                placeholder="请选择车辆自动填充"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="所属单位">
              <n-input
                v-model:value="formData.ownerOrgName"
                placeholder="请选择车辆自动填充"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="标记日期" required>
              <n-date-picker
                v-model:value="formData.markDate"
                type="date"
                clearable
                style="width: 100%"
                value-format="timestamp"
                placeholder="请选择标记日期"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <!-- 空白占位 -->
          </n-grid-item>
        </n-grid>
      </div>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="
            !formData.stockId || !formData.markDate || !formData.ownerOrgName
          "
        >
          确定
        </n-button>
      </n-space>
    </template>

    <!-- 车辆库存选择器 -->
    <VehicleStocksSelector
      v-model:visible="showVehicleSelector"
      :multiple="false"
      :filters="{ stockStatus: 'stocking', trial: 'none' }"
      @cancel="handleVehicleSelectorCancel"
      @confirm="handleVehicleSelectorConfirm"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, markRaw } from "vue";
import { AddOutline } from "@vicons/ionicons5";
import { trialVehiclesApi } from "@/api/trialVehicles";
import VehicleStocksSelector from "@/components/inventory/VehicleStocksSelector.vue";
import messages from "@/utils/messages";

// 使用 markRaw 包装图标组件
const AddOutlineIcon = markRaw(AddOutline);

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:visible", "success"]);

// 响应式数据
const loading = ref(false);
const showVehicleSelector = ref(false);
const selectedVehicle = ref(null);

// 表单数据
const formData = reactive({
  stockId: null, // 库存记录ID，用于主键更新
  vin: "",
  brand: "",
  series: "",
  configName: "",
  color: "",
  markDate: null,
  ownerOrgName: "",
  ownerOrgId: null,
});

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 重置表单
const resetForm = () => {
  selectedVehicle.value = null;
  Object.assign(formData, {
    stockId: null,
    vin: "",
    brand: "",
    series: "",
    configName: "",
    color: "",
    markDate: null,
    ownerOrgName: "",
    ownerOrgId: null,
  });
};

// 处理车辆选择器点击
const handleVehicleSelectorClick = () => {
  showVehicleSelector.value = true;
};

// 处理车辆选择器取消
const handleVehicleSelectorCancel = () => {
  // 不需要特殊处理
};

// 处理车辆选择器确认
const handleVehicleSelectorConfirm = (vehicle) => {
  selectedVehicle.value = vehicle;

  // 填充表单数据，包括库存ID和车辆信息
  Object.assign(formData, {
    stockId: vehicle.id || null, // 保存库存记录ID
    vin: vehicle.vin || "",
    brand: vehicle.brand || "",
    series: vehicle.series || "",
    configName: vehicle.configName || "",
    color: vehicle.color || "",
    ownerOrgName: vehicle.ownerOrgName || "",
    ownerOrgId: vehicle.ownerOrgId || vehicle.ownerId || null,
    // 保持原有的 markDate
  });
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
  resetForm();
};

// 处理提交
const handleSubmit = async () => {
  if (!formData.stockId) {
    messages.warning("请先选择车辆");
    return;
  }

  if (!formData.vin) {
    messages.warning("车辆信息缺失，请重新选择车辆");
    return;
  }

  if (!formData.markDate) {
    messages.warning("请选择标记日期");
    return;
  }

  if (!formData.ownerOrgName) {
    messages.warning("车辆所属单位信息缺失，请重新选择车辆");
    return;
  }

  try {
    loading.value = true;

    // 构建提交数据，只传递必要的字段
    const submitData = {
      stockId: formData.stockId,
      markDate: formData.markDate,
    };

    const response = await trialVehiclesApi.createTrialVehicle(submitData);

    if (response.code === 200) {
      messages.success("新增试驾车成功");
      visible.value = false;
      resetForm();
      emit("success");
    } else {
      messages.error(response.message || "新增试驾车失败");
    }
  } catch (error) {
    console.error("新增试驾车失败:", error);
    messages.error("新增试驾车失败");
  } finally {
    loading.value = false;
  }
};

// 监听弹窗关闭，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      resetForm();
    }
  }
);
</script>

<style lang="scss" scoped>
@use "@/styles/section-title.scss";

.add-trial-vehicle-form {
  padding: 0;
}

.vehicle-selector {
  width: 100%;

  :deep(.n-input) {
    width: 100%;
    cursor: pointer;
  }

  :deep(.n-button) {
    margin-right: -4px;
  }

  .add-icon {
    color: var(--primary-color, #18a058);
    font-size: 18px;
  }
}

// 表单项标签样式
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  padding-bottom: 2px !important;
  margin-bottom: 0 !important;
}

// 表单项样式
:deep(.n-form-item) {
  margin-bottom: 0 !important;
}

// 输入框样式
:deep(.n-input),
:deep(.n-select) {
  width: 100%;
  transition: all 0.3s;
}

// 输入框悬停和聚焦样式
:deep(.n-input:hover),
:deep(.n-input:focus),
:deep(.n-select:hover) {
  box-shadow: 0 0 0 2px rgba(24, 160, 88, 0.1);
}
</style>