<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="销售出库"
    style="width: 800px; max-height: 95vh"
    :mask-closable="false"
    :segmented="{ content: true, footer: true }"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      require-mark-placement="right-hanging"
      class="outbound-form"
    >
      <!-- 车辆信息部分 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">车辆信息</span>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <n-grid :cols="4" :x-gap="16" :y-gap="16" v-if="vehicleData">
          <n-grid-item>
            <n-form-item label="VIN码">
              <n-input
                :value="vehicleData.vin"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车辆品牌">
              <n-input
                :value="vehicleData.brand"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车型系列">
              <n-input
                :value="vehicleData.series"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="配置名称">
              <n-input
                :value="vehicleData.configName"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车辆颜色">
              <n-input
                :value="vehicleData.colorCode"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="库存成本(万元)">
              <n-input
                :value="
                  vehicleData.stockAmount
                    ? (vehicleData.stockAmount / 1000000).toFixed(2)
                    : '-'
                "
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="所属单位">
              <n-input
                :value="vehicleData.ownerOrgName"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="库龄（天）">
              <n-input
                :value="
                  vehicleData.stockDays ? vehicleData.stockDays.toString() : '-'
                "
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="试驾开始日期">
              <n-input
                :value="
                  vehicleData.trialingBeginDate
                    ? new Date(
                        vehicleData.trialingBeginDate
                      ).toLocaleDateString()
                    : '-'
                "
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="试驾状态">
              <n-input
                :value="getTrialStatusText(vehicleData.trialStatus)"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 客户信息部分 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">客户信息</span>
          <n-button
            type="primary"
            size="small"
            @click="showCustomerSelector"
            class="title-button"
          >
            <template #icon>
              <n-icon>
                <component :is="PersonOutlineIcon" />
              </n-icon>
            </template>
            选择客户
          </n-button>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="客户名称" path="customerName" required>
              <n-input
                v-model:value="formData.customerName"
                placeholder="请选择客户"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="联系电话" path="customerPhone">
              <n-input
                v-model:value="formData.customerPhone"
                placeholder="联系电话"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客户地址" path="customerAddress">
              <n-input
                v-model:value="formData.customerAddress"
                placeholder="客户地址"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售顾问" path="salesAgentName">
              <n-input
                v-model:value="formData.salesAgentName"
                placeholder="销售顾问"
                readonly
                style="background-color: #f5f5f5"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 出库信息部分 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">出库信息</span>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="出库日期" path="outboundDate" required>
              <n-date-picker
                v-model:value="formData.outboundDate"
                type="date"
                clearable
                style="width: 100%"
                value-format="timestamp"
                placeholder="请选择出库日期"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="出库金额(元)" path="outboundAmount" required>
              <n-input-number
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="formData.outboundAmount"
                button-placement="both"
                placeholder="请输入出库金额"
                :min="0.01"
                :precision="2"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item
              label="折旧金额(元)"
              path="depreciationAmount"
              required
            >
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="formData.depreciationAmount"
                button-placement="both"
                placeholder="请输入折旧金额"
                :precision="2"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item
              label="厂家支持(元)"
              path="manufacturerSupportAmount"
              required
            >
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="formData.manufacturerSupportAmount"
                button-placement="both"
                placeholder="请输入厂家支持金额"
                :precision="2"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="4">
            <n-form-item label="出库备注" path="remark">
              <n-input
                v-model:value="formData.remark"
                placeholder="请输入出库备注信息"
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 3 }"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </div>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消出库</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="loading">
          确认出库
        </n-button>
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <CustomerSelector
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelect"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, markRaw } from "vue";
import { PersonOutline } from "@vicons/ionicons5";
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import trialVehiclesApi from "@/api/trialVehicles";
import messages from "@/utils/messages";
import { getNumberInputConfig } from "@/config/inputConfig";

// 使用 markRaw 包装图标组件
const PersonOutlineIcon = markRaw(PersonOutline);

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  vehicleData: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:visible", "success"]);

// 响应式数据
const formRef = ref(null);
const loading = ref(false);
const customerSelectorVisible = ref(false);

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

// 表单数据
const formData = reactive({
  // 客户信息
  customerId: null,
  customerName: "",
  customerPhone: "",
  customerAddress: "",
  salesAgentName: "",

  // 出库信息
  outboundDate: Date.now(), // 默认当前日期
  outboundAmount: null,
  depreciationAmount: 0, // 折旧金额，默认为0
  manufacturerSupportAmount: 0, // 厂家支持金额，默认为0
  remark: "",
});

// 表单验证规则
const formRules = {
  customerName: [{ required: true, message: "请选择客户" }],
  outboundDate: [{ required: true, message: "请选择出库日期" }],
  outboundAmount: [
    {
      validator: (_, value) => {
        if (value === null || value === undefined || value === "") {
          return new Error("请输入出库金额");
        }
        if (Number(value) <= 0) {
          return new Error("出库金额必须大于0");
        }
        return true;
      },
    },
  ],
  depreciationAmount: [
    {
      validator: (_, value) => {
        if (value === null || value === undefined || value === "") {
          return new Error("请输入折旧金额");
        }
        if (Number(value) < 0) {
          return new Error("折旧金额不能小于0");
        }
        return true;
      },
    },
  ],
  manufacturerSupportAmount: [
    {
      validator: (_, value) => {
        if (value === null || value === undefined || value === "") {
          return new Error("请输入厂家支持金额");
        }
        if (Number(value) < 0) {
          return new Error("厂家支持金额不能小于0");
        }
        return true;
      },
    },
  ],
};

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 试驾状态文本转换
const getTrialStatusText = (status) => {
  const statusMap = {
    trialing: "试驾中",
    available: "可试驾",
    returned: "已归还",
  };
  return statusMap[status] || "未知";
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    // 客户信息
    customerId: null,
    customerName: "",
    customerPhone: "",
    customerAddress: "",
    salesAgentName: "",

    // 出库信息
    outboundDate: Date.now(), // 默认当前日期
    outboundAmount: null,
    depreciationAmount: 0, // 折旧金额，默认为0
    manufacturerSupportAmount: 0, // 厂家支持金额，默认为0
    remark: "",
  });
};

// 显示客户选择器
const showCustomerSelector = () => {
  customerSelectorVisible.value = true;
};

// 处理客户选择
const handleCustomerSelect = (customer) => {
  formData.customerId = customer.id;
  formData.customerName = customer.customerName || customer.name || "";
  formData.customerPhone = customer.mobile || customer.phone || "";
  formData.customerAddress = customer.address || "";
  formData.salesAgentName = customer.salesAgentName || "";
  customerSelectorVisible.value = false;
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
  resetForm();
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    loading.value = true;

    // 构建提交数据，包含 amount、customerId、vin、customerName、remark、depreciationAmount 和 manufacturerSupportAmount
    const submitData = {
      amount: Math.round(formData.outboundAmount * 100), // 转换为分
      customerId: formData.customerId,
      vin: props.vehicleData?.vin || "", // 车架号
      customerName: formData.customerName || "", // 客户姓名
      remark: formData.remark ? formData.remark.replace(/[\r\n]+/g, " ") : "", // 备注信息
      depreciationAmount: Math.round(formData.depreciationAmount * 100), // 折旧金额，转换为分
      manufacturerSupportAmount: Math.round(
        formData.manufacturerSupportAmount * 100
      ), // 厂家支持金额，转换为分
    };

    console.log("提交出库数据:", submitData);

    // 调用出库接口 PUT /trial/vehicles/{stockId}
    const response = await trialVehiclesApi.outboundTrialVehicle(
      props.vehicleData?.id,
      submitData
    );

    if (response.code === 200) {
      messages.success("销售出库成功");
      visible.value = false;
      resetForm();
      emit("success");
    } else {
      messages.error(response.message || "出库失败");
    }
  } catch (error) {
    console.error("出库失败:", error);
    if (error.errors) {
      // 表单验证错误
      return;
    }
    messages.error("出库失败");
  } finally {
    loading.value = false;
  }
};

// 监听弹窗关闭，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      resetForm();
    }
  }
);
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.outbound-form {
  // 使用通用样式
  .section-container {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-right: 12px;
    }

    .title-button {
      margin-left: 8px;
    }
  }

  .section-divider {
    margin: 0 0 16px 0;
  }

  :deep(.n-form-item-label) {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  :deep(.n-form-item-feedback-wrapper) {
    min-height: 18px;
  }

  :deep(.n-input),
  :deep(.n-input-number),
  :deep(.n-date-picker) {
    width: 100%;
  }

  // 只读字段样式
  :deep(.n-input[readonly]) {
    background-color: #f5f5f5;
    cursor: default;

    .n-input__input {
      cursor: default;
    }
  }
}
</style>
