<template>
  <div class="derivative-costs-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">确认状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.confirmed"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in confirmStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">销售单位</div>
          <div class="filter-options">
            <n-space :size="8" :wrap="true">
              <n-button
                @click="showOrgSelector = true"
                :type="selectedOrgs.length > 0 ? 'primary' : 'default'"
                round
                size="small"
              >
                <template #icon>
                  <n-icon><Building /></n-icon>
                </template>
                {{ selectedOrgText }}
              </n-button>
              <!-- 清除全部按钮 -->
              <n-button
                v-if="selectedOrgs.length > 0"
                ghost
                type="success"
                round
                size="small"
                @click="clearOrgSelection"
                class="selected-org-button clear-all-button"
              >
                <span class="org-name">清除全部</span>
                <n-icon
                  size="14"
                  class="close-icon"
                  style="margin-left: 6px; right: -5px"
                >
                  <CloseOutline />
                </n-icon>
              </n-button>
              <n-button
                v-for="org in selectedOrgs"
                :key="org.id"
                ghost
                type="success"
                round
                size="small"
                @click="removeOrg(org.id)"
                class="selected-org-button"
              >
                <span class="org-name">{{ org.name || org.orgName }}</span>
                <n-icon
                  size="14"
                  class="close-icon"
                  style="margin-left: 6px; right: -5px"
                >
                  <CloseOutline />
                </n-icon>
              </n-button>
            </n-space>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button
          type="success"
          @click="confirmStatus"
          round
          :disabled="selectedRowKeys.length === 0"
        >
          <template #icon>
            <n-icon><CreateOutline /></n-icon>
          </template>
          确认状态 ({{ selectedRowKeys.length }})
        </n-button>
        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入订单号/VIN进行搜索"
          style="width: 320px"
          clearable
          @keydown.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="getRowKey"
          :children-key="'children'"
          :expanded-row-keys="expandedKeys"
          :checked-row-keys="selectedRowKeys"
          @update:checked-row-keys="handleSelectionChange"
          @update:expanded-row-keys="handleExpand"
          :max-height="tableMaxHeight"
          :virtual-scroll="false"
          striped
          size="medium"
        />
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          show-size-picker
          :show-quick-jumper="pagination.showQuickJumper"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        >
          <template #prefix="{ itemCount }"> 共 {{ itemCount }} 条 </template>
        </n-pagination>
      </div>
    </div>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      v-model:visible="showOrgSelector"
      :selected-orgs="selectedOrgs"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
      multiple
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useDerivativeCostsPage from "./DerivativeCostsPage.js";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";

// 使用组合式函数获取所有页面逻辑
const {
  // 图标
  SearchOutline,
  RefreshOutline,
  CreateOutline,
  CloseOutline,
  Building,

  // 响应式数据
  tableRef,
  loading,
  editableRowKeys,
  confirmStatusOptions,
  filterForm,
  derivativeCostsData,
  pagination,
  windowHeight,
  showOrgSelector,
  selectedOrgs,
  selectedRowKeys,
  expandedKeys,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  selectedOrgText,

  // 业务方法
  handleSearch,
  refreshData,
  handlePageChange,
  handlePageSizeChange,
  editRow,
  cancelEdit,
  saveRow,
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,
  handleSelectionChange,
  confirmStatus,
  handleExpand,
  getRowKey,
  findRowByKey,

  // 生命周期方法
  initialize,
  cleanup,
} = useDerivativeCostsPage();

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./DerivativeCostsPage.scss";
</style>
