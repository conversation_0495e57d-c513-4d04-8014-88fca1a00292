<template>
  <div class="daily-money-page">
    <!-- 筛选区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <!-- 第一行：统计日期 -->
        <div class="filter-row">
          <div class="filter-label">统计日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <!-- 第二行：业务机构 -->
        <div class="filter-row">
          <div class="filter-label">业务机构</div>
          <div class="filter-options org-selector-container">
            <div class="org-selector-wrapper">
              <n-button
                type="primary"
                ghost
                @click="showOrgSelector = true"
                class="org-selector-button"
              >
                <template #icon>
                  <n-icon><component :is="BusinessOutlineIcon" /></n-icon>
                </template>
                {{ selectedOrgText }}
              </n-button>
              <n-button
                v-if="
                  filterForm.selectedOrgs && filterForm.selectedOrgs.length > 0
                "
                text
                type="error"
                size="small"
                @click="clearOrgSelection"
                class="org-clear-button"
              >
                <template #icon>
                  <n-icon><component :is="CloseIcon" /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button
          type="primary"
          @click="handleRefresh"
          :loading="loading"
          round
        >
          <template #icon>
            <n-icon><component :is="RefreshIcon" /></n-icon>
          </template>
          刷新
        </n-button>
        <n-input
          v-model:value="filterForm.keywords"
          placeholder="输入关键字进行过滤"
          clearable
          @input="handleKeywordSearch"
          @clear="handleKeywordClear"
          class="keyword-search-input"
        >
          <template #prefix>
            <n-icon><component :is="SearchIcon" /></n-icon>
          </template>
        </n-input>
      </n-space>
      <n-space>
        <!-- 右侧可以放置其他按钮 -->
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          :row-class-name="getRowClassName"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="medium"
          bordered
        >
          <template #empty>
            <div class="empty-state">
              <n-empty description="暂无账户数据" />
            </div>
          </template>
        </n-data-table>
      </div>
    </div>

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择业务机构"
      :business-permission="null"
      :single="false"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, h } from "vue";
import {
  NCard,
  NDataTable,
  NEmpty,
  NRadioGroup,
  NRadioButton,
  NDatePicker,
  NButton,
  NIcon,
  NTooltip,
  NInput,
  useMessage,
} from "naive-ui";
import { accountsApi } from "@/api/accounts.js";
import { doGet } from "@/utils/requests";
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
} from "@/utils/dateRange";
import { convertToWanOrYi } from "@/utils/money.js";
import { renderTextWithLinks } from "@/utils/textLinkRenderer.js";
import { matchesBusinessNumber } from "@/utils/businessNumberExtractor.js";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import {
  BusinessOutline,
  Close,
  InformationCircleOutline,
  Refresh,
  Search,
} from "@vicons/ionicons5";

// 消息提示
const message = useMessage();

// 图标
const BusinessOutlineIcon = BusinessOutline;
const CloseIcon = Close;
const RefreshIcon = Refresh;
const SearchIcon = Search;

// 组件引用
const tableRef = ref(null);

// 响应式数据
const loading = ref(false);
const accountsList = ref([]);
const tableData = ref([]);
const originalTableData = ref([]); // 存储原始数据，用于前端筛选

// 业务机构选择器相关状态
const showOrgSelector = ref(false);

// 筛选表单
const filterForm = reactive({
  dateRange: "thisMonth", // 默认本月
  customDateRange: null,
  selectedOrgs: [], // 选中的机构列表
  keywords: "", // 关键字搜索
});

// 计算属性
const tableMaxHeight = computed(() => {
  // 计算表格最大高度，预留页面其他元素的空间（包括新增的工具栏）
  return window.innerHeight - 350; // 增加60px为工具栏预留空间
});

const scrollX = computed(() => {
  // 根据账户数量动态计算表格宽度
  const baseWidth = 450; // 基础列宽度（发生时间200 + 摘要250）
  const accountWidth = 150; // 每个账户列宽度
  const remarkWidth = 350; // 备注列宽度（根据您的调整）
  return baseWidth + accountsList.value.length * accountWidth + remarkWidth;
});

// 表格列配置
const columns = computed(() => {
  const baseColumns = [
    {
      title: "发生时间",
      key: "date",
      width: 200,
      fixed: "left",
      align: "center",
      ellipsis: false, // 不省略，完整显示
    },
    {
      title: "摘要",
      key: "summary",
      width: 250,
      fixed: "left",
      align: "left",
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const summary = row.summary || "";
        return renderTextWithLinks(summary, {
          onOrderClick: handleOrderClick,
          onVinClick: handleVinClick,
          onPaymentClick: handlePaymentClick,
          onReceiptClick: handleReceiptClick,
        });
      },
    },
  ];

  // 动态添加账户列
  const accountColumns = accountsList.value.map((account) => ({
    title: () => {
      // 自定义标题渲染，包含账户名称和info图标
      return h(
        "div",
        {
          style:
            "display: flex; align-items: center; justify-content: center; gap: 4px;",
        },
        [
          h("span", account.abbr),
          h(
            NTooltip,
            {
              trigger: "hover",
              placement: "top",
            },
            {
              trigger: () =>
                h(
                  NIcon,
                  {
                    size: 14,
                    style: "color: #909399; cursor: help;",
                  },
                  {
                    default: () => h(InformationCircleOutline),
                  }
                ),
              default: () =>
                account.ownerOrgName || account.orgName || "未知机构",
            }
          ),
        ]
      );
    },
    key: `account_${account.id}`,
    width: 150,
    align: "right",
    render(row) {
      const amount = row[`account_${account.id}`] || 0;
      const formattedAmount = formatAmount(amount);

      // 期初金额行和合计行不显示为超链接
      const isOpeningBalance = row.id && row.id.startsWith("opening_balance_");
      const isSummaryRow = row.isSummaryRow;

      // 如果是合计行，显示为粗体
      if (isSummaryRow) {
        return h(
          "span",
          {
            style: "font-weight: 700; font-size: 14px;",
          },
          formattedAmount
        );
      }

      // 如果金额不为0且不是期初金额行，显示为超链接样式
      if (amount !== 0 && !isOpeningBalance) {
        return h(
          "span",
          {
            style:
              "color: var(--n-color-primary); cursor: pointer; text-decoration: underline;",
            onClick: () => handleAmountClick(row, account.id, amount),
          },
          formattedAmount
        );
      }

      return formattedAmount;
    },
  }));

  // 备注列
  const remarkColumn = {
    title: "备注",
    key: "remark",
    width: 350,
    align: "left",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      const remark = row.remark || "";
      if (!remark || remark === "-") {
        return "-";
      }
      return renderTextWithLinks(remark, {
        onOrderClick: handleOrderClick,
        onVinClick: handleVinClick,
        onPaymentClick: handlePaymentClick,
        onReceiptClick: handleReceiptClick,
      });
    },
  };

  return [...baseColumns, ...accountColumns, remarkColumn];
});

// 获取行类名
const getRowClassName = (row) => {
  if (row.isSummaryRow) {
    return "summary-row-style";
  }
  if (row.id && row.id.startsWith("opening_balance_")) {
    return "opening-balance-row-style";
  }
  return "";
};

// 业务方法
const formatAmount = (amount) => {
  // 金额为0或null时显示0
  if (amount === 0 || amount === null || amount === undefined) {
    return "0";
  }

  // 按金额格式显示（假设金额单位为分）
  const formattedAmount = Math.abs(amount / 100).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  // 金额为正时不显示加号，金额为负则显示减号
  return amount > 0 ? formattedAmount : `-${formattedAmount}`;
};

// 获取查询开始日期
const getQueryStartDate = () => {
  const params = getQueryParams();

  // 如果是自定义日期范围
  if (params.date_scope === "custom" && params.start_date) {
    return params.start_date;
  }

  // 使用工具函数获取日期范围
  let rangeType = params.date_scope || "this_month";
  if (rangeType !== "custom") {
    // 将下划线命名转换回驼峰命名
    rangeType = rangeType.replace(/_([a-z])/g, (_, letter) =>
      letter.toUpperCase()
    );
  }

  const dateRange = getDateRangeParams(rangeType, filterForm.customDateRange);
  return dateRange.startDate;
};

// 转换收款单数据为资金日报表格数据
const transformMoneyDetailsToTableData = (rawData) => {
  const result = [];

  // 添加期初金额行（第一行）
  const startDate = getQueryStartDate();
  if (startDate && accountsList.value.length > 0) {
    const openingBalanceRow = {
      id: `opening_balance_${startDate}`,
      date: `${startDate} 00:00:00`, // 使用查询开始日期
      summary: "上期结转", // 摘要为"上期结转"
      remark: "", // 备注为空
      receptId: null, // 期初金额行没有receptId
    };

    // 为每个账户初始化金额为0
    accountsList.value.forEach((account) => {
      openingBalanceRow[`account_${account.id}`] = 0;
    });

    // 设置各账户的期初金额
    accountsList.value.forEach((account) => {
      // 使用 initAmount 字段作为期初金额
      const openingAmount = account.initAmount || 0;
      openingBalanceRow[`account_${account.id}`] = openingAmount;
    });

    result.push(openingBalanceRow);
  }

  // 如果没有原始数据，只返回期初金额行
  if (!Array.isArray(rawData) || rawData.length === 0) {
    return result;
  }

  // 过滤掉无效数据（accountId 和 executeTime 都必须有值）
  const validData = rawData.filter(
    (item) => item.accountId && item.executeTime
  );

  if (validData.length === 0) {
    console.warn("没有有效的资金明细数据");
    return result; // 返回包含期初金额行的结果
  }

  // 为每条记录生成一行表格数据
  validData.forEach((item, index) => {
    // 保留完整的时间（YYYY-MM-DD HH:mm:ss）
    const fullDateTime = item.executeTime;
    const dateOnly = item.executeTime.split(" ")[0]; // 用于ID生成

    const row = {
      id: `${dateOnly}_${item.accountId}_${index}`,
      date: fullDateTime, // 显示完整时间
      summary: item.summary || "资金变动", // 使用 summary 作为摘要
      remark: item.remark || "-", // 使用 remark 作为备注
      receptId: item.id || item.receptId || null, // 保存 receptId 用于点击时显示
      type: item.type || null, // 保存 type 字段
      originalId: item.id || null, // 保存原始的 id 字段
    };

    // 为每个账户初始化金额为0
    accountsList.value.forEach((account) => {
      row[`account_${account.id}`] = 0;
    });

    // 在对应的账户列显示金额
    const accountKey = `account_${item.accountId}`;
    if (row[accountKey] !== undefined) {
      // 使用新的 amount 字段
      const amount = item.amount || 0;
      row[accountKey] = amount;
    }

    result.push(row);
  });

  // 按日期排序
  result.sort((a, b) => {
    if (a.date !== b.date) {
      return a.date.localeCompare(b.date);
    }
    // 同一天内按摘要排序
    return a.summary.localeCompare(b.summary);
  });

  // 添加合计行作为最后一行
  if (accountsList.value.length > 0) {
    const summaryRow = {
      id: "summary_total_row",
      date: "", // 合计行不显示日期
      summary: "合计", // 摘要显示"合计"
      remark: "", // 先设置为空，后面计算总金额后再设置
      receptId: null, // 合计行没有receptId
      isSummaryRow: true, // 标记为合计行
    };

    // 为每个账户初始化金额为0
    accountsList.value.forEach((account) => {
      summaryRow[`account_${account.id}`] = 0;
    });

    // 计算各账户的合计金额和总金额
    let totalAmount = 0;
    accountsList.value.forEach((account) => {
      const accountTotal = result.reduce((total, row) => {
        const amount = row[`account_${account.id}`] || 0;
        return total + amount;
      }, 0);
      summaryRow[`account_${account.id}`] = accountTotal;
      totalAmount += accountTotal;
    });

    // 设置备注为总计金额（万/亿单位）
    const yuanAmount = totalAmount / 100;
    summaryRow.remark = `总计: ${convertToWanOrYi(yuanAmount)}`;

    result.push(summaryRow);
  }

  return result;
};

// 计算属性：选中机构的显示文本
const selectedOrgText = computed(() => {
  if (filterForm.selectedOrgs && filterForm.selectedOrgs.length > 0) {
    if (filterForm.selectedOrgs.length === 1) {
      // 单选时显示机构名称
      return filterForm.selectedOrgs[0].orgName;
    } else {
      // 多选时显示选中数量
      return `已选择 ${filterForm.selectedOrgs.length} 个机构`;
    }
  }
  return "选择业务机构";
});

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  filterForm.dateRange = value;
  handleDateChange(value, filterForm, fetchData);
};

// 处理自定义日期变化
const handleCustomDateChange = () => {
  fetchData();
};

// 处理机构选择
const handleOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    // 多选模式，保存选中的机构列表
    filterForm.selectedOrgs = [...orgs];
    fetchData();
  }
};

// 处理机构选择取消
const handleOrgCancel = () => {
  showOrgSelector.value = false;
};

// 清空机构选择
const clearOrgSelection = () => {
  filterForm.selectedOrgs = [];
  fetchData();
};

// 处理金额点击事件
const handleAmountClick = (row, accountId, amount) => {
  // 从 row 对象中直接获取 type 和 id 字段
  const receptId = row.receptId || "暂无ID";
  const type = row.type || "未知类型";
  const originalId = row.originalId || "暂无ID";

  // 打印接口返回的 type 和 id 字段
  console.log("点击金额 - type 和 id 字段:", {
    type: type,
    id: originalId,
    receptId: receptId,
    accountId: accountId,
    amount: amount,
    executeTime: row.date,
    summary: row.summary,
    remark: row.remark,
  });

  // 在消息中显示 type 和 id
  message.success(`类型: ${type}, ID: ${originalId}`);

  // 原有的调试信息
  console.log("点击金额:", {
    row,
    accountId,
    amount,
    receptId,
    type,
    originalId,
  });
};

// 处理刷新按钮点击事件
const handleRefresh = async () => {
  try {
    message.info("正在刷新数据...");
    await fetchData();
    message.success("数据刷新成功");
  } catch (error) {
    console.error("刷新数据失败:", error);
    message.error("刷新数据失败，请稍后重试");
  }
};

// 前端筛选数据
const filterTableData = () => {
  if (!filterForm.keywords || !filterForm.keywords.trim()) {
    // 如果没有关键字，显示所有原始数据
    tableData.value = [...originalTableData.value];
    return;
  }

  const keyword = filterForm.keywords.trim().toLowerCase();

  // 筛选数据：在摘要和备注中搜索关键字，包括业务编号智能识别
  const filteredData = originalTableData.value.filter((row) => {
    // 跳过合计行和期初金额行的筛选
    if (row.isSummaryRow || (row.id && row.id.startsWith("opening_balance_"))) {
      return true;
    }

    // 在摘要和备注中搜索关键字
    const summary = (row.summary || "").toLowerCase();
    const remark = (row.remark || "").toLowerCase();

    // 普通文本搜索
    const textMatch = summary.includes(keyword) || remark.includes(keyword);

    // 业务编号智能搜索
    const businessNumberMatch =
      matchesBusinessNumber(row.summary || "", filterForm.keywords) ||
      matchesBusinessNumber(row.remark || "", filterForm.keywords);

    return textMatch || businessNumberMatch;
  });

  tableData.value = filteredData;
};

// 处理关键字搜索
const handleKeywordSearch = () => {
  filterTableData();
};

// 处理关键字清除
const handleKeywordClear = () => {
  filterForm.keywords = "";
  filterTableData();
};

// 业务编号点击处理函数
const handleOrderClick = (orderSn) => {
  message.info(`订单号: ${orderSn}`, {
    duration: 5000,
    closable: true,
  });
};

const handleVinClick = (vin) => {
  message.info(`VIN码: ${vin}`, {
    duration: 5000,
    closable: true,
  });
};

const handlePaymentClick = (paymentNo) => {
  message.info(`支付单号: ${paymentNo}`, {
    duration: 5000,
    closable: true,
  });
};

const handleReceiptClick = (receiptNo) => {
  message.info(`收款单号: ${receiptNo}`, {
    duration: 5000,
    closable: true,
  });
};

// 获取查询参数
const getQueryParams = () => {
  const params = {};

  // 处理日期范围
  if (filterForm.dateRange) {
    // 将驼峰命名转换为下划线命名
    const dateScope =
      filterForm.dateRange === "custom"
        ? "custom"
        : filterForm.dateRange.replace(/([A-Z])/g, "_$1").toLowerCase();

    params.date_scope = dateScope;

    // 如果是自定义日期范围，还需要添加自定义日期参数
    if (dateScope === "custom" && filterForm.customDateRange) {
      const dateParams = getDateRangeParams(
        filterForm.dateRange,
        filterForm.customDateRange
      );
      if (dateParams.startDate) {
        params.start_date = dateParams.startDate;
      }
      if (dateParams.endDate) {
        params.end_date = dateParams.endDate;
      }
    }
  } else {
    // 默认使用本月
    params.date_scope = "this_month";
  }

  // 处理机构（多选模式）
  if (filterForm.selectedOrgs && filterForm.selectedOrgs.length > 0) {
    // 将选中的机构ID转换为逗号分割的字符串
    const orgIds = filterForm.selectedOrgs.map((org) => org.id);
    params.ownerOrgs = orgIds.join(",");
  }

  return params;
};

// 统一的数据获取函数
const fetchData = async () => {
  try {
    // 先获取账户列表
    await fetchAccountsList();
    // 再根据账户列表获取资金日报数据
    await fetchDailyReportData();
  } catch (error) {
    console.error("获取数据失败:", error);
    message.error("获取数据失败，请稍后重试");
  }
};

const fetchAccountsList = async () => {
  try {
    loading.value = true;

    // 获取查询参数
    const params = getQueryParams();
    console.log("查询账户列表参数:", params);

    // 构建API请求参数
    const apiParams = {
      isSimpleQuery: true, // 添加简单列表参数
    };

    // 添加机构参数
    if (params.ownerOrgs) {
      apiParams.ownerOrgs = params.ownerOrgs;
    }

    try {
      // 调用真实的账户列表接口
      const response = await accountsApi.getList(apiParams);
      console.log("账户列表接口返回数据:", response);

      if (response && response.data && response.data.list) {
        accountsList.value = response.data.list;
      } else {
        console.warn("账户列表接口返回数据格式异常:", response);
        accountsList.value = [];
        message.warning("账户列表数据为空");
      }
    } catch (apiError) {
      console.error("调用账户列表接口失败:", apiError);
      message.error("获取账户列表失败，请稍后重试");
      accountsList.value = [];
      throw apiError;
    }
  } catch (error) {
    console.error("获取账户列表失败:", error);
    accountsList.value = [];
    throw error;
  } finally {
    loading.value = false;
  }
};

const fetchDailyReportData = async () => {
  try {
    loading.value = true;

    // 如果没有账户列表，则不查询数据
    if (!accountsList.value || accountsList.value.length === 0) {
      originalTableData.value = [];
      tableData.value = [];
      return;
    }

    // 获取查询参数
    const params = getQueryParams();
    // 添加账户ID列表参数
    const accountIds = accountsList.value.map((account) => account.id);

    // 构建日期参数，使用工具函数获取正确的日期范围
    let beginDate, endDate;

    if (params.date_scope) {
      // 将下划线命名转换回驼峰命名
      const rangeType = params.date_scope.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase()
      );

      // 使用工具函数获取日期范围
      const dateRange = getDateRangeParams(
        rangeType,
        filterForm.customDateRange
      );
      beginDate = dateRange.startDate;
      endDate = dateRange.endDate;

      // 如果是自定义日期且有自定义日期范围参数，优先使用
      if (rangeType === "custom" && params.start_date && params.end_date) {
        beginDate = params.start_date;
        endDate = params.end_date;
      }
    } else {
      // 默认使用本月
      const dateRange = getDateRangeParams("thisMonth");
      beginDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // 构建API请求参数
    const apiParams = {
      accountIds: accountIds.join(","),
      beginDate: beginDate,
      endDate: endDate,
    };

    console.log("调用资金日报接口参数:", apiParams);

    try {
      // 调用真实接口
      const response = await doGet("/money/details", apiParams);
      console.log("资金日报接口返回数据:", response);

      // 处理返回的数据
      if (response && response.data) {
        console.log(
          "接口返回的数据结构:",
          JSON.stringify(response.data, null, 2)
        );

        // 处理返回的收款单数据，转换为资金日报格式
        let rawData = [];
        if (Array.isArray(response.data)) {
          rawData = response.data;
        } else if (response.data.list && Array.isArray(response.data.list)) {
          rawData = response.data.list;
        } else {
          rawData = response.data;
        }

        // 转换数据格式
        const transformedData = transformMoneyDetailsToTableData(rawData);
        originalTableData.value = transformedData; // 保存原始数据

        // 应用前端筛选
        filterTableData();
      } else {
        console.warn("接口返回数据为空");
        originalTableData.value = [];
        tableData.value = [];
        message.warning("接口返回数据为空");
      }
    } catch (apiError) {
      console.error("调用真实接口失败:", apiError);
      message.error(`调用接口失败: ${apiError.message || "未知错误"}`);
      originalTableData.value = [];
      tableData.value = [];
      throw apiError;
    }
  } catch (error) {
    console.error("获取资金日报数据失败:", error);
    message.error("获取资金日报数据失败，请稍后重试");
    originalTableData.value = [];
    tableData.value = [];
    throw error; // 重新抛出错误，让上层处理
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(async () => {
  await nextTick();
  await fetchData();
});
</script>

<style lang="scss" scoped>
.daily-money-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.title-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

/* 筛选区域样式 */
.filter-card {
  margin-bottom: 16px;
}

/* 工具栏样式 */
.toolbar {
  margin-bottom: 16px;
}

/* 关键字搜索输入框样式 */
.keyword-search-input {
  width: 250px;
  margin-left: 12px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
}

.filter-label {
  width: 80px;
  font-weight: bold;
  color: #333;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.custom-radio-group {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.custom-radio-button {
  white-space: nowrap;
}

.custom-date-picker {
  width: 200px;
  min-width: 180px;
}

/* 机构选择器容器样式 */
.org-selector-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.org-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.org-selector-button {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.org-clear-button {
  flex-shrink: 0;
}

/* 表格容器样式 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 0;
}

/* 合计行样式 */
:deep(.summary-row-style) {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  border-top: 2px solid #e0e0e6 !important;
}

:deep(.summary-row-style:hover) {
  background-color: #f8f9fa !important;
}

/* 期初金额行样式 */
:deep(.opening-balance-row-style) {
  background-color: #f0f7ff !important;
  font-weight: 500 !important;
}

:deep(.opening-balance-row-style:hover) {
  background-color: #e6f3ff !important;
}

/* 虚拟滚动表格样式优化 */
:deep(.n-data-table) {
  width: 100% !important;

  /* 表头样式 */
  .n-data-table-base-table-header {
    .n-data-table-th {
      font-weight: 600;
      text-align: center !important;
      background-color: #fafafa;

      &.n-data-table-th--fixed-left {
        background-color: #f5f5f5;
      }

      .n-data-table-th__title {
        justify-content: center;
      }
    }
  }

  /* 虚拟滚动容器样式 */
  .n-data-table-base-table-body {
    overflow: auto;
  }

  /* 确保虚拟滚动表格不出现额外的空白列 */
  .n-data-table-base-table {
    width: 100% !important;
    table-layout: fixed;
    border-collapse: collapse;
  }

  /* 虚拟滚动行样式 */
  .n-data-table-tr {
    height: 48px;
  }

  /* 数据单元格样式 */
  .n-data-table-td {
    text-align: center !important;

    &.n-data-table-td--fixed-left {
      background-color: #fafafa;
    }
  }

  /* 修复虚拟滚动表格列之间的空白区域问题 */
  .n-data-table-base-table-header,
  .n-data-table-base-table-body {
    .n-data-table-th,
    .n-data-table-td {
      border-right: 1px solid var(--n-border-color);
      box-sizing: border-box;

      &:last-child {
        border-right: none;
      }
    }
  }

  /* 确保固定列的边框连续性 */
  .n-data-table-th--fixed-left,
  .n-data-table-td--fixed-left {
    border-right: 1px solid var(--n-border-color) !important;
  }

  /* 修复空白列问题 */
  .n-data-table-base-table {
    .n-data-table-th,
    .n-data-table-td {
      &:not([data-col-key]) {
        display: none !important;
      }
    }
  }

  /* 确保表格内容区域填满容器 */
  .n-data-table-wrapper {
    width: 100% !important;
  }

  /* 修复表格右侧空白问题 */
  .n-data-table-base-table-body-wrapper {
    width: 100% !important;
    overflow-x: auto;
  }
}
</style>