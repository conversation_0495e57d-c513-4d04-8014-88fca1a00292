/* 账户管理页面样式 */

.accounts-page-new {
  width: 100%;
  height: 100%;
  
  // 表格样式
  :deep(.n-data-table) {
    .n-data-table-tr {
      height: auto !important; /* 允许行高自适应内容 */
      min-height: 48px; /* 设置最小行高 */
    }
    
    .n-data-table-td {
      padding: 8px 12px !important; /* 调整单元格内边距 */
      vertical-align: middle; /* 垂直居中对齐 */
    }
    
    // 操作按钮样式
    .n-button.n-button--quaternary {
      padding: 0;
      margin: 0 4px;
    }
  }
  
  // 新行高亮样式
  :deep(.new-row-highlight) {
    background-color: rgba(0, 128, 0, 0.1) !important; /* 浅绿色背景 */
    transition: background-color 0.5s ease; /* 平滑过渡效果 */
    
    &:hover {
      background-color: rgba(0, 128, 0, 0.15) !important;
    }
  }
  
  // 标签样式
  :deep(.n-tag) {
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    
    &.n-tag--success {
      background-color: rgba(0, 128, 0, 0.1);
      color: #18a058;
    }
    
    &.n-tag--error {
      background-color: rgba(255, 0, 0, 0.1);
      color: #d03050;
    }
    
    &.n-tag--default {
      background-color: rgba(128, 128, 128, 0.1);
      color: #666;
    }
  }
  
  // 输入框样式
  :deep(.n-input) {
    .n-input__input {
      font-size: 14px;
    }
    
    .n-input__count {
      font-size: 12px;
    }
  }
  
  // 数字输入框样式
  :deep(.n-input-number) {
    width: 100%;
    
    .n-input-number-suffix {
      color: #666;
    }
  }
  
  // 开关样式
  :deep(.n-switch) {
    &.n-switch--active {
      background-color: #18a058;
    }
  }
}
