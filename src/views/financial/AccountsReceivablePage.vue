<template>
  <div class="accounts-receivable-page">
    <!-- 筛选选项区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">应收日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="dateRange === 'custom'"
              v-model:value="customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleDateRangeChange('custom')"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">应收状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="searchParams.status"
              @update:value="searchData"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in statusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">应收机构</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="selectedOrgs && selectedOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 -->
            <div
              v-if="selectedOrgs && selectedOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in selectedOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">应收科目</div>
          <div class="filter-options subject-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showSubjectSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><DocumentTextOutline /></n-icon>
              </template>
              {{ selectedSubjectText }}
            </n-button>
            <n-button
              v-if="selectedSubjects && selectedSubjects.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearSubjectSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的科目标签 -->
            <div
              v-if="selectedSubjects && selectedSubjects.length > 0"
              class="selected-subjects-tags"
            >
              <n-tag
                v-for="subject in selectedSubjects"
                :key="subject.id"
                type="info"
                size="small"
                closable
                @close="removeSubject(subject)"
                style="margin-left: 4px"
              >
                {{ subject.subjectName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddCircleOutline /></n-icon>
          </template>
          新增
        </n-button>
        <n-tooltip :disabled="batchConfirmEnabled" trigger="hover">
          <template #trigger>
            <n-button
              type="success"
              @click="batchConfirmReceivables"
              round
              :disabled="!batchConfirmEnabled"
            >
              <template #icon>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              确认
            </n-button>
          </template>
          {{ batchConfirmTooltip }}
        </n-tooltip>
        <n-input
          v-model:value="searchParams.keywords"
          placeholder="输入关键词按回车键搜索"
          style="width: 300px"
          clearable
          @keydown.enter="searchData"
          @clear="handleClearKeywords"
        >
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="receivableData"
          :loading="loading"
          :row-key="getRowKey"
          :checked-row-keys="selectedRows"
          :bordered="false"
          :single-line="false"
          :striped="true"
          :max-height="tableMaxHeight"
          :virtual-scroll="true"
          :scroll-x="1350"
          size="medium"
          @update:checked-row-keys="handleCheck"
        >
          <template #empty>
            <span>暂无数据</span>
          </template>
        </n-data-table>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          show-size-picker
          :show-quick-jumper="false"
          @update:page="pagination.onChange"
          @update:page-size="pagination.onUpdatePageSize"
        >
          <template #prefix="{ itemCount }"> 共 {{ itemCount }} 条 </template>
        </n-pagination>
      </div>
    </div>

    <!-- 应收账款弹窗 -->
    <receivable-dialog
      ref="receivableDialogRef"
      v-bind:show="receivableDialogVisible"
      @update:show="receivableDialogVisible = $event"
      :form-data="receivableForm"
      :amount-in-chinese="amountInChinese"
      :saving="receivableSaving"
      @save="saveReceivable"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择应收机构"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />

    <!-- 应收科目选择器 -->
    <fee-subject-selector
      v-model:visible="showSubjectSelector"
      :multiple="true"
      :receivable-only="true"
      :selected-subject-ids="selectedSubjectIds"
      @select="handleSubjectSelect"
    />

    <!-- 订单摘要弹窗 -->
    <order-summary-modal
      v-model:visible="showOrderSummaryModal"
      :order-sn="selectedOrderSn"
      @view-detail="handleViewOrderDetail"
    />

    <!-- 订单详情弹窗 -->
    <order-detail-modal
      v-model:visible="showOrderDetailModal"
      :id="selectedOrderId"
    />

    <!-- 车辆追踪弹窗 -->
    <vehicle-tracking-modal
      v-model:visible="showVehicleTrackingModal"
      :vin="selectedVin"
    />

    <!-- 确认收款弹窗 -->
    <confirm-receipt-dialog
      v-bind:show="showConfirmReceiptDialog"
      @update:show="showConfirmReceiptDialog = $event"
      :receivable-data="selectedReceivableData"
      :saving="confirmReceiptSaving"
      @confirm="handleConfirmReceipt"
    />
  </div>
</template>

<script setup>
import { useAccountsReceivablePage } from "./AccountsReceivablePage.js";

// 使用组合式函数获取所有页面逻辑
const {
  // 状态变量
  tableRef,
  loading,
  selectedRows,
  receivableData,
  tableMaxHeight,

  // 弹窗相关
  receivableDialogVisible,
  receivableDialogRef,
  receivableSaving,
  receivableForm,

  // 选项数据
  amountInChinese,

  // 搜索相关
  searchParams,
  dateRange,
  customDateRange,
  statusOptions,
  dateRangeOptions,

  // 机构选择相关
  showOrgSelector,
  selectedOrgs,
  selectedOrgText,

  // 应收科目选择相关
  showSubjectSelector,
  selectedSubjects,
  selectedSubjectIds,
  selectedSubjectText,

  // 弹窗相关
  showOrderSummaryModal,
  showOrderDetailModal,
  showVehicleTrackingModal,
  selectedOrderSn,
  selectedOrderId,
  selectedVin,

  // 确认收款弹窗相关
  showConfirmReceiptDialog,
  confirmReceiptSaving,
  selectedReceivableData,

  // 分页配置
  pagination,

  // 表格配置
  columns,

  // 计算属性
  batchConfirmEnabled,
  batchConfirmTooltip,

  // 方法
  refreshData,
  searchData,
  handleDateRangeChange,
  handleCheck,
  handleClearKeywords,

  showAddDialog,
  saveReceivable,
  getRowKey,
  batchConfirmReceivables,
  handleViewOrderDetail,
  handleConfirmReceipt,

  // 机构选择相关方法
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,

  // 应收科目选择相关方法
  handleSubjectSelect,
  clearSubjectSelection,
  removeSubject,

  // 图标组件
  RefreshOutline,
  AddCircleOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  DocumentTextOutline,
  Building,

  // 组件
  BizOrgSelector,
  FeeSubjectSelector,
  OrderSummaryModal,
  OrderDetailModal,
  VehicleTrackingModal,
  ReceivableDialog,
  ConfirmReceiptDialog,
} = useAccountsReceivablePage();
</script>

<style lang="scss" scoped>
@use "./AccountsReceivablePage.scss";
</style>
