/* 页面容器样式 */
.users-page-container {
  height: 100%;
  width: 100%;
}

.main-card {
  height: calc(100vh - 80px); /* 减去页面上下边距和头部高度 */
  overflow: hidden; /* 防止内容溢出 */
}

.main-grid {
  height: 100%;
}

.grid-item {
  height: 100%;
}

/* 左侧部门卡片和右侧用户卡片样式 */
.department-card,
.users-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

/* 可滚动容器样式 */
.scrollable-container {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 40px); /* 减去卡片标题高度 */
  min-height: 300px; /* 设置最小高度 */
  padding-bottom: 20px; /* 添加底部内边距，确保最后一项完全可见 */
  position: relative; /* 确保定位上下文 */
}

/* 表格容器特殊样式 */
.table-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保容器占满父元素高度 */
  padding-bottom: 40px; /* 为分页组件留出空间 */
  min-height: 400px; /* 确保表格容器有足够的高度 */
}

/* 树形容器特殊样式 */
.tree-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保容器占满父元素高度 */
}

/* 部门树样式 */
.department-tree {
  width: 100%;
  padding-bottom: 50px; /* 增加底部空间 */
  height: auto !important; /* 强制高度自适应内容 */
  min-height: 100%; /* 最小高度100% */
  display: block; /* 确保正确显示 */
  position: relative; /* 提供定位上下文 */
}

/* 用户表格样式 */
.user-table {
  width: 100%;
  position: relative;
  overflow: visible;
  min-height: 300px; /* 确保表格有足够的高度 */
  font-size: 14px;
}

/* 调整表格整体样式 */
.user-table :deep(.n-data-table-wrapper) {
  font-size: 14px;
}

/* 确保分页组件固定在右下角 */
.user-table :deep(.n-data-table-pagination) {
  position: absolute;
  bottom: -40px;
  right: 0;
  padding: 8px 14px;
  background-color: var(--n-merged-th-color);
  border-top: 1px solid var(--n-merged-border-color);
  z-index: 10;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
  width: auto;
  min-width: 300px;
  display: flex;
  justify-content: flex-end;
}

/* 确保表格内容在超出高度时内部垂直滚动 */
.user-table :deep(.n-data-table-base-table-body) {
  min-height: calc(70vh - 20px);
  max-height: calc(50vh - 20px);
  overflow-y: auto;
}

.user-table :deep(.n-data-table-th) {
  padding: 8px 12px !important;
  height: 40px !important;
  line-height: 1.5 !important;
}

/* 调整表头文字样式 */
.user-table :deep(.n-data-table-th__title) {
  font-weight: 600;
  font-size: 14px;
  text-align: left; /* 表头文字左对齐 */
}

/* 确保表头单元格内容左对齐 */
.user-table :deep(.n-data-table-th) {
  text-align: left !important;
}

.n-tree {
  --n-item-height: 40px;
  height: auto !important; /* 强制树组件高度自适应内容 */
  max-height: none !important; /* 移除最大高度限制 */
}

/* 确保树节点正确显示 */
.n-tree .n-tree-node-wrapper {
  width: 100%;
}

/* 确保树节点内容正确显示 */
.n-tree .n-tree-node-content {
  width: calc(100% - 24px); /* 减去左侧缩进 */
}

/* 修复树节点展开后的滚动问题 */
.n-tree-node--expanded + .n-tree-node-indent {
  height: auto !important;
  visibility: visible !important;
}

/* 确保滚动容器能够正确响应内容变化 */
.tree-container.scrollable-container {
  overflow-y: auto !important;
  height: auto !important;
  max-height: calc(100vh - 150px); /* 设置最大高度 */
}

/* 添加以下样式以调整操作列的布局 */
.n-data-table .n-button.n-button--quaternary {
  padding: 0;
  margin: 0 4px;
}

/* 确保表格内容单元格居中对齐 */
.user-table :deep(.n-data-table-td) {
  text-align: center !important;
}

/* 专门针对操作列的居中样式 */
.user-table :deep(.n-data-table-td[data-col-key="actions"]) {
  text-align: center !important;
}

.user-table
  :deep(.n-data-table-td[data-col-key="actions"] .n-data-table-td__content) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* 操作列中的 NSpace 组件样式 */
.user-table :deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  justify-content: center !important;
  align-items: center !important;
}

/* 可以添加以下样式来调整展开/收起图标的大小 */
.n-tree .n-tree-node-switcher {
  transform: none !important;
}

/* 调整图标大小和对齐方式 */
.n-tree .n-tree-node-switcher svg {
  font-size: 18px;
  width: 1em;
  height: 1em;
}

/* 确保叶子节点没有左边 */
.n-tree .n-tree-node-content__prefix {
  width: auto;
}

/* 隐藏叶子节点的切换器 */
.n-tree .n-tree-node--leaf .n-tree-node-switcher {
  visibility: hidden;
  width: 0;
}

/* 修改节点操作按钮的样式 */
.n-tree .n-tree-node-content {
  position: relative;
}

.tree-node-action {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.n-tree-node:hover .tree-node-action,
.n-tree-node--selected .tree-node-action {
  opacity: 1;
}

.tree-node-action .n-icon {
  font-size: 16px;
}

/* 确保下拉菜单在树节点之上 */
.n-dropdown-menu {
  z-index: 1000;
}
