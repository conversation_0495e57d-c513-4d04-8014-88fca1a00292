import { ref, onMounted, h, computed } from "vue";
import {
  NSpace,
  NCard,
  NGrid,
  NGridItem,
  NDataTable,
  NButton,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NTreeSelect,
  NSelect,
  NSwitch,
  NIcon,
  NTree,
  NDropdown,
} from "naive-ui";
import {
  getDepartments,
  getDepartmentMembers,
  getRoles,
  getUserDetails,
  createUser,
  updateUser,
  toggleUserDisabledStatus,
} from "@/api/users";
import {
  AddCircleOutline,
  PencilOutline,
  TrashOutline,
} from "@vicons/ionicons5";
import { Edit, TrashCan } from "@vicons/carbon";
import { PlusSquareOutlined, MinusSquareOutlined } from "@vicons/antd";
import { MoreVertical16Filled } from "@vicons/fluent";

export function useUsersPage() {
  const departmentTree = ref([]);
  const selectedDepartmentKeys = ref([]);
  const users = ref([]);
  const pagination = ref({
    page: 1,
    pageSize: 50,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    prefix: ({ itemCount }) => `共 ${itemCount} 条`,
    placement: "bottom-right",
  });
  const selectedRowKeys = ref([]);
  const showUserModal = ref(false);
  const editingUser = ref({});
  const roleOptions = ref([]);
  const userForm = ref(null);
  const expandedKeys = ref([]);

  // 新增的状态变量
  const showDepartmentModal = ref(false);
  const editingDepartment = ref({});
  const departmentForm = ref(null);

  // 新增的部门表单规则
  const departmentRules = {
    name: { required: true, message: "请输入部门名称", trigger: "blur" },
  };

  // 动态用户验证规则 - 角色字段已隐藏，手机号码为选填
  const userRules = computed(() => {
    const baseRules = {
      username: { required: true, message: "请输入用户名", trigger: "blur" },
      nickname: { required: true, message: "请输入姓名", trigger: "blur" },
      position: {
        required: true,
        message: "请输入职务",
        trigger: "blur",
        validator: (_, value) => {
          if (!value || value.trim() === "") {
            return new Error("请输入职务");
          }
          if (value.length > 20) {
            return new Error("职务不能超过20个字符");
          }
          return true;
        }
      },
      passwd: { required: true, message: "请输入密码", trigger: "blur" },
      departmentId: {
        required: true,
        message: "请选择所属部门",
        trigger: "change",
        validator: (_, value) => {
          // 检查值是否存在且不为空
          if (value === null || value === undefined || value === "") {
            return new Error("请选择所属部门");
          }
          return true;
        },
      },
      // 手机号码为选填，但如果填写则需要符合11位数字格式
      workMobile: {
        required: false,
        trigger: "blur",
        validator: (_, value) => {
          // 如果没有填写，则通过验证（选填）
          if (!value || value.trim() === "") {
            return true;
          }
          // 如果填写了，则验证格式：必须是11位数字
          const mobileRegex = /^1[3-9]\d{9}$/;
          if (!mobileRegex.test(value)) {
            return new Error("请输入正确的11位手机号码");
          }
          return true;
        },
      },
    };

    // 角色字段已隐藏，不进行验证，但数据会保持原样传递给接口

    return baseRules;
  });

  const columns = [
    { title: "ID", key: "id", width: 80, align: "center" },
    { title: "姓名", key: "nickname", align: "center" },
    { title: "职务", key: "position", align: "center" },
    { title: "手机号码", key: "workMobile", align: "center" },
    {
      title: "状态",
      key: "status",
      align: "center",
      render: (row) => {
        return h(
          NSpace,
          { justify: "center", align: "center", size: "small" },
          {
            default: () => [
              h(
                "span",
                {
                  style: {
                    color: !row.disabled ? "#18a058" : "#999",
                    fontSize: "14px",
                    fontWeight: !row.disabled ? "500" : "normal",
                  },
                },
                "启用"
              ),
              h(NSwitch, {
                value: !row.disabled,
                onUpdateValue: (value) => toggleUserStatus(row, value),
              }),
              h(
                "span",
                {
                  style: {
                    color: row.disabled ? "#18a058" : "#999",
                    fontSize: "14px",
                    fontWeight: row.disabled ? "500" : "normal",
                  },
                },
                "禁用"
              ),
            ],
          }
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      align: "center",
      render: (row) => {
        return h(
          NSpace,
          { justify: "center", align: "center", size: "small" },
          {
            default: () => [
              h(
                NButton,
                {
                  quaternary: true,
                  circle: true,
                  size: "small",
                  onClick: () => editUser(row),
                },
                { default: () => h(NIcon, { component: Edit }) }
              ),
              h(
                NButton,
                {
                  quaternary: true,
                  circle: true,
                  size: "small",
                  onClick: () => deleteUser(row),
                },
                { default: () => h(NIcon, { component: TrashCan }) }
              ),
            ],
          }
        );
      },
    },
  ];

  onMounted(async () => {
    await fetchRoles();
    await fetchDepartments();
    if (departmentTree.value.length > 0) {
      selectedDepartmentKeys.value = [departmentTree.value[0].key];
      await fetchUsers(selectedDepartmentKeys.value[0]);
    }
  });

  function buildTreeData(departments) {
    const options = [];
    const map = {};

    // 第一步：创建所有节点的映射
    departments.forEach((dept) => {
      map[dept.id] = {
        key: dept.id.toString(),
        label: dept.name,
        value: dept.id, // 添加 value 属性，用于 n-tree-select 组件的值匹配
        children: [],
        isLeaf: true, // 初始假设所有节点都是叶子节点
        rawData: dept, // 保存原始数据
      };
    });

    // 第二步：构建树形结构
    departments.forEach((dept) => {
      if (dept.parentId === 0 || !dept.parentId) {
        // 根节点直接添加到结果中
        options.push(map[dept.id]);
      } else {
        // 子节点添加到其父节点下
        const parent = map[dept.parentId];
        if (parent) {
          parent.children.push(map[dept.id]);
          parent.isLeaf = false; // 如果有子节点，则不是叶子节点
        } else {
          // 如果找不到父节点，将其作为根节点处理
          console.warn(
            `Parent node with ID ${dept.parentId} not found for department ${dept.name} (ID: ${dept.id}). Treating as root node.`
          );
          options.push(map[dept.id]);
        }
      }
    });

    return options;
  }

  async function fetchDepartments() {
    try {
      const response = await getDepartments();
      departmentTree.value = buildTreeData(response.data);
      if (departmentTree.value.length > 0) {
        selectedDepartmentKeys.value = [departmentTree.value[0].key];
        // 初始化 expandedKeys 为所有顶级部门的 key
        expandedKeys.value = departmentTree.value.map((dept) => dept.key);
        await fetchUsers(selectedDepartmentKeys.value[0]);
      }
    } catch (error) {
      console.error("Failed to fetch departments:", error);
    }
  }

  async function fetchUsers(departmentId) {
    try {
      const response = await getDepartmentMembers(
        departmentId,
        pagination.value.page,
        pagination.value.pageSize
      );
      users.value = response.data.map((user) => ({
        ...user,
        roles: user.roles || [], // 确保每个用户都有 roles 属性，即使它可能为空
      }));
      pagination.value.itemCount = response.total;
    } catch (error) {
      console.error("Failed to fetch users:", error);
      users.value = [];
    }
  }

  async function fetchRoles() {
    try {
      const response = await getRoles();
      roleOptions.value = response.data.map((role) => ({
        label: role.roleName,
        value: role.id,
      }));
    } catch (error) {
      console.error("Failed to fetch roles:", error);
      roleOptions.value = []; // 如果获取失败，设置为空数组
    }
  }

  function handlePageChange(page) {
    pagination.value.page = page;
    fetchUsers(selectedDepartmentKeys.value[0]);
  }

  function handlePageSizeChange(pageSize) {
    pagination.value.pageSize = pageSize;
    pagination.value.page = 1;
    fetchUsers(selectedDepartmentKeys.value[0]);
  }

  function handleCheck(rowKeys) {
    selectedRowKeys.value = rowKeys;
  }

  function handleAddUser() {
    // 获取当前选中的部门ID，如果没有选中则使用第一个部门
    const currentDepartmentId =
      selectedDepartmentKeys.value.length > 0
        ? parseInt(selectedDepartmentKeys.value[0])
        : departmentTree.value.length > 0
          ? parseInt(departmentTree.value[0].key)
          : null;

    editingUser.value = {
      username: "",
      nickname: "",
      position: "",
      passwd: "",
      workMobile: "",
      departmentId: currentDepartmentId, // 数字类型的部门ID，与 value 属性匹配
      roles: [0], // 默认角色设置为[0]
      disabled: false, // 默认启用状态
    };
    showUserModal.value = true;
  }

  async function editUser(user) {
    try {
      const response = await getUserDetails(user.id);
      editingUser.value = {
        ...response.data,
        // 确保 departmentId 是数字类型，与 n-tree-select 的 value 字段匹配
        departmentId: response.data.departmentId
          ? parseInt(response.data.departmentId)
          : null,
      };
      showUserModal.value = true;
    } catch (error) {
      console.error("Failed to get user details:", error);
    }
  }

  function closeUserModal() {
    showUserModal.value = false;
    editingUser.value = {};
  }

  async function saveUser() {
    try {
      await userForm.value.validate();
      if (editingUser.value.id) {
        await updateUser(editingUser.value);
      } else {
        await createUser(editingUser.value);
      }
      closeUserModal();
      await fetchUsers(selectedDepartmentKeys.value[0]);
    } catch (error) {
      console.error("Failed to save user:", error);
    }
  }

  async function toggleUserStatus(user, value) {
    try {
      // value 为 true 表示启用（disabled: false），value 为 false 表示禁用（disabled: true）
      // 所以 state 应该是 !value（取反）
      const state = !value;
      await toggleUserDisabledStatus(user.id, state);
      await fetchUsers(selectedDepartmentKeys.value[0]);
    } catch (error) {
      console.error("Failed to toggle user status:", error);
    }
  }

  async function deleteUser(user) {
    // Implement delete user logic
  }

  async function handleBatchDelete() {
    // Implement batch delete logic
  }

  function renderSwitcherIcon({ expanded, isLeaf }) {
    if (isLeaf) {
      return null;
    }
    return expanded ? h(MinusSquareOutlined) : h(PlusSquareOutlined);
  }

  // 修改 overrideNodeClickBehavior 函数
  const overrideNodeClickBehavior = ({ option }, e) => {
    // 阻止事件冒泡，以防止触发两次
    if (e) e.stopPropagation();

    // 更新选中状态
    selectedDepartmentKeys.value = [option.key];

    // 加载用户列表
    fetchUsers(option.key);

    // 处理展开/收起
    if (option.children && option.children.length > 0) {
      // 如果当前节点已展开，则收起
      if (expandedKeys.value.includes(option.key)) {
        expandedKeys.value = expandedKeys.value.filter(
          (key) => key !== option.key
        );
      } else {
        // 如果当前节点未展开，则展开当前节点，同时收起其他所有节点
        // 保留所有父节点的展开状态
        const parentKeys = findAllParentKeys(departmentTree.value, option.key);
        expandedKeys.value = [...parentKeys, option.key];
      }
    } else {
      // 如果点击的是叶子节点，收起所有其他节点，只保留当前节点的父节点展开
      const parentKeys = findAllParentKeys(departmentTree.value, option.key);
      expandedKeys.value = [...parentKeys];
    }

    // 返回 'prevent-default' 来阻止默认行为
    return "prevent-default";
  };

  function handleTreeExpand(keys) {
    expandedKeys.value = keys;
    // 添加一个小延时，确保DOM更新后再触发滚动条更新
    setTimeout(() => {
      // 强制更新滚动容器
      const scrollContainer = document.querySelector(".tree-container");
      if (scrollContainer) {
        scrollContainer.style.overflow = "hidden";
        setTimeout(() => {
          scrollContainer.style.overflow = "auto";
        }, 10);
      }
    }, 100);
  }

  // 添加一个辅助函数来查找节点
  function findNodeByKey(tree, key) {
    for (const node of tree) {
      if (node.key === key) {
        return node;
      }
      if (node.children) {
        const found = findNodeByKey(node.children, key);
        if (found) return found;
      }
    }
    return null;
  }

  // 查找节点的所有父节点的key
  function findAllParentKeys(tree, key, parentPath = []) {
    for (const node of tree) {
      // 创建当前路径
      const currentPath = [...parentPath];

      // 如果找到了目标节点，返回父节点路径
      if (node.key === key) {
        return currentPath;
      }

      // 如果当前节点有子节点，递归查找
      if (node.children && node.children.length > 0) {
        // 将当前节点添加到路径中
        currentPath.push(node.key);
        const result = findAllParentKeys(node.children, key, currentPath);
        if (result) {
          return result;
        }
      }
    }

    // 如果没有找到，返回null
    return null;
  }

  // 新增、编辑和删除部门的函数
  async function addDepartment(parentKey) {
    editingDepartment.value = { parentId: parentKey, name: "" };
    showDepartmentModal.value = true;
  }

  async function renameDepartment(key) {
    const department = findNodeByKey(departmentTree.value, key);
    if (department) {
      editingDepartment.value = { ...department };
      showDepartmentModal.value = true;
    }
  }

  async function deleteDepartment(key) {
    // 这里应该调用后端 API 来删除部门
    console.log("删除部门:", key); // 使用key参数，避免未使用警告
    // 删除成功后重新获取部门树
    await fetchDepartments();
  }

  async function saveDepartment() {
    try {
      await departmentForm.value.validate();
      // 这里应该调用后端 API 来保存或更新部门
      // 保存成功后重新获取部门树
      await fetchDepartments();
      showDepartmentModal.value = false;
    } catch (error) {
      console.error("Failed to save department:", error);
    }
  }

  // 处理拖动排序
  function handleDrop(/* { node, dragNode, dropPosition } */) {
    // 这里应该调用后端 API 来更新部门顺序
    // 更新成功后重新获取部门树
    fetchDepartments();
  }

  // 修改 renderSuffix 函数
  function renderSuffix({ option }) {
    return () =>
      h(
        "div",
        { class: "tree-node-action" },
        h(
          NDropdown,
          {
            trigger: "click",
            options: [
              {
                label: "新增子部门",
                key: "add",
                icon: renderIcon(AddCircleOutline),
              },
              {
                label: "重命名",
                key: "rename",
                icon: renderIcon(PencilOutline),
              },
              {
                label: "删除",
                key: "delete",
                icon: renderIcon(TrashOutline),
              },
            ],
            onSelect: (key) => {
              switch (key) {
                case "add":
                  addDepartment(option.key);
                  break;
                case "rename":
                  renameDepartment(option.key);
                  break;
                case "delete":
                  deleteDepartment(option.key);
                  break;
              }
            },
          },
          {
            default: () => h(NIcon, { component: MoreVertical16Filled }),
          }
        )
      );
  }

  function renderIcon(icon) {
    return () => h(NIcon, null, { default: () => h(icon) });
  }

  // 返回所有需要在组件中使用的数据和方法
  return {
    // 响应式数据
    departmentTree,
    selectedDepartmentKeys,
    users,
    pagination,
    selectedRowKeys,
    showUserModal,
    editingUser,
    roleOptions,
    userForm,
    expandedKeys,
    showDepartmentModal,
    editingDepartment,
    departmentForm,

    // 计算属性
    userRules,

    // 常量
    departmentRules,
    columns,

    // 方法
    handlePageChange,
    handlePageSizeChange,
    handleCheck,
    handleAddUser,
    editUser,
    closeUserModal,
    saveUser,
    toggleUserStatus,
    deleteUser,
    handleBatchDelete,
    renderSwitcherIcon,
    overrideNodeClickBehavior,
    handleTreeExpand,
    findNodeByKey,
    findAllParentKeys,
    addDepartment,
    renameDepartment,
    deleteDepartment,
    saveDepartment,
    handleDrop,
    renderSuffix,
    renderIcon,

    // 组件引用
    NSpace,
    NCard,
    NGrid,
    NGridItem,
    NDataTable,
    NButton,
    NModal,
    NForm,
    NFormItem,
    NInput,
    NTreeSelect,
    NSelect,
    NSwitch,
    NIcon,
    NTree,
    NDropdown,
  };
}
