<script setup>
import { useUsersPage } from "./UsersPage.js";

// 使用组合式函数获取所有数据和方法
const {
  // 响应式数据
  departmentTree,
  selectedDepartmentKeys,
  users,
  pagination,
  selectedRowKeys,
  showUserModal,
  editingUser,
  roleOptions,
  userForm,
  expandedKeys,
  showDepartmentModal,
  editingDepartment,
  departmentForm,

  // 计算属性
  userRules,

  // 常量
  departmentRules,
  columns,

  // 方法
  handlePageChange,
  handlePageSizeChange,
  handleCheck,
  handleAddUser,
  editUser,
  closeUserModal,
  saveUser,
  toggleUserStatus,
  deleteUser,
  handleBatchDelete,
  renderSwitcherIcon,
  overrideNodeClickBehavior,
  handleTreeExpand,
  findNodeByKey,
  findAllParentKeys,
  addDepartment,
  renameDepartment,
  deleteDepartment,
  saveDepartment,
  handleDrop,
  renderSuffix,
  renderIcon,

  // 组件引用
  NSpace,
  NCard,
  NGrid,
  NGridItem,
  NDataTable,
  NButton,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NTreeSelect,
  NSelect,
  NSwitch,
  NIcon,
  NTree,
  NDropdown,
} = useUsersPage();
</script>

<template>
  <n-space vertical :size="24" class="users-page-container">
    <n-card title="" class="main-card">
      <n-grid :cols="12" :x-gap="24" class="main-grid">
        <!-- 左侧组织机构树 -->
        <n-grid-item span="4" class="grid-item">
          <n-card title="组织机构" class="department-card">
            <div class="scrollable-container tree-container">
              <n-tree
                block-line
                :data="departmentTree"
                :expanded-keys="expandedKeys"
                :selected-keys="selectedDepartmentKeys"
                :render-suffix="renderSuffix"
                :render-switcher-icon="renderSwitcherIcon"
                :override-default-node-click-behavior="
                  overrideNodeClickBehavior
                "
                selectable
                draggable
                :style="{ maxHeight: 'none', height: 'auto' }"
                @update:expanded-keys="handleTreeExpand"
                @drop="handleDrop"
                class="department-tree"
              />
            </div>
          </n-card>
        </n-grid-item>

        <!-- 右侧用户列表 -->
        <n-grid-item span="8" class="grid-item">
          <n-card title="用户列表" class="users-card">
            <template #header-extra>
              <n-space>
                <n-button @click="handleAddUser">新增用户</n-button>
                <n-button
                  @click="handleBatchDelete"
                  :disabled="!selectedRowKeys.length"
                  >批量删除</n-button
                >
              </n-space>
            </template>
            <div class="scrollable-container table-container">
              <n-data-table
                :columns="columns"
                :data="users"
                :pagination="pagination"
                @update:page="handlePageChange"
                @update:page-size="handlePageSizeChange"
                :row-key="(row) => row.id"
                @update:checked-row-keys="handleCheck"
                :bordered="true"
                :single-line="false"
                size="small"
                class="user-table"
              />
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 用户编辑对话框 -->
    <n-modal v-model:show="showUserModal" :mask-closable="false">
      <n-card
        style="width: 600px"
        :title="editingUser.id ? '编辑用户' : '新增用户'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          :model="editingUser"
          :rules="userRules"
          ref="userForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="登陆用户名" path="username" required>
            <n-input
              v-model:value="editingUser.username"
              placeholder="请输入登陆用户名"
            />
          </n-form-item>
          <n-form-item label="姓名" path="nickname" required>
            <n-input
              v-model:value="editingUser.nickname"
              placeholder="请输入姓名"
            />
          </n-form-item>

          <n-form-item label="密码" path="passwd" required>
            <n-input
              v-model:value="editingUser.passwd"
              type="password"
              placeholder="请输入密码"
            />
          </n-form-item>
                    <n-form-item label="职务" path="position" required>
            <n-input
              v-model:value="editingUser.position"
              placeholder="请输入职务"
              maxlength="20"
              show-count
            />
          </n-form-item>
          <n-form-item label="手机号" path="workMobile" required>
            <n-input
              v-model:value="editingUser.workMobile"
              placeholder="请输入手机号"
            />
          </n-form-item>
          <n-form-item label="所属部门" path="departmentId" required>
            <n-tree-select
              v-model:value="editingUser.departmentId"
              :options="departmentTree"
              key-field="value"
              label-field="label"
              children-field="children"
              placeholder="请选择所属部门"
              clearable
            />
          </n-form-item>
          <!-- 角色字段 - 隐藏显示，但保持数据完整性 -->
          <!-- <n-form-item label="角色" path="roles">
            <n-select
              v-model:value="editingUser.roles"
              multiple
              :options="roleOptions"
              placeholder="请选择角色"
            />
          </n-form-item> -->
          <!-- 状态字段 - 仅在编辑用户时显示 -->
          <n-form-item v-if="editingUser.id" label="状态" path="disabled">
            <n-space align="center" :size="12">
              <span
                :style="{
                  color: !editingUser.disabled ? '#18a058' : '#999',
                  fontSize: '14px',
                  fontWeight: !editingUser.disabled ? '500' : 'normal',
                }"
              >
                启用
              </span>
              <n-switch
                :value="!editingUser.disabled"
                @update:value="(value) => (editingUser.disabled = !value)"
              />
              <span
                :style="{
                  color: editingUser.disabled ? '#18a058' : '#999',
                  fontSize: '14px',
                  fontWeight: editingUser.disabled ? '500' : 'normal',
                }"
              >
                禁用
              </span>
            </n-space>
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="closeUserModal">取消</n-button>
            <n-button type="primary" @click="saveUser">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>

    <!-- 部门编辑对话框 -->
    <n-modal v-model:show="showDepartmentModal" :mask-closable="false">
      <n-card
        style="width: 400px"
        :title="editingDepartment.key ? '编辑部门' : '新增部门'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          :model="editingDepartment"
          :rules="departmentRules"
          ref="departmentForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <n-form-item label="部门名称" path="name">
            <n-input
              v-model:value="editingDepartment.name"
              placeholder="请输入部门名称"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showDepartmentModal = false">取消</n-button>
            <n-button type="primary" @click="saveDepartment">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </n-space>
</template>

<style scoped src="./UsersPage.scss"></style>
