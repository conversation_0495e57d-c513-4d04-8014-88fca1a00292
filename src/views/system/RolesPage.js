import { ref, onMounted, h } from 'vue';
import { useDialog } from 'naive-ui';
import { RefreshOutline, AddOutline, TrashOutline, PencilOutline } from '@vicons/ionicons5';
import { getRoles, getMenus, saveRole, updateRole, deleteRole, getRoleDetail } from '@/api/roles';
import messages from '@/utils/messages';
import { NButton, NSpace, NIcon } from 'naive-ui';

// 导出composable函数
export function useRolesPage() {
  // 响应式数据
  const roles = ref([]);
  const menuTree = ref([]);
  const leafMenuIds = ref(new Set()); // 缓存所有叶子节点ID
  const dialogVisible = ref(false);
  const isEdit = ref(false);
  const formRef = ref(null);
  const menuTreeRef = ref(null);
  const selectedRoles = ref([]);

  const form = ref({
    roleName: '',
    roleCode: '',
    menus: []
  });

  const dialog = useDialog();

  // 表格列配置
  const columns = [
    { type: 'selection', align: 'center' },
    { title: '角色ID', key: 'id', align: 'center' },
    { title: '角色代码', key: 'roleCode', align: 'center' },
    { title: '角色名称', key: 'roleName', align: 'center' },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      align: 'center',
      render: (row) => {
        return h(NSpace, { justify: 'center', size: 'small' }, () => [
          h(NIcon, {
            component: PencilOutline,
            size: 20,
            color: '#2080f0',
            style: {
              cursor: 'pointer'
            },
            onClick: () => handleEdit(row)
          }),
          h(NIcon, {
            component: TrashOutline,
            size: 20,
            color: '#d03050',
            style: {
              cursor: 'pointer'
            },
            onClick: () => handleDelete(row)
          })
        ]);
      }
    }
  ];

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await getRoles();
      roles.value = response.data.map(role => ({
        ...role,
        menus: role.menus || []
      }));
    } catch (error) {
      messages.error('获角色列失败');
    }
  };

  // 获取所有叶子节点ID
  const getLeafMenuIds = (menuTree) => {
    const leafIds = new Set();

    const traverse = (nodes) => {
      nodes.forEach(node => {
        if (!node.subMenus || node.subMenus.length === 0) {
          // 这是叶子节点
          leafIds.add(node.id);
        } else {
          // 这是父节点，继续遍历子节点
          traverse(node.subMenus);
        }
      });
    };

    traverse(menuTree);
    return leafIds;
  };

  // 获取菜单树
  const fetchMenuTree = async () => {
    try {
      const response = await getMenus();
      menuTree.value = buildMenuTree(response.data);

      // 缓存叶子节点ID
      leafMenuIds.value = getLeafMenuIds(menuTree.value);
    } catch (error) {
      console.error('获取菜单数据失败:', error);
      messages.error(`获取菜单列表失败: ${error.message || '未知错误'}`);
    }
  };

  // 构建菜单树
  const buildMenuTree = (menus) => {
    const menuMap = {};
    menus.forEach(menu => menuMap[menu.id] = { ...menu, subMenus: [] });

    const rootMenus = [];
    menus.forEach(menu => {
      if (menu.parentId === 1) {
        rootMenus.push(menuMap[menu.id]);
      } else if (menuMap[menu.parentId]) {
        // 确保父菜单存在再添加子菜单
        menuMap[menu.parentId].subMenus.push(menuMap[menu.id]);
      }
    });

    // 移除空的 subMenus 数组
    const removeEmptySubMenus = (menu) => {
      if (!menu || !menu.subMenus) {
        return; // 如果菜单项不存在或没有 subMenus 属性，直接返回
      }

      if (menu.subMenus.length === 0) {
        delete menu.subMenus;
      } else {
        menu.subMenus.forEach(removeEmptySubMenus);
      }
    };
    rootMenus.forEach(removeEmptySubMenus);

    return rootMenus;
  };

  // 刷新角色列表
  const refreshRoles = () => {
    fetchRoles();
  };

  // 显示新增对话框
  const showAddDialog = () => {
    isEdit.value = false;
    form.value = { roleName: '', roleCode: '', menus: [] };
    dialogVisible.value = true;
  };

  // 处理编辑
  const handleEdit = async (row) => {
    isEdit.value = true;
    dialogVisible.value = true;

    try {
      // 获取角色详情
      const roleResponse = await getRoleDetail(row.id);
      const roleDetail = roleResponse.data;

      // 确保roleCode不为null或undefined
      const roleCode = roleDetail.roleCode || '';

      // 获取角色的原始权限数据
      const originalMenus = roleDetail.menus || [];

      // 计算与叶子节点的交集，只保留叶子节点权限用于渲染
      const leafIntersection = originalMenus.filter(menuId => leafMenuIds.value.has(menuId));

      console.log('编辑角色权限处理:', {
        original: originalMenus,
        leafNodes: Array.from(leafMenuIds.value),
        intersection: leafIntersection
      });

      form.value = {
        id: roleDetail.id,
        roleName: roleDetail.roleName,
        roleCode: roleCode,
        menus: leafIntersection // 只传递叶子节点权限给树组件渲染
      };
    } catch (error) {
      console.error('获取角色详情失败:', error);
      messages.error('获取角色详情失败');
    }
  };

  // 处理删除
  const handleDelete = (row) => {
    dialog.warning({
      title: '警告',
      content: '确定要删除这个角色吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteRole(row.id);
          messages.success('删除成功');
          fetchRoles();
        } catch (error) {
          messages.error('删除失败');
        }
      }
    });
  };

  // 处理批量删除
  const batchDelete = () => {
    if (selectedRoles.value.length === 0) {
      messages.warning('请选择要删除的角色');
      return;
    }
    dialog.warning({
      title: '警告',
      content: `确定要删除这 ${selectedRoles.value.length} 个角色吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await Promise.all(selectedRoles.value.map(role => deleteRole(role.id)));
          messages.success('批量删除成功');
          fetchRoles();
        } catch (error) {
          messages.error('批量删除失败');
        }
      }
    });
  };

  // 处理选择变化
  const handleSelectionChange = (selection) => {
    selectedRoles.value = selection;
  };

  // 保存或更新角色
  const handleSave = async (submitData) => {
    // 如果子组件传递了数据，使用子组件的数据；否则使用form.value
    const dataToSave = submitData || form.value;

    // 确保 roleCode 字段不为空
    if (!dataToSave.roleCode || dataToSave.roleCode.trim() === '') {
      messages.error('角色代码不能为空');
      return;
    }

    // 确保至少选择了一个菜单权限
    if (!dataToSave.menus || dataToSave.menus.length === 0) {
      messages.error('请至少选择一个菜单权限');
      return;
    }

    const roleData = {
      ...dataToSave,
      roleCode: dataToSave.roleCode.trim() // 确保去除空格
    };

    try {
      if (isEdit.value) {
        await updateRole(roleData);
        messages.success('角色更新成功');
      } else {
        await saveRole(roleData);
        messages.success('角色创建成功');
      }
      dialogVisible.value = false;
      fetchRoles();
    } catch (error) {
      console.error('保存失败:', error);
      messages.error('保存失败，请重试');
    }
  };

  // 生命周期钩子
  onMounted(() => {
    fetchRoles();
    fetchMenuTree();
  });

  // 返回所有需要在模板中使用的变量和方法
  return {
    // 响应式数据
    roles,
    menuTree,
    leafMenuIds, // 叶子节点缓存
    dialogVisible,
    isEdit,
    selectedRoles,
    form,
    columns,

    // 图标组件
    RefreshOutline,
    AddOutline,
    TrashOutline,

    // 方法
    refreshRoles,
    showAddDialog,
    handleEdit,
    handleDelete,
    batchDelete,
    handleSelectionChange,
    handleSave
  };
}
