// 角色管理页面样式

.role-management {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

// 表格样式
:deep(.n-data-table-td) {
  text-align: center;
}

:deep(.n-data-table-td .n-data-table-td__content) {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 操作栏图标间距
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 8px !important;
}

// 特定列左对齐
:deep(.n-data-table-td[data-col-key="roleName"] .n-data-table-td__content),
:deep(.n-data-table-td[data-col-key="roleCode"] .n-data-table-td__content) {
  justify-content: flex-start;
}

// 全屏弹窗样式
.fullscreen-modal {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(.fullscreen-modal .n-card-header) {
  padding: 14px 20px !important;
}

:deep(.fullscreen-modal .n-card__content) {
  height: calc(100vh - 120px) !important;
  overflow-y: auto !important;
}

:deep(.fullscreen-modal .n-card__footer) {
  padding: 14px 20px !important;
}

// 最大化按钮样式
:deep(.n-card-header .n-card-header__extra) {
  display: flex;
  align-items: center;
}

:deep(.n-card-header .n-card-header__extra .n-button) {
  margin-right: 8px;
}

// 关闭按钮样式调整
:deep(.n-modal .n-card-header .n-base-close) {
  top: 14px;
  right: 20px;
}
