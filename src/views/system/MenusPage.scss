/* 菜单管理页面样式 */

.menu-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  box-sizing: border-box;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
  height: 60px;
  /* 固定工具栏高度 */
}

/* 表格容器样式 - 优化虚拟滚动 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 数据表格样式 */
:deep(.n-data-table) {
  width: 100% !important;

  /* 表头样式 */
  .n-data-table-th {
    font-weight: 600;
  }

}

/* 菜单名称列的特殊处理，保持左对齐但考虑缩进 */
:deep(.n-data-table-td .n-data-table-td__content span) {
  text-align: left;
  width: 100%;
}

/* 为菜单名称列添加新的样式 */
:deep(.n-data-table-td[data-col-key="menuLabel"] .n-data-table-td__content) {
  justify-content: flex-start;
  /* 左对齐内容 */
}

/* 添加以下样式来增加图标之间的间距 */
:deep(.n-icon) {
  margin: 0 2px;
}

/* 为操作栏添加新的样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 4px !important;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}