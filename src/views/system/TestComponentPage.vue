<template>
  <div class="test-component-page">
    <h1>组件加载测试页面</h1>

    <div class="test-form">
      <n-form>
        <n-form-item label="组件路径">
          <n-input v-model:value="componentPath" placeholder="例如: system/IndexPage" />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="testComponent">测试加载</n-button>
          <n-button @click="testAllComponents" style="margin-left: 10px">测试所有预定义组件</n-button>
        </n-form-item>
      </n-form>
    </div>

    <div v-if="testResult" class="test-result">
      <h2>测试结果</h2>
      <n-alert :type="testResult.success ? 'success' : 'error'" :title="testResult.success ? '加载成功' : '加载失败'">
        {{ testResult.message }}
      </n-alert>
    </div>

    <div v-if="multipleTestResults.length > 0" class="multiple-test-results">
      <h2>多组件测试结果</h2>
      <n-data-table
        :columns="columns"
        :data="multipleTestResults"
        :pagination="{ pageSize: 10 }"
        :bordered="false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { NForm, NFormItem, NInput, NButton, NAlert, NDataTable, NTag } from 'naive-ui'
import { testComponentLoad, testMultipleComponentsLoad } from '@/utils/testComponentLoader'
import loadComponent from '@/utils/componentLoader'

const componentPath = ref('system/IndexPage')
const testResult = ref(null)
const multipleTestResults = ref([])

// 表格列定义
const columns = [
  {
    title: '组件路径',
    key: 'viewPath',
    render(row) {
      return h('span', { style: { fontWeight: 'bold' } }, row.viewPath)
    }
  },
  {
    title: '结果',
    key: 'success',
    render(row) {
      return h(
        NTag,
        {
          type: row.success ? 'success' : 'error',
          size: 'small'
        },
        { default: () => row.success ? '成功' : '失败' }
      )
    }
  },
  {
    title: '消息',
    key: 'message'
  }
]

// 测试单个组件
const testComponent = async () => {
  if (!componentPath.value) {
    testResult.value = {
      success: false,
      message: '请输入组件路径'
    }
    return
  }

  testResult.value = await testComponentLoad(componentPath.value)
}

// 测试所有预定义组件
const testAllComponents = async () => {
  // 预定义一些常用组件路径进行测试
  const componentPaths = [
    'system/IndexPage',
    'system/DashboardPage',
    'system/UsersPage',
    'system/RolesPage',
    'system/MenusPage',
    'example/ExamplePage',
    'inventory/InventoryDashboard'
  ]

  if (!componentPaths.length) {
    testResult.value = {
      success: false,
      message: '没有找到预定义组件'
    }
    return
  }

  // 添加当前输入的组件路径
  if (componentPath.value && !componentPaths.includes(componentPath.value)) {
    componentPaths.push(componentPath.value)
  }

  multipleTestResults.value = await testMultipleComponentsLoad(componentPaths)
}
</script>

<style scoped>
.test-component-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 20px;
  color: #18A058;
}

h2 {
  margin-top: 30px;
  margin-bottom: 15px;
  color: #333;
}

.test-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.test-result {
  margin-top: 20px;
}

.multiple-test-results {
  margin-top: 30px;
}
</style>
