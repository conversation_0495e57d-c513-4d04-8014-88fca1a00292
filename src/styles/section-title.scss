@use '@/styles/variables.scss' as *;

// 区域标题样式
.section-container .section-title,
.section-title {
  display: flex;
  justify-content: flex-start; // 改为左对齐
  align-items: center;
  margin-bottom: 8px;
  margin-top: 16px;

  .title-text {
    font-size: $font-size-large !important; // 增大字号为18px
    font-weight: $font-weight-bold !important; // 加粗字体
    color: $primary-color !important;
    display: flex !important;
    align-items: center !important;
    margin-right: 12px !important; // 添加右侧间距，与按钮保持一定距离

    &::before {
      content: '' !important;
      display: inline-block !important;
      width: 4px !important;
      height: 18px !important; // 增加高度与字体大小匹配
      background-color: $primary-color !important;
      margin-right: 8px !important;
      border-radius: $border-radius-small !important;
    }
  }

  // 按钮样式
  .n-button,
  .title-button {
    margin-left: 0 !important; // 确保按钮紧挨着标题
  }

  // 专门针对标题按钮的样式
  .title-button {
    margin-left: 8px !important; // 与标题保持一定距离
    height: 28px !important; // 调整按钮高度
    padding: 0 12px !important; // 调整按钮内边距
  }
}

// 分割线样式 - 已移至naive-ui-override.scss中以提高优先级
// 这里保留一个基础样式，但主要样式在naive-ui-override.scss中定义
.section-divider {
  margin-top: 0;
  margin-bottom: 16px;
}

// 减少section-title之间的垂直间距
.section-container+.section-container {
  margin-top: 8px;
}

// 减少n-grid组件行之间的垂直间距
.n-grid {
  .n-form-item {
    margin-bottom: 8px;
  }
}