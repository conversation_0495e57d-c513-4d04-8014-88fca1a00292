/**
 * 订单表单样式
 */

.order-form {
  padding: 0 20px;
  max-width: 1400px;
  margin: 0 auto;

  // 表单项标签样式
  :deep(.n-form-item-label) {
    font-weight: 500;
    color: #333;
    padding-bottom: 2px !important; // 进一步减少底部内边距
    margin-bottom: 0 !important;
  }

  // 表单项样式
  :deep(.n-form-item) {
    margin-bottom: 0 !important; // 移除底部边距
  }

  // 输入框样式
  :deep(.n-input),
  :deep(.n-input-number),
  :deep(.n-select),
  :deep(.n-date-picker) {
    width: 100%;
    transition: all 0.3s;
  }

  // 输入框悬停和聚焦样式
  :deep(.n-input:hover),
  :deep(.n-input:focus),
  :deep(.n-input-number:hover),
  :deep(.n-input-number:focus),
  :deep(.n-select:hover),
  :deep(.n-date-picker:hover) {
    box-shadow: 0 0 0 2px rgba(24, 160, 88, 0.1);
  }

  // 区域标题样式 - 已移至全局样式文件section-title.scss
  // 这里保留一些特定于orderForm的样式
  .section-title {
    margin: 20px 0 8px; // 减少上边距，从30px改为20px
  }

  // 分割线样式 - 仅应用于非section-divider类的分割线
  :deep(.n-divider:not(.section-divider)) {
    margin: 5px 0 15px; // 减少上下边距，从8px 0 20px改为5px 0 15px
    background-image: linear-gradient(to right, var(--primary-color, #18a058) 0%, rgba(24, 160, 88, 0.1) 100%);
    height: 2px;
    opacity: 0.8;
  }

  // 隐藏分割线标题
  :deep(.n-divider-title) {
    display: none;
  }

  // 车辆表格样式
  .vehicle-table-container {
    margin-bottom: 20px;
  }

  .table-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
  }

  // 表格单元格样式
  :deep(.n-data-table .n-data-table-td) {
    padding: 8px 12px;
  }

  // 表格行悬停样式
  :deep(.n-data-table-tr:hover) {
    background-color: rgba(24, 160, 88, 0.05);
  }

  // 强制设置网格行间距
  :deep(.n-grid) {
    --n-y-gap: 10px !important;
    row-gap: 10px !important;
  }

  // 减少网格项内部的表单项底部间距
  :deep(.n-grid-item .n-form-item) {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  // 减少表单项内部元素的间距
  :deep(.n-form-item-feedback-wrapper) {
    min-height: 0 !important;
    margin-top: 2px !important;
  }

  // 减少表单项标签和内容之间的间距
  :deep(.n-form-item-label) {
    padding-bottom: 2px !important;
    height: auto !important;
    line-height: 1.2 !important;
  }

  // 减少网格项的内边距
  :deep(.n-grid-item__content) {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}

// 二手车置换信息的行内表单项样式
.inline-form-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.inline-label {
  font-weight: 500;
  color: #333;
  margin-right: 16px;
  min-width: 150px;
}

// 选项行样式
.option-row {
  display: flex;
  align-items: center; // 改回居中对齐
  margin-bottom: 16px; // 调整底部边距
  flex-wrap: nowrap;
  position: relative; // 添加相对定位
  min-height: 32px; // 确保最小高度与单选按钮高度一致

  // 为所有可能包含提示词的行预留足够空间
  &.has-tip {
    min-height: 32px; // 调整高度，与按钮高度一致
  }
}

.option-label {
  font-weight: 500;
  color: #333;
  margin-right: 16px;
  white-space: nowrap;
  width: 250px; // 固定宽度，确保所有标签宽度一致
  display: inline-block; // 确保宽度生效
  text-align: left; // 文本左对齐
}

// 选项按钮组样式
.option-row .n-radio-group {
  position: absolute; // 绝对定位
  left: 250px; // 与标签宽度一致，确保所有按钮组对齐
  top: 50%; // 垂直居中
  transform: translateY(-50%); // 垂直居中
  z-index: 1; // 确保按钮组位于提示词之上
}

// 选项提示样式
.option-tip {
  color: #18a058 !important; // 使用主题色，添加!important确保样式能够正确应用
  font-size: 14px !important;
  position: absolute !important; // 使用绝对定位
  left: calc(250px + 100px) !important; // 放在选项按钮右侧（250px + 最小按钮组宽度）
  white-space: nowrap !important; // 不换行
  display: inline-block !important; // 使其与按钮在同一行
  top: 50% !important; // 垂直居中
  transform: translateY(-50%) !important; // 垂直居中
  line-height: 20px !important; // 调整行高，使文本更易读
  font-weight: 500 !important; // 加粗字体，使其更加醒目
  margin-left: 10px !important; // 添加左侧外边距，与按钮保持一定距离
}

// 万元单位价格显示样式
.price-in-wan {
  color: #ff4d4f;
  font-size: 14px;
  font-weight: 500;
}