/**
 * 测试简化后的车牌号和VIN校验规则
 */

// 车牌号校验函数（简化版）
function validatePlateNumber(value) {
  if (!value) {
    return { valid: false, message: '请输入车牌号' }
  }
  
  // 车牌号必须是7位或8位
  if (value.length !== 7 && value.length !== 8) {
    return { valid: false, message: '车牌号必须是7位或8位字符' }
  }
  
  return { valid: true, message: '车牌号长度正确' }
}

// VIN校验函数（简化版）
function validateVIN(value) {
  if (!value) {
    return { valid: false, message: '请输入VIN' }
  }
  
  // VIN必须是17位字符
  if (value.length !== 17) {
    return { valid: false, message: 'VIN必须是17位字符' }
  }
  
  return { valid: true, message: 'VIN长度正确' }
}

// 测试用例
console.log('=== 简化后的车牌号测试 ===')

// 正确长度的车牌号（不检查格式）
console.log('京A12345 (7位):', validatePlateNumber('京A12345'))
console.log('沪B23456 (7位):', validatePlateNumber('沪B23456'))
console.log('粤AD12345 (8位):', validatePlateNumber('粤AD12345'))

// 包含小写字母的车牌号（应该通过）
console.log('京a12345 (7位小写):', validatePlateNumber('京a12345'))
console.log('粤ad12345 (8位小写):', validatePlateNumber('粤ad12345'))

// 包含特殊字符的车牌号（应该通过，只检查长度）
console.log('ABC1234 (7位英文):', validatePlateNumber('ABC1234'))
console.log('12345678 (8位数字):', validatePlateNumber('12345678'))

// 错误长度的车牌号
console.log('京A1234 (6位):', validatePlateNumber('京A1234'))
console.log('京A123456789 (9位):', validatePlateNumber('京A123456789'))
console.log('空字符串:', validatePlateNumber(''))

console.log('\n=== 简化后的VIN测试 ===')

// 正确长度的VIN（不检查格式）
console.log('WBAVB13506PT12345 (17位):', validateVIN('WBAVB13506PT12345'))
console.log('LSGBC53L8EH123456 (17位):', validateVIN('LSGBC53L8EH123456'))

// 包含小写字母的VIN（应该通过）
console.log('wbavb13506pt12345 (17位小写):', validateVIN('wbavb13506pt12345'))

// 包含I、O、Q的VIN（应该通过，只检查长度）
console.log('WBIVB13506PT12345 (包含I):', validateVIN('WBIVB13506PT12345'))
console.log('WBOVB13506PT12345 (包含O):', validateVIN('WBOVB13506PT12345'))
console.log('WBQVB13506PT12345 (包含Q):', validateVIN('WBQVB13506PT12345'))

// 数字开头的VIN（应该通过，只检查长度）
console.log('1BAVB13506PT12345 (数字开头):', validateVIN('1BAVB13506PT12345'))

// 全数字的VIN（应该通过，只检查长度）
console.log('12345678901234567 (全数字):', validateVIN('12345678901234567'))

// 错误长度的VIN
console.log('WBAVB13506PT1234 (16位):', validateVIN('WBAVB13506PT1234'))
console.log('WBAVB13506PT123456 (18位):', validateVIN('WBAVB13506PT123456'))
console.log('空字符串:', validateVIN(''))

console.log('\n=== 边界情况测试 ===')

// 特殊字符
console.log('车牌号包含空格 "京A 1234" (7位):', validatePlateNumber('京A 1234'))
console.log('VIN包含空格 "WBAVB13506PT 1234" (17位):', validateVIN('WBAVB13506PT 1234'))

// 混合字符
console.log('车牌号混合 "京A1b3c5" (7位):', validatePlateNumber('京A1b3c5'))
console.log('VIN混合 "WBAVb13506pt12345" (17位):', validateVIN('WBAVb13506pt12345'))
