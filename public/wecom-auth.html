<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业微信授权中间页</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }

        .status.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .status.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .debug-info {
            background-color: #fafafa;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }

        .debug-info h3 {
            margin-top: 0;
            color: #666;
        }

        .debug-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #1890ff;
        }

        .debug-item strong {
            color: #1890ff;
            display: inline-block;
            min-width: 120px;
        }

        .code-display {
            background-color: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }

        .btn:hover {
            background-color: #40a9ff;
        }

        .btn.secondary {
            background-color: #f5f5f5;
            color: #666;
        }

        .btn.secondary:hover {
            background-color: #e6f7ff;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>企业微信授权处理</h1>
            <p>正在处理您的登录授权...</p>
        </div>

        <div id="status" class="status loading">
            <span class="loading-spinner"></span>
            正在获取授权码...
        </div>

        <div class="debug-info">
            <h3>调试信息</h3>
            <div class="debug-item">
                <strong>当前URL:</strong>
                <span id="current-url"></span>
            </div>
            <div class="debug-item">
                <strong>授权码(code):</strong>
                <span id="auth-code"></span>
            </div>
            <div class="debug-item">
                <strong>请求URL:</strong>
                <span id="request-url"></span>
            </div>
            <div class="debug-item">
                <strong>请求状态:</strong>
                <span id="request-status">等待中...</span>
            </div>
            <div class="debug-item">
                <strong>响应时间:</strong>
                <span id="response-time">-</span>
            </div>
        </div>

        <div id="response-section" style="display: none;">
            <h3>API响应结果</h3>
            <div class="code-display" id="response-data"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn secondary" onclick="location.reload()">重新加载</button>
            <button class="btn" onclick="copyDebugInfo()">复制调试信息</button>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 更新状态显示
        function updateStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;

            if (type === 'loading') {
                statusEl.innerHTML = `<span class="loading-spinner"></span>${message}`;
            } else {
                statusEl.innerHTML = message;
            }
        }

        // 更新调试信息
        function updateDebugInfo(key, value) {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = value;
            }
        }

        // 复制调试信息
        function copyDebugInfo() {
            const debugInfo = {
                currentUrl: document.getElementById('current-url').textContent,
                authCode: document.getElementById('auth-code').textContent,
                requestUrl: document.getElementById('request-url').textContent,
                requestStatus: document.getElementById('request-status').textContent,
                responseTime: document.getElementById('response-time').textContent,
                responseData: document.getElementById('response-data').textContent
            };

            const debugText = Object.entries(debugInfo)
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n');

            navigator.clipboard.writeText(debugText).then(() => {
                alert('调试信息已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 发送登录请求
        async function sendLoginRequest(code) {
            const apiUrl = `https://jwd.vooice.tech/open-api/system/wecom/login?code=${code}`;
            const startTime = Date.now();

            updateDebugInfo('request-url', apiUrl);
            updateDebugInfo('request-status', '发送中...');
            updateStatus('正在发送登录请求...', 'loading');

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const endTime = Date.now();
                const responseTime = `${endTime - startTime}ms`;
                updateDebugInfo('response-time', responseTime);

                const responseData = await response.json();

                // 显示响应数据
                document.getElementById('response-section').style.display = 'block';
                document.getElementById('response-data').textContent = JSON.stringify(responseData, null, 2);

                if (response.ok && responseData.code === 200) {
                    updateStatus('✅ 登录成功！', 'success');
                    updateDebugInfo('request-status', '成功');

                    // 显示用户信息
                    if (responseData.data) {
                        const userInfo = responseData.data;
                        updateStatus(`✅ 登录成功！欢迎 ${userInfo.nickname}`, 'success');
                    }
                } else {
                    updateStatus(`❌ 登录失败: ${responseData.message || '未知错误'}`, 'error');
                    updateDebugInfo('request-status', '失败');
                }

            } catch (error) {
                const endTime = Date.now();
                const responseTime = `${endTime - startTime}ms`;
                updateDebugInfo('response-time', responseTime);
                updateDebugInfo('request-status', '网络错误');
                updateStatus(`❌ 网络错误: ${error.message}`, 'error');

                // 显示错误信息
                document.getElementById('response-section').style.display = 'block';
                document.getElementById('response-data').textContent = `错误信息: ${error.message}\n堆栈: ${error.stack}`;

                console.error('登录请求失败:', error);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function () {
            // 显示当前URL
            updateDebugInfo('current-url', window.location.href);

            // 获取code参数
            const code = getUrlParameter('code');
            updateDebugInfo('auth-code', code || '未找到');

            if (code) {
                // 如果有code参数，发送登录请求
                sendLoginRequest(code);
            } else {
                // 如果没有code参数，显示错误
                updateStatus('❌ 未找到授权码(code)参数', 'error');
                updateDebugInfo('request-status', '参数缺失');
            }
        });
    </script>
</body>

</html>