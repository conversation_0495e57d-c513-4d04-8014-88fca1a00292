<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        .file-info {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <h1>CSV文件上传测试</h1>

    <div class="test-section">
        <h2>测试说明</h2>
        <div class="info test-result">
            <p>此页面用于测试 FileUploadButton 组件对 CSV 文件的支持。</p>
            <p>测试内容包括：</p>
            <ul>
                <li>CSV 文件格式验证</li>
                <li>文件扩展名检查</li>
                <li>文件大小限制</li>
                <li>多文件上传支持</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>文件格式测试</h2>
        <p>请选择不同格式的文件进行测试：</p>

        <div>
            <label>测试 CSV 文件：</label>
            <input type="file" id="csvFile" accept=".csv" />
            <button onclick="testFileFormat('csvFile', '.csv')">测试 CSV</button>
        </div>

        <div>
            <label>测试 Excel 文件：</label>
            <input type="file" id="excelFile" accept=".xlsx,.xls" />
            <button onclick="testFileFormat('excelFile', '.xlsx,.xls')">测试 Excel</button>
        </div>

        <div>
            <label>测试不支持的文件：</label>
            <input type="file" id="otherFile" />
            <button onclick="testFileFormat('otherFile', '.txt,.pdf')">测试其他格式</button>
        </div>

        <div id="formatTestResult"></div>
    </div>

    <div class="test-section">
        <h2>多文件上传测试</h2>
        <p>选择多个 CSV 文件进行测试：</p>
        <input type="file" id="multipleFiles" accept=".csv,.xlsx,.xls" multiple />
        <button onclick="testMultipleFiles()">测试多文件</button>
        <div id="multipleTestResult"></div>
    </div>

    <div class="test-section">
        <h2>扩展名参数测试</h2>
        <p>测试文件扩展名提取功能：</p>
        <input type="text" id="testFileName" placeholder="输入文件名，如: test.csv"
            style="width: 200px; margin-right: 10px;" />
        <button onclick="testExtensionExtraction()">测试扩展名提取</button>
        <div id="extensionTestResult"></div>
    </div>

    <div class="test-section">
        <h2>创建测试 CSV 文件</h2>
        <p>点击下面的按钮创建一个测试用的 CSV 文件：</p>
        <button onclick="createTestCSV()">创建测试 CSV</button>
        <div id="csvCreateResult"></div>
    </div>

    <script>
        // 模拟 FileUploadButton 组件的文件验证逻辑
        function validateFileFormat(file, acceptFormats) {
            if (acceptFormats === "*" || !acceptFormats) {
                return { valid: true, message: "接受所有文件格式" };
            }

            const fileExt = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();
            const acceptedExts = acceptFormats.split(",").map(ext => ext.trim().toLowerCase());

            if (!acceptedExts.includes(fileExt)) {
                return {
                    valid: false,
                    message: `不支持的文件格式: ${fileExt}，请上传 ${acceptFormats} 格式的文件`
                };
            }

            return { valid: true, message: `文件格式验证通过: ${fileExt}` };
        }

        function testFileFormat(inputId, acceptFormats) {
            const input = document.getElementById(inputId);
            const resultDiv = document.getElementById('formatTestResult');

            if (!input.files.length) {
                resultDiv.innerHTML = '<div class="error test-result">请先选择文件</div>';
                return;
            }

            const file = input.files[0];
            const result = validateFileFormat(file, acceptFormats);

            const fileInfo = `
                <div class="file-info">
                    <strong>文件信息：</strong><br>
                    文件名: ${file.name}<br>
                    文件大小: ${(file.size / 1024).toFixed(2)} KB<br>
                    文件类型: ${file.type || '未知'}
                </div>
            `;

            const resultClass = result.valid ? 'success' : 'error';
            resultDiv.innerHTML = fileInfo + `<div class="${resultClass} test-result">${result.message}</div>`;
        }

        function testMultipleFiles() {
            const input = document.getElementById('multipleFiles');
            const resultDiv = document.getElementById('multipleTestResult');

            if (!input.files.length) {
                resultDiv.innerHTML = '<div class="error test-result">请先选择文件</div>';
                return;
            }

            let results = '<div class="info test-result">多文件测试结果：</div>';

            for (let i = 0; i < input.files.length; i++) {
                const file = input.files[i];
                const result = validateFileFormat(file, '.csv,.xlsx,.xls');
                const resultClass = result.valid ? 'success' : 'error';

                results += `
                    <div class="file-info">
                        <strong>文件 ${i + 1}:</strong> ${file.name} (${(file.size / 1024).toFixed(2)} KB)
                        <div class="${resultClass} test-result">${result.message}</div>
                    </div>
                `;
            }

            resultDiv.innerHTML = results;
        }

        function testExtensionExtraction() {
            const input = document.getElementById('testFileName');
            const resultDiv = document.getElementById('extensionTestResult');

            if (!input.value.trim()) {
                resultDiv.innerHTML = '<div class="error test-result">请输入文件名</div>';
                return;
            }

            const fileName = input.value.trim();

            // 模拟 FileUploadButton 组件中的扩展名提取逻辑
            const fileExt = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

            // 生成模拟的上传参数URL
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, "0");
            const day = String(now.getDate()).padStart(2, "0");
            const dateStr = `${year}${month}${day}`;
            const uploadParamsUrl = `/system/preUploadParams?open_id=${dateStr}&ext_name=${fileExt}`;

            resultDiv.innerHTML = `
                <div class="info test-result">
                    <strong>扩展名提取结果：</strong><br>
                    文件名: ${fileName}<br>
                    提取的扩展名: ${fileExt}<br>
                    生成的API URL: ${uploadParamsUrl}
                </div>
            `;
        }

        function createTestCSV() {
            const csvContent = `VIN,车型代码,车型名称,配置代码,颜色,启票日期,启票单位
TESTVIN001,MODEL001,测试车型1,CONFIG001,白色,2024-01-15,测试单位1
TESTVIN002,MODEL002,测试车型2,CONFIG002,黑色,2024-01-16,测试单位2
TESTVIN003,MODEL003,测试车型3,CONFIG003,银色,2024-01-17,测试单位3`;

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', 'test-start-bill.csv');
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            document.getElementById('csvCreateResult').innerHTML =
                '<div class="success test-result">测试 CSV 文件已创建并下载</div>';
        }
    </script>
</body>

</html>