// 区域市场份额相关功能
document.addEventListener('DOMContentLoaded', function () {
    // 初始化进度条
    initMarketShareBars();
});

// 初始化市场份额进度条
function initMarketShareBars() {
    const items = document.querySelectorAll('.market-share-item');
    const percentages = [];

    // 获取所有百分比值
    items.forEach(item => {
        const percentText = item.querySelector('.market-share-percentage').textContent;
        const percent = parseFloat(percentText.replace('%', ''));
        percentages.push(percent);
    });

    // 找出最大值，用于计算相对宽度
    const maxPercent = Math.max(...percentages);

    // 设置进度条宽度
    items.forEach((item, index) => {
        const bar = item.querySelector('.market-share-bar');
        const percent = percentages[index];
        const width = (percent / maxPercent) * 100;

        // 使用延迟动画效果
        setTimeout(() => {
            bar.style.width = `${width}%`;
        }, index * 200);
    });
}
