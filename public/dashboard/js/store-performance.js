// 门店业绩预览相关功能
document.addEventListener('DOMContentLoaded', function () {
    // 初始化进度条
    initStorePerformanceBars();

    // 初始化销量和销售额可视化
    initDataVisualBars();
});

// 初始化门店业绩进度条
function initStorePerformanceBars() {
    const items = document.querySelectorAll('.store-performance-item');
    const amounts = [];

    // 获取所有销售额值
    items.forEach(item => {
        const amountValue = item.querySelector('.amount-visual .visual-value').textContent;
        const amount = parseFloat(amountValue.replace('w', ''));
        amounts.push(amount);
    });

    // 找出最大值，用于计算相对宽度
    const maxAmount = Math.max(...amounts);

    // 设置进度条宽度
    items.forEach((item, index) => {
        const bar = item.querySelector('.store-performance-bar');
        const amount = amounts[index];
        const width = (amount / maxAmount) * 100;

        // 使用延迟动画效果
        setTimeout(() => {
            bar.style.width = `${width}%`;
        }, index * 200);
    });
}

// 初始化销量和销售额可视化进度条
function initDataVisualBars() {
    const items = document.querySelectorAll('.store-performance-item');

    // 获取所有销量和销售额值
    const volumes = [];
    const amounts = [];

    items.forEach(item => {
        const volumeValue = item.querySelector('.volume-visual .visual-value').textContent;
        const amountValue = item.querySelector('.amount-visual .visual-value').textContent;

        const volume = parseInt(volumeValue.replace('台', ''));
        const amount = parseFloat(amountValue.replace('w', ''));

        volumes.push(volume);
        amounts.push(amount);
    });

    // 找出最大值
    const maxVolume = Math.max(...volumes);
    const maxAmount = Math.max(...amounts);

    // 设置进度条宽度
    items.forEach((item, index) => {
        const volumeBar = item.querySelector('.volume-visual .visual-bar-fill');
        const amountBar = item.querySelector('.amount-visual .visual-bar-fill');

        const volumeWidth = (volumes[index] / maxVolume) * 100;
        const amountWidth = (amounts[index] / maxAmount) * 100;

        // 使用延迟动画效果
        setTimeout(() => {
            volumeBar.style.width = `${volumeWidth}%`;
            amountBar.style.width = `${amountWidth}%`;
        }, index * 200);
    });
}
