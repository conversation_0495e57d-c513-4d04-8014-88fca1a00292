/* 门店业绩预览样式 */
.store-performance {
    padding: 0.05rem 0.1rem;
    position: relative;
    /* height: 1.3rem; */
    overflow: hidden;
}

.store-performance-title {
    text-align: center;
    margin: 0 0 0.08rem 0;
    position: relative;
}

.store-performance-title span {
    border: 1px solid rgba(25, 186, 139, 0.17);
    letter-spacing: 2px;
    padding: 0.01rem 0.15rem;
    background: rgba(255, 255, 255, 0.05);
    font-size: 0.16rem;
    color: #fff;
    position: relative;
    z-index: 2;
    font-weight: 500;
}

.store-performance-title:before,
.store-performance-title:after {
    position: absolute;
    width: 32%;
    height: 1px;
    background: linear-gradient(to right, rgba(25, 186, 139, 0.1), rgba(25, 186, 139, 0.7));
    content: "";
    top: 0.12rem;
}

.store-performance-title:after {
    right: 0;
    background: linear-gradient(to left, rgba(25, 186, 139, 0.1), rgba(25, 186, 139, 0.7));
}

.store-performance-title:before {
    left: 0;
}

.store-performance-list {
    display: flex;
    flex-direction: column;
    gap: 0.06rem;
}

.store-performance-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(25, 186, 139, 0.17);
    border-radius: 2px;
    padding: 0.04rem 0.08rem;
    position: relative;
    transition: all 0.3s ease;
    height: 0.52rem;
}

.store-performance-item:hover {
    background: rgba(25, 186, 139, 0.08);
}

.store-performance-rank {
    width: 0.2rem;
    height: 0.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(25, 186, 139, 0.2);
    border-radius: 2px;
    color: #fff;
    font-weight: bold;
    margin-right: 0.08rem;
    font-size: 0.12rem;
}

.store-performance-item:nth-child(1) .store-performance-rank {
    background: linear-gradient(135deg, #ffb302, #f5ce62);
}

.store-performance-item:nth-child(2) .store-performance-rank {
    background: linear-gradient(135deg, #b8c6db, #f5f7fa);
}

.store-performance-item:nth-child(3) .store-performance-rank {
    background: linear-gradient(135deg, #cd7f32, #e6bc9c);
}

.store-performance-info {
    flex: 1;
    display: flex;
    align-items: center;
}

.store-performance-store {
    color: #fff;
    font-size: 0.14rem;
    font-weight: 500;
    width: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.store-performance-data {
    display: flex;
    align-items: center;
    margin-left: 0.08rem;
    flex: 1;
}

/* 销量和销售额图形化展示 */
.data-visual {
    display: flex;
    align-items: center;
    gap: 0.1rem;
}

.volume-visual,
.amount-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.volume-visual {
    margin-right: 1.05rem;
}

.visual-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.11rem;
    margin-bottom: 0.02rem;
}

.visual-bar-container {
    position: relative;
    height: 0.12rem;
    display: flex;
    align-items: center;
}

.visual-value {
    padding-left: 1.5rem;
    /* position: absolute; */
    top: -0.08rem;
    right: -1.5rem;
    font-size: 0.22rem;
    font-weight: bold;
}

.volume-visual .visual-value {
    color: #fff;
}

.amount-visual .visual-value {
    color: #ffeb7b;
}

.visual-bar {
    height: 0.08rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.volume-visual .visual-bar {
    width: 0.4rem;
}

.amount-visual .visual-bar {
    width: 0.6rem;
}

.visual-bar-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease;
}

.volume-visual .visual-bar-fill {
    background: linear-gradient(to right, #3498db, #2980b9);
}

.amount-visual .visual-bar-fill {
    background: linear-gradient(to right, #f1c40f, #f39c12);
}

.store-performance-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(25, 186, 139, 0.7), rgba(73, 188, 247, 0.7));
    border-radius: 0 2px 2px 0;
    transition: width 1s ease;
}