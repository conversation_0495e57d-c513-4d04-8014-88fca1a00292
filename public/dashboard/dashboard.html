﻿<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>经纬度集团运营数据驾驶舱</title>
    <script type="text/javascript" src="js/jquery.js"></script>
    <link rel="stylesheet" href="css/comon0.css">
    <link rel="stylesheet" href="css/market-share.css">
    <link rel="stylesheet" href="css/store-performance.css">
</head>

<body>
    <div class="loading">
        <div class="loadbox"> <img src="images/loading.gif"></div>
    </div>
    <div class="head">
        <h1>经纬度集团运营数据驾驶舱</h1>
        <div class="weather"><span id="today"></span></div>
    </div>
    <div class="mainbox">
        <ul class="clearfix">
            <li>
                <div class="boxall" style="height: 2.7rem">
                    <div class="alltitle">生意参谋</div>

                    <div class="sycm">
                        <ul class="clearfix">
                            <li>
                                <h2>1824</h2><span>今日销量</span>
                            </li>
                            <li>
                                <h2>1920</h2><span>昨日销量</span>
                            </li>
                            <li>
                                <h2>19%</h2><span>环比增长</span>
                            </li>
                        </ul>
                        <div style="border-bottom: 1px solid rgba(255,255,255,.1)"></div>
                        <ul class="clearfix">
                            <li>
                                <h2>1824</h2><span>今日销售额</span>
                            </li>
                            <li>
                                <h2>1920</h2><span>昨日销售额</span>
                            </li>
                            <li>
                                <h2>19%</h2><span>环比增长</span>
                            </li>
                        </ul>
                    </div>
                    <div class="boxfoot"></div>
                </div>
                <div class="boxall" style="height: 2.65rem">
                    <div class="alltitle">财务状况</div>
                    <div class="sy" id="echarts1"></div>
                    <div class="sy" id="echarts2"></div>
                    <div class="sy" id="echarts3"></div>
                    <div class="boxfoot"></div>
                </div>
                <div class="boxall" style="height: 3.4rem">
                    <div class="alltitle">品牌分布</div>
                    <div id="echarts4" style="height: 2.2rem; width: 100%;"></div>
                    <div class="boxfoot"></div>
                </div>
            </li>
            <li>
                <div class="bar">
                    <div class="barbox">
                        <ul class="clearfix">
                            <li class="pulll_left counter">51,189</li>
                            <li class="pulll_left counter">25,343</li>
                        </ul>
                    </div>
                    <div class="barbox2">
                        <ul class="clearfix">
                            <li class="pulll_left">年度成交量(台)</li>
                            <li class="pulll_left">年度成交额(万元)</li>
                        </ul>
                    </div>
                </div>
                <div class="map" style="position: relative; width: 100%; height: 20vh;">
                    <iframe src="../map.html"
                        style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; border: none; z-index: -1;"></iframe>
                </div>
            </li>
            <li>
                <div class="boxall" style="height:5.2rem">
                    <div class="alltitle">库存总览</div>
                    <div class="tabs">
                        <ul class="clearfix">
                            <li><a class="active" href="#">7天</a></li>
                            <li><a href="#">15天</a></li>
                            <li><a href="#">30天</a></li>
                        </ul>
                    </div>
                    <div class="clearfix">
                        <div class="sy" id="echarts6"></div>
                        <div class="sy" id="echarts7"></div>
                        <div class="sy" id="echarts8"></div>
                    </div>
                    <div class="store-performance">
                        <div class="store-performance-title"><span>门店业绩预览</span></div>
                        <div class="store-performance-list">
                            <div class="store-performance-item">
                                <div class="store-performance-rank">1</div>
                                <div class="store-performance-info">
                                    <div class="store-performance-store">山东翔悦达</div>
                                    <div class="store-performance-data">
                                        <div class="data-visual">

                                            <div class="amount-visual">
                                                <div class="visual-bar-container">
                                                    <span class="visual-value">108.88万元</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="store-performance-bar" style="width: 0"></div>
                            </div>
                            <div class="store-performance-item">
                                <div class="store-performance-rank">2</div>
                                <div class="store-performance-info">
                                    <div class="store-performance-store">临沂龙吉安</div>
                                    <div class="store-performance-data">
                                        <div class="data-visual">

                                            <div class="amount-visual">
                                                <div class="visual-bar-container">
                                                    <span class="visual-value">95.55万元</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="store-performance-bar" style="width: 0"></div>
                            </div>
                            <div class="store-performance-item">
                                <div class="store-performance-rank">3</div>
                                <div class="store-performance-info">
                                    <div class="store-performance-store">聊城东安圣</div>
                                    <div class="store-performance-data">
                                        <div class="data-visual">

                                            <div class="amount-visual">
                                                <div class="visual-bar-container">
                                                    <span class="visual-value">92.5万元</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="store-performance-bar" style="width: 0"></div>
                            </div>
                        </div>
                    </div>
                    <div class="boxfoot"></div>
                </div>
                <div class="boxall" style="height: 3.9rem;">
                    <div class="market-share-title">2025年02月区域市场份额</div>
                    <div class="market-share-container">
                        <ul class="market-share-list">
                            <li class="market-share-item">
                                <div class="market-share-rank">1</div>
                                <div class="market-share-brand">阿维塔</div>
                                <div class="market-share-volume">1,250台</div>
                                <div class="market-share-percentage">2.58%</div>
                                <div class="market-share-bar" style="width: 0"></div>
                            </li>
                            <li class="market-share-item">
                                <div class="market-share-rank">2</div>
                                <div class="market-share-brand">深蓝</div>
                                <div class="market-share-volume">2,345台</div>
                                <div class="market-share-percentage">3.25%</div>
                                <div class="market-share-bar" style="width: 0"></div>
                            </li>
                            <li class="market-share-item">
                                <div class="market-share-rank">3</div>
                                <div class="market-share-brand">凯程</div>
                                <div class="market-share-volume">1,233台</div>
                                <div class="market-share-percentage">4.28%</div>
                                <div class="market-share-bar" style="width: 0"></div>
                            </li>
                            <li class="market-share-item">
                                <div class="market-share-rank">4</div>
                                <div class="market-share-brand">引力</div>
                                <div class="market-share-volume">789台</div>
                                <div class="market-share-percentage">0.25%</div>
                                <div class="market-share-bar" style="width: 0"></div>
                            </li>
                            <li class="market-share-item">
                                <div class="market-share-rank">5</div>
                                <div class="market-share-brand">启源</div>
                                <div class="market-share-volume">1,223台</div>
                                <div class="market-share-percentage">8.22%</div>
                                <div class="market-share-bar" style="width: 0"></div>
                            </li>
                        </ul>
                    </div>
                    <div class="boxfoot"></div>
                </div>
            </li>
        </ul>

    </div>
    <div class="back"></div>
    <script language="JavaScript" src="js/js.js"></script>
    <script type="text/javascript" src="js/echarts.min.js"></script>
    <script type="text/javascript" src="js/china.js"></script>
    <!-- <script type="text/javascript" src="js/area_echarts.js"></script> -->
    <script type="text/javascript">
        $(document).ready(function () {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            $("#today").html(`${year}年${month}月${day}日`);
            myChart1.resize();
            myChart2.resize();
            myChart3.resize();
            myChart6.resize();
            myChart7.resize();
            myChart8.resize();
        })
        window.addEventListener("resize", function () {
            myChart1.resize();
            myChart2.resize();
            myChart3.resize();
            myChart6.resize();
            myChart7.resize();
            myChart8.resize();
        });
    </script>
    <script type="text/javascript">
        var v0 = 1000;
        var v1 = 353;
        var v2 = 178;
        var v3 = 68;
        var myChart6 = echarts.init(document.getElementById('echarts6'));
        option6 = {
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#0088cc',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v1,
                    name: '在途车台',
                    label: {
                        normal: {
                            formatter: v1 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                },
                {
                    value: v0,
                    name: '在库车台',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v1 / v0 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]

        };

        var myChart7 = echarts.init(document.getElementById('echarts7'));
        option7 = {
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#fccb00',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v2,
                    name: '在途车台',
                    label: {
                        normal: {
                            formatter: v2 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                },
                {
                    value: v0,
                    name: '库存车台',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v2 / v0 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        var myChart8 = echarts.init(document.getElementById('echarts8'));
        option8 = {
            series: [{

                type: 'pie',
                radius: ['70%', '80%'],
                color: '#62b62f',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v3,
                    name: '女消费',
                    label: {
                        normal: {
                            formatter: v3 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                }, {
                    value: v0,
                    name: '男消费',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v2 / v0 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        setTimeout(function () {
            myChart6.setOption(option6);
            myChart7.setOption(option7);
            myChart8.setOption(option8);
        }, 500);

    </script>
    <script type="text/javascript">
        var myChart1 = echarts.init(document.getElementById('echarts1'));
        var v1 = 298;
        var v2 = 523;
        var v3 = v1 + v2;
        option1 = {
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#0088cc',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v1,
                    name: '平均单客价',
                    label: {
                        normal: {
                            formatter: v3 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                },
                ]
            }]
        };
        var myChart3 = echarts.init(document.getElementById('echarts3'));
        var v1 = 298
        var v2 = 523
        var v3 = v1 + v2
        option2 = {
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#fccb00',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v1,
                    name: '男消费',
                    label: {
                        normal: {
                            formatter: v1 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                }, {
                    value: v2,
                    name: '女消费',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v1 / v3 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        var myChart2 = echarts.init(document.getElementById('echarts2'));
        option3 = {
            series: [{

                type: 'pie',
                radius: ['70%', '80%'],
                color: '#62b62f',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v2,
                    name: '女消费',
                    label: {
                        normal: {
                            formatter: v2 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                }, {
                    value: v1,
                    name: '男消费',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v2 / v3 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        setTimeout(function () {
            myChart1.setOption(option1);
            myChart2.setOption(option2);
            myChart3.setOption(option3);
        }, 500);

    </script>
    <script type="text/javascript">
        var myChart = echarts.init(document.getElementById('echarts4'));
        var plantCap = [{
            name: '阿维塔',
            value: '322'
        }, {
            name: '启源',
            value: '115'
        }, {
            name: '凯程',
            value: '113'
        }, {
            name: '深蓝',
            value: '95'
        }, {
            name: '引力',
            value: '35'
        }, {
            name: '其他',
            value: '92'
        }];
        var datalist = [{
            offset: [56, 48],
            symbolSize: 180,
            color: 'rgba(73,188,247,.14)',
        }, {
            offset: [30, 70],
            symbolSize: 105,
            color: 'rgba(73,188,247,.14)'
        }, {
            offset: [0, 43],
            symbolSize: 115,
            color: 'rgba(73,188,247,.14)'

        }, {
            offset: [93, 30],
            symbolSize: 93,
            color: 'rgba(73,188,247,.14)'
        }, {
            offset: [26, 19],
            symbolSize: 99,
            color: 'rgba(73,188,247,.14)'
        }, {
            offset: [75, 75],
            symbolSize: 86,
            color: 'rgba(73,188,247,.14)'
        }];

        var datas = [];
        for (var i = 0; i < plantCap.length; i++) {
            var item = plantCap[i];
            var itemToStyle = datalist[i];
            datas.push({
                name: item.value + '\n' + item.name,
                value: itemToStyle.offset,
                symbolSize: itemToStyle.symbolSize,
                label: {
                    normal: {
                        textStyle: {
                            fontSize: 14
                        }
                    }
                },

                itemStyle: {
                    normal: {
                        color: itemToStyle.color,
                        opacity: itemToStyle.opacity
                    }
                },
            })
        }
        option = {
            grid: {
                show: false,
                top: 10,
                bottom: 10
            },

            xAxis: [{
                gridIndex: 0,
                type: 'value',
                show: false,
                min: 0,
                max: 100,
                nameLocation: 'middle',
                nameGap: 5
            }],

            yAxis: [{
                gridIndex: 0,
                min: 0,
                show: false,
                max: 100,
                nameLocation: 'middle',
                nameGap: 30
            }],
            series: [{
                type: 'scatter',
                symbol: 'circle',
                symbolSize: 120,
                label: {
                    normal: {
                        show: true,
                        formatter: '{b}',
                        color: '#FFF',
                        textStyle: {
                            fontSize: '30'
                        }
                    },
                },
                itemStyle: {
                    normal: {
                        color: '#F30'
                    }
                },
                data: datas
            }]

        };
        myChart.setOption(option);
        $(document).ready(function () {
            myChart.resize();

        })
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    </script>
    <script type="text/javascript" src="js/jquery.waypoints.min.js"></script>
    <script type="text/javascript" src="js/jquery.countup.min.js"></script>
    <script type="text/javascript" src="js/market-share.js"></script>
    <script type="text/javascript" src="js/store-performance.js"></script>
    <script type="text/javascript">
        $('.counter').countUp();
    </script>

</body>

</html>