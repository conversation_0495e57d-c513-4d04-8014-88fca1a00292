<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>L7 Choropleth Map</title>
    <!-- 引入 L7 和 L7Plot 的样式 -->
    <link href="https://unpkg.com/@antv/l7@2.6.24/dist/l7.css" rel="stylesheet">
    <link href="https://unpkg.com/@antv/l7plot@2.6.24/dist/l7plot.css" rel="stylesheet">
    <!-- 引入 L7 和 L7Plot 的脚本 -->
    <script src="https://unpkg.com/@antv/l7@2.6.24/dist/l7.min.js"></script>
    <script src="https://unpkg.com/@antv/l7plot@2.6.24/dist/l7plot.min.js"></script>
    <style>
        /* 设置地图容器的大小 */
        #map {
            width: 100%;
            height: 500px;
        }
    </style>
</head>
<body>
    <!-- 地图容器 -->
    <div id="map"></div>
    <script>
        // JavaScript 代码
        const { GaodeMap, Scene } = window.L7;
        const { Choropleth } = window.L7Plot;

        const scene = new Scene({
            id: 'map',
            map: new GaodeMap({
                style: 'blank',
                center: [120.19382669582967, 30.258134],
                zoom: 3,
            }),
        });

        scene.on('loaded', () => {
            fetch('https://gw.alipayobjects.com/os/alisis/geo-data-v0.1.1/administrative-data/area-list.json')
                .then((response) => response.json())
                .then((list) => {
                    const data = list
                        .filter(({ level, parent }) => level === 'city' && parent === 330000)
                        .map((item) => ({ ...item, value: Math.random() * 5000 }));

                    const choropleth = new Choropleth({
                        source: {
                            data,
                            joinBy: {
                                sourceField: 'adcode',
                                geoField: 'adcode',
                            },
                        },
                        viewLevel: {
                            level: 'province',
                            adcode: 330000,
                        },
                        autoFit: true,
                        color: {
                            field: 'value',
                            value: ['#B8E1FF', '#7DAAFF', '#3D76DD', '#0047A5', '#001D70'],
                            scale: { type: 'quantize' },
                        },
                        style: {
                            opacity: 1,
                            stroke: '#ccc',
                            lineWidth: 0.6,
                            lineOpacity: 1,
                        },
                        label: {
                            visible: true,
                            field: 'name',
                            style: {
                                fill: '#000',
                                opacity: 0.8,
                                fontSize: 10,
                                stroke: '#fff',
                                strokeWidth: 1.5,
                                textAllowOverlap: false,
                                padding: [5, 5],
                            },
                        },
                        state: {
                            active: { stroke: 'black', lineWidth: 1 },
                        },
                        tooltip: {
                            items: ['name', 'adcode', 'value'],
                        },
                        zoom: {
                            position: 'bottomright',
                        },
                        legend: {
                            position: 'bottomleft',
                        },
                    });

                    choropleth.addToScene(scene);
                });
        });
    </script>
</body>
</html>