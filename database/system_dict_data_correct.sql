-- =============================================
-- 系统字典初始化数据 (基于 mock/dictData.js 的真实数据)
-- =============================================

-- 清空现有数据
TRUNCATE TABLE `system_dict`;

-- 赠品类别
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('gift_category', '赠品类别', 'GOODS', '商品', 'SYSTEM', 1, '实物赠品'),
('gift_category', '赠品类别', 'SERVICES', '服务', 'SYSTEM', 2, '虚拟赠品');

-- 在售品牌
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('vehicle_brand', '在售品牌', '深蓝', '深蓝', 'SYSTEM', 2, '深蓝汽车', 'info', '#2080f0'),
('vehicle_brand', '在售品牌', '凯程', '凯程', 'SYSTEM', 3, '凯程汽车', 'warning', '#f0a020'),
('vehicle_brand', '在售品牌', '引力', '引力', 'SYSTEM', 4, '引力汽车', 'default', '#909399'),
('vehicle_brand', '在售品牌', '阿维塔', '阿维塔', 'SYSTEM', 5, '阿维塔汽车', 'error', '#d03050'),
('vehicle_brand', '在售品牌', '启源', '启源', 'SYSTEM', 6, '启源汽车', 'default', '#909399');

-- 应收科目
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('receivable_subject', '应收科目', 'vehicle_payment', '车款', 'SYSTEM', 1, '车辆销售款项', 'success', '#18a058'),
('receivable_subject', '应收科目', 'insurance_fee', '保险费', 'SYSTEM', 2, '车辆保险费用', 'info', '#2080f0'),
('receivable_subject', '应收科目', 'license_fee', '上牌费', 'SYSTEM', 3, '车辆上牌费用', 'warning', '#f0a020'),
('receivable_subject', '应收科目', 'service_fee', '服务费', 'SYSTEM', 4, '其他服务费用', 'default', '#909399'),
('receivable_subject', '应收科目', 'decoration_fee', '装潢费', 'SYSTEM', 5, '车辆装潢费用', 'info', '#2080f0'),
('receivable_subject', '应收科目', 'extended_warranty', '延保费', 'SYSTEM', 6, '延长保修费用', 'warning', '#f0a020'),
('receivable_subject', '应收科目', 'finance_fee', '金融服务费', 'SYSTEM', 7, '金融服务费用', 'error', '#d03050'),
('receivable_subject', '应收科目', 'other_fee', '其他费用', 'SYSTEM', 8, '其他杂项费用', 'default', '#909399');

-- 应收对象
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('receivable_target', '应收对象', 'customer', '客户', 'SYSTEM', 1, '直接客户', 'success', '#18a058'),
('receivable_target', '应收对象', 'finance_company', '金融公司', 'SYSTEM', 2, '金融机构', 'info', '#2080f0'),
('receivable_target', '应收对象', 'insurance_company', '保险公司', 'SYSTEM', 3, '保险机构', 'warning', '#f0a020'),
('receivable_target', '应收对象', 'partner', '合作方', 'SYSTEM', 4, '业务合作方', 'default', '#909399'),
('receivable_target', '应收对象', 'government', '政府机构', 'SYSTEM', 5, '政府相关机构', 'error', '#d03050');

-- 收款方式
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('recept_account', '收款方式', 'cash', '现金', 'SYSTEM', 1, '现金支付', 'success', '#18a058'),
('recept_account', '收款方式', 'bank_transfer', '银行转账', 'SYSTEM', 2, '银行转账', 'info', '#2080f0'),
('recept_account', '收款方式', 'pos_card', 'POS刷卡', 'SYSTEM', 3, 'POS机刷卡', 'warning', '#f0a020'),
('recept_account', '收款方式', 'wechat_pay', '微信支付', 'SYSTEM', 4, '微信支付', 'success', '#18a058'),
('recept_account', '收款方式', 'alipay', '支付宝', 'SYSTEM', 5, '支付宝支付', 'info', '#2080f0'),
('recept_account', '收款方式', 'check', '支票', 'SYSTEM', 6, '支票支付', 'default', '#909399'),
('recept_account', '收款方式', 'acceptance_bill', '承兑汇票', 'SYSTEM', 7, '银行承兑汇票', 'warning', '#f0a020'),
('recept_account', '收款方式', 'finance_loan', '金融贷款', 'SYSTEM', 8, '金融机构放款', 'error', '#d03050');

-- 贷款渠道
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('loan_channel', '贷款渠道', 'bank_loan', '银行贷款', 'SYSTEM', 1, '银行直接放贷', 'success', '#18a058'),
('loan_channel', '贷款渠道', 'finance_company', '金融公司', 'SYSTEM', 2, '汽车金融公司', 'info', '#2080f0'),
('loan_channel', '贷款渠道', 'consumer_finance', '消费金融', 'SYSTEM', 3, '消费金融公司', 'warning', '#f0a020'),
('loan_channel', '贷款渠道', 'p2p_platform', 'P2P平台', 'SYSTEM', 4, 'P2P借贷平台', 'error', '#d03050'),
('loan_channel', '贷款渠道', 'other_channel', '其他渠道', 'SYSTEM', 5, '其他金融渠道', 'default', '#909399');

-- 贷款期限
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('loan_months', '贷款期限', '12', '12个月', 'SYSTEM', 1, '1年期'),
('loan_months', '贷款期限', '24', '24个月', 'SYSTEM', 2, '2年期'),
('loan_months', '贷款期限', '36', '36个月', 'SYSTEM', 3, '3年期'),
('loan_months', '贷款期限', '48', '48个月', 'SYSTEM', 4, '4年期'),
('loan_months', '贷款期限', '60', '60个月', 'SYSTEM', 5, '5年期');

-- 专享优惠类型
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('exclusive_discount_type', '专享优惠类型', 'cash_discount', '现金优惠', 'SYSTEM', 1, '直接现金减免', 'success', '#18a058'),
('exclusive_discount_type', '专享优惠类型', 'gift_package', '礼品包', 'SYSTEM', 2, '赠送礼品套装', 'info', '#2080f0'),
('exclusive_discount_type', '专享优惠类型', 'service_discount', '服务优惠', 'SYSTEM', 3, '服务费用减免', 'warning', '#f0a020'),
('exclusive_discount_type', '专享优惠类型', 'insurance_discount', '保险优惠', 'SYSTEM', 4, '保险费用优惠', 'default', '#909399'),
('exclusive_discount_type', '专享优惠类型', 'finance_discount', '金融优惠', 'SYSTEM', 5, '金融利率优惠', 'error', '#d03050');

-- 定金状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('deposit_status', '定金状态', 'received', '已收定金', 'SYSTEM', 1, '已收到客户定金', 'success', '#18a058'),
('deposit_status', '定金状态', 'refunded', '已退定金', 'SYSTEM', 2, '已退还客户定金', 'error', '#d03050'),
('deposit_status', '定金状态', 'transferred', '已转车款', 'SYSTEM', 3, '定金已转入车款', 'info', '#2080f0');

-- 省/直辖市 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('province_city', '省/直辖市', 'shandong', '山东省', 'SYSTEM', 1, '山东省'),
('province_city', '省/直辖市', 'hebei', '河北省', 'SYSTEM', 2, '河北省'),
('province_city', '省/直辖市', 'henan', '河南省', 'SYSTEM', 3, '河南省'),
('province_city', '省/直辖市', 'chongqing', '重庆市', 'SYSTEM', 4, '重庆市');

-- 市/区 (基于dictData.js真实数据，使用parent_option_key关联省份)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `parent_option_key`) VALUES
-- 山东省城市
('city_district', '市/区', 'jinan', '济南市', 'SYSTEM', 1, '山东省济南市', 'shandong'),
('city_district', '市/区', 'dezhou', '德州市', 'SYSTEM', 2, '山东省德州市', 'shandong'),
('city_district', '市/区', 'zibo', '淄博市', 'SYSTEM', 3, '山东省淄博市', 'shandong'),
('city_district', '市/区', 'linyi', '临沂市', 'SYSTEM', 4, '山东省临沂市', 'shandong'),
('city_district', '市/区', 'liaocheng', '聊城市', 'SYSTEM', 5, '山东省聊城市', 'shandong'),
('city_district', '市/区', 'dongying', '东营市', 'SYSTEM', 6, '山东省东营市', 'shandong'),
('city_district', '市/区', 'binzhou', '滨州市', 'SYSTEM', 7, '山东省滨州市', 'shandong'),
-- 河北省城市
('city_district', '市/区', 'shijiazhuang', '石家庄市', 'SYSTEM', 8, '河北省石家庄市', 'hebei'),
('city_district', '市/区', 'tangshan', '唐山市', 'SYSTEM', 9, '河北省唐山市', 'hebei'),
('city_district', '市/区', 'qinhuangdao', '秦皇岛市', 'SYSTEM', 10, '河北省秦皇岛市', 'hebei'),
('city_district', '市/区', 'handan', '邯郸市', 'SYSTEM', 11, '河北省邯郸市', 'hebei'),
-- 河南省城市
('city_district', '市/区', 'zhengzhou', '郑州市', 'SYSTEM', 12, '河南省郑州市', 'henan'),
('city_district', '市/区', 'luoyang', '洛阳市', 'SYSTEM', 13, '河南省洛阳市', 'henan'),
('city_district', '市/区', 'kaifeng', '开封市', 'SYSTEM', 14, '河南省开封市', 'henan'),
-- 重庆市区县
('city_district', '市/区', 'jiangbei', '江北区', 'SYSTEM', 15, '重庆市江北区', 'chongqing');

-- 仓储状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('stock_status', '仓储状态', 'transiting', '在途', 'SYSTEM', 1, '货物运输中', 'info', '#2080f0'),
('stock_status', '仓储状态', 'stocking', '在库', 'SYSTEM', 2, '货物已入库', 'success', '#18a058'),
('stock_status', '仓储状态', 'sold', '已售', 'SYSTEM', 3, '货物已售出', 'warning', '#f0a020'),
('stock_status', '仓储状态', 'returned', '已退', 'SYSTEM', 4, '货物已退回', 'error', '#d03050');

-- 调拨状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('transfer_status', '调拨状态', 'pending', '待确认', 'SYSTEM', 1, '调拨申请待确认', 'warning', '#f0a020'),
('transfer_status', '调拨状态', 'received', '已接收', 'SYSTEM', 2, '调拨申请已接收', 'success', '#18a058'),
('transfer_status', '调拨状态', 'rejected', '已拒绝', 'SYSTEM', 3, '调拨申请已拒绝', 'error', '#d03050');

-- 业务权限 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('business_permission', '业务权限', 'can_stock_in', '可入库', 'SYSTEM', 1, '允许库存入库操作'),
('business_permission', '业务权限', 'can_sell', '可销售', 'SYSTEM', 2, '允许销售操作'),
('business_permission', '业务权限', 'can_stock_out', '可出库', 'SYSTEM', 3, '允许库存出库操作'),
('business_permission', '业务权限', 'can_settle', '可结算', 'SYSTEM', 4, '允许财务结算操作');

-- 机构类型 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('org_type', '机构类型', 'group', '集团', 'SYSTEM', 1, '集团总部'),
('org_type', '机构类型', 'single_store', '单店', 'SYSTEM', 2, '单一门店'),
('org_type', '机构类型', 'direct_sale_store', '直营店', 'SYSTEM', 3, '分公司直营店'),
('org_type', '机构类型', 'secondary_network', '二网', 'SYSTEM', 3, '二级网络'),
('org_type', '机构类型', 'external_partner', '集团外合作方', 'SYSTEM', 4, '集团外合作方');

-- 车辆来源 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('vehicle_source', '车辆来源', 'VEHICLE_EXCHANGE', '车辆置换', 'SYSTEM', 1, '通过车辆置换获得', 'info', '#2080f0'),
('vehicle_source', '车辆来源', 'VEHICLE_ACQ', '外部收购', 'SYSTEM', 2, '从外部收购获得', 'success', '#18a058');

-- 订单状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('order_status', '订单状态', 'confirmed', '已确认', 'SYSTEM', 2, '订单已确认', 'info', '#2080f0'),
('order_status', '订单状态', 'delivered', '已交付', 'SYSTEM', 3, '订单已交付', 'success', '#18a058'),
('order_status', '订单状态', 'canceled', '已取消', 'SYSTEM', 4, '订单已取消', 'error', '#d03050');

-- 客户类型 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('customer_type', '客户类型', 'individual', '个人客户', 'SYSTEM', 1, '个人客户', 'info', '#2080f0'),
('customer_type', '客户类型', 'institutional', '法人客户', 'SYSTEM', 2, '法人客户', 'success', '#18a058');

-- 成交状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('deal_status', '成交状态', 'LEADS', '未成交', 'SYSTEM', 1, '潜在客户，未成交', 'warning', '#f0a020'),
('deal_status', '成交状态', 'CUSTOMER', '已成交', 'SYSTEM', 2, '已成交客户', 'success', '#18a058');

-- 确认状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('rebate_status', '确认状态', 'PENDING', '待返利', 'SYSTEM', 1, '等待返利处理', 'warning', '#f0a020'),
('rebate_status', '确认状态', 'COMPLETED', '已返利', 'SYSTEM', 3, '返利已完成', 'success', '#18a058');

-- 衍生成本确认状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('derivative_costs_confirm_status', '衍生成本确认状态', 'UNCONFIRMED', '未确认', 'SYSTEM', 1, '衍生成本未确认', 'warning', '#f0a020'),
('derivative_costs_confirm_status', '衍生成本确认状态', 'CONFIRMED', '已确认', 'SYSTEM', 2, '衍生成本已确认', 'success', '#18a058');

-- 销售地类型 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('sales_store_type', '销售地类型', '4S', '单店', 'SYSTEM', 1, '4S店销售', 'success', '#18a058'),
('sales_store_type', '销售地类型', '3S', '直营店', 'SYSTEM', 2, '3S店销售', 'info', '#2080f0'),
('sales_store_type', '销售地类型', '2S', '二网', 'SYSTEM', 3, '2S店销售', 'warning', '#f0a020');

-- 收款状态 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('recept_status', '收款状态', 'NO', '未收款', 'SYSTEM', 1, '未收款', 'warning', '#f0a020'),
('recept_status', '收款状态', 'YES', '已收款', 'SYSTEM', 2, '已收款', 'success', '#18a058');

-- 保险公司 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('insurance_provider', '保险公司', 'picc', '中国人保', 'SYSTEM', 1, '中国人民保险公司', 'success', '#18a058'),
('insurance_provider', '保险公司', 'cpic', '太平洋保险', 'SYSTEM', 2, '中国太平洋保险公司', 'info', '#2080f0'),
('insurance_provider', '保险公司', 'pingan', '平安保险', 'SYSTEM', 3, '中国平安保险公司', 'warning', '#f0a020'),
('insurance_provider', '保险公司', 'china_life', '中国人寿', 'SYSTEM', 4, '中国人寿保险公司', 'default', '#909399'),
('insurance_provider', '保险公司', 'taikang', '泰康保险', 'SYSTEM', 5, '泰康人寿保险公司', 'error', '#d03050'),
('insurance_provider', '保险公司', 'sunshine', '阳光保险', 'SYSTEM', 6, '阳光财产保险公司', 'info', '#2080f0'),
('insurance_provider', '保险公司', 'other', '其他保险公司', 'SYSTEM', 7, '其他保险公司', 'default', '#909399');

-- 资金类型 (基于dictData.js真实数据)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('fund_type', '资金类型', '自有资金_重庆_深蓝销司', '自有资金_重庆_深蓝销司', 'SYSTEM', 1, '自有资金重庆深蓝销司', 'success', '#18a058'),
('fund_type', '资金类型', '自有资金_重庆_凯程销司', '自有资金_重庆_凯程销司', 'SYSTEM', 2, '自有资金重庆凯程销司', 'success', '#18a058'),
('fund_type', '资金类型', '自有资金_重庆_引力销司', '自有资金_重庆_引力销司', 'SYSTEM', 3, '自有资金重庆引力销司', 'success', '#18a058'),
('fund_type', '资金类型', '自有资金_重庆_阿维塔销司', '自有资金_重庆_阿维塔销司', 'SYSTEM', 4, '自有资金重庆阿维塔销司', 'success', '#18a058'),
('fund_type', '资金类型', '三方信贷_招商银行_重庆_深蓝销司', '三方信贷_招商银行_重庆_深蓝销司', 'SYSTEM', 5, '三方信贷招商银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_工商银行_重庆_深蓝销司', '三方信贷_工商银行_重庆_深蓝销司', 'SYSTEM', 6, '三方信贷工商银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_建设银行_重庆_深蓝销司', '三方信贷_建设银行_重庆_深蓝销司', 'SYSTEM', 7, '三方信贷建设银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_农业银行_重庆_深蓝销司', '三方信贷_农业银行_重庆_深蓝销司', 'SYSTEM', 8, '三方信贷农业银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_中国银行_重庆_深蓝销司', '三方信贷_中国银行_重庆_深蓝销司', 'SYSTEM', 9, '三方信贷中国银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_交通银行_重庆_深蓝销司', '三方信贷_交通银行_重庆_深蓝销司', 'SYSTEM', 10, '三方信贷交通银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_光大银行_重庆_深蓝销司_特殊', '三方信贷_光大银行_重庆_深蓝销司_特殊', 'SYSTEM', 11, '三方信贷光大银行重庆深蓝销司特殊', 'warning', '#f0a020'),
('fund_type', '资金类型', '承兑汇票_重庆_深蓝销司', '承兑汇票_重庆_深蓝销司', 'SYSTEM', 12, '承兑汇票重庆深蓝销司', 'default', '#909399'),
('fund_type', '资金类型', '信贷保证金_华夏', '信贷保证金_华夏', 'SYSTEM', 13, '信贷保证金华夏', 'error', '#d03050'),
('fund_type', '资金类型', '信贷保证金_兵财', '信贷保证金_兵财', 'SYSTEM', 14, '信贷保证金兵财', 'error', '#d03050');
