CREATE TABLE `dev_jwd`.`vehicle_order`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `order_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'normal' COMMENT '订单类型',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单编号',
  `order_date` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '订单日期',
  `customer_id` int(0) UNSIGNED NOT NULL COMMENT '客户ID',
  `sales_org_id` int(0) UNSIGNED NOT NULL COMMENT '销售单位ID',
  `sales_agent_id` int(0) UNSIGNED NOT NULL COMMENT '销售顾问ID',
  `sales_leader_id` int(0) UNSIGNED NOT NULL COMMENT '销售主管ID',
  `order_status` enum('pending','confirmed','delivered','canceled','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `sku_id` int(0) UNSIGNED NOT NULL COMMENT 'vehicle_sku.id',
  `delivery_date` date NOT NULL COMMENT '交付日期',
  `delivery_org_id` int(0) UNSIGNED NOT NULL COMMENT '交付单位ID',
  `sb_amount` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '启票总价（分）',
  `discount_amount` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠金额（分）',
  `discount_deductible` tinyint(1) NOT NULL DEFAULT 0 COMMENT '优惠金额转车款',
  `sales_cost_amount` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销售费用（分）',
  `deal_amount` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '成交总价（分）',
  `deal_amount_cn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '成交总价大写',
  `payment_method` enum('FULL','LOAN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'FULL' COMMENT '付款方式',
  `loan_channel` enum('车贷通担保','建行','长安金融','上汽金融','其他') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '贷款渠道',
  `loan_months` int(0) NULL DEFAULT NULL COMMENT '贷款期数',
  `loan_amount` int(0) UNSIGNED NULL DEFAULT 0 COMMENT '贷款金额（分）',
  `loan_initial_amount` int(0) UNSIGNED NULL DEFAULT 0 COMMENT '首付金额（分）',
  `loan_initial_ratio` decimal(5, 2) GENERATED ALWAYS AS (if((`loan_amount` > 0),(`loan_initial_amount` / `loan_amount`),0)) STORED COMMENT '首付比例' NULL,
  `loan_rebate_deductible` tinyint(0) NULL DEFAULT NULL COMMENT '返利转车款',
  `used_vehicle_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二手车车牌号',
  `used_vehicle_vin` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二手车VIN',
  `used_vehicle_amount` int(0) UNSIGNED NULL DEFAULT 0 COMMENT '置换金额（分）',
  `used_vehicle_deductible_amount` int(0) UNSIGNED NULL DEFAULT 0 COMMENT '置换补贴（分）',
  `used_vehicle_discount_receivable_amount` int(0) NULL DEFAULT 0 COMMENT '收厂家补贴金额',
  `used_vehicle_discount_payable_amount` int(0) NULL DEFAULT 0 COMMENT '付客户补贴金额',
  `used_vehicle_discount_deductible` tinyint(0) NULL DEFAULT NULL COMMENT '返利转车款',
  `used_vehicle_brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二手车品牌',
  `used_vehicle_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二手车车型',
  `used_vehicle_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二手车颜色',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `creator_id` bigint(0) NOT NULL COMMENT '创建人ID',
  `editor_id` bigint(0) NOT NULL COMMENT '最后编辑人ID',
  `deposit_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '定金类型：线上/线下',
  `deposit_amount` int(0) NULL DEFAULT NULL COMMENT '定金金额',
  `deposit_deductible` tinyint(0) NULL DEFAULT NULL COMMENT '定金转车款',
  `carefree_income` bigint(0) NULL DEFAULT NULL,
  `delivery_equipment` bigint(0) NULL DEFAULT NULL,
  `extended_warranty_income` bigint(0) NULL DEFAULT NULL,
  `has_derivative_income` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `has_gift_items` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `has_insurance` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `license_plate_fee` bigint(0) NULL DEFAULT 0,
  `notary_fee` bigint(0) NULL DEFAULT 0,
  `pre_interest` bigint(0) NULL DEFAULT 0,
  `temp_plate_fee` bigint(0) NULL DEFAULT 0,
  `vps_income` bigint(0) NULL DEFAULT 0,
  `other_income` bigint(0) NULL DEFAULT 0,
  `loan_fee` bigint(0) NULL DEFAULT 0 COMMENT '分期服务费(分)',
  `loan_rebate_receivable_amount` bigint(0) NULL DEFAULT 0 COMMENT '应收-机构-分期返利(分)',
  `loan_rebate_payable_amount` bigint(0) NULL DEFAULT 0 COMMENT '应付-客户-分期返利(分)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单备注',
  `exclusive_discount_amount` bigint(0) NULL DEFAULT 0 COMMENT '专项优惠金额',
  `exclusive_discount_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '专项优惠类型',
  `exclusive_discount_deductible` tinyint(0) NULL DEFAULT NULL COMMENT '专项优惠抵车款',
  `exclusive_discount_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '专项优惠说明',
  `sales_amount` int(0) NULL DEFAULT NULL,
  `invoice_amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `gift_items` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `insurance_org_id` bigint(0) NULL DEFAULT NULL COMMENT '保险经办机构',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_order_sn`(`order_sn`) USING BTREE,
  INDEX `idx_customer`(`customer_id`) USING BTREE,
  INDEX `idx_sales_org`(`sales_org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 302 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '销售订单主表' ROW_FORMAT = Dynamic;