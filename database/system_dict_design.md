# 系统字典表设计说明

## 表结构设计

### 主表：system_dict

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 主键ID |
| dict_group_key | varchar | 100 | 是 | - | 字典组编码 |
| dict_group_value | varchar | 200 | 是 | - | 字典组名称 |
| dict_option_key | varchar | 100 | 是 | - | 字典项编码 |
| dict_optionValue | varchar | 200 | 是 | - | 字典项名称 |
| dict_group_orgs | text | - | 否 | NULL | 字典组可用机构(逗号分割的id列表) |
| dict_type | enum | - | 是 | SYSTEM | 字典类型(SYSTEM:系统,CUSTOMIZE:自定义) |
| sort | int | 11 | 否 | 0 | 排序号 |
| remark | varchar | 500 | 否 | NULL | 备注 |
| option_type | varchar | 50 | 否 | NULL | 选项类型(用于前端显示样式) |
| option_color | varchar | 20 | 否 | NULL | 选项颜色 |
| parent_option_key | varchar | 100 | 否 | NULL | 父级选项编码(用于级联字典) |
| is_active | tinyint | 1 | 是 | 1 | 是否启用(0:禁用,1:启用) |
| create_time | datetime | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | 是 | CURRENT_TIMESTAMP | 更新时间 |
| create_by | varchar | 100 | 否 | NULL | 创建人 |
| update_by | varchar | 100 | 否 | NULL | 更新人 |

## 索引设计

### 主键索引
- `PRIMARY KEY (id)` - 主键索引

### 唯一索引
- `uk_dict_group_option (dict_group_key, dict_option_key)` - 确保同一字典组内选项编码唯一

### 普通索引
- `idx_dict_group_key (dict_group_key)` - 按字典组查询
- `idx_dict_type (dict_type)` - 按字典类型查询
- `idx_is_active (is_active)` - 按启用状态查询
- `idx_parent_option_key (parent_option_key)` - 级联字典查询

### 复合索引
- `idx_dict_group_sort (dict_group_key, sort)` - 按字典组和排序查询
- `idx_dict_group_type_active (dict_group_key, dict_type, is_active)` - 按字典组、类型和状态查询
- `idx_parent_group_active (parent_option_key, dict_group_key, is_active)` - 级联字典查询优化

## 设计特点

### 1. 统一存储
- 将原来分散在 dictList 和 dictOptions 中的数据统一存储在一张表中
- 通过 dict_group_key 关联字典组和字典项

### 2. 权限控制
- dict_group_orgs 字段支持机构级别的权限控制
- 可以指定某个字典组只对特定机构可见

### 3. 级联支持
- parent_option_key 字段支持级联字典（如省市关系）
- 可以构建多级字典结构

### 4. 扩展性强
- option_type 和 option_color 支持前端样式定制
- dict_type 区分系统字典和自定义字典
- 预留了创建人、更新人等审计字段

### 5. 性能优化
- 合理的索引设计确保查询性能
- 支持按字典组批量查询
- 支持级联字典的高效查询

## 数据迁移

### 原数据结构
```javascript
// dictList - 字典组信息
{
  dict_code: 'vehicle_brand',
  dict_name: '在售品牌',
  remark: '长安旗下在售品牌'
}

// dictOptions - 字典项信息
{
  optionValue: '深蓝',
  optionLabel: '深蓝',
  sort: 2,
  remark: '深蓝汽车',
  type: 'info',
  color: '#2080f0'
}
```

### 新数据结构
```sql
INSERT INTO system_dict (
  dict_group_key, dict_group_value, 
  dict_option_key, dict_optionValue,
  dict_type, sort, remark, option_type, option_color
) VALUES (
  'vehicle_brand', '在售品牌',
  '深蓝', '深蓝', 
  'SYSTEM', 2, '深蓝汽车', 'info', '#2080f0'
);
```

## 常用查询示例

### 1. 查询字典组列表
```sql
SELECT DISTINCT dict_group_key, dict_group_value, dict_type
FROM system_dict 
WHERE is_active = 1 
ORDER BY dict_group_key;
```

### 2. 查询指定字典组的选项
```sql
SELECT dict_option_key, dict_optionValue, sort, remark, option_type, option_color
FROM system_dict 
WHERE dict_group_key = 'vehicle_brand' AND is_active = 1 
ORDER BY sort;
```

### 3. 查询级联字典
```sql
SELECT p.dict_optionValue as province_name, c.dict_optionValue as city_name
FROM system_dict p
LEFT JOIN system_dict c ON p.dict_option_key = c.parent_option_key
WHERE p.dict_group_key = 'province_city' AND c.dict_group_key = 'city_district'
AND p.is_active = 1 AND c.is_active = 1
ORDER BY p.sort, c.sort;
```

### 4. 按机构权限查询
```sql
SELECT dict_option_key, dict_optionValue, sort
FROM system_dict 
WHERE dict_group_key = 'vehicle_brand' 
AND is_active = 1 
AND (dict_group_orgs IS NULL OR FIND_IN_SET('123', dict_group_orgs) > 0)
ORDER BY sort;
```

## 后续开发建议

### 1. API接口设计
- GET /api/dict/groups - 获取字典组列表
- GET /api/dict/{groupKey}/options - 获取字典选项
- POST/PUT/DELETE /api/dict - 字典CRUD操作

### 2. 缓存策略
- 使用Redis缓存热点字典数据
- 按字典组进行缓存分片
- 设置合理的缓存过期时间

### 3. 权限控制
- 结合用户机构信息进行权限过滤
- 支持字典组级别的权限控制
- 考虑角色权限的集成

### 4. 数据同步
- 提供数据导入导出功能
- 支持从现有dictData.js迁移
- 考虑多环境数据同步方案
