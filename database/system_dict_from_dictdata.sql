-- =============================================
-- 系统字典初始化数据 (严格按照 mock/dictData.js 逐一生成)
-- =============================================

-- 清空现有数据
TRUNCATE TABLE `system_dict`;

-- =============================================
-- 按照 dictData.js 中的 dictOptions 逐一生成
-- =============================================

-- 1. gift_category - 赠品类别 (dictData.js line 127-140)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('gift_category', '赠品类别', 'GOODS', '商品', 'SYSTEM', 1, '实物赠品'),
('gift_category', '赠品类别', 'SERVICES', '服务', 'SYSTEM', 2, '虚拟赠品');

-- 2. vehicle_brand - 在售品牌 (dictData.js line 141-182)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('vehicle_brand', '在售品牌', '深蓝', '深蓝', 'SYSTEM', 2, '深蓝汽车', 'info', '#2080f0'),
('vehicle_brand', '在售品牌', '凯程', '凯程', 'SYSTEM', 3, '凯程汽车', 'warning', '#f0a020'),
('vehicle_brand', '在售品牌', '引力', '引力', 'SYSTEM', 4, '引力汽车', 'default', '#909399'),
('vehicle_brand', '在售品牌', '阿维塔', '阿维塔', 'SYSTEM', 5, '阿维塔汽车', 'error', '#d03050'),
('vehicle_brand', '在售品牌', '启源', '启源', 'SYSTEM', 6, '启源汽车', 'default', '#909399');

-- 3. vehicle_status - 车辆状态 (dictData.js line 184-217, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('vehicle_status', '车辆状态', '1', '在库', 'SYSTEM', 1, '车辆在库', 'success', '#18a058'),
('vehicle_status', '车辆状态', '2', '预售', 'SYSTEM', 2, '车辆预售', 'warning', '#f0a020'),
('vehicle_status', '车辆状态', '3', '已售', 'SYSTEM', 3, '车辆已售', 'info', '#2080f0'),
('vehicle_status', '车辆状态', '4', '维修中', 'SYSTEM', 4, '车辆维修中', 'error', '#d03050');

-- 4. user_permissions - 用户权限 (dictData.js line 219-251, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('user_permissions', '用户权限', 'read', '读取', 'SYSTEM', 1, '读取权限', 'info', '#2080f0'),
('user_permissions', '用户权限', 'write', '写入', 'SYSTEM', 2, '写入权限', 'warning', '#f0a020'),
('user_permissions', '用户权限', 'delete', '删除', 'SYSTEM', 3, '删除权限', 'error', '#d03050'),
('user_permissions', '用户权限', 'admin', '管理', 'SYSTEM', 4, '管理权限', 'success', '#18a058');

-- 5. recept_account - 收款方式 (dictData.js line 253-284, 对应dictList中的recept_account)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('recept_account', '收款方式', 'cash', '现金', 'SYSTEM', 1, '现金支付'),
('recept_account', '收款方式', 'citic_8835', '中信银行(8835)', 'SYSTEM', 2, '中信银行账户8835'),
('recept_account', '收款方式', 'qrcode', '公户二维码', 'SYSTEM', 3, '公司账户二维码支付'),
('recept_account', '收款方式', 'pos', 'POS机刷卡', 'SYSTEM', 4, 'POS机刷卡支付'),
('recept_account', '收款方式', 'loan', '贷款', 'SYSTEM', 5, '银行贷款');

-- 6. payment_account - 付款账户 (dictData.js line 285-304, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('payment_account', '付款账户', 'cash', '现金', 'SYSTEM', 1, '现金支付'),
('payment_account', '付款账户', 'citic_8835', '中信银行(8835)', 'SYSTEM', 2, '中信银行账户8835'),
('payment_account', '付款账户', 'pos', 'POS机刷卡', 'SYSTEM', 4, 'POS机刷卡支付');

-- 7. receivable_subject - 应收科目 (dictData.js line 305-354)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('receivable_subject', '应收科目', '1001', '车款-定金', 'SYSTEM', 1, '购车定金'),
('receivable_subject', '应收科目', '1002', '车款-尾款', 'SYSTEM', 1, '购车尾款'),
('receivable_subject', '应收科目', '1007', '车款-首付款', 'SYSTEM', 2, '购车首付款'),
('receivable_subject', '应收科目', '1008', '车款-分期款', 'SYSTEM', 3, '分期银行贷款'),
('receivable_subject', '应收科目', '1006', '返利-银行分期返利', 'SYSTEM', 4, '银行返利'),
('receivable_subject', '应收科目', '1003', '分期-金融服务费', 'SYSTEM', 5, '金融服务费'),
('receivable_subject', '应收科目', '1004', '保险-交强险保费', 'SYSTEM', 6, '车辆保险费'),
('receivable_subject', '应收科目', '1005', '其他衍生收入', 'SYSTEM', 7, '其他费用');

-- 8. payable_subject - 应付科目 (dictData.js line 355-404, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('payable_subject', '应付科目', '1001', '车款-启票款', 'SYSTEM', 1, '启票款'),
('payable_subject', '应付科目', '1002', '车款-尾款', 'SYSTEM', 1, '售车尾款'),
('payable_subject', '应付科目', '1007', '车款-退置换', 'SYSTEM', 2, '退置换'),
('payable_subject', '应付科目', '1008', '二手车置换补贴', 'SYSTEM', 3, '置换补贴'),
('payable_subject', '应付科目', '1006', '返利-银行分期返利', 'SYSTEM', 4, '银行返利'),
('payable_subject', '应付科目', '1003', '分期-金融服务成本', 'SYSTEM', 5, '金融服务费'),
('payable_subject', '应付科目', '1004', '保险-交强险保费', 'SYSTEM', 6, '车辆保险费'),
('payable_subject', '应付科目', '1005', '其他衍生成本', 'SYSTEM', 7, '其他费用');

-- 9. receivable_target - 应收对象 (dictData.js line 406-437)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('receivable_target', '应收对象', '客户', '客户', 'SYSTEM', 1, '客户'),
('receivable_target', '应收对象', '银行', '银行', 'SYSTEM', 2, '银行'),
('receivable_target', '应收对象', '保险公司', '保险公司', 'SYSTEM', 3, '保险公司'),
('receivable_target', '应收对象', '经销商', '经销商', 'SYSTEM', 4, '经销商'),
('receivable_target', '应收对象', '其他', '其他', 'SYSTEM', 5, '其他');

-- 10. payable_target - 应付对象 (dictData.js line 440-471, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('payable_target', '应付对象', '客户', '客户', 'SYSTEM', 1, '客户'),
('payable_target', '应付对象', '银行', '银行', 'SYSTEM', 2, '银行'),
('payable_target', '应付对象', '保险公司', '保险公司', 'SYSTEM', 3, '保险公司'),
('payable_target', '应付对象', '经销商', '经销商', 'SYSTEM', 4, '经销商'),
('payable_target', '应付对象', '其他', '其他', 'SYSTEM', 5, '其他');

-- 11. loan_channel - 贷款渠道 (dictData.js line 473-504)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('loan_channel', '贷款渠道', '车贷通担保', '车贷通担保', 'SYSTEM', 1, '车贷通担保'),
('loan_channel', '贷款渠道', '建行', '建行', 'SYSTEM', 2, '建设银行'),
('loan_channel', '贷款渠道', '长安金融', '长安金融', 'SYSTEM', 3, '长安金融'),
('loan_channel', '贷款渠道', '上汽金融', '上汽金融', 'SYSTEM', 4, '上汽金融'),
('loan_channel', '贷款渠道', '其他', '其他', 'SYSTEM', 5, '其他金融机构');

-- 12. loan_months - 贷款期限 (dictData.js line 506-543)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('loan_months', '贷款期限', '3', '3期', 'SYSTEM', 1, '3个月'),
('loan_months', '贷款期限', '6', '6期', 'SYSTEM', 2, '6个月'),
('loan_months', '贷款期限', '12', '12期', 'SYSTEM', 3, '12个月'),
('loan_months', '贷款期限', '24', '24期', 'SYSTEM', 4, '24个月'),
('loan_months', '贷款期限', '36', '36期', 'SYSTEM', 5, '36个月'),
('loan_months', '贷款期限', '60', '60期', 'SYSTEM', 6, '60个月');

-- 13. exclusive_discount_type - 专享优惠类型 (dictData.js line 545-576)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('exclusive_discount_type', '专享优惠类型', 'GOVERNMENT', '政企优惠', 'SYSTEM', 2, '政企客户专享优惠'),
('exclusive_discount_type', '专享优惠类型', 'WORK_CAR', '工作车优惠', 'SYSTEM', 3, '工作车专享优惠'),
('exclusive_discount_type', '专享优惠类型', 'INTERNAL', '内购优惠', 'SYSTEM', 4, '员工内购专享优惠'),
('exclusive_discount_type', '专享优惠类型', 'TEST_DRIVE', '试乘试驾车优惠', 'SYSTEM', 5, '试乘试驾车专享优惠'),
('exclusive_discount_type', '专享优惠类型', 'OTHERS', '其他请备注', 'SYSTEM', 5, '其他请填写订单备注');

-- 14. deposit_status - 定金状态 (dictData.js line 578-603)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('deposit_status', '定金状态', 'received', '已收定金', 'SYSTEM', 1, '已收到客户定金', 'success', '#18a058'),
('deposit_status', '定金状态', 'refunded', '已退定金', 'SYSTEM', 2, '已退还客户定金', 'error', '#d03050'),
('deposit_status', '定金状态', 'transferred', '已转车款', 'SYSTEM', 3, '定金已转入车款', 'info', '#2080f0');

-- 15. province_city - 省/直辖市 (dictData.js line 605-630)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('province_city', '省/直辖市', 'shandong', '山东省', 'SYSTEM', 1, '山东省'),
('province_city', '省/直辖市', 'hebei', '河北省', 'SYSTEM', 2, '河北省'),
('province_city', '省/直辖市', 'henan', '河南省', 'SYSTEM', 3, '河南省'),
('province_city', '省/直辖市', 'chongqing', '重庆市', 'SYSTEM', 4, '重庆市');

-- 16. city_district - 市/区 (dictData.js line 632-742, 级联字典使用parent_option_key)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `parent_option_key`) VALUES
-- 山东省城市
('city_district', '市/区', 'jinan', '济南市', 'SYSTEM', 1, '山东省济南市', 'shandong'),
('city_district', '市/区', 'dezhou', '德州市', 'SYSTEM', 2, '山东省德州市', 'shandong'),
('city_district', '市/区', 'zibo', '淄博市', 'SYSTEM', 3, '山东省淄博市', 'shandong'),
('city_district', '市/区', 'linyi', '临沂市', 'SYSTEM', 4, '山东省临沂市', 'shandong'),
('city_district', '市/区', 'liaocheng', '聊城市', 'SYSTEM', 5, '山东省聊城市', 'shandong'),
('city_district', '市/区', 'dongying', '东营市', 'SYSTEM', 6, '山东省东营市', 'shandong'),
('city_district', '市/区', 'binzhou', '滨州市', 'SYSTEM', 7, '山东省滨州市', 'shandong'),
-- 河南省城市
('city_district', '市/区', 'zhengzhou', '郑州市', 'SYSTEM', 8, '河南省郑州市', 'henan'),
('city_district', '市/区', 'luoyang', '洛阳市', 'SYSTEM', 9, '河南省洛阳市', 'henan'),
('city_district', '市/区', 'zhumadian', '驻马店市', 'SYSTEM', 10, '河南省驻马店市', 'henan'),
-- 河北省城市
('city_district', '市/区', 'shijiazhuang', '石家庄市', 'SYSTEM', 11, '河北省石家庄市', 'hebei'),
('city_district', '市/区', 'cangzhou', '沧州市', 'SYSTEM', 12, '河北省沧州市', 'hebei'),
('city_district', '市/区', 'langfang', '廊坊市', 'SYSTEM', 13, '河北省廊坊市', 'hebei'),
-- 重庆市区县
('city_district', '市/区', 'yuzhong', '渝中区', 'SYSTEM', 14, '重庆市渝中区', 'chongqing'),
('city_district', '市/区', 'jiangbei', '江北区', 'SYSTEM', 15, '重庆市江北区', 'chongqing');

-- 17. business_permission - 业务权限 (dictData.js line 744-769)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('business_permission', '业务权限', 'can_stock_in', '可入库', 'SYSTEM', 1, '允许库存入库操作'),
('business_permission', '业务权限', 'can_sell', '可销售', 'SYSTEM', 2, '允许销售操作'),
('business_permission', '业务权限', 'can_stock_out', '可出库', 'SYSTEM', 3, '允许库存出库操作'),
('business_permission', '业务权限', 'can_settle', '可结算', 'SYSTEM', 4, '允许财务结算操作');

-- 18. org_type - 机构类型 (dictData.js line 771-802)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`) VALUES
('org_type', '机构类型', 'group', '集团', 'SYSTEM', 1, '集团总部'),
('org_type', '机构类型', 'single_store', '单店', 'SYSTEM', 2, '单一门店'),
('org_type', '机构类型', 'direct_sale_store', '直营店', 'SYSTEM', 3, '分公司直营店'),
('org_type', '机构类型', 'secondary_network', '二网', 'SYSTEM', 3, '二级网络'),
('org_type', '机构类型', 'external_partner', '集团外合作方', 'SYSTEM', 4, '集团外合作方');

-- 19. stock_status - 仓储状态 (dictData.js line 804-837)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('stock_status', '仓储状态', 'transiting', '在途', 'SYSTEM', 1, '货物运输中', 'info', '#2080f0'),
('stock_status', '仓储状态', 'stocking', '在库', 'SYSTEM', 2, '货物已入库', 'success', '#18a058'),
('stock_status', '仓储状态', 'sold', '已售', 'SYSTEM', 3, '货物已售出', 'warning', '#f0a020'),
('stock_status', '仓储状态', 'returned', '已退', 'SYSTEM', 4, '货物已退回', 'error', '#d03050');

-- 20. transfer_status - 调拨状态 (dictData.js line 839-864)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('transfer_status', '调拨状态', 'pending', '待确认', 'SYSTEM', 1, '调拨申请待确认', 'warning', '#f0a020'),
('transfer_status', '调拨状态', 'received', '已接收', 'SYSTEM', 2, '调拨申请已接收', 'success', '#18a058'),
('transfer_status', '调拨状态', 'rejected', '已拒绝', 'SYSTEM', 3, '调拨申请已拒绝', 'error', '#d03050');

-- 21. vehicle_source - 车辆来源 (dictData.js line 866-883, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('vehicle_source', '车辆来源', 'VEHICLE_EXCHANGE', '车辆置换', 'SYSTEM', 1, '通过车辆置换获得', 'info', '#2080f0'),
('vehicle_source', '车辆来源', 'VEHICLE_ACQ', '外部收购', 'SYSTEM', 2, '从外部收购获得', 'success', '#18a058');

-- 22. order_status - 订单状态 (dictData.js line 885-910, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('order_status', '订单状态', 'confirmed', '已确认', 'SYSTEM', 2, '订单已确认', 'info', '#2080f0'),
('order_status', '订单状态', 'delivered', '已交付', 'SYSTEM', 3, '订单已交付', 'success', '#18a058'),
('order_status', '订单状态', 'canceled', '已取消', 'SYSTEM', 4, '订单已取消', 'error', '#d03050');

-- 23. customer_type - 客户类型 (dictData.js line 912-929, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('customer_type', '客户类型', 'individual', '个人客户', 'SYSTEM', 1, '个人客户', 'info', '#2080f0'),
('customer_type', '客户类型', 'institutional', '法人客户', 'SYSTEM', 2, '法人客户', 'success', '#18a058');

-- 24. deal_status - 成交状态 (dictData.js line 931-948, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('deal_status', '成交状态', 'LEADS', '未成交', 'SYSTEM', 1, '潜在客户，未成交', 'warning', '#f0a020'),
('deal_status', '成交状态', 'CUSTOMER', '已成交', 'SYSTEM', 2, '已成交客户', 'success', '#18a058');

-- 25. rebate_status - 确认状态 (dictData.js line 950-967)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('rebate_status', '确认状态', 'PENDING', '待返利', 'SYSTEM', 1, '等待返利处理', 'warning', '#f0a020'),
('rebate_status', '确认状态', 'COMPLETED', '已返利', 'SYSTEM', 3, '返利已完成', 'success', '#18a058');

-- 26. derivative_costs_confirm_status - 衍生成本确认状态 (dictData.js line 969-986)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('derivative_costs_confirm_status', '衍生成本确认状态', 'UNCONFIRMED', '未确认', 'SYSTEM', 1, '衍生成本未确认', 'warning', '#f0a020'),
('derivative_costs_confirm_status', '衍生成本确认状态', 'CONFIRMED', '已确认', 'SYSTEM', 2, '衍生成本已确认', 'success', '#18a058');

-- 27. sales_store_type - 销售地类型 (dictData.js line 988-1013)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('sales_store_type', '销售地类型', '4S', '单店', 'SYSTEM', 1, '4S店销售', 'success', '#18a058'),
('sales_store_type', '销售地类型', '3S', '直营店', 'SYSTEM', 2, '3S直营店销售', 'info', '#2080f0'),
('sales_store_type', '销售地类型', '2S', '二网', 'SYSTEM', 3, '2S二网销售', 'warning', '#f0a020');

-- 28. insurance_provider - 保险公司 (dictData.js line 1015-1080, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('insurance_provider', '保险公司', 'PICC', '中国人保', 'SYSTEM', 1, '中国人民保险集团股份有限公司', 'success', '#18a058'),
('insurance_provider', '保险公司', 'CPIC', '太平洋保险', 'SYSTEM', 2, '中国太平洋保险(集团)股份有限公司', 'info', '#2080f0'),
('insurance_provider', '保险公司', 'PINGAN', '平安保险', 'SYSTEM', 3, '中国平安保险(集团)股份有限公司', 'warning', '#f0a020'),
('insurance_provider', '保险公司', 'CLIC', '中国人寿', 'SYSTEM', 4, '中国人寿保险股份有限公司', 'error', '#d03050'),
('insurance_provider', '保险公司', 'TAIKANG', '泰康保险', 'SYSTEM', 5, '泰康保险集团股份有限公司', 'default', '#909399'),
('insurance_provider', '保险公司', 'SUNSHINE', '阳光保险', 'SYSTEM', 6, '阳光保险集团股份有限公司', 'default', '#909399'),
('insurance_provider', '保险公司', 'TIANAN', '天安保险', 'SYSTEM', 7, '天安财产保险股份有限公司', 'default', '#909399'),
('insurance_provider', '保险公司', 'OTHERS', '其他', 'SYSTEM', 8, '其他保险公司', 'default', '#909399');

-- 29. fund_type - 资金类型 (dictData.js line 1082-1195, 字典组不在dictList中)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('fund_type', '资金类型', '三方信贷_兵财_重庆_深蓝销司', '三方信贷_兵财_重庆_深蓝销司', 'SYSTEM', 1, '三方信贷兵财重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '兵财融资_重庆_深蓝销司', '兵财融资_重庆_深蓝销司', 'SYSTEM', 2, '兵财融资重庆深蓝销司', 'success', '#18a058'),
('fund_type', '资金类型', '三方信贷_浦发银行_重庆_深蓝销司', '三方信贷_浦发银行_重庆_深蓝销司', 'SYSTEM', 3, '三方信贷浦发银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_华夏银行_重庆_深蓝销司', '三方信贷_华夏银行_重庆_深蓝销司', 'SYSTEM', 4, '三方信贷华夏银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_光大银行_重庆_深蓝销司', '三方信贷_光大银行_重庆_深蓝销司', 'SYSTEM', 5, '三方信贷光大银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_华夏银行_重庆_深蓝销司_特殊', '三方信贷_华夏银行_重庆_深蓝销司_特殊', 'SYSTEM', 6, '三方信贷华夏银行重庆深蓝销司特殊', 'warning', '#f0a020'),
('fund_type', '资金类型', '三方信贷_兴业银行_重庆_深蓝销司_特殊', '三方信贷_兴业银行_重庆_深蓝销司_特殊', 'SYSTEM', 7, '三方信贷兴业银行重庆深蓝销司特殊', 'warning', '#f0a020'),
('fund_type', '资金类型', '现金_重庆_深蓝销司', '现金_重庆_深蓝销司', 'SYSTEM', 8, '现金重庆深蓝销司', 'success', '#18a058'),
('fund_type', '资金类型', '三方信贷_兴业银行_重庆_深蓝销司', '三方信贷_兴业银行_重庆_深蓝销司', 'SYSTEM', 9, '三方信贷兴业银行重庆深蓝销司', 'info', '#2080f0'),
('fund_type', '资金类型', '三方信贷_其它银行_重庆_深蓝销司_特殊', '三方信贷_其它银行_重庆_深蓝销司_特殊', 'SYSTEM', 10, '三方信贷其它银行重庆深蓝销司特殊', 'warning', '#f0a020'),
('fund_type', '资金类型', '三方信贷_光大银行_重庆_深蓝销司_特殊', '三方信贷_光大银行_重庆_深蓝销司_特殊', 'SYSTEM', 11, '三方信贷光大银行重庆深蓝销司特殊', 'warning', '#f0a020'),
('fund_type', '资金类型', '承兑汇票_重庆_深蓝销司', '承兑汇票_重庆_深蓝销司', 'SYSTEM', 12, '承兑汇票重庆深蓝销司', 'default', '#909399'),
('fund_type', '资金类型', '信贷保证金_华夏', '信贷保证金_华夏', 'SYSTEM', 13, '信贷保证金华夏', 'error', '#d03050'),
('fund_type', '资金类型', '信贷保证金_兵财', '信贷保证金_兵财', 'SYSTEM', 14, '信贷保证金兵财', 'error', '#d03050');

-- 30. recept_status - 收款状态 (dictData.js line 1197-1214)
INSERT INTO `system_dict` (`dict_group_key`, `dict_group_value`, `dict_option_key`, `dict_optionValue`, `dict_type`, `sort`, `remark`, `option_type`, `option_color`) VALUES
('recept_status', '收款状态', 'NO', '未收款', 'SYSTEM', 1, '未收款', 'warning', '#f0a020'),
('recept_status', '收款状态', 'YES', '已收款', 'SYSTEM', 2, '已收款', 'success', '#18a058');

-- =============================================
-- 数据完整性检查和统计
-- =============================================

-- 检查字典组数量
-- SELECT COUNT(DISTINCT dict_group_key) as total_groups FROM system_dict;
-- 预期结果：30个字典组

-- 检查字典项总数
-- SELECT COUNT(*) as total_options FROM system_dict;
-- 预期结果：约150+个字典项

-- 按字典组统计选项数量
-- SELECT dict_group_key, dict_group_value, COUNT(*) as option_count
-- FROM system_dict
-- GROUP BY dict_group_key, dict_group_value
-- ORDER BY dict_group_key;

-- 检查级联字典
-- SELECT p.dict_optionValue as province, c.dict_optionValue as city
-- FROM system_dict p
-- LEFT JOIN system_dict c ON p.dict_option_key = c.parent_option_key
-- WHERE p.dict_group_key = 'province_city' AND c.dict_group_key = 'city_district'
-- ORDER BY p.sort, c.sort;

-- =============================================
-- 注意事项
-- =============================================
-- 1. 所有数据严格按照 mock/dictData.js 中的真实数据生成
-- 2. 保留了原始的 optionValue、optionLabel、sort、remark、type、color 信息
-- 3. 级联字典使用 parent_option_key 字段关联
-- 4. 所有数据的 dict_type 都设置为 'SYSTEM'
-- 5. 部分字典组在 dictList 中没有定义，已补充合理的组名称
