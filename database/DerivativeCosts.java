package com.vooice.model;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "derivative_costs")
public class DerivativeCosts {
    /**
     * 数据主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 销售订单号
     */
    @Column(name = "order_sn")
    private String orderSn;

    /**
     * 车辆VIN码
     */
    private String vin;

    /**
     * 确认状态：0-未确认，1-已确认
     */
    private Boolean confirmed;

    /**
     * 经办机构ID
     */
    @Column(name = "editor_org_id")
    private Long editorOrgId;

    /**
     * 经办机构名称
     */
    @Column(name = "editor_org_name")
    private String editorOrgName;

    /**
     * 经办人ID
     */
    @Column(name = "editor_id")
    private Long editorId;

    /**
     * 经办人姓名
     */
    @Column(name = "editor_name")
    private String editorName;

    /**
     * 公证费收入（分）
     */
    @Column(name = "notary_fee_income")
    private Long notaryFeeIncome;

    /**
     * 公证费成本（分）
     */
    @Column(name = "notary_fee_cost")
    private Long notaryFeeCost;

    /**
     * 畅行无忧收入（分）
     */
    @Column(name = "carefree_income")
    private Long carefreeIncome;

    /**
     * 畅行无忧成本（分）
     */
    @Column(name = "carefree_cost")
    private Long carefreeCost;

    /**
     * 延保收入（分）
     */
    @Column(name = "extended_warranty_income")
    private Long extendedWarrantyIncome;

    /**
     * 延保成本（分）
     */
    @Column(name = "extended_warranty_cost")
    private Long extendedWarrantyCost;

    /**
     * VPS收入（分）
     */
    @Column(name = "vps_income")
    private Long vpsIncome;

    /**
     * VPS成本（分）
     */
    @Column(name = "vps_cost")
    private Long vpsCost;

    /**
     * 预收利息收入（分）
     */
    @Column(name = "pre_interest_income")
    private Long preInterestIncome;

    /**
     * 预收利息成本（分）
     */
    @Column(name = "pre_interest_cost")
    private Long preInterestCost;

    /**
     * 牌照费收入（分）
     */
    @Column(name = "license_plate_fee_income")
    private Long licensePlateFeeIncome;

    /**
     * 牌照费成本（分）
     */
    @Column(name = "license_plate_fee_cost")
    private Long licensePlateFeeCost;

    /**
     * 临牌费收入（分）
     */
    @Column(name = "temp_plate_fee_income")
    private Long tempPlateFeeIncome;

    /**
     * 临牌费成本（分）
     */
    @Column(name = "temp_plate_fee_cost")
    private Long tempPlateFeeCost;

    /**
     * 交车装备收入（分）
     */
    @Column(name = "delivery_equipment_income")
    private Long deliveryEquipmentIncome;

    /**
     * 交车装备成本（分）
     */
    @Column(name = "delivery_equipment_cost")
    private Long deliveryEquipmentCost;

    /**
     * 其他收入（分）
     */
    @Column(name = "other_income")
    private Long otherIncome;

    /**
     * 其他成本（分）
     */
    @Column(name = "other_cost")
    private Long otherCost;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}