
-- 订单详情查询SQL（使用AS别名实现ORM映射）
-- 返回VehicleOrderDetailVO对象，字段名采用驼峰命名法

SELECT
    -- 基本信息
    vo.id as id,
    vo.order_type as orderType,
    vo.order_sn as orderSn,
    vo.order_date as orderDate,
    vo.order_status as orderStatus,
    vo.delivery_date as deliveryDate,
    vo.payment_method as paymentMethod,
    vo.remark as remark,
    vo.create_time as createTime,
    vo.update_time as updateTime,

    -- 客户信息
    c.customer_name as customerName,
    c.customer_type as customerType,
    c.deal_status as dealStatus,
    c.customer_id_code as customerIdCode,
    c.mobile as mobile,
    c.address as address,
    c.remark as customerRemark,
    c.owner_org_name as ownerOrgName,

    -- 机构信息
    sales_org.org_name as salesOrgName,
    delivery_org.org_name as deliveryOrgName,

    -- 人员信息
    sales_agent.agent_name as salesAgentName,
    sales_leader.agent_name as sales<PERSON>eader<PERSON><PERSON>,
    creator.agent_name as creator<PERSON><PERSON>,
    editor.agent_name as editorName,

    -- 车辆信息
    vs.brand as brand,
    vs.series as series,
    vs.model_code as modelCode,
    vs.model_name as modelName,
    vs.config_code as configCode,
    vs.config_name as configName,
    vs.color_code as colorCode,
    vo.sku_id as skuId,
    
    -- 金额信息（分）
    vo.sb_amount as sbAmount,
    vo.deal_amount as dealAmount,
    vo.deal_amount_cn as dealAmountCn,
    vo.discount_amount as discountAmount,
    vo.discount_deductible as discountDeductible,

    -- 专享优惠
    vo.exclusive_discount_type as exclusiveDiscountType,
    vo.exclusive_discount_amount as exclusiveDiscountAmount,
    vo.exclusive_discount_deductible as exclusiveDiscountPayableDeductible,
    vo.exclusive_discount_remark as exclusiveDiscountRemark,

    -- 定金信息
    vo.deposit_type as depositType,
    vo.deposit_amount as depositAmount,
    vo.deposit_deductible as depositDeductible,

    -- 贷款信息
    vo.loan_channel as loanChannel,
    vo.loan_months as loanMonths,
    vo.loan_amount as loanAmount,
    vo.loan_initial_amount as loanInitialAmount,
    vo.loan_initial_ratio as loanInitialRatio,
    vo.loan_rebate_deductible as loanRebatePayableDeductible,
    vo.loan_fee as loanFee,
    vo.loan_rebate_receivable_amount as loanRebateReceivableAmount,
    vo.loan_rebate_payable_amount as loanRebatePayableAmount,
    
    -- 二手车信息
    vo.used_vehicle_id as usedVehicleId,
    vo.used_vehicle_vin as usedVehicleVin,
    vo.used_vehicle_amount as usedVehicleAmount,
    vo.used_vehicle_deductible_amount as usedVehicleDiscountPayableDeductibleAmount,
    vo.used_vehicle_discount_receivable_amount as usedVehicleDiscountReceivableAmount,
    vo.used_vehicle_discount_payable_amount as usedVehicleDiscountPayableAmount,
    vo.used_vehicle_discount_deductible as usedVehicleDiscountPayableDeductible,
    vo.used_vehicle_brand as usedVehicleBrand,
    vo.used_vehicle_model as usedVehicleModel,
    vo.used_vehicle_color as usedVehicleColor,

    -- 衍生收入
    vo.has_derivative_income as hasDerivativeIncome,
    vo.notary_fee as notaryFee,
    vo.carefree_income as carefreeIncome,
    vo.extended_warranty_income as extendedWarrantyIncome,
    vo.vps_income as vpsIncome,
    vo.pre_interest as preInterest,
    vo.license_plate_fee as licensePlateFee,
    vo.temp_plate_fee as tempPlateFee,
    vo.delivery_equipment as deliveryEquipment,
    vo.other_income as otherIncome,

    -- 保险和赠品
    vo.has_insurance as hasInsurance,
    vo.has_gift_items as hasGiftItems

FROM vehicle_order vo
LEFT JOIN crm_customer c ON vo.customer_id = c.id
LEFT JOIN biz_org sales_org ON vo.sales_org_id = sales_org.id
LEFT JOIN biz_org delivery_org ON vo.delivery_org_id = delivery_org.id
LEFT JOIN biz_org_member sales_agent ON vo.sales_agent_id = sales_agent.agent_id
LEFT JOIN biz_org_member sales_leader ON vo.sales_leader_id = sales_leader.agent_id
LEFT JOIN biz_org_member creator ON vo.creator_id = creator.agent_id
LEFT JOIN biz_org_member editor ON vo.editor_id = editor.agent_id
LEFT JOIN vehicle_sku vs ON vo.sku_id = vs.id
WHERE vo.id = ?