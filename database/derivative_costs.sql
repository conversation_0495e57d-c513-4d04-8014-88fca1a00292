CREATE TABLE `dev_jwd`.`Untitled`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '销售订单号',
  `vin` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车辆VIN码',
  `confirmed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '确认状态：0-未确认，1-已确认',
  `editor_org_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '经办机构ID',
  `editor_org_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经办机构名称',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '经办人ID',
  `editor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经办人姓名',
  `notary_fee_income` bigint(0) NULL DEFAULT 0 COMMENT '公证费收入（分）',
  `notary_fee_cost` bigint(0) NULL DEFAULT 0 COMMENT '公证费成本（分）',
  `carefree_income` bigint(0) NULL DEFAULT 0 COMMENT '畅行无忧收入（分）',
  `carefree_cost` bigint(0) NULL DEFAULT 0 COMMENT '畅行无忧成本（分）',
  `extended_warranty_income` bigint(0) NULL DEFAULT 0 COMMENT '延保收入（分）',
  `extended_warranty_cost` bigint(0) NULL DEFAULT 0 COMMENT '延保成本（分）',
  `vps_income` bigint(0) NULL DEFAULT 0 COMMENT 'VPS收入（分）',
  `vps_cost` bigint(0) NULL DEFAULT 0 COMMENT 'VPS成本（分）',
  `pre_interest_income` bigint(0) NULL DEFAULT 0 COMMENT '预收利息收入（分）',
  `pre_interest_cost` bigint(0) NULL DEFAULT 0 COMMENT '预收利息成本（分）',
  `license_plate_fee_income` bigint(0) NULL DEFAULT 0 COMMENT '牌照费收入（分）',
  `license_plate_fee_cost` bigint(0) NULL DEFAULT 0 COMMENT '牌照费成本（分）',
  `temp_plate_fee_income` bigint(0) NULL DEFAULT 0 COMMENT '临牌费收入（分）',
  `temp_plate_fee_cost` bigint(0) NULL DEFAULT 0 COMMENT '临牌费成本（分）',
  `delivery_equipment_income` bigint(0) NULL DEFAULT 0 COMMENT '交车装备收入（分）',
  `delivery_equipment_cost` bigint(0) NULL DEFAULT 0 COMMENT '交车装备成本（分）',
  `other_income` bigint(0) NULL DEFAULT 0 COMMENT '其他收入（分）',
  `other_cost` bigint(0) NULL DEFAULT 0 COMMENT '其他成本（分）',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_order_sn`(`order_sn`) USING BTREE COMMENT '订单号唯一索引',
  INDEX `idx_vin`(`vin`) USING BTREE COMMENT 'VIN码索引',
  INDEX `idx_confirmed`(`confirmed`) USING BTREE COMMENT '确认状态索引',
  INDEX `idx_editor_org`(`editor_org_id`) USING BTREE COMMENT '经办机构索引',
  INDEX `idx_create_time`(`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '衍生费用管理表' ROW_FORMAT = Dynamic;