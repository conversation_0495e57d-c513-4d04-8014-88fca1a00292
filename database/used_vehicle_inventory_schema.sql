-- 二手车库存管理系统数据库设计
-- 创建时间: 2024-01-15
-- 说明: 基于用户需求的二手车库存管理表结构

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 二手车库存主表
-- ----------------------------
DROP TABLE IF EXISTS `used_vehicle_inventory`;
CREATE TABLE `used_vehicle_inventory` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据主键',

  -- 车辆基本信息
  `vehicle_type` varchar(50) NOT NULL COMMENT '车辆类型(必填)',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色(选填)',
  `vehicle_id` varchar(20) NOT NULL COMMENT '车牌号(必填)',
  `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
  `series` varchar(100) DEFAULT NULL COMMENT '型号',
  `vin` varchar(17) NOT NULL COMMENT '车架号(必填,17位)',
  `registration_date` date DEFAULT NULL COMMENT '注册日期',
  `mileage` decimal(10,2) DEFAULT NULL COMMENT '行驶里程(公里)',
  `check_deadline` date DEFAULT NULL COMMENT '审车到期日期',
  `insurance_deadline` date DEFAULT NULL COMMENT '保险到期时间',

  -- 库存来源信息
  `vehicle_source` varchar(20) NOT NULL COMMENT '库存来源(必填): 置换/收购',
  `inbound_org_id` bigint(20) NOT NULL COMMENT '收购单位(必填)',
  `inbound_payment_method` varchar(50) NOT NULL COMMENT '付款方式(必填)',
  `stock_org_id` bigint(20) NOT NULL COMMENT '车辆存放单位(必填)',
  `stock_status` varchar(20) NOT NULL COMMENT '库存状态(必填): 在库/已售',
  `inbound_date` date DEFAULT NULL COMMENT '入库日期(yyyy-MM-dd)',

  -- 入库相关信息
  `inbound_customer_id` varchar(50) DEFAULT NULL COMMENT '卖方客户标识',
  `vo_id` bigint(20) DEFAULT NULL COMMENT '关联新车订单id(选填)',
  `inbound_amount` bigint(20) DEFAULT NULL COMMENT '入库价格(分)',
  `inbound_agent_id` varchar(50) DEFAULT NULL COMMENT '入库专员',

  -- 线索信息
  `leads_source` varchar(20) DEFAULT '线下' COMMENT '收车线索来源: 线上/线下(默认线下)',
  `leads_provider_name` varchar(100) DEFAULT NULL COMMENT '销售线索提供者姓名',
  `leads_provider_phone` varchar(20) DEFAULT NULL COMMENT '销售线索提供者联系方式',

  -- 出库相关信息
  `outbound_org_id` bigint(20) DEFAULT NULL COMMENT '出库公司',
  `outbound_payment_method` varchar(50) DEFAULT NULL COMMENT '收款方式',
  `outbound_date` date DEFAULT NULL COMMENT '出库日期(yyyy-MM-dd)',
  `outbound_amount` bigint(20) DEFAULT NULL COMMENT '出库价格(分)',
  `outbound_method` varchar(20) DEFAULT NULL COMMENT '出库方式: 零售/批发',
  `outbound_agent_id` varchar(50) DEFAULT NULL COMMENT '出库人员',
  `outbound_customer_id` varchar(50) DEFAULT NULL COMMENT '买方客户标识',

  -- 计算字段
  `gross_profit` bigint(20) GENERATED ALWAYS AS (`outbound_amount` - `inbound_amount`) STORED COMMENT '毛利润(出库金额减入库金额)',

  -- 备注
  `remark` text DEFAULT NULL COMMENT '备注',

  -- 系统字段
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vehicle_id` (`vehicle_id`),
  UNIQUE KEY `uk_vin` (`vin`),
  KEY `idx_stock_org_id` (`stock_org_id`),
  KEY `idx_inbound_date` (`inbound_date`),
  KEY `idx_outbound_date` (`outbound_date`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='二手车库存表';

-- 设置外键约束
SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 插入示例数据
-- ----------------------------
INSERT INTO `used_vehicle_inventory` (
  `vehicle_type`, `color`, `vehicle_id`, `brand`, `series`, `vin`,
  `registration_date`, `mileage`, `check_deadline`, `insurance_deadline`,
  `vehicle_source`, `inbound_org_id`, `inbound_payment_method`, `stock_org_id`,
  `stock_status`, `inbound_date`, `inbound_customer_id`, `vo_id`,
  `inbound_amount`, `inbound_agent_id`, `leads_source`,
  `leads_provider_name`, `leads_provider_phone`,
  `outbound_org_id`, `outbound_payment_method`, `outbound_date`,
  `outbound_amount`, `outbound_method`, `outbound_agent_id`,
  `outbound_customer_id`, `remark`
) VALUES
(
  '轿车', '珍珠白', '京A12345', '奥迪', 'A6L', 'LSGKB54E8HS123456',
  '2020-03-15', 45000.00, '2025-03-15', '2024-12-31',
  '置换', 1, '银行转账', 1,
  '已售', '2024-01-15', 'CUST001', 12345,
  35000000, 'EMP001', '线下',
  '张销售', '13800138001',
  1, '现金', '2024-02-20',
  42000000, '零售', 'EMP002',
  'CUST002', '客户置换新车，车况良好'
),
(
  'SUV', '曜石黑', '沪B67890', '宝马', 'X5', 'WBAFR9C50DD123789',
  '2019-08-20', 68000.00, '2024-08-20', '2024-10-15',
  '收购', 2, '现金', 2,
  '在库', '2024-01-20', 'CUST003', NULL,
  28000000, 'EMP003', '线上',
  '李顾问', '13900139002',
  NULL, NULL, NULL,
  NULL, NULL, NULL,
  NULL, '收购车辆，待售'
),
(
  '轿车', '银色', '粤C11111', '奔驰', 'C级', 'WDD2050461F123456',
  '2021-06-10', 32000.00, '2025-06-10', '2024-11-30',
  '置换', 1, '分期付款', 1,
  '在库', '2024-02-01', 'CUST004', 67890,
  38000000, 'EMP001', '线下',
  '王经理', '13700137003',
  NULL, NULL, NULL,
  NULL, NULL, NULL,
  NULL, '置换豪华版新车'
);
