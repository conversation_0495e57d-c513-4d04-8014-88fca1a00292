/*
 Navicat Premium Data Transfer

 Source Server         : root@QJ-PROD
 Source Server Type    : MySQL
 Source Server Version : 80030
 Source Host           : bj-cynosdbmysql-grp-69qoyjdc.sql.tencentcdb.com:27849
 Source Schema         : dev_jwd

 Target Server Type    : MySQL
 Target Server Version : 80030
 File Encoding         : 65001

 Date: 01/07/2025 00:23:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for biz_org
-- ----------------------------
DROP TABLE IF EXISTS `biz_org`;
CREATE TABLE `biz_org` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '机构名称',
  `org_full_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构全称',
  `province` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省/直辖市代码，关联字典表',
  `city` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '市/区代码，关联字典表',
  `address` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
  `contact_person` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `sales_brands` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主营品牌，逗号分隔的字典值',
  `biz_permissions` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务权限，逗号分隔的字典值(can_stock_in,can_sell,can_stock_out,can_settle)',
  `org_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构类型，字典值(group,single_store,secondary_network)',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态(active:启用,inactive:停用)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNQ_NAME` (`org_name`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务组织架构表';

-- ----------------------------
-- Table structure for biz_org_member
-- ----------------------------
DROP TABLE IF EXISTS `biz_org_member`;
CREATE TABLE `biz_org_member` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_id` bigint NOT NULL COMMENT '机构ID，关联biz_organizations.id',
  `agent_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工ID/工号',
  `agent_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工姓名',
  `position` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职务',
  `business_role` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务角色，字典值(sales_manager,warehouse_keeper,finance_specialist等)',
  `data_range` text COLLATE utf8mb4_unicode_ci COMMENT '数据查询范围，逗号分割的biz_org_id字符串',
  `data_range_names` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态(active:启用,inactive:停用)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_agent` (`org_id`,`agent_id`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务组织成员表';

-- ----------------------------
-- Table structure for crm_customer
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer`;
CREATE TABLE `crm_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '更新者ID',
  `customer_type` varchar(20) NOT NULL COMMENT '客户分类(individual个人/institutional机构)',
  `deal_status` varchar(20) NOT NULL COMMENT '成交状态(LEADS/CUSTOMER)',
  `owner_org_id` bigint NOT NULL COMMENT '客户归属机构ID',
  `owner_org_name` varchar(100) NOT NULL COMMENT '客户归属机构名称',
  `owner_seller_id` bigint NOT NULL COMMENT '客户归属销售顾问ID',
  `owner_seller_name` varchar(100) NOT NULL COMMENT '客户归属销售顾问',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称',
  `customer_id_code` varchar(100) NOT NULL COMMENT '客户识别码（身份证号码/统一社会信用代码）',
  `mobile` varchar(100) NOT NULL COMMENT '联系方式',
  `address` varchar(255) DEFAULT NULL COMMENT '联络地址',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_org_id` (`owner_org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户信息表';

-- ----------------------------
-- Table structure for vehicle_order
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_order`;
CREATE TABLE `vehicle_order` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '数据主键',
  `order_type` varchar(255) NOT NULL DEFAULT 'normal' COMMENT '订单类型',
  `order_sn` varchar(64) NOT NULL COMMENT '订单编号',
  `order_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '订单日期',
  `customer_id` int unsigned NOT NULL COMMENT '客户ID',
  `sales_org_id` int unsigned NOT NULL COMMENT '销售单位ID',
  `sales_agent_id` int unsigned NOT NULL COMMENT '销售顾问ID',
  `sales_leader_id` int unsigned NOT NULL COMMENT '销售主管ID',
  `order_status` enum('pending','confirmed','delivered','canceled','archived') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `sku_id` int unsigned NOT NULL COMMENT 'vehicle_sku.id',
  `delivery_date` date NOT NULL COMMENT '交付日期',
  `delivery_org_id` int unsigned NOT NULL COMMENT '交付单位ID',
  `sb_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '启票总价（分）',
  `discount_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '优惠金额（分）',
  `discount_deductible` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠金额转车款',
  `sales_cost_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '销售费用（分）',
  `deal_amount` int unsigned NOT NULL DEFAULT '0' COMMENT '成交总价（分）',
  `deal_amount_cn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '成交总价大写',
  `payment_method` enum('FULL','LOAN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'FULL' COMMENT '付款方式',
  `loan_channel` enum('车贷通担保','建行','长安金融','上汽金融','其他') DEFAULT NULL COMMENT '贷款渠道',
  `loan_months` int DEFAULT NULL COMMENT '贷款期数',
  `loan_amount` int unsigned DEFAULT '0' COMMENT '贷款金额（分）',
  `loan_initial_amount` int unsigned DEFAULT '0' COMMENT '首付金额（分）',
  `loan_initial_ratio` decimal(5,2) GENERATED ALWAYS AS (if((`loan_amount` > 0),(`loan_initial_amount` / `loan_amount`),0)) STORED COMMENT '首付比例',
  `loan_rebate_deductible` tinyint DEFAULT NULL COMMENT '返利转车款',
  `used_vehicle_id` varchar(20) DEFAULT NULL COMMENT '二手车车牌号',
  `used_vehicle_vin` varchar(17) DEFAULT NULL COMMENT '二手车VIN',
  `used_vehicle_amount` int unsigned DEFAULT '0' COMMENT '置换金额（分）',
  `used_vehicle_deductible_amount` int unsigned DEFAULT '0' COMMENT '置换补贴（分）',
  `used_vehicle_discount_receivable_amount` int DEFAULT '0' COMMENT '收厂家补贴金额',
  `used_vehicle_discount_payable_amount` int DEFAULT '0' COMMENT '付客户补贴金额',
  `used_vehicle_discount_deductible` tinyint DEFAULT NULL COMMENT '返利转车款',
  `used_vehicle_brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '二手车品牌',
  `used_vehicle_model` varchar(50) DEFAULT NULL COMMENT '二手车车型',
  `used_vehicle_color` varchar(20) DEFAULT NULL COMMENT '二手车颜色',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint NOT NULL COMMENT '创建人ID',
  `editor_id` bigint NOT NULL COMMENT '最后编辑人ID',
  `deposit_type` varchar(255) DEFAULT NULL COMMENT '定金类型：线上/线下',
  `deposit_amount` int DEFAULT NULL COMMENT '定金金额',
  `deposit_deductible` tinyint DEFAULT NULL COMMENT '定金转车款',
  `carefree_income` bigint DEFAULT NULL,
  `delivery_equipment` bigint DEFAULT NULL,
  `extended_warranty_income` bigint DEFAULT NULL,
  `has_derivative_income` varchar(20) DEFAULT NULL,
  `has_gift_items` varchar(20) DEFAULT NULL,
  `has_insurance` varchar(20) DEFAULT NULL,
  `license_plate_fee` bigint DEFAULT '0',
  `notary_fee` bigint DEFAULT '0',
  `pre_interest` bigint DEFAULT '0',
  `temp_plate_fee` bigint DEFAULT '0',
  `vps_income` bigint DEFAULT '0',
  `other_income` bigint DEFAULT '0',
  `loan_fee` bigint DEFAULT '0' COMMENT '分期服务费(分)',
  `loan_rebate_receivable_amount` bigint DEFAULT '0' COMMENT '应收-机构-分期返利(分)',
  `loan_rebate_payable_amount` bigint DEFAULT '0' COMMENT '应付-客户-分期返利(分)',
  `remark` varchar(255) DEFAULT NULL COMMENT '订单备注',
  `exclusive_discount_amount` bigint DEFAULT '0' COMMENT '专项优惠金额',
  `exclusive_discount_type` varchar(255) DEFAULT NULL COMMENT '专项优惠类型',
  `exclusive_discount_deductible` tinyint DEFAULT NULL COMMENT '专项优惠抵车款',
  `exclusive_discount_remark` varchar(255) DEFAULT NULL COMMENT '专项优惠说明',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_sn` (`order_sn`),
  UNIQUE KEY `uniq_order_sn` (`order_sn`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_sales_org` (`sales_org_id`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_order_status` (`order_status`)
) ENGINE=InnoDB AUTO_INCREMENT=295 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售订单主表';

-- ----------------------------
-- Table structure for vehicle_sku
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_sku`;
CREATE TABLE `vehicle_sku` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `brand` varchar(255) DEFAULT NULL COMMENT '品牌',
  `series` varchar(255) DEFAULT NULL COMMENT '车系',
  `model_code` varchar(255) DEFAULT NULL COMMENT '车型代码',
  `model_name` varchar(255) DEFAULT NULL COMMENT '车型名称',
  `config_code` varchar(255) DEFAULT NULL COMMENT '配置代码',
  `config_name` varchar(255) DEFAULT NULL COMMENT '配置名称',
  `sku_id` varchar(255) DEFAULT NULL COMMENT '物料代码',
  `color_code` varchar(255) DEFAULT NULL COMMENT '颜色代码=物料代码第3段',
  `sb_price` int DEFAULT NULL COMMENT '启票单价',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_id` (`sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1357 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='车辆SKU信息表';

SET FOREIGN_KEY_CHECKS = 1;
