-- 完整的订单详情查询SQL
-- 包含所有关联表的信息：客户、车辆、机构、人员等

SELECT 
    -- 订单基本信息
    vo.id,
    vo.order_type,
    vo.order_sn,
    vo.order_date,
    vo.order_status,
    vo.delivery_date,
    vo.payment_method,
    vo.create_time,
    vo.update_time,
    vo.creator_id,
    vo.editor_id,
    vo.remark,
    
    -- 客户信息
    c.id as customer_id,
    c.customer_name,
    c.customer_type,
    c.deal_status,
    c.customer_id_code,
    c.mobile,
    c.address,
    c.remark as customer_remark,
    c.owner_org_id,
    c.owner_org_name,
    c.owner_seller_id,
    c.owner_seller_name,
    
    -- 销售机构信息
    sales_org.id as sales_org_id,
    sales_org.org_name as sales_org_name,
    sales_org.org_full_name as sales_org_full_name,
    sales_org.address as sales_org_address,
    sales_org.contact_person as sales_org_contact_person,
    sales_org.contact_phone as sales_org_contact_phone,
    
    -- 交付机构信息
    delivery_org.id as delivery_org_id,
    delivery_org.org_name as delivery_org_name,
    delivery_org.org_full_name as delivery_org_full_name,
    delivery_org.address as delivery_org_address,
    delivery_org.contact_person as delivery_org_contact_person,
    delivery_org.contact_phone as delivery_org_contact_phone,
    
    -- 销售顾问信息
    sales_agent.id as sales_agent_id,
    sales_agent.agent_name as sales_agent_name,
    sales_agent.position as sales_agent_position,
    sales_agent.business_role as sales_agent_role,
    
    -- 销售主管信息
    sales_leader.id as sales_leader_id,
    sales_leader.agent_name as sales_leader_name,
    sales_leader.position as sales_leader_position,
    sales_leader.business_role as sales_leader_role,
    
    -- 创建人信息
    creator.agent_name as creator_name,
    
    -- 编辑人信息
    editor.agent_name as editor_name,
    
    -- 车辆SKU信息
    vs.id as sku_id,
    vs.brand,
    vs.series,
    vs.model_code,
    vs.model_name,
    vs.config_code,
    vs.config_name,
    vs.sku_id as vehicle_sku_id,
    vs.color_code,
    vs.sb_price as sku_sb_price,
    
    -- 金额信息
    vo.sb_amount,
    vo.sales_amount,
    vo.deal_amount,
    vo.deal_amount_cn,
    vo.discount_amount,
    vo.discount_deductible,
    vo.sales_cost_amount,
    vo.gross_profit_amount,
    vo.gross_profit_rate,
    
    -- 专享优惠信息
    vo.exclusive_discount_type,
    vo.exclusive_discount_amount,
    vo.exclusive_discount_deductible,
    vo.exclusive_discount_remark,
    
    -- 定金信息
    vo.deposit_type,
    vo.deposit_amount,
    vo.deposit_deductible,
    
    -- 贷款信息
    vo.loan_channel,
    vo.loan_months,
    vo.loan_amount,
    vo.loan_initial_amount,
    vo.loan_initial_ratio,
    vo.loan_rebate_deductible,
    vo.loan_fee,
    vo.loan_rebate_receivable_amount,
    vo.loan_rebate_payable_amount,
    
    -- 二手车置换信息
    vo.used_vehicle_id,
    vo.used_vehicle_vin,
    vo.used_vehicle_amount,
    vo.used_vehicle_deductible_amount,
    vo.used_vehicle_discount_receivable_amount,
    vo.used_vehicle_discount_payable_amount,
    vo.used_vehicle_discount_deductible,
    vo.used_vehicle_brand,
    vo.used_vehicle_model,
    vo.used_vehicle_color,
    
    -- 其他衍生收入信息
    vo.has_derivative_income,
    vo.notary_fee,
    vo.carefree_income,
    vo.extended_warranty_income,
    vo.vps_income,
    vo.pre_interest,
    vo.license_plate_fee,
    vo.temp_plate_fee,
    vo.delivery_equipment,
    vo.other_income,
    
    -- 保险信息
    vo.has_insurance,
    vo.insurance_org_id,
    vo.insurance_org_name,
    
    -- 赠品信息
    vo.has_gift_items

FROM vehicle_order vo

-- 关联客户表
LEFT JOIN crm_customer c ON vo.customer_id = c.id

-- 关联销售机构表
LEFT JOIN biz_org sales_org ON vo.sales_org_id = sales_org.id

-- 关联交付机构表
LEFT JOIN biz_org delivery_org ON vo.delivery_org_id = delivery_org.id

-- 关联销售顾问表
LEFT JOIN biz_org_member sales_agent ON vo.sales_agent_id = sales_agent.id

-- 关联销售主管表
LEFT JOIN biz_org_member sales_leader ON vo.sales_leader_id = sales_leader.id

-- 关联创建人表
LEFT JOIN biz_org_member creator ON vo.creator_id = creator.id

-- 关联编辑人表
LEFT JOIN biz_org_member editor ON vo.editor_id = editor.id

-- 关联车辆SKU表
LEFT JOIN vehicle_sku vs ON vo.sku_id = vs.id

WHERE vo.id = ?;

-- 如果需要查询赠品明细，可以使用以下查询：
-- SELECT * FROM vehicle_order_gift_items WHERE order_id = ?;

-- 如果需要查询订单状态变更历史，可以使用以下查询：
-- SELECT * FROM vehicle_order_status_log WHERE order_id = ? ORDER BY create_time DESC;

-- 如果需要查询订单相关的收付款记录，可以使用以下查询：
-- SELECT * FROM finance_receivable WHERE order_id = ?;
-- SELECT * FROM finance_payable WHERE order_id = ?;
