CREATE TABLE `dev_jwd`.`system_option`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `option_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `option_group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `option_group_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `optionValue` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `optionLabel` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `option_order` int(0) NULL DEFAULT NULL,
  `option_comment` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
  `option_org_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `IDX_OPTION_GROUP_VALUE`(`option_group`, `optionValue`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 679 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- 插入字典选项数据
-- 礼品类别
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('gift_category', '礼品类别', 'CUSTOMIZE', 'GOODS', '商品', 1, '实物赠品', '57,58,59'),
('gift_category', '礼品类别', 'CUSTOMIZE', 'SERVICES', '服务', 2, '虚拟赠品', '57,58,59');

-- 在售品牌
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('vehicle_brand', '在售品牌', 'CUSTOMIZE', '深蓝', '深蓝', 2, '深蓝汽车', '57,58,59'),
('vehicle_brand', '在售品牌', 'CUSTOMIZE', '凯程', '凯程', 3, '凯程汽车', '57,58,59'),
('vehicle_brand', '在售品牌', 'CUSTOMIZE', '引力', '引力', 4, '引力汽车', '57,58,59'),
('vehicle_brand', '在售品牌', 'CUSTOMIZE', '阿维塔', '阿维塔', 5, '阿维塔汽车', '57,58,59'),
('vehicle_brand', '在售品牌', 'CUSTOMIZE', '启源', '启源', 6, '启源汽车', '57,58,59');

-- 应收对象
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('receivable_target', '应收对象', 'CUSTOMIZE', '客户', '客户', 1, '客户', '57,58,59'),
('receivable_target', '应收对象', 'CUSTOMIZE', '银行', '银行', 2, '银行', '57,58,59'),
('receivable_target', '应收对象', 'CUSTOMIZE', '保险公司', '保险公司', 3, '保险公司', '57,58,59'),
('receivable_target', '应收对象', 'CUSTOMIZE', '经销商', '经销商', 4, '经销商', '57,58,59'),
('receivable_target', '应收对象', 'CUSTOMIZE', '其他', '其他', 5, '其他', '57,58,59');

-- 贷款渠道
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('loan_channel', '贷款渠道', 'CUSTOMIZE', '车贷通担保', '车贷通担保', 1, '车贷通担保', '57,58,59'),
('loan_channel', '贷款渠道', 'CUSTOMIZE', '建行', '建行', 2, '建设银行', '57,58,59'),
('loan_channel', '贷款渠道', 'CUSTOMIZE', '长安金融', '长安金融', 3, '长安金融', '57,58,59'),
('loan_channel', '贷款渠道', 'CUSTOMIZE', '上汽金融', '上汽金融', 4, '上汽金融', '57,58,59'),
('loan_channel', '贷款渠道', 'CUSTOMIZE', '其他', '其他', 5, '其他金融机构', '57,58,59');

-- 贷款期限
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('loan_months', '贷款期限', 'CUSTOMIZE', '3', '3期', 1, '3个月', '57,58,59'),
('loan_months', '贷款期限', 'CUSTOMIZE', '6', '6期', 2, '6个月', '57,58,59'),
('loan_months', '贷款期限', 'CUSTOMIZE', '12', '12期', 3, '12个月', '57,58,59'),
('loan_months', '贷款期限', 'CUSTOMIZE', '24', '24期', 4, '24个月', '57,58,59'),
('loan_months', '贷款期限', 'CUSTOMIZE', '36', '36期', 5, '36个月', '57,58,59'),
('loan_months', '贷款期限', 'CUSTOMIZE', '60', '60期', 6, '60个月', '57,58,59');

-- 专享优惠类型
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('exclusive_discount_type', '专享优惠类型', 'CUSTOMIZE', 'GOVERNMENT', '政企优惠', 2, '政企客户专享优惠', '57,58,59'),
('exclusive_discount_type', '专享优惠类型', 'CUSTOMIZE', 'WORK_CAR', '工作车优惠', 3, '工作车专享优惠', '57,58,59'),
('exclusive_discount_type', '专享优惠类型', 'CUSTOMIZE', 'INTERNAL', '内购优惠', 4, '员工内购专享优惠', '57,58,59'),
('exclusive_discount_type', '专享优惠类型', 'CUSTOMIZE', 'TEST_DRIVE', '试乘试驾车优惠', 5, '试乘试驾车专享优惠', '57,58,59'),
('exclusive_discount_type', '专享优惠类型', 'CUSTOMIZE', 'OTHERS', '其他请备注', 5, '其他请填写订单备注', '57,58,59');

-- 定金状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('deposit_status', '定金状态', 'SYSTEM', 'received', '已收定金', 1, '已收到客户定金', '57,58,59'),
('deposit_status', '定金状态', 'SYSTEM', 'refunded', '已退定金', 2, '已退还客户定金', '57,58,59'),
('deposit_status', '定金状态', 'SYSTEM', 'transferred', '已转车款', 3, '定金已转入车款', '57,58,59');

-- 业务权限
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('business_permission', '业务权限', 'SYSTEM', 'can_stock_in', '可入库', 1, '允许库存入库操作', '57,58,59'),
('business_permission', '业务权限', 'SYSTEM', 'can_sell', '可销售', 2, '允许销售操作', '57,58,59'),
('business_permission', '业务权限', 'SYSTEM', 'can_stock_out', '可出库', 3, '允许库存出库操作', '57,58,59'),
('business_permission', '业务权限', 'SYSTEM', 'can_settle', '可结算', 4, '允许财务结算操作', '57,58,59');

-- 机构类型
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('org_type', '机构类型', 'SYSTEM', 'group', '集团', 1, '集团总部', '57,58,59'),
('org_type', '机构类型', 'SYSTEM', 'single_store', '单店', 2, '单一门店', '57,58,59'),
('org_type', '机构类型', 'SYSTEM', 'direct_sale_store', '直营店', 3, '分公司直营店', '57,58,59'),
('org_type', '机构类型', 'SYSTEM', 'secondary_network', '二网', 3, '二级网络', '57,58,59'),
('org_type', '机构类型', 'SYSTEM', 'external_partner', '集团外合作方', 4, '集团外合作方', '57,58,59');

-- 仓储状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('stock_status', '仓储状态', 'SYSTEM', 'transiting', '在途', 1, '货物运输中', '57,58,59'),
('stock_status', '仓储状态', 'SYSTEM', 'stocking', '在库', 2, '货物已入库', '57,58,59'),
('stock_status', '仓储状态', 'SYSTEM', 'sold', '已售', 3, '货物已售出', '57,58,59'),
('stock_status', '仓储状态', 'SYSTEM', 'returned', '已退', 4, '货物已退回', '57,58,59');

-- 调拨状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('transfer_status', '调拨状态', 'SYSTEM', 'pending', '待确认', 1, '调拨申请待确认', '57,58,59'),
('transfer_status', '调拨状态', 'SYSTEM', 'received', '已接收', 2, '调拨申请已接收', '57,58,59'),
('transfer_status', '调拨状态', 'SYSTEM', 'rejected', '已拒绝', 3, '调拨申请已拒绝', '57,58,59');

-- 车辆来源
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('vehicle_source', '车辆来源', 'CUSTOMIZE', 'VEHICLE_EXCHANGE', '车辆置换', 1, '通过车辆置换获得', '57,58,59'),
('vehicle_source', '车辆来源', 'CUSTOMIZE', 'VEHICLE_ACQ', '外部收购', 2, '从外部收购获得', '57,58,59');

-- 订单状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('order_status', '订单状态', 'SYSTEM', 'pending', '待处理', 2, '订单需要修改', '57,58,59'),
('order_status', '订单状态', 'SYSTEM', 'confirmed', '已确认', 2, '订单已确认', '57,58,59'),
('order_status', '订单状态', 'SYSTEM', 'delivered', '已交付', 3, '订单已交付', '57,58,59'),
('order_status', '订单状态', 'SYSTEM', 'canceled', '已取消', 4, '订单已取消', '57,58,59');

-- 客户类型
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('customer_type', '客户类型', 'CUSTOMIZE', 'individual', '个人客户', 1, '个人客户', '57,58,59'),
('customer_type', '客户类型', 'CUSTOMIZE', 'institutional', '法人客户', 2, '法人客户', '57,58,59');

-- 成交状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('deal_status', '成交状态', 'SYSTEM', 'LEADS', '未成交', 1, '潜在客户，未成交', '57,58,59'),
('deal_status', '成交状态', 'SYSTEM', 'CUSTOMER', '已成交', 2, '已成交客户', '57,58,59');

-- 保险返利确认状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('rebate_status', '保险返利确认状态', 'SYSTEM', 'PENDING', '待返利', 1, '等待返利处理', '57,58,59'),
('rebate_status', '保险返利确认状态', 'SYSTEM', 'COMPLETED', '已返利', 3, '返利已完成', '57,58,59');

-- 衍生成本确认状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('derivative_costs_confirm_status', '衍生成本确认状态', 'SYSTEM', 'UNCONFIRMED', '未确认', 1, '衍生成本未确认', '57,58,59'),
('derivative_costs_confirm_status', '衍生成本确认状态', 'SYSTEM', 'CONFIRMED', '已确认', 2, '衍生成本已确认', '57,58,59');

-- 销售地类型
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('sales_store_type', '销售地类型', 'SYSTEM', '4S', '单店', 1, '4S店销售', '57,58,59'),
('sales_store_type', '销售地类型', 'SYSTEM', '3S', '直营店', 2, '3S直营店销售', '57,58,59'),
('sales_store_type', '销售地类型', 'SYSTEM', '2S', '二网', 3, '2S二网销售', '57,58,59');

-- 收款状态
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('recept_status', '收款状态', 'SYSTEM', 'NO', '未收款', 1, '未收款', '57,58,59'),
('recept_status', '收款状态', 'SYSTEM', 'YES', '已收款', 2, '已收款', '57,58,59');

-- 保险公司
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('insurance_provider', '保险公司', 'CUSTOMIZE', 'PICC', '中国人保', 1, '中国人民保险集团股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'CPIC', '太平洋保险', 2, '中国太平洋保险(集团)股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'PINGAN', '平安保险', 3, '中国平安保险(集团)股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'CLIC', '中国人寿', 4, '中国人寿保险股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'TAIKANG', '泰康保险', 5, '泰康保险集团股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'SUNSHINE', '阳光保险', 6, '阳光保险集团股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'TIANAN', '天安保险', 7, '天安财产保险股份有限公司', '57,58,59'),
('insurance_provider', '保险公司', 'CUSTOMIZE', 'OTHERS', '其他', 8, '其他保险公司', '57,58,59');

-- 资金类型
INSERT INTO `dev_jwd`.`system_option` (`option_group`, `option_group_name`, `option_group_type`, `optionValue`, `optionLabel`, `option_order`, `option_comment`, `option_org_range`) VALUES
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_兵财_重庆_深蓝销司', '三方信贷_兵财_重庆_深蓝销司', 1, '三方信贷兵财重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '兵财融资_重庆_深蓝销司', '兵财融资_重庆_深蓝销司', 2, '兵财融资重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_浦发银行_重庆_深蓝销司', '三方信贷_浦发银行_重庆_深蓝销司', 3, '三方信贷浦发银行重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_华夏银行_重庆_深蓝销司', '三方信贷_华夏银行_重庆_深蓝销司', 4, '三方信贷华夏银行重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_光大银行_重庆_深蓝销司', '三方信贷_光大银行_重庆_深蓝销司', 5, '三方信贷光大银行重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_华夏银行_重庆_深蓝销司_特殊', '三方信贷_华夏银行_重庆_深蓝销司_特殊', 6, '三方信贷华夏银行重庆深蓝销司特殊', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_兴业银行_重庆_深蓝销司_特殊', '三方信贷_兴业银行_重庆_深蓝销司_特殊', 7, '三方信贷兴业银行重庆深蓝销司特殊', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '现金_重庆_深蓝销司', '现金_重庆_深蓝销司', 8, '现金重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_兴业银行_重庆_深蓝销司', '三方信贷_兴业银行_重庆_深蓝销司', 9, '三方信贷兴业银行重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_其它银行_重庆_深蓝销司_特殊', '三方信贷_其它银行_重庆_深蓝销司_特殊', 10, '三方信贷其它银行重庆深蓝销司特殊', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '三方信贷_光大银行_重庆_深蓝销司_特殊', '三方信贷_光大银行_重庆_深蓝销司_特殊', 11, '三方信贷光大银行重庆深蓝销司特殊', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '承兑汇票_重庆_深蓝销司', '承兑汇票_重庆_深蓝销司', 12, '承兑汇票重庆深蓝销司', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '信贷保证金_华夏', '信贷保证金_华夏', 13, '信贷保证金华夏', '57,58,59'),
('fund_type', '资金类型', 'CUSTOMIZE', '信贷保证金_兵财', '信贷保证金_兵财', 14, '信贷保证金兵财', '57,58,59');