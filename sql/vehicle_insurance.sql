-- 车险表 DDL
CREATE TABLE `vehicle_insurance` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `vehicle_order_id` BIGINT NOT NULL COMMENT '新车订单ID',
  `ci_fee` INT NOT NULL DEFAULT 0 COMMENT '交强险保费，单位：分',
  `ci_rebate` INT NOT NULL DEFAULT 0 COMMENT '交强险返利，单位：分',
  `bi_fee` INT NOT NULL DEFAULT 0 COMMENT '商业险保费，单位：分',
  `bi_rebate` INT NOT NULL DEFAULT 0 COMMENT '商业险返利，单位：分',
  `oi_fee` INT NOT NULL DEFAULT 0 COMMENT '非车险保费，单位：分',
  `oi_rebate` INT NOT NULL DEFAULT 0 COMMENT '非车险返利，单位：分',
  `rebate_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '返利状态：PENDING-待返利，PROCESSING-返利中，COMPLETED-已返利，FAILED-返利失败',
  `biz_org_id` BIGINT NOT NULL COMMENT '经办机构ID',
  `biz_agent_id` BIGINT NOT NULL COMMENT '经办员工ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_vehicle_order_id` (`vehicle_order_id`),
  INDEX `idx_biz_org_id` (`biz_org_id`),
  INDEX `idx_biz_agent_id` (`biz_agent_id`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车险表';
